diff --git a/components/wd-input-number/types.ts b/components/wd-input-number/types.ts
index 5a0f31b5b7e9b937a0f5545ec810c28598d2d533..774f1cd8715ccf2db007d20870a0b0a17f7542c0 100644
--- a/components/wd-input-number/types.ts
+++ b/components/wd-input-number/types.ts
@@ -1,13 +1,13 @@
 /*
  * @Author: weisheng
  * @Date: 2024-03-15 20:40:34
- * @LastEditTime: 2025-02-19 12:47:54
+ * @LastEditTime: 2025-05-28 16:24:32
  * @LastEditors: weisheng
  * @Description:
- * @FilePath: /wot-design-uni/src/uni_modules/wot-design-uni/components/wd-input-number/types.ts
+ * @FilePath: /lsym-cx-mini/node_modules/.pnpm_patches/wot-design-uni@1.9.1/components/wd-input-number/types.ts
  * 记得注释
  */
-import type { PropType } from 'vue'
+import type { ExtractPropTypes, PropType } from 'vue'
 import { baseProps, makeBooleanProp, makeNumberProp, makeNumericProp, makeRequiredProp, makeStringProp, numericProp } from '../common/props'
 
 /**
@@ -19,20 +19,6 @@ export type InputNumberBeforeChange = (value: number | string) => boolean | Prom
 
 export type OperationType = 'add' | 'sub'
 
-/**
- * 输入数字组件事件类型枚举
- * Input: 用户输入事件
- * Blur: 失焦事件
- * Watch: 监听值变化事件
- * Button: 按钮点击事件
- */
-export enum InputNumberEventType {
-  Input = 'input',
-  Blur = 'blur',
-  Watch = 'watch',
-  Button = 'button'
-}
-
 export const inputNumberProps = {
   ...baseProps,
   /**
@@ -102,5 +88,11 @@ export const inputNumberProps = {
   /**
    * 是否开启长按加减手势
    */
-  longPress: makeBooleanProp(false)
+  longPress: makeBooleanProp(false),
+  /**
+   * 是否立即更新模式，false 时仅在失焦和按钮点击时更新
+   */
+  immediate: makeBooleanProp(true)
 }
+
+export type InputNumberProps = ExtractPropTypes<typeof inputNumberProps>
diff --git a/components/wd-input-number/wd-input-number.vue b/components/wd-input-number/wd-input-number.vue
index 320b8dc53238b59891915848324d092d46242c5e..53866332e7fdb989428d8ce2a2fe413ca3827447 100644
--- a/components/wd-input-number/wd-input-number.vue
+++ b/components/wd-input-number/wd-input-number.vue
@@ -14,7 +14,8 @@
       <input
         class="wd-input-number__input"
         :style="`${inputWidth ? 'width: ' + inputWidth : ''}`"
-        type="digit"
+        type="number"
+        :input-mode="precision ? 'decimal' : 'numeric'"
         :disabled="disabled || disableInput"
         :value="String(inputValue)"
         :placeholder="placeholder"
@@ -52,7 +53,7 @@ export default {
 import wdIcon from '../wd-icon/wd-icon.vue'
 import { computed, nextTick, ref, watch } from 'vue'
 import { isDef, isEqual } from '../common/util'
-import { inputNumberProps, InputNumberEventType, type OperationType } from './types'
+import { inputNumberProps, type OperationType } from './types'
 import { callInterceptor } from '../common/interceptor'
 
 const props = defineProps(inputNumberProps)
@@ -64,32 +65,33 @@ let longPressTimer: ReturnType<typeof setTimeout> | null = null // 长按定时
  * 判断数字是否达到最小值限制
  */
 const minDisabled = computed(() => {
-  const value = formatValue(inputValue.value)
+  const value = getNumericValue(inputValue.value) // 计算禁用状态时使用数值
   const { disabled, min, step } = props
-  return disabled || Number(value) <= min || changeStep(value, -step) < min
+  return disabled || value <= min || changeStep(value, -step) < min
 })
 
 /**
  * 判断数字是否达到最大值限制
  */
 const maxDisabled = computed(() => {
-  const value = formatValue(inputValue.value)
+  const value = getNumericValue(inputValue.value) // 计算禁用状态时使用数值
   const { disabled, max, step } = props
-  return disabled || Number(value) >= max || changeStep(value, step) > max
+  return disabled || value >= max || changeStep(value, step) > max
 })
 
 // 监听 modelValue 变化
 watch(
   () => props.modelValue,
   (value) => {
-    updateValue(value, InputNumberEventType.Watch)
+    syncFromProp(value)
   }
 )
 
 // 监听 max, min, precision 变化
 watch([() => props.max, () => props.min, () => props.precision], () => {
-  const value = formatValue(inputValue.value)
-  updateValue(value, InputNumberEventType.Watch)
+  const value = getNumericValue(inputValue.value) // 属性变化时先获取数值
+  const formatted = formatValueForDisplay(value) // 然后格式化为显示值
+  syncFromProp(formatted)
 })
 
 /**
@@ -104,17 +106,19 @@ function isValueEqual(value1: number | string, value2: number | string) {
 
 // 获取初始值
 function getInitValue() {
-  const formatted = formatValue(props.modelValue)
-  if (!isValueEqual(formatted, props.modelValue)) {
-    emit('update:modelValue', formatted)
-  }
-  return formatted
+  return props.modelValue
 }
 
-// 将值转换为指定精度
+// 将值转换为指定精度（数值计算）
 function toPrecision(value: number) {
   const precision = Number(props.precision)
-  return Number(parseFloat(`${Math.round(value * Math.pow(10, precision)) / Math.pow(10, precision)}`).toFixed(precision))
+  return Math.round(value * Math.pow(10, precision)) / Math.pow(10, precision)
+}
+
+// 将值格式化为指定精度的字符串（用于显示）
+function formatToPrecisionString(value: number) {
+  const precision = Number(props.precision)
+  return value.toFixed(precision)
 }
 
 /**
@@ -144,90 +148,333 @@ function toStrictlyStep(value: number | string) {
   return (Math.round(Number(value) / props.step) * precisionFactory * props.step) / precisionFactory
 }
 
-// 内部更新处理函数
-function doUpdate(value: string | number) {
-  inputValue.value = value
-  const formatted = formatValue(value)
-  nextTick(() => {
-    inputValue.value = formatted
-    emit('update:modelValue', inputValue.value)
-    emit('change', { value: inputValue.value })
-  })
+/**
+ * 在严格步进模式下应用边界限制
+ * @param value 当前值
+ * @param min 最小值
+ * @param max 最大值
+ * @returns 符合边界和步进规则的值
+ */
+function applyStrictBounds(value: number, min: number, max: number): number {
+  if (!props.stepStrictly) {
+    return Math.min(Math.max(value, min), max)
+  }
+
+  // 在严格步进模式下，需要找到符合步进规则且在边界内的值
+
+  // 如果当前值在边界内，直接返回
+  if (value >= min && value <= max) {
+    return value
+  }
+
+  // 使用高精度计算
+  const stepPrecision = getPrecision(props.step)
+  const factor = Math.pow(10, stepPrecision)
+
+  // 如果值小于最小值，寻找大于等于min的最近步进值
+  if (value < min) {
+    // 计算大于等于min的最小步进倍数
+    const minSteps = Math.ceil((min * factor) / (props.step * factor))
+    const candidate = toPrecision((minSteps * props.step * factor) / factor)
+
+    // 如果候选值超过最大值，返回小于等于max的最大步进值
+    if (candidate > max) {
+      const maxSteps = Math.floor((max * factor) / (props.step * factor))
+      return toPrecision((maxSteps * props.step * factor) / factor)
+    }
+    return candidate
+  }
+
+  // 如果值大于最大值，寻找小于等于max的最近步进值
+  if (value > max) {
+    // 计算小于等于max的最大步进倍数
+    const maxSteps = Math.floor((max * factor) / (props.step * factor))
+    const candidate = toPrecision((maxSteps * props.step * factor) / factor)
+
+    // 如果候选值小于最小值，返回大于等于min的最小步进值
+    if (candidate < min) {
+      const minSteps = Math.ceil((min * factor) / (props.step * factor))
+      return toPrecision((minSteps * props.step * factor) / factor)
+    }
+    return candidate
+  }
+
+  return value
 }
 
 /**
- * 清理输入字符串中多余的小数点
- * @param value 输入的字符串
- * @returns 清理后的字符串
+ * 统一的数值标准化函数
+ * 应用严格步进、边界限制和精度处理
+ * @param value 要标准化的值
+ * @returns 标准化后的值
  */
-function cleanExtraDecimal(value: string): string {
-  const precisionAllowed = Number(props.precision) > 0
-  if (precisionAllowed) {
-    const dotIndex = value.indexOf('.')
-    if (dotIndex === -1) {
-      return value
-    } else {
-      const integerPart = value.substring(0, dotIndex + 1)
-      // 去除后续出现的'.'
-      const decimalPart = value.substring(dotIndex + 1).replace(/\./g, '')
-      return integerPart + decimalPart
+function normalizeValue(value: number): number {
+  let result = value
+
+  // 应用严格步进规则
+  if (props.stepStrictly) {
+    result = toStrictlyStep(result)
+  }
+
+  // 应用边界限制（考虑严格步进）
+  result = applyStrictBounds(result, props.min, props.max)
+
+  // 应用精度处理
+  if (isDef(props.precision)) {
+    result = toPrecision(result)
+  }
+
+  return result
+}
+
+/**
+ * 检查值是否为中间输入状态（如 "1.", ".5", "-" 等）
+ * @param value 要检查的值
+ * @returns 是否为中间状态
+ */
+function isIntermediateValue(value: string): boolean {
+  if (!value) return false
+
+  const str = String(value)
+  // 以小数点结尾（如 "1."）
+  if (str.endsWith('.')) return true
+
+  // 以小数点开头（如 ".5"）
+  if (str.startsWith('.') || str.startsWith('-.')) return true
+
+  // 只有负号
+  if (str === '-') return true
+
+  // 如果存在位数precision限制，且值为整数，也应当是中间态
+  if (props.precision && str.indexOf('.') === -1) return true
+
+  return false
+}
+
+/**
+ * 清理和格式化输入值（用于输入过程中）
+ * @param value 要处理的值
+ * @returns 处理后的值
+ */
+function cleanAndFormatInput(value: string): string {
+  if (!value) return ''
+
+  // 移除非数字和小数点的字符（保留负号）
+  let cleaned = value.replace(/[^\d.-]/g, '')
+
+  // 处理负号：只允许在开头
+  const hasNegative = cleaned.startsWith('-')
+  cleaned = cleaned.replace(/-/g, '')
+  if (hasNegative) {
+    cleaned = '-' + cleaned
+  }
+
+  // 处理小数点
+  const precision = Number(props.precision)
+  if (precision > 0) {
+    // 允许小数的情况
+    const parts = cleaned.split('.')
+    if (parts.length > 2) {
+      // 多个小数点，只保留第一个
+      cleaned = parts[0] + '.' + parts.slice(1).join('')
     }
   } else {
-    // 不允许小数：保留整数部分
-    const dotIndex = value.indexOf('.')
-    return dotIndex !== -1 ? value.substring(0, dotIndex) : value
+    // 不允许小数，移除小数点
+    cleaned = cleaned.split('.')[0]
+  }
+
+  // 处理以小数点开头的情况：".5" -> "0.5"
+  if (cleaned.startsWith('.')) {
+    return '0' + cleaned
   }
+  if (cleaned.startsWith('-.')) {
+    return '-0' + cleaned.substring(1)
+  }
+
+  return cleaned
 }
 
 /**
- * 更新输入框的值
- * @param value 新的值
- * @param eventType 触发更新的事件类型
+ * 同步属性变化，不触发拦截器
+ * @param value 新值
  */
-function updateValue(value: string | number, eventType: InputNumberEventType = InputNumberEventType.Input) {
-  const fromUser = eventType !== InputNumberEventType.Watch // watch时不认为是用户直接输入
-  const forceFormat = eventType === InputNumberEventType.Blur || eventType === InputNumberEventType.Button
-  // 对于 Input 和 Watch 类型，如果值以'.'结尾，则直接更新，不进行格式化
-  if ((eventType === InputNumberEventType.Input || eventType === InputNumberEventType.Watch) && String(value).endsWith('.') && props.precision) {
-    inputValue.value = value
-    nextTick(() => {
-      inputValue.value = cleanExtraDecimal(String(value))
-      emit('update:modelValue', inputValue.value)
-      emit('change', { value: inputValue.value })
-    })
-    return
+function syncFromProp(value: string | number) {
+  const formatted = formatValueForDisplay(value) // 属性同步时使用显示格式化
+  inputValue.value = formatted
+}
+
+/**
+ * 更新输入框显示值，不提交到 modelValue
+ * @param value 新值
+ */
+function updateDisplayValue(value: string | number) {
+  inputValue.value = value
+}
+
+/**
+ * 获取纯数值（用于计算）
+ * @param value 输入值
+ * @returns 标准化后的数值，或者在allowNull且为空时返回特殊标记
+ */
+function getNumericValue(value: string | number): number {
+  // 如果允许空值，且值为空，返回NaN以便后续处理
+  if (props.allowNull && (!isDef(value) || value === '')) {
+    return NaN
   }
-  if (!forceFormat && isValueEqual(value, inputValue.value)) {
-    return
+
+  let str = String(value)
+
+  // 如果不允许空值，但值为空，则使用最小值
+  if (!props.allowNull && (!isDef(value) || value === '')) {
+    return props.min
+  }
+
+  // 处理中间状态（失焦时使用）
+  if (isIntermediateValue(str)) {
+    // "1." -> "1"
+    if (str.endsWith('.')) {
+      str = str.slice(0, -1)
+    }
+    // ".5" -> "0.5"
+    if (str.startsWith('.')) {
+      str = '0' + str
+    }
+    if (str.startsWith('-.')) {
+      str = '-0' + str.substring(1)
+    }
+    // "-" -> 使用最小值
+    if (str === '-' || str === '') {
+      return props.min
+    }
+  }
+
+  let numericValue = Number(str)
+
+  if (isNaN(numericValue)) {
+    numericValue = props.min
   }
 
-  const update = () => doUpdate(value)
+  // 统一的数值标准化处理
+  return normalizeValue(numericValue)
+}
+
+/**
+ * 格式化值用于显示
+ * @param value 输入值
+ * @returns 格式化后的显示值（字符串或数值）
+ */
+function formatValueForDisplay(value: string | number): string | number {
+  // 如果允许空值，且值为空，直接返回空值
+  if (props.allowNull && (!isDef(value) || value === '')) {
+    return ''
+  }
+
+  const numericValue = getNumericValue(value)
+
+  // 如果定义了精度，返回格式化后的字符串
+  if (isDef(props.precision)) {
+    return formatToPrecisionString(numericValue)
+  }
+
+  return numericValue
+}
+
+/**
+ * 格式化并提交值到 modelValue
+ * @param value 要提交的值
+ */
+function commitValue(value: string | number) {
+  // 特殊处理allowNull情况
+  if (props.allowNull && (!isDef(value) || value === '')) {
+    const displayValue = ''
+
+    // 只有在值真正发生变化时才触发更新
+    if (isValueEqual('', props.modelValue)) {
+      updateDisplayValue(displayValue)
+      return
+    }
+
+    const doCommit = () => {
+      updateDisplayValue(displayValue)
+      emit('update:modelValue', '')
+      emit('change', { value: '' })
+    }
 
-  if (fromUser) {
     callInterceptor(props.beforeChange, {
-      args: [value],
-      done: update
+      args: [''],
+      done: doCommit
     })
-  } else {
-    update()
+    return
+  }
+
+  const numericValue = getNumericValue(value) // 获取用于提交的数值
+  const displayValue = formatValueForDisplay(value) // 获取用于显示的格式化值
+
+  // 只有在值真正发生变化时才触发更新
+  if (isValueEqual(numericValue, props.modelValue)) {
+    // 即使 modelValue 没变化，也要确保显示值是格式化后的
+    updateDisplayValue(displayValue)
+    return
   }
+
+  const doCommit = () => {
+    updateDisplayValue(displayValue)
+    emit('update:modelValue', numericValue)
+    emit('change', { value: numericValue })
+  }
+
+  callInterceptor(props.beforeChange, {
+    args: [numericValue],
+    done: doCommit
+  })
+}
+
+/**
+ * 处理失焦事件，强制格式化并提交
+ * @param value 失焦时的值
+ */
+function handleBlurValue(value: string | number) {
+  commitValue(value)
+}
+
+/**
+ * 处理按钮点击，直接提交
+ * @param value 按钮操作后的值
+ */
+function handleButtonValue(value: string | number) {
+  commitValue(value)
 }
 
 // 根据步进值改变值
 function changeStep(val: string | number, step: number) {
   val = Number(val)
   if (isNaN(val)) {
-    return props.min
+    // 如果值无效，使用最小值并标准化
+    return normalizeValue(props.min)
   }
   const precision = Math.max(getPrecision(val), getPrecision(step))
   const precisionFactor = Math.pow(10, precision)
-  return toPrecision((val * precisionFactor + step * precisionFactor) / precisionFactor)
+  const result = (val * precisionFactor + step * precisionFactor) / precisionFactor
+  return normalizeValue(result)
 }
 
 function changeValue(step: number) {
-  if ((step < 0 && (minDisabled.value || props.disableMinus)) || (step > 0 && (maxDisabled.value || props.disablePlus))) return
-  const value = changeStep(inputValue.value, step)
-  updateValue(value, InputNumberEventType.Button)
+  // 检查按钮是否应该被禁用
+  if ((step < 0 && (minDisabled.value || props.disableMinus)) || (step > 0 && (maxDisabled.value || props.disablePlus))) {
+    return
+  }
+
+  // 计算新值
+  let newValue = changeStep(inputValue.value, step)
+
+  // 强制确保值严格在最大最小值范围内
+  newValue = Math.min(Math.max(newValue, props.min), props.max)
+  // 如果开启精度控制，确保精度正确
+  if (isDef(props.precision)) {
+    newValue = toPrecision(newValue)
+  }
+
+  handleButtonValue(newValue)
 }
 
 // 增减值
@@ -236,14 +483,88 @@ function handleClick(type: OperationType) {
   changeValue(diff)
 }
 
+/**
+ * 处理空值输入的逻辑
+ * @param rawValue 原始空值
+ * @returns 是否处理了空值（true表示已处理，false表示需要继续处理）
+ */
+function handleEmptyInput(rawValue: string): boolean {
+  if (rawValue !== '') return false
+
+  updateDisplayValue('')
+
+  // 在立即更新模式且允许空值时，提交空值
+  if (props.immediate && props.allowNull) {
+    commitValue('')
+  }
+
+  return true
+}
+
+/**
+ * 处理中间状态输入的逻辑
+ * @param processedValue 处理后的值
+ * @returns 是否为中间状态（true表示是中间状态，false表示不是）
+ */
+function handleIntermediateInput(processedValue: string): boolean {
+  const precision = Number(props.precision)
+  const isIntermediate = precision > 0 && isIntermediateValue(processedValue)
+
+  if (isIntermediate) {
+    updateDisplayValue(processedValue)
+  }
+
+  return isIntermediate
+}
+
+/**
+ * 处理正常输入的逻辑
+ * @param processedValue 处理后的值
+ */
+function handleNormalInput(processedValue: string): void {
+  updateDisplayValue(processedValue)
+
+  // 根据 immediate 模式决定是否立即提交
+  if (props.immediate) {
+    commitValue(processedValue)
+  }
+}
+
+/**
+ * 异步处理输入值的核心逻辑
+ * @param rawValue 原始输入值
+ */
+function processInputValue(rawValue: string): void {
+  // 处理空值情况
+  if (handleEmptyInput(rawValue)) {
+    return
+  }
+
+  // 清理和格式化输入值
+  const processedValue = cleanAndFormatInput(rawValue)
+
+  // 处理中间输入状态
+  if (handleIntermediateInput(processedValue)) {
+    return
+  }
+
+  // 处理正常输入
+  handleNormalInput(processedValue)
+}
+
 function handleInput(event: any) {
   const rawValue = event.detail.value || ''
-  updateValue(rawValue, InputNumberEventType.Input)
+
+  // 立即更新显示值为用户输入的原始值，确保视图及时响应
+  updateDisplayValue(rawValue)
+
+  // 在 nextTick 中处理格式化和提交逻辑
+  nextTick(() => processInputValue(rawValue))
 }
 
 function handleBlur(event: any) {
   const value = event.detail.value || ''
-  updateValue(value, InputNumberEventType.Blur)
+  handleBlurValue(value)
   emit('blur', { value })
 }
 
@@ -284,31 +605,6 @@ function clearlongPressTimer() {
 function handleFocus(event: any) {
   emit('focus', event.detail)
 }
-
-// 格式化值
-function formatValue(value: string | number) {
-  if (props.allowNull && (!isDef(value) || value === '')) {
-    return ''
-  }
-
-  let formatted = Number(value)
-
-  if (isNaN(formatted)) {
-    formatted = props.min
-  }
-
-  if (props.stepStrictly) {
-    formatted = toStrictlyStep(value)
-  }
-
-  formatted = Math.min(Math.max(formatted, props.min), props.max)
-
-  if (isDef(props.precision)) {
-    formatted = toPrecision(formatted)
-  }
-
-  return formatted
-}
 </script>
 
 <style lang="scss" scoped>
