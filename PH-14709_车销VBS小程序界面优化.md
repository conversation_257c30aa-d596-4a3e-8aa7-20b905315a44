# PH-14709 车销VBS小程序界面优化

| 日期 | 版本 | 修订内容 | 修订人 |
| ---------- | ---- | -------- | ------ |
| 2025/07/31 | 1.0 | 新建 | 齐梦越 |

## 1.需求背景
车销小程序用户在使用中遇到以下体验问题：
1. **商品规格折叠**：购物车中默认仅展示默认领货规格，非默认领货规格折叠，在车销业务过程中，用户需频繁点击【展开】按钮录入其他规格；录入其他规格后折叠，用户无法直观看到其他规格的数量录入情况；
2. **界面字体较小**：界面字体和录入商品数量显示很小，用户年龄在40-60岁范围，容易误操作输入错误的规格数量；
3. **分类布局太大**：领货界面侧边栏商品分类占用大部分面积，导致界面字体布局不足，而车销业务员在领货过程中并不参考商品分类：
    -   新司机：按照仓库货品的摆放，在小程序中按照商品名称进行搜索，一边下单一边拣货。
    -   老司机：直接通过小程序商品界面进行下单，从上至下逐一录入商品，先下单再拣货。
4. **商品名称难辨**：部分商品名称十分相近，司机下品时容易重复下同一个品，需要将已下过的品更加直观的展示出来，告知司机此品已在购物车中。

## 2.需求目标
1. 小程序所有商品录入和单据详情页面，规格全部展开，并按照规格从大到小的顺序展示，便于用户选择查看；
2. 商品规格字体、商品录入区域调大；
3. 适应用户操作习惯，商品录入页面 左侧分类板块 挪至顶部展示；
4. 商品录入页面，添加商品数量后，对商品栏增加特殊标记区分名称，避免录错录重。

## 3.功能特性
- 商品录入页面调整
	- 内容
		- 商品规格全部展开，取消【折叠/展开】按钮，规格按照从大到小排列
		- 商品规格字体、商品录入区域的（+）（-）按钮、可编辑框调整增大
		- 左侧分类栏 挪至顶部展示
		- 添加商品后，商品数量>0时，将该商品栏特殊标记
	- 页面
		- 业务员端
			- 领货：领货录入、领货购物车、领货购物车_批量管理、提交领货_库存不足
			- 回货：回货录入
			- 车销：车销开单录入、车销开单_购物车、提交车销开单_库存不足
			- 车销退：车销退单录入、车销退单_购物车
		- 仓管端
			- 领货：领货录入、领货购物车、领货购物车_批量管理、提交领货_库存不足
			- 领货返回：领货返回录入、领货返回购物车、提交领货返回_库存不足
			- 回货：回货录入
			- 车销：车销开单录入、车销开单_购物车、提交车销开单_库存不足
			- 车销退：车销退单录入、车销退单_购物车
	- 注意点
		- 部分商品规格信息比较多，界面UI展示需简洁。
		- 避免录入数量的地方过于紧密，导致误输入错规格数量。
		- 需考虑到页面整体美观性和操作性，图片区域可适当缩小。

【全文完】
