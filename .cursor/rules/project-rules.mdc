---
description:
globs:
alwaysApply: false
---
# uni-app 项目开发规则

## 组件开发规范

### 组件使用优先级
1. **优先使用项目组件**：`src/components/` 和 `src/business/` 目录下的自定义组件
2. **其次使用 wot-design-uni**：组件库组件，前缀 `wd-`
3. **最后考虑原生组件**：uni-app 原生组件

### 组件库规范
- wot-design-uni 文档：https://wot-design-uni.cn/llms-full.txt
- 优先使用组件库的主题和样式
- 遵循组件库的最佳实践和 API 规范
- 组件前缀使用 `wd-`

### 自定义组件开发
- **通用组件**：放在 `src/components/`
- **业务组件**：放在 `src/business/`
- **页面组件**：放在对应页面的 `cmp/` 目录下
- 组件要支持主题切换和多端适配
- 提供完整的 TypeScript 类型定义

### 组件命名规范
- **自定义组件**：使用 PascalCase (如 `BackEditCard`)
- **wot-design-uni 组件**：使用 `wd-` 前缀
- **组件文件名**：使用 kebab-case (如 `back-edit-card.vue`)

## 项目结构规范

### 页面组织
- **主页面**：`src/pages/` - 包含 index、profile、sales 等主要功能页面
- **基础页面**：`src/pagesBase/` - 包含 login、print 等基础功能页面
- **业务员页面**：`src/pagesSalesMan/` - 业务员相关的功能页面
- **仓库页面**：`src/pagesWarehouse/` - 仓库管理相关页面

### 组件组织
- **通用组件**：[src/components/](mdc:src/components) - 如 NavSearchBar、EmptyStatus
- **业务组件**：[src/business/](mdc:src/business) - 如 CartBar、DetailInfoCard
- **页面组件**：页面同级的 `cmp/` 目录 - 如 [src/pagesSalesMan/back/cmp/](mdc:src/pagesSalesMan/back/cmp)

### API 组织
- **API 定义**：[src/api/apiDefinitions.ts](mdc:src/api/apiDefinitions.ts)
- **API 实例**：[src/api/index.ts](mdc:src/api/index.ts)
- **Mock 数据**：[src/api/mock/modules/](mdc:src/api/mock/modules)

## 样式开发规范

### 样式工具
- **优先使用 UnoCSS**：编写样式代码
- **避免使用 gap 属性**：低版本 iOS 不支持
- **使用 wot-design-uni 主题**：保持样式一致性

### 样式编写规范
- 使用 UnoCSS 原子类优先
- 避免深层嵌套的 CSS

## 状态管理规范

### Store 组织
- **用户状态**：[src/store/useUserStore.ts](mdc:src/store/useUserStore.ts)
- **设备信息**：[src/store/useDeviceInfo.ts](mdc:src/store/useDeviceInfo.ts)
- **销售状态**：[src/store/useSalesStore.ts](mdc:src/store/useSalesStore.ts)
- **仓库状态**：[src/store/useWmsStore.ts](mdc:src/store/useWmsStore.ts)

### Composables 组织
- **通用逻辑**：[src/composables/](mdc:src/composables) 目录
- **命名规范**：使用 `use` 前缀 (如 `useGlobalToast`)
- **单一职责**：每个 composable 只负责一个特定功能

## 开发规范

### 代码规范
- **语言**：优先使用 TypeScript
- **响应式**：使用 Vue 3 Composition API
- **类型定义**：使用 [src/api/globals.d.ts](mdc:src/api/globals.d.ts) 中的类型

### 错误处理
- 使用 `useGlobalToast` 显示错误信息
- 使用 `useGlobalLoading` 管理加载状态
- 使用 `useGlobalMessage` 显示确认对话框

### 导航规范
- 使用 `useRouter` 进行页面导航
- 路由配置在 [pages.config.ts](mdc:pages.config.ts)
- 页面路由信息在 Vue 文件的 `<route>` 标签中

### 请求规范
- 使用 Alova 进行 API 请求
- 配置文件：[alova.config.ts](mdc:alova.config.ts)
- 使用 `useRequest` Hook 处理请求状态

## 特殊功能规范

### 蓝牙打印
- 打印相关代码：[src/utils/bluetooth/](mdc:src/utils/bluetooth)
- 使用 `ReliablePrinter` 和 `PrintCommandGenerator`

### 滚动加载
- 使用 mescroll-uni 组件
- 配置文件：[src/utils/mescroll.ts](mdc:src/utils/mescroll.ts)

### 权限管理
- 认证工具：[src/utils/auth.ts](mdc:src/utils/auth.ts)
- 用户信息存储在 Pinia Store 中

## 部署和构建

### 环境配置
- 环境变量：[src/config/env.ts](mdc:src/config/env.ts)
- 应用配置：[manifest.config.ts](mdc:manifest.config.ts)
- 构建配置：[vite.config.ts](mdc:vite.config.ts)

### 必须实现的优化
- 骨架屏加载状态 (参考 `BackEditSkeleton.vue`)
- 分页加载 (使用 `usePagination`)
- 图片懒加载
- 路由懒加载

### 禁止的性能反模式
- 深层嵌套的 computed
- 未清理的定时器和监听器
- 大量数据的响应式包装
- 频繁的DOM操作

