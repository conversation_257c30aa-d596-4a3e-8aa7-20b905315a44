---
description:
globs:
alwaysApply: false
---
# API 和数据管理规则

## API 请求规范

### Alova 配置
- 配置文件：[alova.config.ts](mdc:alova.config.ts)
- API 实例：[src/api/core/instance.ts](mdc:src/api/core/instance.ts)
- 中间件：[src/api/core/middleware.ts](mdc:src/api/core/middleware.ts)

### API 定义
参考：[src/api/apiDefinitions.ts](mdc:src/api/apiDefinitions.ts)
```typescript
// API 接口定义
export interface ApiEndpoint {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE'
  url: string
  data?: any
  params?: any
}

// 使用 Alova 创建 API
const api = createAlova({
  baseURL: process.env.API_BASE_URL,
  requestAdapter: adapterFetch(),
  responseAdapter: adapterResponse(),
})
```

### 请求 Hook 使用
```typescript
// 基础请求
const { data, loading, error, send } = useRequest(
  () => Apis.someInterface.getSomeDataUsingGET(),
  { immediate: true }
)

// 错误处理
.onError((error) => {
  globalLoading.close()
  globalToast.error(error.error.message || '请求失败')
})

// 成功处理
.onSuccess((response) => {
  globalLoading.close()
  // 处理成功响应
})
```

## 数据类型规范

### TypeScript 类型定义
参考：[src/api/globals.d.ts](mdc:src/api/globals.d.ts)
- 使用接口定义 API 响应类型
- 使用泛型提高类型复用性
- 使用联合类型定义枚举值

```typescript
// 通用响应类型
interface ApiResponse<T = any> {
  code: number
  data: T
  message: string
  success: boolean
}

// 业务数据类型
interface VehSaleGoodsDTO {
  gid: number
  gdCode: string
  gdName: string
  category?: CategoryInfo
  vehSaleWrhQty?: number
}
```

### 数据转换
- 使用工具函数转换数据格式
- 保持前端数据结构的一致性
- 处理后端数据的兼容性问题

## Mock 数据规范

### Mock 配置
参考：[src/api/mock/](mdc:src/api/mock)
- 开发环境使用 Mock 数据
- Mock 数据结构与真实 API 保持一致
- 使用生成器创建随机数据

### Mock 数据生成
参考：[src/api/mock/utils/generators.ts](mdc:src/api/mock/utils/generators.ts)
```typescript
// 生成随机商品数据
export function generateSkuData(): VehSaleGoodsDTO {
  return {
    gid: faker.number.int({ min: 1, max: 1000 }),
    gdCode: faker.string.alphanumeric(8),
    gdName: faker.commerce.productName(),
    // ...其他字段
  }
}
```

## 状态管理规范

### Pinia Store 设计
参考：[src/store/](mdc:src/store)
```typescript
export const useDataStore = defineStore('data', () => {
  // 状态定义
  const items = ref<Item[]>([])
  const loading = ref(false)

  // 计算属性
  const filteredItems = computed(() => {
    return items.value.filter(/* 过滤逻辑 */)
  })

  // 方法
  function fetchItems() {
    // 获取数据逻辑
  }

  return {
    items,
    loading,
    filteredItems,
    fetchItems
  }
})
```

### 持久化存储
参考：[src/store/persist.ts](mdc:src/store/persist.ts)
- 使用 `pinia-plugin-persistedstate` 插件
- 选择性持久化关键数据
- 配置过期时间和存储策略

### Composables 设计
参考：[src/composables/](mdc:src/composables)
```typescript
// 基础数据获取
export function useDataFetch<T>(apiCall: () => Promise<T>) {
  const data = ref<T>()
  const loading = ref(false)
  const error = ref<Error>()

  const execute = async () => {
    try {
      loading.value = true
      data.value = await apiCall()
    } catch (err) {
      error.value = err as Error
    } finally {
      loading.value = false
    }
  }

  return { data, loading, error, execute }
}

// 分页数据管理
export function usePagination<T>(
  apiCall: (page: number, pageSize: number) => Promise<ApiResponse<T[]>>,
  options: {
    defaultPageSize?: number
    immediate?: boolean
    resetOnParamsChange?: boolean
    cacheKey?: string
    cacheTime?: number
  } = {}
) {
  const data = ref<T[]>([])
  const loading = ref(false)
  const error = ref<Error>()
  const hasMore = ref(true)
  const currentPage = ref(1)
  const pageSize = ref(options.defaultPageSize || 20)

  const loadMore = async () => {
    if (loading.value || !hasMore.value) return

    try {
      loading.value = true
      const response = await apiCall(currentPage.value, pageSize.value)

      if (currentPage.value === 1) {
        data.value = response.data
      } else {
        data.value.push(...response.data)
      }

      hasMore.value = response.data.length === pageSize.value
      currentPage.value++
    } catch (err) {
      error.value = err as Error
    } finally {
      loading.value = false
    }
  }

  const refresh = async () => {
    currentPage.value = 1
    hasMore.value = true
    await loadMore()
  }

  const reset = () => {
    data.value = []
    currentPage.value = 1
    hasMore.value = true
    error.value = undefined
  }

  return {
    data: readonly(data),
    loading: readonly(loading),
    error: readonly(error),
    hasMore: readonly(hasMore),
    loadMore,
    refresh,
    reset
  }
}
```

## 错误处理规范

### 全局错误处理
- 使用 `useGlobalToast` 显示错误消息
- 使用 `useGlobalMessage` 显示确认对话框
- 使用 `useGlobalLoading` 管理全局加载状态

### 错误分类处理
```typescript
// 网络错误
if (error.code === 'NETWORK_ERROR') {
  globalToast.error('网络连接异常，请检查网络设置')
}

// 业务错误
if (error.code === 'BUSINESS_ERROR') {
  globalToast.warning(error.message)
}

// 系统错误
if (error.code === 'SYSTEM_ERROR') {
  globalToast.error('系统异常，请稍后重试')
}
```

### 重试机制
```typescript
const { send: retryableRequest } = useRequest(apiCall, {
  immediate: false,
  retry: 3,
  retryDelay: 1000,
})
```

## 缓存策略

### 请求缓存
- 使用 Alova 的缓存功能
- 设置合理的缓存时间
- 根据数据特性选择缓存策略

### 本地存储
- 使用 `uni.getStorageSync` 存储临时数据
- 使用 Pinia 持久化存储长期数据
- 定期清理过期缓存

## 数据验证

### 前端验证
- 使用 wot-design-uni 表单组件的验证功能
- 实现自定义验证规则
- 提供友好的错误提示

### 数据格式化
```typescript
// 数字格式化
const formatNumber = (num: number) => {
  return num.toLocaleString('zh-CN')
}

// 日期格式化
const formatDate = (date: string | Date) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
}
```

## 分页管理规范

### usePagination Hook 使用
```typescript
// 标准分页 Hook
const {
  data: listData,
  loading,
  error,
  hasMore,
  loadMore,
  refresh,
  reset
} = usePagination(
  (page, pageSize) => Apis.someInterface.getListUsingGET({
    page,
    pageSize,
    ...otherParams
  }),
  {
    defaultPageSize: 20,
    immediate: true
  }
)

// 列表渲染
const list = computed(() => listData.value?.data || [])
```

### 分页组件集成
```typescript
// 与 mescroll 组件集成
const mescrollOptions = {
  down: {
    callback: refresh,
  },
  up: {
    callback: loadMore,
    noMoreSize: 0,
    textNoMore: '没有更多数据了'
  }
}

// 分页状态管理
const pageState = reactive({
  current: 1,
  size: 20,
  total: 0,
  hasMore: true
})
```

### 条件查询分页
```typescript
// 带条件的分页查询
const searchForm = reactive({
  keyword: '',
  status: '',
  dateRange: []
})

const {
  data: searchResults,
  loading: searchLoading,
  refresh: refreshSearch
} = usePagination(
  (page, pageSize) => Apis.searchInterface.searchUsingPOST({
    data: {
      ...searchForm,
      page,
      pageSize
    }
  }),
  {
    immediate: false,
    resetOnParamsChange: true
  }
)

// 搜索条件变化时重置分页
watch(searchForm, () => {
  refreshSearch()
}, { deep: true })
```

### 分页缓存策略
```typescript
// 带缓存的分页
const {
  data,
  loading,
  refresh
} = usePagination(apiCall, {
  cacheKey: 'list-cache',
  cacheTime: 5 * 60 * 1000, // 5分钟缓存
  staleTime: 2 * 60 * 1000   // 2分钟内不重新请求
})
```

## 性能优化

### 请求优化
- 使用防抖和节流减少请求频率
- 实现请求取消机制
- 使用 `usePagination` 处理分页数据加载
- 实现预加载机制提升用户体验

### 数据优化
- 使用虚拟滚动处理长列表
- 实现懒加载减少初始加载时间
- 使用浅比较避免不必要的重新渲染
- 合理设置分页大小平衡性能和体验

### 内存管理
- 及时清理不用的数据引用
- 使用弱引用避免内存泄漏
- 监控内存使用情况
- 分页数据及时释放避免内存堆积

## 接口文档

### API 文档维护
- 保持接口文档的实时更新
- 提供完整的请求和响应示例
- 标注接口的版本和变更记录

### 类型文档
- 为复杂数据类型提供详细说明
- 使用 JSDoc 注释增强类型信息
- 提供数据流向图和关系图
