---
description:
globs:
alwaysApply: false
---
# 组件开发规则

## 组件优先级 (严格按此顺序选择)

1. **项目组件优先**: [src/components/](mdc:src/components) 和 [src/business/](mdc:src/business)
2. **wot-design-uni组件**: 使用 `wd-` 前缀
3. **uni-app原生组件**: 最后选择

## Vue 组件标准结构

### 必须的文件结构顺序
```vue
<script setup lang="ts">
// 导入 (按导入规范顺序)
import { computed, ref } from 'vue'
import type { ComponentProps } from '@/types'

// 接口定义 (必须明确定义)
interface Props {
  sku: VehSaleGoodsDTO
  disabled?: boolean
}

interface Emits {
  quantityChange: [payload: { sku: VehSaleGoodsDTO, quantity: number }]
}

// 属性和事件声明
const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const loading = ref(false)
const quantity = ref(0)

// 计算属性
const computedValue = computed(() => {
  return props.sku.vehSaleWrhQty || 0
})

// 方法定义
function handleQuantityChange(newQuantity: number) {
  quantity.value = newQuantity
  emit('quantityChange', { sku: props.sku, quantity: newQuantity })
}

// 暴露给父组件 (可选)
defineExpose({
  reset: () => quantity.value = 0
})
</script>

<template>
  <view class="component-name">
    <!-- 内容 -->
  </view>
</template>

<style lang="scss" scoped>
.component-name {
  // 样式定义
}
</style>
```

## 组件设计原则

### Props 设计规范
```typescript
interface Props {
  // 必需属性 - 不提供默认值
  data: DataType

  // 可选属性 - 使用 ?
  disabled?: boolean
  size?: 'small' | 'medium' | 'large'

  // 复杂对象 - 明确类型
  config?: {
    showHeader: boolean
    maxItems: number
  }

  // 避免 - 过于宽泛的类型
  // options?: any // ❌ 错误
  // value?: unknown // ❌ 错误
}
```

### Emits 设计规范
```typescript
interface Emits {
  // 明确的事件载荷类型
  change: [value: string]
  update: [payload: { id: number, data: UpdateData }]

  // 用户交互事件
  click: [event: MouseEvent]
  submit: [formData: FormData]

  // 避免 - 模糊的事件定义
  // action: [any] // ❌ 错误
  // event: [] // ❌ 不够具体
}
```

## 业务组件模式 (基于项目现有组件)

### 卡片组件模式
参考: [src/pagesSalesMan/back/cmp/BackEditCard.vue](mdc:src/pagesSalesMan/back/cmp/BackEditCard.vue)
```typescript
// 卡片组件必须包含的功能
interface CardProps {
  data: BusinessData // 业务数据
  editable?: boolean // 是否可编辑
  selectable?: boolean // 是否可选择
}

interface CardEmits {
  edit: [data: BusinessData]
  select: [selected: boolean, data: BusinessData]
  action: [actionType: string, data: BusinessData]
}
```

### 头部组件模式
参考: [src/pagesSalesMan/back/cmp/BackEditHeader.vue](mdc:src/pagesSalesMan/back/cmp/BackEditHeader.vue)
```typescript
// 头部组件必须暴露的方法
defineExpose({
  openSelect: () => void // 打开选择器
  reset: () => void // 重置状态
})
```

### 骨架屏组件模式
参考: [src/pagesSalesMan/back/cmp/BackEditSkeleton.vue](mdc:src/pagesSalesMan/back/cmp/BackEditSkeleton.vue)
```vue
<template>
  <view class="skeleton-container">
    <wd-skeleton :loading="true" :row="3" :row-width="['100%', '80%', '60%']" />
  </view>
</template>
```

## 组件通信模式

### 父子组件通信 (标准模式)
```typescript
// 父组件
const childRef = ref<InstanceType<typeof ChildComponent>>()

function handleChildEvent(payload: EventPayload) {
  // 处理子组件事件
}

function callChildMethod() {
  childRef.value?.someMethod()
}

// 子组件
defineExpose({
  someMethod: () => {
    // 暴露给父组件的方法
  }
})
```

### 兄弟组件通信 (通过父组件)
```typescript
// 父组件作为中介
const sharedData = ref<SharedData>()

function handleComponent1Event(data: Data1) {
  sharedData.value = transformData(data)
}
```

### 深层组件通信 (provide/inject)
```typescript
// 顶层组件
provide('contextKey', reactive({
  data: contextData,
  methods: {
    updateData: (newData: Data) => { /* 更新逻辑 */ }
  }
}))

// 深层组件
const context = inject<ContextType>('contextKey')
```

## 组件生命周期管理

### 资源清理 (必须实现)
```typescript
import { onUnmounted } from 'vue'

const timer = ref<NodeJS.Timeout>()

onUnmounted(() => {
  // 清理定时器
  if (timer.value) {
    clearInterval(timer.value)
  }

  // 清理事件监听
  uni.off('eventName', handler)

  // 清理其他资源
})
```

### 页面生命周期 (uni-app页面组件)
```typescript
onLoad((options) => {
  // 页面加载时的初始化
})

onShow(() => {
  // 页面显示时的处理
})

onHide(() => {
  // 页面隐藏时的清理
})
```

## 组件状态管理

### 本地状态
```typescript
// 简单状态
const loading = ref(false)
const data = ref<DataType[]>([])

// 复杂状态 - 使用 reactive
const form = reactive({
  name: '',
  email: '',
  settings: {
    notifications: true
  }
})
```

### 计算属性优化
```typescript
// 正确 - 浅层依赖
const filteredList = computed(() => {
  return list.value.filter(item => item.status === activeStatus.value)
})

// 避免 - 深层嵌套计算
const complexComputed = computed(() => {
  return list.value.map(item => ({
    ...item,
    nested: item.data.map(subItem => ({ // ❌ 避免深层嵌套
      ...subItem,
      computed: someComplexCalculation(subItem)
    }))
  }))
})
```

## 组件性能优化

### 避免不必要的重新渲染
```typescript
// 使用 shallowRef 处理大型对象
const largeObject = shallowRef<LargeData>({})

// 使用 readonly 防止意外修改
const readonlyData = readonly(computed(() => processedData.value))
```

### 异步组件加载
```typescript
// 大型组件懒加载
const HeavyComponent = defineAsyncComponent(() =>
  import('./components/HeavyComponent.vue')
)
```

## 组件测试要求

### 必须测试的方面
- Props 验证和默认值
- 事件触发和载荷
- 插槽内容渲染
- 条件渲染逻辑
- 用户交互响应

