---
description:
globs:
alwaysApply: false
---
# 样式和 UI 规则

## UnoCSS 使用规范

### 原子类优先原则
```vue
<template>
  <!-- 正确：使用原子类组合 -->
  <view class="flex items-center justify-between p-4 bg-white rounded-lg shadow-sm">
    <!-- 内容 -->
  </view>

  <!-- 避免：内联样式 -->
  <view style="display: flex; padding: 16px;">
    <!-- 内容 -->
  </view>
</template>
```

### 常用布局模式
```scss
// 水平居中布局
.flex-center-h {
  @apply flex items-center justify-center;
}

// 垂直居中布局
.flex-center-v {
  @apply flex flex-col items-center justify-center;
}

// 卡片容器
.card {
  @apply bg-white rounded-lg shadow-sm p-4;
}

// 按钮容器
.btn-container {
  @apply flex items-center justify-center gap-3;
}
```

### iOS 兼容性要求 (严格遵守)
```scss
// ❌ 禁止使用 gap 属性 (低版本iOS不支持)
.container {
  display: flex;
  gap: 16px; /* 禁止 */
}

// ✅ 正确：使用 margin 或 padding 替代
.container {
  display: flex;

  > *:not(:last-child) {
    margin-right: 16px;
  }
}

// ✅ 或使用 UnoCSS 的 space 工具类
.container {
  @apply flex space-x-4;
}
```

## wot-design-uni 组件样式

### 主题变量使用
```scss
// 使用组件库的主题变量
.custom-component {
  color: var(--wd-color-primary);
  background-color: var(--wd-color-bg-page);
  border: 1px solid var(--wd-color-border);
}

// 响应主题切换
.theme-aware {
  background: var(--wd-color-bg-white);
  transition: background-color 0.3s ease;
}
```

### 组件样式定制
```vue
<template>
  <wd-button custom-class="custom-btn" type="primary">
    自定义按钮
  </wd-button>
</template>

<style lang="scss" scoped>
:deep(.custom-btn) {
  border-radius: 8px !important;
  font-weight: 600;

  // 保持主题适配
  background: var(--wd-button-primary-bg) !important;
}
</style>
```

## 安全区域适配

### 底部安全区域 (必须实现)
```scss
.bottom-fixed {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;

  // 安全区域适配 (三层兼容)
  padding-bottom: 48rpx !important;
  padding-bottom: calc(48rpx + constant(safe-area-inset-bottom)) !important;
  padding-bottom: calc(48rpx + env(safe-area-inset-bottom)) !important;
}
```

### 顶部安全区域
```scss
.top-fixed {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;

  // 状态栏高度适配
  padding-top: var(--status-bar-height);
  padding-top: calc(var(--status-bar-height) + env(safe-area-inset-top));
}
```

## 性能优化

### CSS 性能规则
```scss
// ✅ 正确：使用 transform 和 opacity 进行动画
.optimized-animation {
  transform: translateX(0);
  opacity: 1;
  transition: transform 0.3s ease, opacity 0.3s ease;
}

// ❌ 避免：使用 left/top 等触发重排的属性
.poor-animation {
  left: 0;
  transition: left 0.3s ease; /* 会触发重排 */
}

// ✅ 正确：使用 will-change 提示优化
.will-animate {
  will-change: transform, opacity;
}
```



