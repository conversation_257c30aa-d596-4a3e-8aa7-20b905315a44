## 项目概况

**技术栈：**
- uni-app + Vue 3 Composition API + TypeScript
- wot-design-uni 组件库
- UnoCSS 原子化CSS
- Alova 请求库
- Pinia 状态管理

**项目结构：**
- `src/pages/` - 主页面
- `src/pagesSalesMan/` - 业务员功能页面
- `src/pagesWarehouse/` - 仓管功能页面
- `src/components/` - 通用组件
- `src/business/` - 业务组件

## 当前需求分析（PH-14709）

根据需求文档，本期需要优化以下4个核心问题：

### 1. 商品规格展示优化
- **现状**：默认折叠非默认领货规格，需频繁点击展开
- **目标**：全部规格展开，按从大到小排序，取消折叠/展开按钮

### 2. 界面字体和录入区域优化
- **现状**：字体小，录入按钮小，40-60岁用户容易误操作
- **目标**：放大商品规格字体、(+)(-)按钮、可编辑框

### 3. 分类布局调整
- **现状**：左侧分类栏占用过多空间
- **目标**：将左侧分类栏移至顶部展示

### 4. 商品状态标记
- **现状**：已录入商品不够直观，容易重复录入
- **目标**：商品数量>0时增加特殊标记

## 涉及页面范围

**业务员端：**
- 领货：录入、购物车、批量管理
- 回货：录入
- 车销：开单录入、购物车
- 车销退：录入、购物车

**仓管端：**
- 领货：录入、购物车、批量管理
- 领货返回：录入、购物车
- 回货：录入
- 车销：开单录入、购物车
- 车销退：录入、购物车

从代码结构看，主要需要修改的是商品录入相关的组件，如 `SalesBackEditSkuCard.vue` 等SKU卡片组件，以及相关的页面布局。





## 项目整体架构

```mermaid
graph TB
    subgraph "uni-app 小程序架构"
        A[App.vue] --> B[TabBar 自定义导航]
        B --> C1[业务员端]
        B --> C2[仓管端] 
        B --> C3[销售端]
        B --> C4[个人中心]
    end

    subgraph "页面分包结构"
        D1[src/pages - 主包页面]
        D2[src/pagesBase - 基础功能]
        D3[src/pagesSalesMan - 业务员功能]
        D4[src/pagesWarehouse - 仓管功能]
    end

    subgraph "组件体系"
        E1[src/components - 通用组件]
        E2[src/business - 业务组件]
        E3[页面级组件 - cmp目录]
    end
```

## 核心业务流程架构

```mermaid
graph LR
    subgraph "业务员端流程"
        A1[领货] --> A2[回货] --> A3[车销] --> A4[车销退]
        A1 --> A11[pick - 领货录入]
        A1 --> A12[pick-cart - 领货购物车]
        A2 --> A21[back - 回货录入]
        A3 --> A31[sales - 车销开单]
        A3 --> A32[sales-cart - 车销购物车]
        A4 --> A41[sales-back - 车销退录入]
    end

    subgraph "仓管端流程"
        B1[warehouse-pick - 仓管领货]
        B2[warehouse-pick-return - 领货返回]
        B3[warehouse-back - 仓管回货]
        B4[warehouse-sales - 仓管车销]
        B5[warehouse-sales-back - 车销退]
    end
```

## 商品录入页面组件架构

```mermaid
graph TB
    subgraph "商品录入页面结构"
        A[录入页面] --> B[搜索栏 NavSearchBar]
        A --> C[分类选择器]
        A --> D[商品列表区域]
        A --> E[购物车栏 CartBar]
        
        D --> D1[商品卡片组件]
        D1 --> D11[商品图片 SkuImage]
        D1 --> D12[商品信息展示]
        D1 --> D13[规格选择器]
        D1 --> D14[数量输入控件]
        
        C --> C1[左侧分类栏 - 当前实现]
        C --> C2[顶部分类栏 - 需求目标]
    end
```

## 具体页面和组件对应关系

### 业务员端页面组件

```mermaid
graph LR
    subgraph "pagesSalesMan 业务员功能"
        A[pick 领货] --> A1[pick-edit.vue - 领货录入]
        A --> A2[pick-cart.vue - 领货购物车]
        A --> A3[pick-batch.vue - 批量管理]
        A1 --> A11[cmp/PickEditSkuCard.vue]
        
        B[back 回货] --> B1[back-edit.vue - 回货录入]
        B1 --> B11[cmp/BackEditSkuCard.vue]
        
        C[sales 车销] --> C1[sales-edit.vue - 车销录入]
        C --> C2[sales-cart.vue - 车销购物车]
        C1 --> C11[cmp/SalesEditSkuCard.vue]
        
        D[sales-back 车销退] --> D1[sales-back-edit.vue]
        D1 --> D11[cmp/SalesBackEditSkuCard.vue]
    end
```

### 仓管端页面组件

```mermaid
graph LR
    subgraph "pagesWarehouse 仓管功能"
        A[warehouse-pick] --> A1[warehouse-pick-edit.vue]
        A --> A2[warehouse-pick-cart.vue]
        A --> A3[warehouse-pick-batch.vue]
        A1 --> A11[cmp/WarehousePickEditSkuCard.vue]
        
        B[warehouse-pick-return] --> B1[warehouse-pick-return-edit.vue]
        B1 --> B11[cmp/WarehousePickReturnEditSkuCard.vue]
        
        C[warehouse-back] --> C1[warehouse-back-edit.vue]
        C1 --> C11[cmp/WarehouseBackEditSkuCard.vue]
        
        D[warehouse-sales] --> D1[warehouse-sales-edit.vue]
        D1 --> D11[cmp/WarehouseSalesEditSkuCard.vue]
        
        E[warehouse-sales-back] --> E1[warehouse-sales-back-edit.vue]
        E1 --> E11[cmp/WarehouseSalesBackEditSkuCard.vue]
    end
```

## 核心组件功能分析

### 商品卡片组件共同特征
从 `SalesEditSkuCard.vue` 和 `WarehouseSalesBackEditSkuCard.vue` 可以看出：

1. **商品信息展示**：图片、名称、编码、库存
2. **规格管理**：多规格展开/折叠、数量输入
3. **交互功能**：勾选、数量修改、规格切换
4. **状态标记**：缺货标记、已录入状态

### 需要优化的组件列表

```mermaid
graph TB
    subgraph "需要优化的SKU卡片组件"
        A[业务员端]
        A --> A1[PickEditSkuCard.vue]
        A --> A2[BackEditSkuCard.vue] 
        A --> A3[SalesEditSkuCard.vue]
        A --> A4[SalesBackEditSkuCard.vue]
        
        B[仓管端]
        B --> B1[WarehousePickEditSkuCard.vue]
        B --> B2[WarehousePickReturnEditSkuCard.vue]
        B --> B3[WarehouseBackEditSkuCard.vue]
        B --> B4[WarehouseSalesEditSkuCard.vue]
        B --> B5[WarehouseSalesBackEditSkuCard.vue]
    end
    
    subgraph "对应的录入页面"
        C[各种 *-edit.vue 页面]
        C --> C1[分类布局调整]
        C --> C2[商品状态标记逻辑]
    end
```

## 技术架构支撑

```mermaid
graph TB
    subgraph "技术栈架构"
        A[Vue 3 + Composition API]
        B[TypeScript 类型系统]
        C[wot-design-uni 组件库]
        D[UnoCSS 样式系统]
        E[Alova 请求管理]
        F[Pinia 状态管理]
        
        A --> G[响应式数据管理]
        B --> H[类型安全保障]
        C --> I[UI组件基础]
        D --> J[原子化样式]
        E --> K[API请求处理]
        F --> L[全局状态共享]
    end
```

这个架构分析显示了项目的完整结构，可以看出需要优化的主要是各个业务模块下的商品录入相关组件，涉及约10个SKU卡片组件和对应的录入页面。





## 本期需求涉及的具体文件和功能构成 - 需求涉及的具体文件清单

### 1. 业务员端 - 需要修改的文件

```mermaid
graph TB
    subgraph "pagesSalesMan - 业务员端"
        A1[pick/pick-edit.vue<br/>领货录入主页面]
        A2[pick/pick-cart.vue<br/>领货购物车页面]
        A3[pick/cmp/PickEditSkuCard.vue<br/>领货商品卡片组件]
        
        B1[back/back-edit.vue<br/>回货录入主页面]
        B2[back/cmp/BackEditSkuCard.vue<br/>回货商品卡片组件]
        
        C1[sales/sales-edit.vue<br/>车销开单录入页面]
        C2[sales/sales-cart.vue<br/>车销购物车页面]
        C3[sales/cmp/SalesEditSkuCard.vue<br/>车销商品卡片组件]
        
        D1[sales-back/sales-back-edit.vue<br/>车销退录入页面]
        D2[sales-back/sales-back-cart.vue<br/>车销退购物车页面]
        D3[sales-back/cmp/SalesBackEditSkuCard.vue<br/>车销退商品卡片组件]
    end
```

### 2. 仓管端 - 需要修改的文件

```mermaid
graph TB
    subgraph "pagesWarehouse - 仓管端"
        A1[warehouse-pick/warehouse-pick-edit.vue<br/>仓管领货录入页面]
        A2[warehouse-pick/warehouse-pick-cart.vue<br/>仓管领货购物车页面]
        A3[warehouse-pick/cmp/WarehousePickEditSkuCard.vue<br/>仓管领货商品卡片]
        
        B1[return-pick/return-pick-edit.vue<br/>领货返回录入页面]
        B2[return-pick/return-pick-cart.vue<br/>领货返回购物车页面]
        B3[return-pick/cmp/ReturnPickEditSkuCard.vue<br/>领货返回商品卡片]
        
        C1[warehouse-back/warehouse-back-edit.vue<br/>仓管回货录入页面]
        C2[warehouse-back/cmp/WarehouseBackEditSkuCard.vue<br/>仓管回货商品卡片]
        
        D1[warehouse-sales/warehouse-sales-edit.vue<br/>仓管车销录入页面]
        D2[warehouse-sales/warehouse-sales-cart.vue<br/>仓管车销购物车页面]
        D3[warehouse-sales/cmp/WarehouseSalesEditSkuCard.vue<br/>仓管车销商品卡片]
        
        E1[warehouse-sales/warehouse-sales-back-edit.vue<br/>仓管车销退录入页面]
        E2[warehouse-sales/cmp/WarehouseSalesBackEditSkuCard.vue<br/>仓管车销退商品卡片]
    end
```

## 具体文件功能分析和修改点

### 主录入页面文件 (需要修改分类布局)

#### 1. `src/pagesSalesMan/pick/pick-edit.vue` - 业务员领货录入
**当前结构：**
- 顶部搜索栏 `CartNavBar`
- 左侧分类栏 (当前实现)
- 右侧商品列表区域 `mescroll-body`
- 底部购物车栏 `CartBar`

**需要修改：**
- 将左侧分类栏移至顶部 `wd-tabs`
- 调整商品列表布局为全宽
- 增加商品状态标记逻辑

#### 2. `src/pagesSalesMan/sales-back/sales-back-edit.vue` - 车销退录入
**当前结构：**
```vue
<template>
  <view class="sales-back-edit">
    <CartNavBar /> <!-- 顶部导航 -->
    <view class="flex">
      <CategorySidebar /> <!-- 左侧分类栏 - 需要移除 -->
      <view class="main-content"> <!-- 右侧内容区 - 需要改为全宽 -->
        <mescroll-body>
          <SalesBackEditSkuCard /> <!-- 商品卡片 - 需要优化 -->
        </mescroll-body>
      </view>
    </view>
    <CartBar /> <!-- 底部购物车 -->
  </view>
</template>
```

**需要修改为：**
```vue
<template>
  <view class="sales-back-edit">
    <CartNavBar />
    <wd-tabs> <!-- 新增：顶部分类栏 -->
      <wd-tab v-for="cat in categoryList" />
    </wd-tabs>
    <view class="main-content full-width"> <!-- 全宽布局 -->
      <mescroll-body>
        <SalesBackEditSkuCard /> <!-- 优化后的商品卡片 -->
      </mescroll-body>
    </view>
    <CartBar />
  </view>
</template>
```

### 商品卡片组件文件 (核心修改)

#### 1. `src/pagesSalesMan/sales-back/cmp/SalesBackEditSkuCard.vue`
**当前功能：**
- 商品基本信息展示
- 规格选择器 (默认折叠)
- 数量输入控件
- 库存状态显示

**需要修改的具体内容：**

```mermaid
graph TB
    subgraph "SalesBackEditSkuCard.vue 修改点"
        A[规格展示区域]
        A --> A1[取消折叠/展开按钮]
        A --> A2[全部规格展开显示]
        A --> A3[规格按从大到小排序]
        A --> A4[放大规格字体大小]
        
        B[数量输入区域]
        B --> B1[放大 + - 按钮尺寸]
        B --> B2[放大输入框字体]
        B --> B3[增大可点击区域]
        
        C[商品状态标记]
        C --> C1[数量>0时显示特殊标记]
        C --> C2[已录入商品视觉区分]
    end
```

#### 2. `src/pagesWarehouse/warehouse-pick/cmp/WarehousePickEditSkuCard.vue`
**当前功能：**
- 复用 `SalesBackEditSkuCard` 的逻辑
- 传入 `type="vehSaleUseSignBck"` 参数

**修改点：**
- 同样需要应用规格展示、字体放大、状态标记优化

### 购物车页面文件

#### 1. `src/pagesSalesMan/sales-back/sales-back-cart.vue`
**当前结构：**
- 顶部 `CartNavBar`
- 分类 `wd-tabs` (已经是顶部布局)
- 商品列表使用相同的 `SalesBackEditSkuCard`

**修改点：**
- 商品卡片组件优化会自动应用
- 可能需要调整购物车模式下的显示逻辑

## 组件依赖关系图

```mermaid
graph TB
    subgraph "页面组件依赖关系"
        A[录入页面 *-edit.vue]
        A --> B[商品卡片 *SkuCard.vue]
        A --> C[分类选择器]
        A --> D[购物车栏 CartBar]
        A --> E[导航栏 CartNavBar]
        
        B --> F[规格选择器组件]
        B --> G[数量输入组件]
        B --> H[商品图片组件]
        
        I[购物车页面 *-cart.vue]
        I --> B
        I --> J[wd-tabs 分类]
        I --> K[滑动操作 wd-swipe-action]
    end
```

## 需要新增/修改的通用组件

### 1. 可能需要新增的组件
- `src/business/CategoryTabs.vue` - 统一的顶部分类标签组件
- `src/business/SkuQuantityInput.vue` - 优化后的数量输入组件
- `src/business/SkuSpecSelector.vue` - 优化后的规格选择器

### 2. 需要修改的现有组件
- `src/business/CartBar.vue` - 可能需要调整样式适配新布局
- `src/business/CartNavBar.vue` - 导航栏可能需要微调

## 修改优先级和影响范围

```mermaid
graph LR
    subgraph "修改优先级"
        A[高优先级<br/>商品卡片组件]
        B[中优先级<br/>录入页面布局]
        C[低优先级<br/>购物车页面]
        
        A --> A1[所有 *SkuCard.vue 组件<br/>约10个文件]
        B --> B1[所有 *-edit.vue 页面<br/>约10个文件]
        C --> C1[所有 *-cart.vue 页面<br/>约6个文件]
    end
```

总计需要修改约 **26个文件**，其中核心是 **10个商品卡片组件** 和 **10个录入页面**，这些修改会影响整个商品录入流程的用户体验。





## `pick-edit.vue`页面整体结构分析

### 1. 页面层级结构

```mermaid
graph TB
    subgraph "pick-edit.vue 页面结构"
        A[.pick-edit-page 根容器]
        A --> B[CartNavBar 自定义导航栏]
        A --> C[PickeEditHeader 历史导入栏]
        A --> D[PickEditSkeleton 骨架屏]
        A --> E[.main 主内容区]
        A --> F[CartBar 底部购物车]
        
        E --> E1[.main-sidebar 左侧分类栏]
        E --> E2[主内容区域]
        
        E1 --> E11[wd-sidebar 分类组件]
        E2 --> E21[EditSort 排序工具栏]
        E2 --> E22[mescroll-body 滚动容器]
        E22 --> E221[PickEditSkuCard 商品卡片]
    end
```

### 2. 具体组件使用分析

````vue path=src/pagesSalesMan/pick/pick-edit.vue mode=EXCERPT
<template>
  <!-- 根容器 -->
  <view class="pick-edit-page relative box-border min-h-screen w-screen overflow-x-hidden" :style="pageStyle">
    
    <!-- 1. 自定义导航栏 - 固定顶部 -->
    <CartNavBar mode="common">
      <template #title>
        <view class="absolute left-50% top-50% w-full flex transform items-center justify-center -translate-x-1/2 -translate-y-1/2">
          <view class="inline-flex items-center justify-center w-auto!" @click="goToSearch">
            <text class="text-lg text-black font-medium">全部商品</text>
            <text class="i-carbon-search ml-2 text-4 text-black" />
          </view>
        </view>
      </template>
    </CartNavBar>

    <!-- 2. 历史导入栏 - 固定在导航栏下方 -->
    <PickeEditHeader 
      ref="pickeEditHeaderRef" 
      v-model:current-wrh="skuQueryParams.wrhGid" 
      :clear-and-update="clearAndUpdate" 
      custom-class="fixed z-3" 
      custom-style="top: var(--navbar-total-height)" 
      @change="handleRefresh" 
      @import-recent="handleOpenImport" 
    />

    <!-- 3. 骨架屏 - 加载状态 -->
    <PickEditSkeleton v-if="loading" custom-class="top-[var(--navbar-total-height)]" />
    
    <!-- 4. 主内容区 -->
    <view v-else class="main relative z-1 w-screen bg-white pl-96px">
      <!-- 4.1 左侧分类栏 - 固定定位 -->
      <scroll-view
        class="main-sidebar fixed left-0"
        style="top: calc(var(--navbar-total-height) + 74rpx)"
        scroll-y
        @touchmove.stop
      >
        <wd-sidebar v-model="skuQueryParams.sortCode" custom-class="relative w-96px h-auto!" @change="handleRefresh">
          <wd-sidebar-item
            v-for="(category, index) in categoryList"
            :key="index"
            :label="category.label || ''"
            :value="category.value"
            :badge="category.count"
            :max="99"
          />
        </wd-sidebar>
      </scroll-view>

      <!-- 4.2 右侧主内容区域 -->
      <view class="box-border w-[calc(100vw-96px)] flex flex-auto flex-col bg-white">
        <!-- 4.2.1 排序工具栏 - 固定定位 -->
        <view class="fixed right-0 z-10 box-border w-[calc(100vw-96px)] pl-3" style="top: calc(var(--navbar-total-height) + 74rpx)">
          <EditSort v-model:sorts="skuQueryParams.sorts" @change="handleRefresh" />
        </view>
        
        <!-- 4.2.2 占位间距 -->
        <wd-gap height="calc(74rpx + 36px)" />

        <!-- 4.2.3 滚动商品列表 -->
        <mescroll-body
          v-if="skuQueryParams.wrhGid && !categoryLoading"
          :down="downOption"
          :up="upOption"
          @init="mescrollInit"
          @down="downCallback"
          @up="upCallback"
        >
          <view class="p-3 pb-0">
            <SkuListSkeleton v-if="!loading && skuPage === 1 && skuListLoading" />
            <template v-else>
              <PickEditSkuCard
                v-for="sku in skuList"
                :key="sku.goods.gid"
                :sku="sku"
                @change="handleSpecChange"
              />
            </template>
          </view>
        </mescroll-body>
      </view>
    </view>

    <!-- 5. 底部购物车 - 固定底部 -->
    <CartBar
      :count="skuCount"
      :amount="total"
      safe-area-bottom 
      fixed
      button-text="提交"
      @click-cart="goToCart"
      @click-button="handleSubmit"
    />
  </view>
</template>
````

## 布局定位和尺寸计算详解

### 1. CSS 变量和尺寸单位使用

````scss path=src/pagesSalesMan/pick/pick-edit.vue mode=EXCERPT
.pick-edit-page {
  min-height: 100vh;
  padding-bottom: 120rpx; // 底部购物车高度
  padding-bottom: calc(120rpx + const(safe-area-inset-bottom)) !important; // iOS安全区域
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom)) !important; // 现代浏览器安全区域

  :deep(.mescroll-body) {
    // 滚动容器最小高度 = 视口高度 - 导航栏 - 历史导入栏 - 排序栏 - 购物车栏
    min-height: calc(100vh - var(--navbar-total-height) - 74rpx - 36px - 120rpx) !important;
    min-height: calc(100vh - var(--navbar-total-height) - 74rpx - 36px - 120rpx - const(safe-area-inset-bottom)) !important;
    min-height: calc(100vh - var(--navbar-total-height) - 74rpx - 36px - 120rpx - env(safe-area-inset-bottom)) !important;
  }

  .main {
    &-sidebar {
      // 左侧分类栏高度 = 视口高度 - 导航栏 - 历史导入栏 - 购物车栏
      height: calc(100vh - var(--navbar-total-height) - 74rpx - 120rpx) !important;
      height: calc(100vh - var(--navbar-total-height) - 74rpx - 120rpx - const(safe-area-inset-bottom)) !important;
      height: calc(100vh - var(--navbar-total-height) - 74rpx - 120rpx - env(safe-area-inset-bottom)) !important;
    }
  }
}
````

### 2. 关键尺寸和定位分析

```mermaid
graph TB
    subgraph "垂直布局尺寸计算"
        A["顶部: 0"]
        A --> B["导航栏: var(--navbar-total-height)"]
        B --> C["历史导入栏: 74rpx"]
        C --> D["排序工具栏: 36px"]
        D --> E["商品列表区域: 自适应"]
        E --> F["底部购物车: 120rpx"]
        F --> G["安全区域: env(safe-area-inset-bottom)"]
    end
```

### 3. 水平布局分析

```mermaid
graph LR
    subgraph "水平布局结构"
        A["左侧分类栏: 96px 固定宽度"]
        A --> B["右侧内容区: calc(100vw - 96px)"]
        
        B --> B1["排序工具栏: 全宽"]
        B --> B2["商品列表: padding 12px"]
    end
```

## 组件使用规范分析

### 1. 导航栏组件 `CartNavBar`

````vue path=src/pagesSalesMan/pick/pick-edit.vue mode=EXCERPT
<CartNavBar mode="common">
  <template #title>
    <!-- 自定义标题内容，使用绝对定位居中 -->
    <view class="absolute left-50% top-50% w-full flex transform items-center justify-center -translate-x-1/2 -translate-y-1/2">
      <view class="inline-flex items-center justify-center w-auto!" @click="goToSearch">
        <text class="text-lg text-black font-medium">全部商品</text>
        <text class="i-carbon-search ml-2 text-4 text-black" />
      </view>
    </view>
  </template>
</CartNavBar>
````

**使用特点：**
- `mode="common"` 通用模式
- 使用插槽自定义标题内容
- 标题区域可点击跳转搜索页面

### 2. 历史导入栏 `PickeEditHeader`

````vue path=src/pagesSalesMan/pick/pick-edit.vue mode=EXCERPT
<PickeEditHeader 
  ref="pickeEditHeaderRef" 
  v-model:current-wrh="skuQueryParams.wrhGid" 
  :clear-and-update="clearAndUpdate" 
  custom-class="fixed z-3" 
  custom-style="top: var(--navbar-total-height)" 
  @change="handleRefresh" 
  @import-recent="handleOpenImport" 
/>
````

**定位规范：**
- `fixed` 固定定位
- `z-3` 层级为3
- `top: var(--navbar-total-height)` 紧贴导航栏下方
- 高度为 `74rpx`

### 3. 左侧分类栏 `wd-sidebar`

````vue path=src/pagesSalesMan/pick/pick-edit.vue mode=EXCERPT
<scroll-view
  class="main-sidebar fixed left-0"
  style="top: calc(var(--navbar-total-height) + 74rpx)"
  scroll-y
  @touchmove.stop
>
  <wd-sidebar v-model="skuQueryParams.sortCode" custom-class="relative w-96px h-auto!" @change="handleRefresh">
    <wd-sidebar-item
      v-for="(category, index) in categoryList"
      :key="index"
      :label="category.label || ''"
      :value="category.value"
      :badge="category.count"
      :max="99"
    />
  </wd-sidebar>
</scroll-view>
````

**布局特点：**
- 固定宽度 `96px`
- 固定定位 `fixed left-0`
- 顶部位置：导航栏高度 + 历史导入栏高度
- 可滚动 `scroll-y`
- 阻止触摸事件冒泡 `@touchmove.stop`

### 4. 商品列表滚动容器 `mescroll-body`

````vue path=src/pagesSalesMan/pick/pick-edit.vue mode=EXCERPT
<mescroll-body
  v-if="skuQueryParams.wrhGid && !categoryLoading"
  :down="downOption"
  :up="upOption"
  @init="mescrollInit"
  @down="downCallback"
  @up="upCallback"
>
  <view class="p-3 pb-0">
    <SkuListSkeleton v-if="!loading && skuPage === 1 && skuListLoading" />
    <template v-else>
      <PickEditSkuCard
        v-for="sku in skuList"
        :key="sku.goods.gid"
        :sku="sku"
        @change="handleSpecChange"
      />
    </template>
  </view>
</mescroll-body>
````

**功能特点：**
- 支持下拉刷新和上拉加载
- 内容区域 `padding: 12px` (除底部)
- 条件渲染：需要选择仓位且分类加载完成

### 5. 底部购物车 `CartBar`

````vue path=src/pagesSalesMan/pick/pick-edit.vue mode=EXCERPT
<CartBar
  :count="skuCount"
  :amount="total"
  safe-area-bottom 
  fixed
  button-text="提交"
  @click-cart="goToCart"
  @click-button="handleSubmit"
/>
````

**定位特点：**
- `fixed` 固定在底部
- `safe-area-bottom` 适配安全区域
- 高度 `120rpx`

## 尺寸单位使用规范

### 1. rpx vs px 使用原则

```scss
// rpx (响应式像素) - 用于：
width: 96px;           // 固定宽度
height: 74rpx;         // 高度尺寸
padding: 28rpx;        // 内边距
font-size: 28rpx;      // 字体大小

// px - 用于：
padding: 12px;         // 与CSS标准对应的尺寸
height: 36px;          // 工具栏等固定高度
```

### 2. CSS变量使用

```scss
// 系统级变量
var(--navbar-total-height)           // 导航栏总高度
var(--greyapplication-bottomlayer)   // 背景色
var(--textapplication-text-1)        // 主文本色

// 安全区域适配
const(safe-area-inset-bottom)        // iOS安全区域
env(safe-area-inset-bottom)          // 现代浏览器安全区域
```

### 3. 计算表达式规范

```scss
// 复杂布局计算
width: calc(100vw - 96px);                    // 视口宽度减去固定宽度
top: calc(var(--navbar-total-height) + 74rpx); // 多个高度累加
min-height: calc(100vh - var(--navbar-total-height) - 74rpx - 36px - 120rpx); // 多层级高度计算
```

## 响应式和适配策略

### 1. 安全区域适配

```scss
// 三层适配策略
padding-bottom: 120rpx;                                           // 基础值
padding-bottom: calc(120rpx + const(safe-area-inset-bottom)) !important;  // iOS适配
padding-bottom: calc(120rpx + env(safe-area-inset-bottom)) !important;    // 现代浏览器适配
```

### 2. 层级管理

```scss
// z-index 层级规范
z-1    // 主内容区
z-2    // 次要固定元素  
z-3    // 重要固定元素 (历史导入栏)
z-10   // 排序工具栏
```

这个分析显示了 `pick-edit.vue` 采用了精确的布局计算和组件化设计，每个区域的定位和尺寸都有明确的计算逻辑，为后续的布局调整提供了清晰的修改方向。