# 更新日志 


### [1.0.2](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/compare/v1.0.1...v1.0.2) (2025-07-14)


### ✨ Features | 新功能

* ✨ FEGC-1134 签到打卡问题处理 ([ba66acb](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/ba66acb3a1f6d9ca38cce74ed9666c48cce647cb))

### [1.0.1](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/compare/v1.0.0...v1.0.1) (2025-07-04)


### 🐛 Bug Fixes | Bug 修复

* 🐛 FEGC-1125 调整区分仓管和业务员使用的名字和code字段 ([10041da](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/10041da6519d79245fd2ed62d5594d0deac8daff))

## 1.0.0 (2025-07-01)


### 🐛 Bug Fixes | Bug 修复

* 🐛 仓管端回货差异单详情页面.界面问题 ([5b3d0d2](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/5b3d0d2f5a766301570e34eaf442fd2863a50f87))
* 🐛 仓管端领货返回商品规格全展开 ([c9bd0d1](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/c9bd0d155d282a87d8c1bda2f4daa46bd9ba6bcb))
* 🐛 仓管端领货和领货返回仓位查询添加仓库参数 ([7eb0421](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/7eb04214a767c9777d887a7195fbdced54ada426))
* 🐛 更改仓管端选择业务员和仓位弹窗文本过长时显示不正确的bug ([cd8c32c](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/cd8c32c4d4d2afac0af86417149909d40da03262))
* 🐛 工作台展示存在超过七天未处理的差异单2 ([a790bef](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/a790befbf66b504958684e5671439ce53e268a09))
* 🐛 修复仓管端领货返回购物车接口调用错误 ([802b25f](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/802b25fdc2c83c7947609c37bba4c3b0560c11ef))
* 🐛 修复工作台存在超过n天未处理的订单，n设置多一天bug ([1db1594](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/1db15946b251f9ae5f096d6d985179062ac99e81))
* 🐛 修复领货返回详情页左上角返回错误bug ([3b50d33](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/3b50d3357c9b8102cddf1c55752cfb480dcafa14))
* 🐛 EGC-1077 车销tab门店详情页面打卡距离显示调整为固定文案 ([2d8963e](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/2d8963e470ad39eace8012f54d45d56f6bbec596))
* 🐛 FEGC-1002 修复领货退页面bug问题 ([18e00d3](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/18e00d3dd60af44aa87bc0119d39a7fc4f88b9bf))
* 🐛 FEGC-1010 领货支持只有一个仓位的时候默认选择该仓位 ([6d5eb07](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/6d5eb076c4e69ebe865699f0c7876a7185b30362))
* 🐛 FEGC-1011 【业务员】领货创建页面 > 商品列表问题修复 ([c736ff4](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/c736ff49df958016400b7645251a7dcfdde57d17))
* 🐛 FEGC-1011 修复领货商品列表库存余数显示单位不对的问题 ([3342f1b](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/3342f1b8f19ca4439e4261cf4140144998f18008))
* 🐛 FEGC-1011 业务员创建领货页面提交时添加二次确认 ([cd8795d](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/cd8795da4dafd713e34f798f8043c3d7fdfc4956))
* 🐛 FEGC-1011 业务员领货购物车提交时添加二次确认 ([1565101](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/15651010481bbc2b3c679aa0c95f5ba5069d9bbc))
* 🐛 FEGC-1012 快速建单弹窗查询单据应当传入仓位 ([5722b9e](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/5722b9e9326b3df51839a2a57551151aba7c3739))
* 🐛 FEGC-1012 快速建单时购物车不为空需要弹出二次确认 ([1b5f90d](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/1b5f90dad1891169c756631e8f8784f54efbffdf))
* 🐛 FEGC-1012 领货创建页面快速建单支持按配置查询最近N天的数据 ([513bee2](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/513bee28b926262492212fb0638b11006643532b))
* 🐛 FEGC-1014 领货购物车默认页面问题修复 ([878cb3e](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/878cb3e54180e8b5c4ad9cdac589e4b4a2fbed96))
* 🐛 FEGC-1015 领货购物车管理页面问题修复 ([857c9a2](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/857c9a26ad8ed5ca06dfd6e64e2b1a1239c9948d))
* 🐛 FEGC-1016 【业务员】领货单据列表页面日期加醋 ([cebde6e](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/cebde6e73b43711e0efd91ea9674696d2c6ab7d7))
* 🐛 FEGC-1017 修复领货单详情页面问题 ([334e0d7](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/334e0d76b289481f311444959fbae620689905f9))
* 🐛 FEGC-1018 领货单详情页面商品列表问题修复 ([ace586f](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/ace586f253caf0fed5c7aa452a50537a86e83a3a))
* 🐛 FEGC-1020 【仓管端】领货返回单详情页面开发bug修复 ([ac13953](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/ac139533305119c5c0e3afc33a4e9d1fa4f0d22e))
* 🐛 FEGC-1024 修复仓管端领货开单页面两次进入仓位不同时，购物车未清空bug ([0ce3330](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/0ce333096ae4f6803b7bac7ee880376586ec6cb2))
* 🐛 FEGC-1024 修复仓管端领货开单页面bug ([0634766](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/0634766ef773fa06faf5684161e1205f7b8129fb))
* 🐛 FEGC-1025 领货开单页面bug修复 ([a88f6ca](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/a88f6ca96770dce5fa0a89a87b72bba39de06067))
* 🐛 FEGC-1025 修复仓管端领货录入左侧分类最后一项不显示bug ([253f201](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/253f20164d3c7d0d5db8a25ef3b3bdbb594788b9))
* 🐛 FEGC-1025 修复领货录入排序 ([12f44fb](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/12f44fbafd3a762d73ed2ad834b75e354edf54a6))
* 🐛 FEGC-1026修复领货购物车bug问题 ([79f7ac8](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/79f7ac8845813920548d6cc1d485f58a563e1f26))
* 🐛 FEGC-1027 领货开单商品搜索页面扫码失败后提示信息变更 ([6b4204c](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/6b4204cc08e67345f744c43447b3346275184de1))
* 🐛 FEGC-1027 商品搜索页面搜索框自动获取焦点 ([0a0ffc8](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/0a0ffc81f77dd0fc811c2a0185efbe39e3edefb1))
* 🐛 FEGC-1028 领货确认列表页面卡片新增领货业务员code显示 ([a4dce5e](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/a4dce5eafb5c869fb5a08f38360b5e7d957846a6))
* 🐛 FEGC-1029 解决工作台不显示bug ([7f54ee4](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/7f54ee4eb775012230ee50ce8e354503207592f2))
* 🐛 FEGC-1029 修复仓管端个人中心所属物流中心显示样式 ([f1fe8a3](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/f1fe8a376154652ee95ea8f922a691e299e60728))
* 🐛 FEGC-1030 领货确认“已申请”详情测试问题 ([5819973](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/5819973aa01b96ccfec1d97f2d487ea78a99b17b))
* 🐛 FEGC-1030 领货确认“已申请”详情测试问题 ([ac834cc](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/ac834cc437abe89e62ac8bb5273c7ff256a306a7))
* 🐛 FEGC-1030 领货确认详情页面报领数量未对齐的问题 ([b19c723](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/b19c723d7bcb40ad6510196e4083de6b671d2d9e))
* 🐛 FEGC-1031 领货确认合并审核问题修复 ([33d5119](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/33d51198bba6684557e11c42cdd675529b23bbae))
* 🐛 FEGC-1033 仓管领货详情不展示打印按钮 ([26fe491](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/26fe49173bfb560d551d357ebab8e9f2dbe58716))
* 🐛 FEGC-1034 修复领货退单页面 -- 选择业务员/仓位弹窗问题 ([625e8f9](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/625e8f994258c49aa7ed57dbe06d1e916e7af3a1))
* 🐛 FEGC-1035 修复领货退单页面步进器/购物车问题 ([9cc0c34](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/9cc0c3466fccf64f7deac2271bdb4b58afb51088))
* 🐛 FEGC-1035 修复领货退最大可输入数 <= 车余数量 ([774b360](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/774b3601f5272db510574876874b842b7194b463))
* 🐛 FEGC-1035 修改领货退剩余商品数量计算逻辑 ([b01df54](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/b01df5477c6fe6d86728e97e7e6e6d8a4a62fecd))
* 🐛 FEGC-1035修复领货返回第二次进入选择不同的仓位，购物车数据未清空bug ([18d49d2](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/18d49d2fe4f9a5e79f0ce5cc11c79619410bdda6))
* 🐛 FEGC-1036 【业务员】库存.页面问题修复 ([4ef607b](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/4ef607bd5ffc3e376e81ef53a53726e5e0648fc6))
* 🐛 FEGC-1036 【业务员】库存.页面问题修复 ([197a085](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/197a085c26d1644bb3e371dbe4a37b440a221575))
* 🐛 FEGC-1037 仓管端领货和领货退购物车车余以M+N的形式展示 ([1b4030d](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/1b4030dae608b1ea17779b428cb5becbbf0d6614))
* 🐛 FEGC-1040 跳整领货单详情页面打印功能部分文本 ([0322e15](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/0322e151939537812b42c3c5c28b438d931d1cf1))
* 🐛 FEGC-1044 【业务员】车销开、退单购物车删除商品增加二次确认 ([b825839](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/b8258396e266b23628e2d509c94337e4ea1820bc))
* 🐛 FEGC-1044 修复车销开单购物车页面.界面问题 ([508d860](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/508d86049ae1a1ee20b0c6ca898df908465ab186))
* 🐛 FEGC-1045 修复车销开单详情页面问题 ([7fba9d4](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/7fba9d4f6436b1e56a87df838c0d2cdab1ff33ca))
* 🐛 FEGC-1048 车销tab门店列表问题修复 ([e57bda9](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/e57bda982c0932da2be3bd046414182bf5f96faf))
* 🐛 FEGC-1049 车销tab门店详情页面打卡功能问题修复 ([90cae49](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/90cae495ca82810de58d6ad9a28f4db7f3d72b17))
* 🐛 FEGC-1050 车销tab门店详情页面问题修复 ([01d64e6](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/01d64e6b2faeffd630cf5a583df18427d7d34692))
* 🐛 FEGC-1052 更新仓管端领货退和领货库存校验公共组件问题汇 ([80ce868](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/80ce8687fb9956d3e25bbe6221f84b4449e18e5e))
* 🐛 FEGC-1052 更新仓管端领货退和领货库存校验公共组件问题汇2 ([6381663](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/6381663309e6cbaebe3f1c50764aaab11db4e536))
* 🐛 FEGC-1053 【仓管端】领货返回单据列表页面问题修复 ([ac3780d](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/ac3780d5323b8259248bd9eceee4398efae5a802))
* 🐛 FEGC-1053 领货返回单据列表页面问题解决 ([885331c](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/885331cc41c0015a2f1e1cace87e3abb6b9d9240))
* 🐛 FEGC-1055 回货单创建页面.界面问题修复 ([5bc9d1d](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/5bc9d1db462aa48e6cc72884315115de7310dbe5))
* 🐛 FEGC-1056 领货返回单详情页面问题修复 ([6e443ae](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/6e443aecd519fbb05cd50dfc23819ce185bfdc28))
* 🐛 FEGC-1057 领货返回商品清单接口调用错误的问题 ([c7ce1a4](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/c7ce1a4666fe406770bf700d35e803f2657f40f0))
* 🐛 FEGC-1058 回货单详情页面.界面问题 ([aefd4b3](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/aefd4b397912876c119166b8dfdf568af75aa69d))
* 🐛 FEGC-1058 回货单详情页面.界面问题 ([20fae15](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/20fae159fff644e08771f534f49e5fcc3d4d6ca0))
* 🐛 FEGC-1058 回货单详情页面车销领货退货无法跳转的问题 ([a0088da](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/a0088da99c24a308d8050fe4e9ddda44be6a5b88))
* 🐛 FEGC-1059 工作台展示存在超过七天未处理的差异单 ([35ba1d5](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/35ba1d52c30c4fd890e2e953af341898cea6237e))
* 🐛 FEGC-1059 工作台展示存在超过七天未处理的差异单3 ([79ff561](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/79ff5617df6ced0306fe06fe324327c343715a88))
* 🐛 FEGC-1059 工作台展示存在超过七天未处理的差异单4 ([e72b485](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/e72b4857c7106c0770b2bb875fc8dd22507dac6d))
* 🐛 FEGC-1060 调整个人中心页面物流中心字体大小 ([3d4821c](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/3d4821cf235fe9b618a353a5916d3f03cd34dc8e))
* 🐛 FEGC-1060 更改字体大小 ([d2c4f14](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/d2c4f1415f8a3edb51b9b26be29af96f1a76ab2b))
* 🐛 FEGC-1062 回货单据列表页面.界面问题修复 ([c879beb](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/c879beb4866ae5f16afd03e13ae813e74d7a0687))
* 🐛 FEGC-1062 领货返回列表金额调整为垂直居中显示 ([75423fc](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/75423fca3279ebec0c70ec3af7dbca920dd5b958))
* 🐛 FEGC-1064 领货购物车页面多规格商品单位错误及步进器体验优化 问题 ([86c7574](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/86c7574767a9a1aaf62db6bd00573c7347a639f6))
* 🐛 FEGC-1065 修复回货差异单详情页面.界面问题 ([fffbf85](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/fffbf8511799fcf05d0d35e4238804f0e2c3bfb6))
* 🐛 FEGC-1066领货单详情页面打印页面问题 ([97a83f5](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/97a83f5444c74b92c21bf106b3e268142844efd3))
* 🐛 FEGC-1069 买赔单单据列表问题修复 ([49c9895](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/49c98957f6ac9c334560daf921edb89a0f644cbe))
* 🐛 FEGC-1072 修复【仓管端】车销单开单页面.界面问题 ([39f39c4](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/39f39c4a175383a40376fad9ecb3bbbfce5773b4))
* 🐛 FEGC-1072 修复仓管开单切换门店时没有校验已选门店是否在门店列表中的问题 ([e07cef1](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/e07cef1f54799467df26d0bcb4abaca533b9fe48))
* 🐛 FEGC-1073 买赔单详情页面问题汇总 ([f8b8eb0](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/f8b8eb040477dacac6d3bbd915b1718382e233e8))
* 🐛 FEGC-1073 赔付信息和关联单据标题应与商品清单对齐 ([4349c68](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/4349c682394700e71473ed17a5a10db74e323f86))
* 🐛 FEGC-1073 修复买赔单详情页面问题 ([be34777](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/be347779236316f553f6d9cdbd1b529321b05e07))
* 🐛 FEGC-1075 历史车销单列表问题修复 ([ca9a89a](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/ca9a89a0def25d2d64c56fad0f4390b3ced675b3))
* 🐛 FEGC-1081 领货、回货提交后应当更新首页数据 ([e9325e8](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/e9325e8dbe79f22f95d7830362386025d098a090))
* 🐛 FEGC-1082 回货差异编辑页面支持跳转单据 ([e38ebba](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/e38ebba1a6acec0a927da8b9b40062be7c596b0a))
* 🐛 FEGC-1083 【仓管端】回货确认详情页面.界面问题 ([064ba80](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/064ba80eb94ef9b02885bb70dc28aa5063060814))
* 🐛 FEGC-1084 仓管端】回货确认列表.界面问题修复 ([872a221](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/872a2213a269e158106c3d6661b386453dea23f2))
* 🐛 FEGC-1089 回货差异创建买赔单时控制图片必填 ([31acab7](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/31acab78d29774d5d74a52250dfac15a1f3a4adf))
* 🐛 FEGC-1090 领货返回联调问题修复 ([876631f](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/876631f6045b2505a59aeaaa48045b14bd2e764e))
* 🐛 FEGC-1090 领货确认页面车销仓位改为出货仓位 ([8ccdfb6](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/8ccdfb668d36e1b295e7a0601835857d1ed3e043))
* 🐛 FEGC-1094 历史车销单增加物流中心参数 ([317618a](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/317618a576b934298f474f4ba2c6d4c63ae4e37d))
* 🐛 FEGC-1095 领货创建页面验收问题修复 ([cc8e571](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/cc8e571b1823e09f62adde938cb10f3d960864f7))
* 🐛 FEGC-1096 领货详情页作废报错提示调整为使用接口返回的 ([6197fca](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/6197fcaa2b762aecd2514fca74771a377b06ab40))
* 🐛 FEGC-1102 仓管录入实领数可以超过报领数 ([a1c6e9c](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/a1c6e9c385a72ca9da03866bbdc5b40b187f20e1))
* 🐛 FEGC-1103 历史车销单已作废状态调整为查询310状态的单据 ([aef72c3](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/aef72c3aab20e7cfacd3da1224b3b1565a1c9676))
* 🐛 FEGC-1104 回货详情页面差异数量错误问题修复 ([3e81b56](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/3e81b562007cd083a5762ea241ef5e9b0fd04243))
* 🐛 FEGC-1108 回货差异单列表页面选择仓位查询增加物流中心参数 ([bc4f595](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/bc4f595f29eeac9457507deb093c91b411867115))
* 🐛 FEGC-1109 回货详情页面.关联回货差异,跳转无法处理差异 ([d60c83c](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/d60c83cb35b7ca2766c00e76290f00e98e252d1c))
* 🐛 FEGC-1112 领货详情页新增“创建人”“业务员”字段 ([6adb515](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/6adb515b8502657b513d92eebae1f4be948588ae))
* 🐛 FEGC-1113 仓管端领货搜索编辑数量后更新编辑页面数据 ([342b76f](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/342b76f883398f1927deda446c6804fba73ef7b2))
* 🐛 FEGC-1114 修复购物车页面商品点击热区问题导致点选异常的问题 ([9f42f45](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/9f42f4562bac239dfe0d46f179dd0fdea5a52ef3))
* 🐛 FEGC-1118 业务员端权限问题修复 ([9ef0215](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/9ef021530dc8eda094135f3161401225a803a4e5))
* 🐛 FEGC-1121 【业务员】【仓管】我的页面.员工名称、物流中心名称代码超长时换行显示 ([1846fd2](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/1846fd21b47d4483643d7b5c818616904d7ad2aa))
* 🐛 FEGC-1123 修复回货差异单详情页面.分类下拉后，编辑的数量的层级问题 ([48c7d94](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/48c7d94179c27cb1b43bc98e4158e99bff66a8b6))
* 🐛 useDraft组件导出业务员对象 ([0458fcb](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/0458fcb0f05c52c4e3964f44499517e03af1b8a0))
* 领货退仅applied状态显示作废按钮 ([2cf1f62](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/2cf1f62aa146b878fe54b4447a4f709bb9c7312c))


### ✨ Features | 新功能

* ✨ 初始化功能页面，添加多个模块列表 ([38e28d8](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/38e28d8bd893717a8d3bfe95aecbb0a60be1a77f))
* ✨ 首页支持排序条件 ([9e3bf9d](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/9e3bf9dc1ae452ddfb2d196693d590acd6279f60))
* ✨ 新增仓管领货确认页面 ([0b65ae7](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/0b65ae78d9c63576c370b11629447565233cfac0))
* ✨ FEGC-1000领货开单页面开发 ([94bf1f0](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/94bf1f0f171c952ad0dcc8160feddf87f9715714))
* ✨ FEGC-1002领货退单页面开发 ([018380c](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/018380c357dd4261bae621776c76ab16e78533c6))
* ✨ FEGC-1005 【仓管端】车销单开单页面开发 ([f96c3be](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/f96c3becc0194b2f05af53597d26b4c06e67cab0))
* ✨ FEGC-1006 回货差异开发 ([50e5dd4](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/50e5dd4c2de7642ea5f99d9ef3e6da2dc183d857))
* ✨ FEGC-1010 “选择仓位”弹窗标题，目前为“选择仓库”，应修改为“选择仓位” ([59d8e1d](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/59d8e1d7a6e0030835215ba43e8249da9849f844))
* ✨ FEGC-1020 【仓管端】领货返回单详情页面开发 ([3de3a35](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/3de3a35706cc2bf258e601a2a5a1aa836428e0a8))
* ✨ FEGC-1021 【业务员】车销退单创建页面开发 ([1ff5ee9](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/1ff5ee97a72b45906b09574b7d655011ba69135a))
* ✨ FEGC-1029【仓管端】工作台展示存在超过七天未处理的差异单提示，【业务员端】我的页面展示物流中心 ([cd83ef1](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/cd83ef141e8849a2d0b4a6c2e50e29beb50094a7))
* ✨ FEGC-1032 仓管领货确认批量审核逻辑调整 ([8c2c5f0](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/8c2c5f0ba49e0fadfe652514629b3baf406c988d))
* ✨ FEGC-1040 添加领货打印页面 ([b9df8be](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/b9df8be8fe1b57d41e64aed791d30b1f0952e921))
* ✨ FEGC-1041 业务员端回货差异单列表页面开发 ([4156624](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/41566246fe76c5cd3821be41e0eb2cd998a97e7c))
* ✨ FEGC-1042 【业务员】回货差异单详情页面开发 ([885804e](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/885804eb492521b409917c9e4f5bee1a9ac0f4c2))
* ✨ FEGC-1051 【仓管端】车销单退单页面开发 ([3f2acfa](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/3f2acfadb815d852ee96d43ee34ebb1fab46d241))
* ✨ FEGC-1074 为全部分类添加角标取值为skuCount ([fe31d1e](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/fe31d1e17a97b0c07de20fd077aeaf437daa406b))
* ✨ FEGC-1080 车销开、退单查询商品接口增加storeGid参数 ([a7d739e](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/a7d739ee1725e77d9cf32443ab6ad691e7cc4b8a))
* ✨ FEGC-1083 回货确认详情页面.界面问题 ([a19c1c0](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/a19c1c0a4d1aaa9962e874c394433e3fdf743310))
* ✨ FEGC-1087 车余库存等字段调整为M箱N瓶格式显示 ([a814c28](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/a814c287338d1059277c203ea039afae5d757102))
* ✨ FEGC-1087 回货差异车余库存等字段调整为M箱N瓶格式显示 ([eeceeac](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/eeceeac623a21d481fb9158e9dec16e329a13279))
* ✨ FEGC-1088 车销打卡距离默认值调整为200m ([981e5e4](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/981e5e49c160e4cc19c46c18fd5a58b08a631b88))
* ✨ FEGC-1093 库存.点击历史领货/历史回货跳转到已审核领货单/回货单界面 ([8852879](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/8852879c2cfb3a998de5975132808d1a92b09a7f))
* ✨ FEGC-1099 增加首页下拉刷新功能，展示最新的货值、品项、不可用 ([c1252c0](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/c1252c0d044e03f5e11a7a7eee9b745a322a3ac4))
* ✨ FEGC-1100 车销开单详情页面.如果门店地址为空，不要展示为null ([0f3c21f](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/0f3c21fa00e0e566174f41a9a12a6344dc01f0f3))
* ✨ FEGC-1101 我的界面单据查询顺序调整 ([514be79](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/514be799b61eba504ecf193b4cf5685c0ff4bea3))
* ✨ FEGC-1105 领货返回列表移除已作废状体啊筛选 ([fc15241](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/fc152410b078f4e440cde44e07976db30d886ca0))
* ✨ FEGC-1112 领货详情页新增“创建人”“业务员”字段 ([7dfeca0](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/7dfeca024d9fbc40e3644cd7baadcf9a9fb3c008))
* ✨ FEGC-1115 回货差异开单时切换门店要重新获取商品价格 ([7825550](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/7825550e0223f2b4fb4fccc093d780f5a2ebc73e))
* ✨ FEGC-1116 领货返回移除作废按钮 ([9ad3e61](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/9ad3e61be3b6c728c7d223f4fe8dd70bdb5fff14))
* ✨ FEGC-1116 支持权限控制 ([bac0445](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/bac04459ef72293453db2d5f35b9d793b62cb8f8))
* ✨ FEGC-1126 仓管端领货确认增加实领数量限制及校验 ([9d55cd1](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/9d55cd1efc1857099a0dd0fd2540f256a7b054a8))
* ✨ FEGC-1128 打印支持区分草稿和单据 ([2fd5484](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/2fd5484594fdc6e997c88b33481e82c496c6a019))
* ✨ FEGC-1129 回货开单、车销签退增加校验逻辑 ([b676fbb](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/b676fbb5fe5b383642bfa8f99e525a8b0c96f815))
* ✨ FEGC-969 完成登录页面开发 ([cc2a8d7](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/cc2a8d7f39245f49e3c740d86276c3546f913fd4))
* ✨ FEGC-970 【业务员】首页开发 ([165f509](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/165f509375b6912440bf13a290497fbd687a2767))
* ✨ FEGC-973 【业务员】车销tab门店详情页面打卡功能开发 ([7449353](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/7449353e19e2049cf3718d8b96ecca86aad79999))
* ✨ FEGC-975 【业务员】车销开单购物车页面开发 ([81bc8eb](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/81bc8ebcdf4cc6d68ef74cfaba4eccb0e7c77b12))
* ✨ FEGC-975 【业务员】车销开单购物车页面开发 ([017b69d](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/017b69d5989a448130c4e97b18996612e2077b0e))
* ✨ FEGC-977 历史车销单列表开发 ([7b970ed](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/7b970ed0b3329f08c9969d33ad0b1ba4baaa0fef))
* ✨ FEGC-981 【业务员】领货创建页面开发 ([e34e08d](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/e34e08d9eab200cafa74ec908ec37b9183200991))
* ✨ FEGC-981 【业务员】领货创建页面开发 ([ce0319d](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/ce0319d7bd3dd3815e5bf2ebd10fbafbaf8916d1))
* ✨ FEGC-981 【业务员】领货创建页面开发 ([f5db1fb](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/f5db1fbd0ba7b2a5b43c1c8f2a6ea4b1b61cb505))
* ✨ FEGC-981 领货创建分类数量同步逻辑调整 ([e79bc0d](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/e79bc0ddc07dea11f5f31327278643ac7eb6e1cb))
* ✨ FEGC-983 【业务员】领货单详情页面开发 ([55ea56f](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/55ea56fb1e90e3364a228f729fe119ac2e28f393))
* ✨ FEGC-983 步进器支持失焦才更新 ([8869c15](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/8869c159062889a1fd747d274125cf4323c4ddb5))
* ✨ FEGC-983 车销领货创建页面支持导入 ([7304a81](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/7304a8118c2fc84314c7b62baaaccc98ca6b74b1))
* ✨ FEGC-983 车销领货购物车和搜索页面开发 ([84483be](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/84483beee00bf03283c16e4dffbfc878076c62ac))
* ✨ FEGC-987 打印功能开发 ([eaec985](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/eaec9853f923bd695d80ac4da0d680798009e95e))
* ✨ FEGC-987 打印功能开发 ([7c7f8cf](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/7c7f8cfed21cbbe83e1237cfb8b13ad2367eb7d8))
* ✨ FEGC-988 【业务员】回货单创建页面开发 ([6d30fb6](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/6d30fb6cd28140b37404a76200554b84d1dae7d9))
* ✨ FEGC-996 【业务员】买赔单单据列表开发 ([0e2beeb](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/0e2beeb7e54d6208fe147aa34162d2183b46cb96))
* ✨ FEGC-998 【仓管端】领货确认列表页面开发 ([5ca9bb6](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/5ca9bb693f135f123c2e51a5998d3f50a1724a81))
* ✨ init ([b3399e1](https://gitlab.hd123.com:20022/vue/lsym-cx-mini/commit/b3399e1f59ca3286ebc2b9de15b2e592b8cc9945))
