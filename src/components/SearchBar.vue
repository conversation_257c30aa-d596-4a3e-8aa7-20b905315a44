<script lang="ts" setup>
// 定义组件属性
const props = defineProps({
  // 搜索关键词
  modelValue: {
    type: String,
    default: '',
  },
  // 搜索框占位符
  placeholder: {
    type: String,
    default: '搜索',
  },
  // 是否显示取消按钮
  hideCancel: {
    type: Boolean,
    default: false,
  },
  // 是否将占位符居左显示
  placeholderLeft: {
    type: Boolean,
    default: true,
  },
  // 自定义样式
  customStyle: {
    type: String,
    default: '',
  },
  // 自定义类名
  customClass: {
    type: String,
    default: '',
  },
  // 是否使用浅色主题
  light: {
    type: Boolean,
    default: false,
  },
  // 背景颜色
  bgColor: {
    type: String,
    default: 'white',
  },
  // 是否显示扫码图标
  showScan: {
    type: Boolean,
    default: true,
  },
  // 是否显示筛选图标
  showFilter: {
    type: Boolean,
    default: true,
  },
  // 是否存在筛选条件
  hasFilter: {
    type: <PERSON>olean,
    default: false,
  },
  // 是否自动获取焦点
  focus: {
    type: Boolean,
    default: false,
  },
})
// 定义事件
const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
  (e: 'search',): void
  (e: 'focus', event: any): void
  (e: 'blur', event: any): void
  (e: 'clear'): void
  (e: 'filter'): void
  (e: 'scan'): void
}>()
const { error: showError } = useGlobalToast()
// 内部关键词
const keyword = ref(props.modelValue)

// 监听关键词变化
watch(() => props.modelValue, (newVal) => {
  if (newVal !== keyword.value) {
    keyword.value = newVal
  }
})

// 监听内部关键词变化
watch(() => keyword.value, (newVal) => {
  emit('update:modelValue', newVal)
})

// 处理搜索事件
function handleSearch() {
  emit('search')
}

// 处理聚焦事件
function handleFocus(event: any) {
  emit('focus', event)
}

// 处理失焦事件
function handleBlur(event: any) {
  emit('blur', event)
}

// 处理清除事件
function handleClear() {
  emit('clear')
}

// 处理筛选按钮点击事件
function handleFilter() {
  emit('filter')
}

// 处理扫码事件
function handleScan() {
  uni.scanCode({
    scanType: ['barCode', 'qrCode'],
    success: (res) => {
      keyword.value = res.result
      nextTick(() => {
        handleSearch()
      })
    },
    fail: (res) => {
      console.error(`扫码失败${res.errMsg ? `：${res.errMsg}` : ''}！`)
      // 提示文本不再展示非中文错误信息
      // showError(`扫码失败${res.errMsg ? `：${res.errMsg}` : ''}！`)
      showError(`扫码失败！`)
    },
  })
}
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <view
    class="search-bar-container flex items-center px-3 py-8px" :class="[customClass]"
    :style="`background-color: ${bgColor}; --wot-search-input-height: 36px;${customStyle}`"
  >
    <wd-search
      v-model="keyword" :placeholder="placeholder" :hide-cancel="hideCancel"
      :placeholder-left="placeholderLeft" :light="light" :custom-style="customStyle"
      :custom-class="`flex-1 ${customClass}`" :focus="focus" @search="handleSearch" @focus="handleFocus"
      @blur="handleBlur"
      @clear="handleClear"
    >
      <!-- 前缀插槽 - 扫码图标 -->
      <template #prefix>
        <view v-if="showScan" class="cursor-pointer px-24rpx" @click.stop="handleScan">
          <slot name="prefix">
            <text class="i-tdesign-scan text-32rpx text-[#343A40]" />
          </slot>
        </view>
        <slot v-else name="prefix" />
      </template>

      <!-- 后缀插槽 - 筛选图标 -->
      <template #suffix>
        <view v-if="showFilter" class="cursor-pointer pl-3" @click.stop="handleFilter">
          <slot name="suffix">
            <text class="i-tdesign-filter text-lg text-[#343A40]" :style="{ color: hasFilter ? 'var( --atom-primary-primary-6)' : '#343A40' }" />
          </slot>
        </view>
        <slot v-else name="suffix" />
      </template>
    </wd-search>

    <!-- 右侧操作区域 -->
    <view v-if="$slots.action" class="action-slot ml-2">
      <slot name="action" />
    </view>
  </view>
</template>

<style lang="scss" scoped>
.search-bar-container {
  width: 100%;
  box-sizing: border-box;

  :deep(.wd-search) {
    padding: 0 !important;
    background-color: transparent !important;
  }

  :deep(.wd-search__input) {
    padding-left: 0 !important;
  }

  :deep(.wd-search__block) {
    border-radius: 30px !important;
  }

  :deep(.wd-search__search-left-icon) {
    display: none;
  }
}
</style>
