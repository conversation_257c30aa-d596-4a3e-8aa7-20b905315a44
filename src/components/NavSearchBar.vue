<script lang="ts" setup>
/**
 * 导航栏搜索组件
 * 用于自定义导航栏中的搜索框
 */

// 定义组件属性
const props = defineProps({
  // 搜索关键词
  modelValue: {
    type: String,
    default: '',
  },
  // 搜索框占位符
  placeholder: {
    type: String,
    default: '请输入商品信息',
  },
  // 是否显示取消按钮
  hideCancel: {
    type: Boolean,
    default: true,
  },
  // 是否使用浅色主题
  light: {
    type: Boolean,
    default: true,
  },
  // 自定义样式
  customStyle: {
    type: String,
    default: '',
  },
  // 是否显示返回按钮
  showBack: {
    type: Boolean,
    default: false,
  },
  // 是否只读,支持抛出点击事件
  readonly: {
    type: Boolean,
    default: false,
  },
  // 是否显示扫码图标
  showScan: {
    type: Boolean,
    default: true,
  },

})

// 定义事件
const emit = defineEmits(['update:modelValue', 'search', 'focus', 'blur', 'clear', 'back', 'click', 'scan'])

const { capsule } = storeToRefs(useDeviceInfo())
const { error: showError } = useGlobalToast()

// 内部关键词
const keyword = ref(props.modelValue)

// 监听关键词变化
watch(() => props.modelValue, (newVal) => {
  keyword.value = newVal
})

// 监听内部关键词变化
watch(() => keyword.value, (newVal) => {
  emit('update:modelValue', newVal)
})

// 处理搜索事件
function handleSearch() {
  emit('search', keyword.value)
}

// 处理聚焦事件
function handleFocus(event: any) {
  emit('focus', event)
}

// 处理失焦事件
function handleBlur(event: any) {
  emit('blur', event)
}

// 处理清除事件
function handleClear() {
  keyword.value = ''
  emit('clear')
}

// 处理返回按钮点击事件
function handleBack() {
  emit('back')
  // 默认行为是返回上一页
  uni.navigateBack()
}

function handleClick() {
  emit('click')
}

// 处理扫码事件
function handleScan() {
  uni.scanCode({
    scanType: ['barCode', 'qrCode'],
    success: (res) => {
      emit('scan', res.result)
    },
    fail: () => {
      showError(`扫码失败！`)
    },
  })
}
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <view class="relative" :style="`height: ${capsule.top + capsule.height}px;`">
    <view
      :style="`--wot-search-input-height: ${capsule.height}px;padding-top: ${capsule.top}px;${customStyle}`"
      class="nav-search-bar fixed top-0 z-999 w-full"
    >
      <view class="w-full flex items-center" @click.stop="handleClick">
        <!-- 返回按钮 -->
        <view v-if="showBack" class="flex-none pl-24rpx" @click.stop="handleBack">
          <wd-icon name="arrow-left" size="48rpx" custom-class="text-[#5C5C5C]" />
        </view>

        <wd-search
          v-model="keyword"
          :disabled="readonly"
          :hide-cancel="hideCancel"
          placeholder-left
          :placeholder="placeholder"
          custom-class="py-0! px-3! bg-transparent! box-border"
          :custom-style="`width: calc(100vw - ${capsule.width}px - ${showBack ? '72rpx' : '0px'});`"
          :light="light"
          @search="handleSearch"
          @focus="handleFocus"
          @blur="handleBlur"
          @clear="handleClear"
        >
          <!-- 前缀插槽 - 扫码图标 -->
          <template #prefix>
            <view v-if="showScan" class="px-24rpx" @click.stop="handleScan">
              <text class="i-tdesign-scan text-32rpx text-[#343A40]" />
            </view>
          </template>
        </wd-search>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.nav-search-bar {
  background-color: transparent;

  :deep() {
    .wd-search {
      border-radius: 4px;
    }
    .wd-search__search-left-icon {
      display: none;
    }
    .wd-search__input {
      padding-left: 0 !important;
    }
  }
}
</style>
