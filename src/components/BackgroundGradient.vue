<!--
 * @Author: we<PERSON><PERSON>
 * @Date: 2025-05-15 15:30:00
 * @LastEditTime: 2025-05-15 15:50:00
 * @LastEditors: weisheng
 * @Description: 背景渐变组件
 * @FilePath: /lsym-cx-mini/src/components/BackgroundGradient.vue
-->
<script setup lang="ts">
// 组件无需传入参数
</script>

<template>
  <view class="bg-container">
    <view class="bg-gradient" />
    <view class="circle-1 bg-circle" />
    <view class="bg-circle circle-2" />
    <view class="bg-circle circle-3" />
    <view class="bg-circle circle-4" />
  </view>
</template>

<style lang="scss" scoped>
.bg-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  overflow: hidden;
}

.bg-gradient {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom,
    rgba(255, 255, 255, 0.3) 0%,
    rgba(255, 255, 255, 0.8) 25.6%,
    rgba(232, 237, 245, 0.6) 40.3%,
    rgba(232, 237, 245, 0.6) 63.8%,
    rgba(232, 237, 245, 0.15) 77.6%,
    rgba(232, 237, 245, 0.6) 89.7%);
}

.bg-circle {
  position: absolute;
  border-radius: 50%;
  filter: blur(100px);
}

.circle-1 {
  width: 250px;
  height: 250px;
  background-color: #8BB4F3;
  top: -50px;
  left: -100px;
  opacity: 0.6;
}

.circle-2 {
  width: 300px;
  height: 300px;
  background-color: rgba(0, 87, 255, 0.55);
  top: -150px;
  right: -100px;
  opacity: 0.7;
  filter: blur(200px);
}

.circle-3 {
  width: 200px;
  height: 200px;
  background-color: #31D0E8;
  bottom: 100px;
  left: -50px;
  opacity: 0.7;
  filter: blur(200px);
}

.circle-4 {
  width: 250px;
  height: 250px;
  background-color: rgba(0, 87, 255, 0.8);
  bottom: -100px;
  right: -50px;
  opacity: 0.7;
  filter: blur(200px);
}
</style>
