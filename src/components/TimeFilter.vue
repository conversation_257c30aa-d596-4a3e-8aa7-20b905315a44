<script lang="ts" setup>
import { isArray } from 'wot-design-uni/components/common/util'

export type TimeFilterType = '今日' | '本周' | '本月' | '自定义'

// confirm 事件类型声明
interface TimeFilterConfirmPayload {
  type: TimeFilterType | null
  range: string | [string, string]
}

const props = defineProps({
  // 是否在tabbar页面使用
  useInTabbar: {
    type: Boolean,
    default: false,
  },
  // 类型
  defaultType: {
    type: String as PropType<TimeFilterType>,
  },
})

// emit
const emit = defineEmits<{
  (e: 'confirm', payload: TimeFilterConfirmPayload): void
}>()

// 弹窗显示状态
const showFilterPopup = ref<boolean>(false)

// 时间筛选标签
const tags = ref<TimeFilterType[]>(['今日', '本周', '本月', '自定义'])

// 选中的标签，默认不选中
const selectedTag = ref<TimeFilterType | null>(props.defaultType || null)

// 区间选择的时间，v-model为数组
const customRange = ref<(number | '')[]>(['', ''])

// 是否显示自定义时间选择器
const showCustomDate = ref<boolean>(false)

// 日期格式化
function fullDate(val: number | string) {
  if (!val)
    return ''
  const d = new Date(Number(val))
  const y = d.getFullYear()
  const m = (d.getMonth() + 1).toString().padStart(2, '0')
  const day = d.getDate().toString().padStart(2, '0')
  return `${y}-${m}-${day}`
}

// 获取今日区间
function getTodayRange(): number {
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime()
  return today
}

// 获取本周区间（周一到周日）
function getWeekRange(): [number, number] {
  const now = new Date()
  const day = now.getDay() || 7 // 周日为0，转为7
  const monday = new Date(now.getFullYear(), now.getMonth(), now.getDate() - day + 1)
  const sunday = new Date(now.getFullYear(), now.getMonth(), now.getDate() - day + 7, 23, 59, 59, 999)
  return [monday.getTime(), sunday.getTime()]
}

// 获取本月区间
function getMonthRange(): [number, number] {
  const now = new Date()
  const start = new Date(now.getFullYear(), now.getMonth(), 1).getTime()
  const end = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999).getTime()
  return [start, end]
}

// 选择标签
function selectTag(tag: TimeFilterType) {
  selectedTag.value = tag
  showCustomDate.value = tag === '自定义'
}

// 重置筛选条件
function handleReset() {
  selectedTag.value = null
  customRange.value = ['', '']
  showCustomDate.value = false
}

// 确认筛选条件
function handleConfirm() {
  let range: number | [number, number] | null = null
  if (selectedTag.value === '今日') {
    range = getTodayRange()
  }
  else if (selectedTag.value === '本周') {
    range = getWeekRange()
  }
  else if (selectedTag.value === '本月') {
    range = getMonthRange()
  }
  else if (selectedTag.value === '自定义') {
    if (customRange.value[0] && customRange.value[1]) {
      range = [Number(customRange.value[0]), Number(customRange.value[1])]
    }
  }
  const rangeStr = isArray(range) ? (range.map(fullDate) as [string, string]) : (range ? fullDate(range) : '')
  emit('confirm', {
    type: selectedTag.value,
    range: rangeStr,
  })
  closeFilterPopup()
}

// 打开筛选弹窗
function openFilterPopup() {
  showFilterPopup.value = true
}

// 关闭筛选弹窗
function closeFilterPopup() {
  showFilterPopup.value = false
}

defineExpose<{
  open: () => void
  close: () => void
}>({
      open: openFilterPopup,
      close: closeFilterPopup,
    })
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <!-- 筛选弹窗 -->
  <wd-popup v-model="showFilterPopup" position="right" close-on-click-modal safe-area-inset-bottom>
    <view class="time-filter h-full w-[680rpx] flex flex-col bg-white">
      <!-- 内容区 -->
      <view class="flex-1 overflow-y-auto p-6">
        <!-- 时间筛选标签组 -->
        <view class="mb-4">
          <text class="mb-3 block text-28rpx text-[#333] font-600">
            时间
          </text>
          <view class="flex flex-wrap">
            <view
              v-for="item in tags"
              :key="item"
              class="mr-2 cursor-pointer select-none rounded px-4 py-2 text-sm"
              :class="[
                selectedTag === item
                  ? 'bg-[#E5EDFF] text-[var(--atom-primary-primary-6)] border border-[var(--atom-primary-primary-6)]'
                  : 'bg-[#F5F6F7] text-[#333]',
              ]"
              @click="selectTag(item)"
            >
              {{ item }}
            </view>
          </view>
        </view>
        <!-- 自定义时间区间选择器 -->
        <view v-if="selectedTag === '自定义'" class="mt-4">
          <wd-datetime-picker
            v-model="customRange"
            type="date"
            @confirm="(e) => customRange = e.value"
          >
            <!-- 默认插槽自定义触发器 -->
            <view class="flex items-center">
              <view class="min-w-[120rpx] flex-1 border border-[#D6D6D6] rounded px-3 py-2 text-center text-28rpx text-[#5C5C5C]">
                {{ customRange[0] ? fullDate(customRange[0]) : '开始日期' }}
              </view>
              <text class="text-28rpx text-[#333]">
                至
              </text>
              <view class="min-w-[120rpx] flex-1 border border-[#D6D6D6] rounded px-3 py-2 text-center text-28rpx text-[#5C5C5C]">
                {{ customRange[1] ? fullDate(customRange[1]) : '结束日期' }}
              </view>
            </view>
          </wd-datetime-picker>
        </view>
      </view>
      <!-- 底部按钮 -->
      <view class="flex px-6">
        <wd-button custom-class="flex-1 mr-2!" size="large" type="primary" :round="false" plain @click="handleReset">
          重置
        </wd-button>
        <wd-button custom-class="flex-1" size="large" type="primary" :round="false" @click="handleConfirm">
          确定
        </wd-button>
      </view>
      <wd-gap v-if="useInTabbar" height="calc(50px + 24rpx)" />
    </view>
  </wd-popup>
</template>

<style lang="scss" scoped>
.time-filter {
  :deep(.wd-picker__wraper){
    padding-bottom: 50px;
  }
}
</style>
