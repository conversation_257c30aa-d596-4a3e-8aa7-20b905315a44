<!--
* @Author: weish<PERSON>
* @Date: 2025-01-24 10:00:00
 * @LastEditTime: 2025-06-23 16:15:07
 * @LastEditors: weisheng
* @Description: 权限包装组件
 * @FilePath: /lsym-cx-mini/src/components/PermissionWrapper.vue
-->
<script setup lang="ts">
import type { PermissionType } from '@/utils/permissions'
import { usePermissionChecker } from '@/composables/usePermission'

interface Props {
  // 单个权限检查
  permission?: PermissionType
  // 多个权限任意一个检查
  anyPermissions?: PermissionType[]
  // 多个权限全部检查
  allPermissions?: PermissionType[]
  // 按钮权限检查
  buttonKey?: string
  // 是否显示无权限时的内容（默认不显示）
  showFallback?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showFallback: false,
})

// 获取权限检查工具
const { checkPermission, checkAnyPermission, checkAllPermissions, checkButtonPermission } = usePermissionChecker()

// 计算是否有权限
const hasPermission = computed(() => {
  // 按钮权限检查
  if (props.buttonKey) {
    return checkButtonPermission(props.buttonKey)
  }

  // 单个权限检查
  if (props.permission) {
    return checkPermission(props.permission)
  }

  // 多个权限任意一个检查
  if (props.anyPermissions && props.anyPermissions.length > 0) {
    return checkAnyPermission(props.anyPermissions)
  }

  // 多个权限全部检查
  if (props.allPermissions && props.allPermissions.length > 0) {
    return checkAllPermissions(props.allPermissions)
  }

  // 没有配置权限要求，默认显示
  return true
})
</script>

<template>
  <template v-if="hasPermission">
    <!-- 有权限时显示的内容 -->
    <slot />
  </template>
  <template v-else-if="showFallback">
    <!-- 无权限时显示的内容 -->
    <slot name="fallback" />
  </template>
</template>

<style lang="scss" scoped>
// 无额外样式
</style>
