<script lang="ts" setup>
import emptyIcon from '@/static/icon/ic_empty.svg'

defineProps({
  /** 描述 */
  tip: String,
  customClass: {
    type: String,
    default: '',
  },
  customStyle: {
    type: String,
    default: '',
  },
})
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <wd-status-tip :tip="tip" :image="emptyIcon" :custom-class="customClass" :custom-style="customStyle" />
</template>
