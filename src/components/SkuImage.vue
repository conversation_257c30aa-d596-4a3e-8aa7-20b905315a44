<script setup lang="ts">
import { isArray, objToStyle } from 'wot-design-uni/components/common/util'
import type { CSSProperties } from 'vue'
import skuDefaultImage from '@/static/icon/ic_sku_default.svg'

// 图片模式类型
type ImageMode = 'scaleToFill' | 'aspectFit' | 'aspectFill' | 'widthFix' | 'heightFix' | 'top' | 'bottom' | 'center' | 'left' | 'right' | 'top left' | 'top right' | 'bottom left' | 'bottom right'

const props = defineProps({
  /** 商品图片链接 */
  src: {
    type: String,
    default: '',
  },
  /** 预览图片，不传则使用src */
  previewSrc: {
    type: Array as PropType<string[]>,
    default: () => [],
  },
  /** 是否开启预览 */
  enablePreview: {
    type: Boolean,
    default: true,
  },
  /** 图片宽度 */
  width: {
    type: [String, Number],
    default: '160rpx',
  },
  /** 图片高度 */
  height: {
    type: [String, Number],
    default: '160rpx',
  },
  /** 图片填充模式 */
  mode: {
    type: String as () => ImageMode,
    default: 'aspectFill',
  },
  /** 图片圆角大小 */
  radius: {
    type: [String, Number],
    default: '12rpx',
  },
  customClass: {
    type: String,
    default: '',
  },
  customStyle: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['click'])

const imgStyle = computed(() => {
  const style: CSSProperties = {
    width: CommonUtil.addUnit(props.width),
    height: CommonUtil.addUnit(props.height),
    borderRadius: props.radius,
  }
  return `${objToStyle(style)} ${props.customStyle}`
})

const enablePreviewValue = computed(() => props.enablePreview && isArray(props.previewSrc) && props.previewSrc.length > 0)

function setResize(src: string) {
  // 如果已经包含?了则直接&拼接
  if (src.includes('?')) {
    return `${src}&x-oss-process=image/resize,l_360`
  }
  else {
    return `${src}?x-oss-process=image/resize,l_360`
  }
}

// 图片链接计算属性
const imgSrc = computed(() => props.src ? setResize(props.src) : skuDefaultImage)
// 预览图片链接计算属性
const previewImgSrc = computed(() => props.previewSrc)

// 图片加载状态
const isLoading = ref(true)
const isError = ref(false)

// 处理图片加载完成
function handleLoad(_event: any) {
  isLoading.value = false
  isError.value = false
}

// 处理图片加载失败
function handleError(_event: any) {
  isLoading.value = false
  isError.value = true
}

// 处理点击图片
function handleClick() {
  emit('click')
  if (enablePreviewValue.value && !isError.value && !isLoading.value) {
    uni.previewImage({
      urls: previewImgSrc.value,
      current: 0,
    })
  }
}
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <view
    :class="`sku-image ${customClass}`" :style="imgStyle"
  >
    <wd-img
      :src="imgSrc"
      :width="width"
      :height="height"
      :mode="mode"
      custom-class="h-full w-full"
      @load="handleLoad"
      @error="handleError"
      @click="handleClick"
    >
      <!-- 加载中状态 -->
      <template #loading>
        <view class="flex-center h-full w-full bg-$gray-100">
          <wd-loading size="26px" color="#999" />
        </view>
      </template>

      <!-- 加载失败状态 -->
      <template #error>
        <view class="h-full w-full flex flex-col items-center justify-center bg-$gray-100">
          <wd-icon name="warning" size="26px" color="#999" class="mt-2" />
          <text class="mt-2 text-28rpx text-[#999]">
            加载失败
          </text>
        </view>
      </template>
    </wd-img>

    <!-- 预览图标 -->
    <view v-if="enablePreviewValue && !isLoading && !isError" class="absolute bottom-0 right-0 h-36rpx w-36rpx flex items-center justify-center overflow-hidden rounded-tl-12rpx bg-black/40" @click="handleClick">
      <text class="i-tdesign-zoom-in text-3 text-white" />
    </view>
  </view>
</template>

<style lang="scss" scoped>
.sku-image {
  position: relative;
  overflow: hidden;

  .preview-icon {
    position: absolute;
    right: 0;
    top: 0;
    width: 30rpx;
    height: 30rpx;
    background-color: rgba(0, 0, 0, 0.4);
    border-radius: 0 0 0 5.73rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .flex-center {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
