<script setup lang="ts">
import dayjs from 'dayjs'

const router = useRouter()
const { navBarTotalHeight } = useDeviceInfo()
const { checkAnyPermission } = usePermissionChecker()

// 用户信息 - 从全局存储获取
const userStore = useUserStore()
const wmsStore = useWmsStore()

// 判断用户是否为仓管员
const isWarehouseUser = computed(() => userStore.userRole === 'warehouse')

// 获取当前选择的物流中心信息
const currentWms = computed(() => wmsStore.currentWms)

const userInfo = computed(() => ({
  name: userStore.userName,
  userId: userStore.userCode,
  avatar: '/static/icon/ic_avatar.svg', // 使用默认头像，实际项目中可能需要从用户信息中获取
}))

// 当前选中的时间范围
const activeTime = ref('day')

// 日期范围
const dateRange = reactive({
  start: dayjs().format('YYYY-MM-DD'),
  end: dayjs().format('YYYY-MM-DD'),
})

// 日历选择器的值
const calendarValue = ref<number>(Date.now())

// 计算日历的最大值和最小值
const calendarMinDate = computed(() => {
  const today = new Date()
  // 往前推6个月的1号
  const sixMonthsAgo = new Date(today.getFullYear(), today.getMonth() - 6, 1)
  return sixMonthsAgo.getTime()
})

const calendarMaxDate = computed(() => {
  // 当天
  const today = new Date()
  return today.getTime()
})

// 日历类型映射
const calendarTypeMap: Record<string, 'date' | 'week' | 'month'> = {
  day: 'date',
  week: 'week',
  month: 'month',
}

// 计算日历的类型
const calendarType = computed(() => {
  return calendarTypeMap[activeTime.value] || 'date'
})

// 创建请求参数
const requestParams = reactive({
  beginDate: dateRange.start,
  finishDate: dateRange.end,
})

// 使用 alova 的 useRequest 钩子调用 mainInterface.getTemplatesUsingGET_1 接口
const {
  loading: _statsLoading,
  data: statsData,
  send: fetchStats,
} = useRequest(
  () => Apis.mainInterface.getDataUsingPOST({
    data: {
      ...requestParams,
      vehSaleEmpGid: userStore.vehSaleEmp?.gid || 0,
    },
  }),
  {
    immediate: false,
    initialData: {
      code: 0,
      data: {
        vehSaleTotal: 0,
        vehSaleCount: 0,
        vehSaleBckTotal: 0,
        wholeSaleTotal: 0,
      },
      msg: '',
    },
  },
)

// 销售统计数据列表
const statistics = computed(() => {
  return [
    {
      name: '销售额',
      value: `¥ ${statsData.value?.data?.vehSaleTotal?.toFixed(2) || '0.00'}`,
      icon: 'arrow-right',
      type: 'sale' as 'sale' | 'return',
      hasPermission: checkAnyPermission([PERMISSIONS.VEH_SALE_VIEW]),
    },
    {
      name: '订单数',
      value: statsData.value?.data?.vehSaleCount?.toString() || '0',
      icon: 'arrow-right',
      type: 'sale' as 'sale' | 'return',
      hasPermission: checkAnyPermission([PERMISSIONS.VEH_SALE_VIEW]),
    },
    {
      name: '退货额',
      value: `¥ ${statsData.value?.data?.vehSaleBckTotal?.toFixed(2) || '0.00'}`,
      icon: 'arrow-right',
      type: 'return' as 'sale' | 'return',
      hasPermission: checkAnyPermission([PERMISSIONS.VEH_SALE_BCK_VIEW]),
    },
    {
      name: '买赔额',
      value: `¥ ${statsData.value?.data?.wholeSaleTotal?.toFixed(2) || '0.00'}`,
      icon: 'arrow-right',
      type: 'compensation-list' as 'sale' | 'return' | 'compensation-list',
      hasPermission: checkAnyPermission([PERMISSIONS.WHOLE_SALE_VIEW]),
    },
  ].filter(item => item.hasPermission)
})

// 功能选项
const functionOptions = computed(() => {
  return [
    {
      id: 'pick-list',
      name: '领货单',
      icon: '../../static/images/ic_pick.svg',
      color: '#12B886',
      route: isWarehouseUser.value ? 'pick-confirm-list' : 'pick-list',
      hasPermission: checkAnyPermission([PERMISSIONS.VEH_SALE_USE_SIGN_VIEW]),
    },
    {
      id: 'back-list',
      name: '回货单',
      icon: '../../static/images/ic_back.svg',
      color: '#1C64FD',
      route: isWarehouseUser.value ? 'back-confirm-list' : 'back-list',
      hasPermission: checkAnyPermission([PERMISSIONS.VEH_SALE_USE_SIGN_ARV_VIEW]),
    },
    {
      id: 'sales-list',
      name: '车销单',
      icon: '../../static/images/ic_sales_icon.svg',
      color: '#5A0A05',
      route: 'sales-list',
      hasPermission: checkAnyPermission([PERMISSIONS.VEH_SALE_VIEW, PERMISSIONS.VEH_SALE_BCK_VIEW]),
    },
    {
      id: 'back-diff-list',
      name: '回货差异单',
      icon: '../../static/icon/ic_back_diff.svg',
      color: '#1C64FD',
      route: 'back-diff-list',
      hasPermission: checkAnyPermission([PERMISSIONS.VEH_SALE_USE_SIGN_ARV_DIFF_VIEW]),
    },
    {
      id: 'compensation-list',
      name: '买赔单',
      icon: '../../static/images/ic_compensation_icon.svg',
      color: '#FAAD14',
      route: 'compensation-list',
      hasPermission: checkAnyPermission([PERMISSIONS.WHOLE_SALE_VIEW]),
    },
    {
      id: 'return-pick-list',
      name: '领货返回单',
      icon: '../../static/images/ic_return_pick.svg',
      color: '#12B886',
      route: 'return-pick-list',
      hasPermission: checkAnyPermission([PERMISSIONS.VEH_SALE_USE_SIGN_BCK_VIEW]),
    },
  ].filter(item => item.hasPermission)
})
// 导航到车销单列表
function navigateToSalesList(type: 'sale' | 'return' | 'compensation-list') {
  const params = {
    beginDate: dateRange.start,
    endDate: dateRange.end,
    type,
  }
  router.push({
    name: type === 'compensation-list' ? 'compensation-list' : 'sales-list',
    params: {
      ...params,
    },
  })
}

// 导航到物流中心选择页面
function navigateToWmsSelect() {
  router.push({
    name: 'wms-select',
    params: {
      from: 'profile',
    },
  })
}

// 初始加载日统计数据
onShow(() => {
  handleTimeRangeChange(activeTime.value)
})

// 加载统计数据
function loadStatistics() {
  fetchStats()
}

// 处理时间范围变更
function handleTimeRangeChange(range: string) {
  activeTime.value = range

  // 更新日期范围显示
  const today = new Date()
  let startDate = new Date()
  let endDate = new Date()

  if (range === 'day') {
    startDate = today
    endDate = today
    calendarValue.value = today.getTime()
  }
  else if (range === 'week') {
    const dayOfWeek = today.getDay() || 7
    startDate = new Date(today.setDate(today.getDate() - dayOfWeek + 1))
    endDate = new Date(new Date().setDate(startDate.getDate() + 6))
    calendarValue.value = startDate.getTime()
  }
  else if (range === 'month') {
    startDate = new Date(today.getFullYear(), today.getMonth(), 1)
    endDate = new Date(today.getFullYear(), today.getMonth() + 1, 0)
    calendarValue.value = startDate.getTime()
  }

  // 格式化日期
  dateRange.start = dayjs(startDate).format('YYYY-MM-DD')
  dateRange.end = dayjs(endDate).format('YYYY-MM-DD')

  // 更新请求参数
  requestParams.beginDate = dateRange.start
  requestParams.finishDate = dateRange.end

  // 加载统计数据
  loadStatistics()
}

// 处理功能项点击
function handleFunctionClick(route: string) {
  router.push({ name: route })
}

// 获取全局消息框
const globalMessage = useGlobalMessage()

// 处理退出登录
function handleLogout() {
  globalMessage.confirm({
    title: '退出登录',
    msg: '确定要退出登录吗？',
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    success: (res) => {
      if (res.action === 'confirm') {
        // 执行退出登录逻辑
        userStore.clearUser()
        wmsStore.clearCurrentWms()
        // 使用路由导航到登录页
        router.replaceAll({ name: 'login' })
      }
    },
  })
}

// 切换账号 - 使用GlobalMessage进行二次确认
function handleSwitchAccount() {
  globalMessage.confirm({
    title: '切换账号',
    msg: '确定要切换账号吗？当前账号将退出登录',
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    success: (res) => {
      if (res.action === 'confirm') {
        // 执行退出登录逻辑
        userStore.clearUser()

        // 使用路由导航到登录页
        router.replaceAll({ name: 'login' })
      }
    },
  })
}

const scrollTop = ref(0)

onPageScroll((event) => {
  scrollTop.value = event.scrollTop
})

const navbarStyle = computed(() => {
  const opacity = Math.min(scrollTop.value / 100, 1)
  return `background-color: rgba(255, 255, 255, ${opacity}); transition: background-color 0.3s;`
})

// 处理日历确认选择
function handleCalendarConfirm({ value }: { value: number }) {
  calendarValue.value = value

  const selectedDate = new Date(value)
  let startDate = selectedDate
  let endDate = selectedDate

  if (activeTime.value === 'week') {
    // 周选择：获取选中日期所在周的起始和结束
    const dayOfWeek = selectedDate.getDay() || 7
    startDate = new Date(selectedDate.setDate(selectedDate.getDate() - dayOfWeek + 1))
    endDate = new Date(new Date(value).setDate(startDate.getDate() + 6))
  }
  else if (activeTime.value === 'month') {
    // 月选择：获取选中日期所在月的起始和结束
    startDate = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), 1)
    endDate = new Date(selectedDate.getFullYear(), selectedDate.getMonth() + 1, 0)
  }

  // 更新日期范围
  dateRange.start = dayjs(startDate).format('YYYY-MM-DD')
  dateRange.end = dayjs(endDate).format('YYYY-MM-DD')

  // 更新请求参数
  requestParams.beginDate = dateRange.start
  requestParams.finishDate = dateRange.end

  // 加载统计数据
  loadStatistics()
}

// 当前业务员  userStore.vehSaleEmp
const wms = computed(() => {
  return userStore.vehSaleEmp?.wms ? userStore.vehSaleEmp?.wms : null
})
</script>

<template>
  <view class="profile box-border min-h-[calc(100vh-50px)] w-screen bg-[#F9F9F9] pb-6 min-h-[calc(100vh-50px-constant(safe-area-inset-bottom))]! min-h-[calc(100vh-50px-env(safe-area-inset-bottom))]!">
    <wd-navbar
      safe-area-inset-top fixed :bordered="false"
      title="我的"
      :custom-style="navbarStyle"
    />
    <!-- 用户信息头部 -->
    <view class="user-header relative flex" :style="{ 'padding-top': `${navBarTotalHeight}px` }">
      <!-- 背景装饰 -->
      <view class="header-bg-mask absolute left-0 top-0 h-full w-full" />
      <view class="ellipse-1 absolute left-1/4 top-0 h-50 w-50 rounded-full opacity-60" />
      <view class="ellipse-2 absolute right-1/4 top-1/3 h-40 w-40 rounded-full opacity-70" />
      <view class="ellipse-3 absolute bottom-1/4 left-1/3 h-45 w-45 rounded-full opacity-70" />
      <view class="ellipse-4 absolute bottom-10 right-20 h-30 w-30 rounded-full opacity-70" />

      <!-- 用户信息 -->
      <view class="relative z-1 mx-3 box-border w-full flex items-center justify-between py-4">
        <view class="flex items-center">
          <!-- 头像 -->
          <view class="relative h-13 w-13 flex-none overflow-hidden">
            <image class="h-full w-full" :src="userInfo.avatar" mode="aspectFill" />
          </view>

          <!-- 用户名和ID -->
          <view class="ml-4">
            <view class="flex items-center">
              <text class="break-all text-4 text-[#2D2D2D] font-medium">
                {{ userInfo.name }}
                [{{ userInfo.userId }}]
              </text>
            </view>
          </view>
        </view>

        <!-- 切换账号按钮 -->
        <view class="flex flex-none items-center" @click="handleSwitchAccount">
          <text class="text-primary i-carbon-switch-layer-2 mr-1 text-[#1C64FD]" />
          <text class="text-28rpx text-[#1C64FD]">
            切换账号
          </text>
        </view>
      </view>
    </view>

    <!-- 仓管角色：物流中心切换区域 -->
    <view class="mx-3 mt-2 flex items-center justify-between rounded-[16px] bg-white px-4 py-4">
      <view>
        <view class="mb-2 text-3.25 text-[#5c5c5c] font-medium">
          所属物流中心
        </view>
        <view class="break-all text-32rpx text-[#2D2D2D] font-bold">
          {{ (isWarehouseUser ? currentWms?.name : wms?.name) || '未选择物流中心' }}
        </view>
      </view>
      <view v-if="isWarehouseUser">
        <wd-button :round="false" @click="navigateToWmsSelect">
          切换
        </wd-button>
      </view>
    </view>

    <!-- 业务员角色：数据统计 业务员能看 -->
    <view v-if="!isWarehouseUser && statistics.length > 0" class="mx-3 mt-2 rounded-[16px] bg-white px-4 py-3">
      <view class="mb-3 flex items-center justify-between">
        <view class="text-[16px] text-[#2D2D2D] font-medium">
          数据统计
        </view>
        <view class="flex items-center rounded-1 bg-[#F5F5F5] p-1">
          <view
            class="time-item rounded-1 px-3 py-1 text-28rpx"
            :class="[activeTime === 'day' ? 'bg-[#1C64FD] text-white' : 'text-[#8A8A8A]']"
            @click="handleTimeRangeChange('day')"
          >
            日
          </view>
          <view
            class="time-item rounded-1 px-3 py-1 text-28rpx"
            :class="[activeTime === 'week' ? 'bg-[#1C64FD] text-white' : 'text-[#8A8A8A]']"
            @click="handleTimeRangeChange('week')"
          >
            周
          </view>
          <view
            class="time-item rounded-1 px-3 py-1 text-28rpx"
            :class="[activeTime === 'month' ? 'bg-[#1C64FD] text-white' : 'text-[#8A8A8A]']"
            @click="handleTimeRangeChange('month')"
          >
            月
          </view>
        </view>
      </view>

      <!-- 日期范围 -->
      <view class="date-range mb-3 flex items-center justify-center">
        <wd-calendar
          v-model="calendarValue"
          :type="calendarType"
          :first-day-of-week="1"
          :min-date="calendarMinDate"
          :max-date="calendarMaxDate"

          use-default-slot hide-second
          @confirm="handleCalendarConfirm"
        >
          <view class="flex cursor-pointer items-center text-[var(--textapplication-text-1)]">
            <text class="i-carbon-chevron-left text-28rpx" />
            <text class="mx-2 text-28rpx text-[#2D2D2D]">
              {{ dateRange.start }}
            </text>
            <text class="text-28rpx text-[#2D2D2D]">
              至
            </text>
            <text class="mx-2 text-28rpx text-[#2D2D2D]">
              {{ dateRange.end }}
            </text>
            <text class="i-carbon-chevron-right text-28rpx" />
          </view>
        </wd-calendar>
      </view>

      <!-- 统计卡片区域 - 第一行 -->
      <view v-if="statistics.length > 0" class="grid grid-cols-2 mb-3 gap-3">
        <view
          v-for="(item, index) in statistics.slice(0, statistics.length > 2 ? 2 : statistics.length)" :key="index"
          class="cursor-pointer border border-[#E5E5E5] rounded-1 border-solid p-3"
          @click="navigateToSalesList(item.type)"
        >
          <view class="flex items-center">
            <text class="text-26rpx text-[#636D78]">
              {{ item.name }}
            </text>
            <wd-icon name="arrow-right" size="14px" custom-class="text-[#5C5C5C] ml-1" />
          </view>
          <text class="text-[22px] text-[#343A40] font-bold">
            {{ item.value }}
          </text>
        </view>
      </view>

      <!-- 统计卡片区域 - 第二行 -->
      <view v-if="statistics.length > 2" class="grid grid-cols-2 gap-3">
        <view
          v-for="(item, index) in statistics.slice(2, statistics.length)" :key="index"
          class="cursor-pointer border border-[#E5E5E5] rounded-1 border-solid p-3"
          @click="navigateToSalesList(item.type)"
        >
          <view class="flex items-center">
            <text class="text-26rpx text-[#636D78]">
              {{ item.name }}
            </text>
            <wd-icon name="arrow-right" size="14px" custom-class="text-[#5C5C5C] ml-1" />
          </view>
          <text class="text-[22px] text-[#343A40] font-bold">
            {{ item.value }}
          </text>
        </view>
      </view>
    </view>

    <!-- 功能列表 -->
    <view class="mx-3 mt-2 rounded-lg bg-white">
      <view
        v-for="item in functionOptions"
        :key="item.id"
        class="function-item flex items-center justify-between border-b border-[#F5F5F5] px-4 py-4"
        @click="handleFunctionClick(item.route)"
      >
        <view class="flex items-center">
          <image
            :src="item.icon"
            class="h-48rpx w-48rpx"
            mode="scaleToFill"
          />

          <text class="ml-3 text-[16px] text-[#2D2D2D]">
            {{ item.name }}
          </text>
        </view>
        <wd-icon name="arrow-right" size="16px" custom-class="text-[#999999]" />
      </view>
    </view>

    <!-- 退出登录 -->
    <view class="logout-btn mx-3 mt-2 flex justify-center rounded-lg bg-white py-3" @click="handleLogout">
      <text class="text-[16px] text-[#F14646]">
        退出登录
      </text>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.profile{

  :deep(){
    .wd-action-sheet{
      padding-bottom: calc(50px + constant(safe-area-inset-bottom)) !important;
      padding-bottom: calc(50px + env(safe-area-inset-bottom)) !important;
    }

    .wd-button {
      min-width: 120rpx !important;
    }
  }
}
.user-header {
  position: relative;
  background-color: #FFFFFF;
  overflow: hidden;
  padding-bottom: 16px;
}

.header-bg-mask {
  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0.8) 0%,
    rgba(255, 255, 255, 0.6) 40.33%,
    rgba(232, 237, 245, 0.6) 63.79%,
    rgba(232, 237, 245, 0.6) 77.65%,
    rgba(232, 237, 245, 0.15) 88.97%
  );
}

.ellipse-1 {
  background-color: #8BB4F3;
  filter: blur(100px);
}

.ellipse-2 {
  background-color: rgba(0, 87, 255, 0.55);
  filter: blur(200px);
}

.ellipse-3 {
  background-color: #31D0E8;
  filter: blur(200px);
}

.ellipse-4 {
  background-color: rgba(0, 87, 255, 0.8);
  filter: blur(200px);
}

.time-item {
  transition: all 0.2s ease;
}
</style>

<route lang="json">
  {
    "layout": "tabbar",
    "name": "profile",
    "style": {
      "navigationStyle": "custom",
      "navigationBarTitleText": "我的"
    }
  }
</route>
