<script setup lang="ts">
import dayjs from 'dayjs'

const { checkAnyPermission, checkAllPermissions, checkPermission } = usePermissionChecker()

const { confirm: showModal } = useGlobalMessage()
const router = useRouter()
const toast = useToast('globalToast')
// 用户信息 - 从全局存储获取
const userStore = useUserStore()
const wmsStore = useWmsStore()
// 获取当前选择的物流中心信息
const currentWms = computed(() => wmsStore.currentWms)

// 功能列表
const features = computed(() => {
  const enableCreateVehSale = userStore.getOption('PS4_EnableCreateVehSale')
  return [
    {
      id: 1,
      name: '领货确认',
      icon: '/static/icon/ic_linghuoqueren.svg',
      route: 'pick-confirm-list',
      hasPermission: checkAnyPermission([PERMISSIONS.VEH_SALE_USE_SIGN_VIEW]),
    },
    {
      id: 2,
      name: '回货确认',
      icon: '/static/icon/ic_huihuoqueren.svg',
      route: 'back-confirm-list',
      hasPermission: checkAnyPermission([PERMISSIONS.VEH_SALE_USE_SIGN_ARV_VIEW]),
    },
    {
      id: 3,
      name: '领货',
      icon: '/static/icon/ic_linghuo.svg',
      route: 'warehouse-pick-edit',
      hasPermission: checkAllPermissions([PERMISSIONS.VEH_SALE_USE_SIGN_VIEW, PERMISSIONS.VEH_SALE_USE_SIGN_CREATE]),

    },
    {
      id: 4,
      name: '领货返回',
      icon: '/static/icon/ic_linghuofanhui.svg',
      route: 'return-pick-edit',
      hasPermission: checkAllPermissions([PERMISSIONS.VEH_SALE_USE_SIGN_BCK_VIEW, PERMISSIONS.VEH_SALE_USE_SIGN_BCK_CREATE]),

    },
    {
      id: 5,
      name: '车销开单',
      icon: '/static/icon/ic_chexiaodan.svg',
      route: 'warehouse-sales-edit',
      hasPermission: enableCreateVehSale === '1' && checkAllPermissions([PERMISSIONS.VEH_SALE_VIEW, PERMISSIONS.VEH_SALE_CREATE]),

    },
    {
      id: 6,
      name: '车销退单',
      icon: '/static/icon/ic_chexiaotuidan.svg',
      route: 'warehouse-sales-back-edit',
      hasPermission: enableCreateVehSale === '1' && checkAllPermissions([PERMISSIONS.VEH_SALE_BCK_VIEW, PERMISSIONS.VEH_SALE_BCK_CREATE]),

    },
    {
      id: 7,
      name: '差异处理',
      icon: '/static/icon/ic_chayichuli.svg',
      route: 'back-diff-list',
      hasPermission: enableCreateVehSale === '1' && checkAnyPermission([PERMISSIONS.VEH_SALE_USE_SIGN_ARV_DIFF_VIEW]),

    },
  ].filter(item => item.hasPermission)
})

// 导航到功能页面
function navigateToFeature(route: string) {
  router.push({
    name: route,
  })
}

const scrollTop = ref(0)

onPageScroll((event) => {
  scrollTop.value = event.scrollTop
})

const navbarStyle = computed(() => {
  const opacity = Math.min(scrollTop.value / 100, 1)
  return `background-color: rgba(255, 255, 255, ${opacity}); transition: background-color 0.3s;`
})

const { close: closeGlobalLoading } = useGlobalLoading()

// 获取当前配置的超过n天未处理的差异单提示天数
const vehSaleUseSignArvDiffDay = computed(() => Number(userStore.userInfo?.options?.find(option => option.optionCaption === 'PS4_VehSaleUseSignArvDiffDay')?.optionValue || 7))

function redirectToProcess() {
  router.push({
    name: 'back-diff-list',
    params: {
      beginDate: '1970-01-01',
      endDate: dayjs().subtract(vehSaleUseSignArvDiffDay.value, 'day').format('YYYY-MM-DD'), // dayjs().format('YYYY-MM-DD'),
    },
  })
}

// 跳转到回货差异列表页面
function goToProcess() {
  showModal({
    title: '提示',
    msg: `存在超过 ${vehSaleUseSignArvDiffDay.value || 7} 天未处理的差异单，请及时处理`,
    confirmButtonText: '去处理',
    cancelButtonText: '残忍拒绝',
    success(res) {
      if (res.action === 'confirm') {
        redirectToProcess()
      }
    },
    fail(res) {
      console.log('取消', res)
    },
  })
}

// 查询是否存在未处理的差异单
const {
  data: diffData,
  send: getDiffData,
} = useRequest(
  () => Apis.vehsaleusesignarvdiffInterface.hasDiffDataUsingPOST({
    data: {
      beginDate: '1970-01-01',
      finishDate: dayjs().subtract(vehSaleUseSignArvDiffDay.value, 'day').format('YYYY-MM-DD'), //  dayjs().format('YYYY-MM-DD'),
      wms: currentWms.value?.gid || 0,
    },
  }),
  {
    immediate: false,
  },
).onSuccess((event) => {
  closeGlobalLoading()
  const response = event.data
  // 检查是否有超过n天未处理的差异单
  if (response.data) {
    // 存在超过n天未处理的差异单
    goToProcess()
  }
}).onError((error) => {
  // 查询失败处理
  toast.error(error.error?.message || '获取差异单数据失败')
})

// 检查是否有查看差异单的权限
const hasDiffPermission = computed(() => {
  return checkPermission(PERMISSIONS.VEH_SALE_USE_SIGN_ARV_DIFF_VIEW)
})

const hasDiffData = computed(() => {
  return hasDiffPermission.value && diffData.value?.data
})

onLoad(() => {
  // 只有在有差异查看权限时才获取差异数据
  if (hasDiffPermission.value) {
    getDiffData()
  }
})
</script>

<template>
  <view class="warehouse-dashboard flex flex-col">
    <wd-navbar
      :bordered="false"
      safe-area-inset-top placeholder fixed
      title="工作台"
      :custom-style="navbarStyle"
    />

    <!-- 使用背景组件 -->
    <BackgroundGradient />

    <!-- 通知卡片 -->
    <view v-if="hasDiffData" class="mb-3 mt-2 box-border px-5">
      <view class="relative z-10 box-border border-[#fff] border-[0.25] rounded-3 rounded-bl-none border-solid bg-[rgba(229,237,255,0.5)] p-3">
        <view class="w-full flex items-center justify-between">
          <view class="flex items-center">
            <text class="text-3.25 text-[#2D2D2D] leading-[1.4em]">
              存在超过
              <text class="text-[var(--frequentapplication-primary-content)]">
                {{ vehSaleUseSignArvDiffDay }}天未处理
              </text>
              的差异单，请及时处理
            </text>
          </view>
          <view class="h-5 flex items-center justify-center rounded-1 bg-[#1C64FD] px-1 py-.5" @click="redirectToProcess()">
            <text class="px-1 text-center text-3 text-white leading-[1.5em]">
              去处理
            </text>
          </view>
        </view>
      </view>
    </view>

    <!-- 业务标题 -->
    <view class="relative z-1 mb-4 flex px-5">
      <text class="text-4 text-gray-800 font-medium">
        车销业务
      </text>
    </view>

    <!-- 功能网格 -->
    <view class="grid grid-cols-4 mb-3 px-5">
      <view
        v-for="feature in features"
        :key="feature.id"
        class="feature-item mb-2 flex flex-col items-center py-2"
        @click="navigateToFeature(feature.route)"
      >
        <view class="relative mb-3">
          <image
            :src="feature.icon"
            class="h-12 w-12"
            mode="aspectFit"
          />
        </view>
        <text class="text-3 text-gray-700">
          {{ feature.name }}
        </text>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.warehouse-dashboard {
  position: relative;
  z-index: 0;
  min-height: calc(100vh - 50px) !important;
  min-height: calc(100vh - 50px - constant(safe-area-inset-bottom)) !important;
  min-height: calc(100vh - 50px - env(safe-area-inset-bottom)) !important;

  .feature-item {
    z-index: 1;
  }

}
</style>

<route lang="json">
{
  "name": "warehouse-dashboard",
  "layout": "tabbar",
  "style": {
    "navigationBarTitleText": "工作台",
    "navigationStyle": "custom"
  }
}
</route>
