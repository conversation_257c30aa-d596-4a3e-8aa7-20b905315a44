<!--
 * @Description: 业务员商品搜索页面
-->
<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import HomeSkuCard from './cmp/HomeSkuCard.vue'
import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'

const globalToast = useGlobalToast()
const searchHistoryStore = useSearchHistoryStore()

// 搜索关键词
const searchKeyword = ref('')
// 是否显示搜索结果
const showResults = ref(false)

const { mescrollInit, getMescroll } = useMescroll(onPageScroll, onReachBottom)

// 使用 usePagination 实现分页查询
const {
  // 数据列表
  data: skuList,
  total,
  reload: reloadProducts,
  // 重新加载数据
  send: fetchProducts,
  loading: skuListLoading,
} = usePagination(
  (page, pageSize) => Apis.vehsalewrhinvInterface.queryGoodsUsingPOST_1({
    data: {
      page: page - 1, // 后端页码从0开始，usePagination从1开始
      pageSize,
      keyword: searchKeyword.value || undefined,
    },
  }),
  {
    immediate: false,
    append: true,
    initialData: [],
    total: response => response.total || 0,
    data: response => response.data || [],
  },
)

// 历史搜索记录
const searchHistory = computed(() => searchHistoryStore.getHistory)

// 下拉刷新回调
async function downCallback(mescroll: any) {
  try {
    await reloadProducts()
    mescroll.endSuccess()
  }
  catch (error) {
    console.error('下拉刷新失败:', error)
    mescroll.endErr()
  }
}

// 上拉加载更多 - 结合usePagination使用
async function upCallback(mescroll: any) {
  try {
    if (mescroll.num === 1) {
      await reloadProducts()
    }
    else {
      await fetchProducts({
        page: mescroll.num,
        pageSize: mescroll.size,
      })
    }
    mescroll.endBySize(skuList.value.length, total.value)
  }
  catch (error: any) {
    console.error('加载商品列表失败:', error)
    globalToast.error(error.error?.message || '加载商品列表失败')
    mescroll.endErr() // 请求失败，结束加载
  }
}

function handleRefresh() {
  const mescroll = getMescroll()
  if (mescroll && mescroll.resetUpScroll) {
    mescroll.resetUpScroll()
  }
}

// 处理搜索事件
function handleSearch() {
  const keyword = searchKeyword.value.trim()
  if (keyword) {
    // 添加到历史记录
    searchHistoryStore.addSearchKeyword(keyword)
    showResults.value = true
    handleRefresh()
  }
}

function handleClear() {
  showResults.value = false
}

// 点击历史记录标签
function handleHistoryClick(keyword: string) {
  searchKeyword.value = keyword
  handleSearch()
}

// 删除历史记录
function handleRemoveHistory(keyword: string) {
  searchHistoryStore.removeSearchKeyword(keyword)
}

// 清空历史记录
function handleClearHistory() {
  searchHistoryStore.clearHistory()
}

onLoad((options: any) => {
  // 初始化历史搜索记录
  searchHistoryStore.init()
  searchKeyword.value = options.keyword || ''
  if (searchKeyword.value) {
    handleSearch()
  }
})
</script>

<template>
  <view class="search-page relative box-border min-h-screen w-screen overflow-x-hidden pt-52px">
    <!-- 搜索栏 -->
    <view class="fixed top-0 z-10 box-border w-full bg-white">
      <SearchBar
        v-model="searchKeyword"
        :focus="true"
        placeholder="请输入商品信息"
        :show-filter="false"
        @search="handleSearch"
        @clear="handleClear"
      />
    </view>
    <!-- 主内容区域 -->
    <view class="main relative z-1 w-screen bg-[#F9F9F9]">
      <!-- 历史记录部分 -->
      <view v-if="!showResults && searchHistory.length > 0" class="bg-white p-3">
        <view class="mb-4 flex items-center justify-between">
          <text class="text-4 text-black font-medium">
            历史记录
          </text>
          <view class="cursor-pointer" @click="handleClearHistory">
            <wd-icon name="delete" size="16px" color="#97A0AA" />
          </view>
        </view>

        <!-- 历史记录标签 -->
        <view class="flex flex-wrap gap-2">
          <wd-tag
            v-for="(keyword, index) in searchHistory"
            :key="index"
            custom-class="px-4! py-1! text-[var(--textapplication-text-1)]!  text-28rpx! font-600!"
            @click="handleHistoryClick(keyword)"
            @close="handleRemoveHistory(keyword)"
          >
            {{ keyword }}
          </wd-tag>
        </view>
      </view>

      <!-- 搜索结果部分 -->
      <view v-if="showResults">
        <!-- 商品列表 - 使用mescroll实现上拉加载更多 -->
        <mescroll-body
          :down="downOption"
          height="100%"
          :up="upOption"
          @init="mescrollInit"
          @down="downCallback"
          @up="upCallback"
        >
          <view class="px-2.5 py-2">
            <!-- 加载状态 -->
            <SkuListSkeleton v-if="skuListLoading" />
            <!-- 商品列表 -->
            <HomeSkuCard
              v-for="(sku, index) in skuList"
              :key="sku.gid || index"
              :sku="sku"
            />
          </view>
        </mescroll-body>
      </view>

      <!-- 无历史记录且无搜索结果时的空状态 -->
      <view v-if="!showResults && searchHistory.length === 0" class="empty-state flex flex-col items-center justify-center py-20">
        <wd-icon name="search" size="64px" color="#E5E5E5" />
        <text class="mt-4 text-gray-400">
          搜索商品
        </text>
        <text class="mt-1 text-28rpx text-gray-400">
          输入商品信息进行搜索
        </text>
      </view>
    </view>

    <wd-gap height="0" safe-area-bottom />
  </view>
</template>

<style lang="scss" scoped>
.search-page {
  min-height: 100vh;
  background-color: #F9F9F9;

  :deep(.mescroll-body) {
    min-height: calc(100vh - 52px) !important;
    min-height: calc(100vh - 52px - constant(safe-area-inset-bottom)) !important;
    min-height: calc(100vh - 52px - env(safe-area-inset-bottom)) !important;
  }

}
</style>

<route lang="json">
{
  "name": "salesman-search",
  "style": {
    "navigationBarTitleText": "搜索商品",
    "backgroundColor": "#F9F9F9"
  }
}
</route>
