<!--
 * @Author: weish<PERSON>
 * @Date: 2025-06-13 10:05:46
 * @LastEditTime: 2025-07-01 16:42:36
 * @LastEditors: weisheng
 * @Description:
 * @FilePath: /lsym-cx-mini/src/pages/salesman/cmp/HomeSkuCard.vue
 * 记得注释
-->
<!--
 * @Description: 首页商品卡片组件
-->
<script setup lang="ts">
import { isDef } from 'wot-design-uni/components/common/util'
import type { VehSaleGoodsDTO } from '@/api/globals.d'

// 定义组件属性
const props = defineProps<{
  sku: VehSaleGoodsDTO
}>()

/**
 * 商品图片列表
 */
const skuImgList = computed(() => {
  const imgList = props.sku.imageDetails && props.sku.imageDetails.length > 0 ? props.sku.imageDetails.map(item => item.fileUrl) : []
  return imgList.filter(item => isDef(item))
})

/**
 * 商品主图
 */
const skuMainImg = computed(() => {
  return skuImgList.value.length > 0 ? skuImgList.value[0] : ''
})
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <view class="sku-item flex rounded-lg bg-white p-3">
    <!-- 商品图片 -->
    <SkuImage
      :src="skuMainImg"
      :preview-src="skuImgList"
      width="86px"
      height="86px"
      mode="aspectFill"
      custom-class="mr-3 flex-shrink-0"
    />
    <view class="flex flex-1 flex-col justify-between">
      <view>
        <view class="line-clamp-2 mb-1 break-all text-28rpx text-[#2D2D2D] font-medium">
          {{ sku.name }}
        </view>
        <view class="mb-1 flex flex-wrap items-center gap-1">
          <text class="break-all rounded bg-[#F5F6F7] px-1.5 py-0.5 text-3 text-[#5C5C5C]">
            {{ sku.code }}
          </text>
          <text class="break-all rounded bg-[#F5F6F7] px-1.5 py-0.5 text-3 text-[#5C5C5C]">
            {{ sku.qpcStr || '' }}
          </text>
        </view>
      </view>
      <view class="mt-auto flex items-end justify-between">
        <view class="flex items-baseline">
          <text class="text-26rpx text-[#F57F00]">
            ¥
          </text>
          <view class="flex items-baseline">
            <text class="mx-0.5 text-36rpx text-[#F57F00] font-bold">
              {{ formatPrice(sku.price || 0).integer }}
            </text>
            <text v-if="formatPrice(sku.price || 0).decimal" class="text-26rpx text-[#F57F00] font-medium">
              .{{ formatPrice(sku.price || 0).decimal }}
            </text>
          </view>
          <text class="ml-1 text-[12px] text-[#8A8A8A]">
            /{{ sku.munit || '箱' }}
          </text>
        </view>
        <view class="inventory-count">
          <text class="text-26rpx text-[#1C64FD] font-medium">
            {{ getQpcQty(sku.vehSaleWrhQtyStr!, sku.munit!, sku.minMunit!) }}
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.sku-item {
  &:not(:last-child) {
    margin-bottom: 24rpx;
  }
}
</style>
