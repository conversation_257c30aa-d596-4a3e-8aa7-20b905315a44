<!--
 * @Description: 首页数据统计卡片和领货回货卡片组件
-->
<script setup lang="ts">
import { createGlobalLoadingMiddleware } from '@/api/core/middleware'
import type { VehSaleWrhInvResponseDTO } from '@/api/globals'
import ReturnVerifyDialog, { type ReturnVerifyData } from '@/business/ReturnVerifyDialog.vue'

// 定义组件属性
defineProps<{
  statsData: VehSaleWrhInvResponseDTO
}>()

const router = useRouter()
const userStore = useUserStore()
const { error: showError, success: showSuccess } = useGlobalToast()
const globalLoading = useGlobalLoading()
const { confirm: showConfirm } = useGlobalMessage()
const { checkAccess: checkPickPermission } = usePermissionGuard(['vehSaleUseSignCreate', 'vehSaleUseSignView'], { tipType: 'message', messgae: '抱歉，您没有权限创建领货单，请联系管理员', checkAll: true })
const { checkAccess: checkBackPermission } = usePermissionGuard(['vehSaleUseSignArvCreate', 'vehSaleUseSignArvView'], { tipType: 'message', messgae: '抱歉，您没有权限创建回货单，请联系管理员', checkAll: true })

// 校验弹框控制
const showVerifyDialog = ref(false)

/**
 * 领货校验
 * 校验通过则跳转到领货页面
 * 校验失败则提示
 */
const { send: navigateToPick } = useRequest(Apis.vehsaleusesignInterface.verifyCreateUsingPOST_3({ params: {
  vehSaleEmpGid: userStore.vehSaleEmp?.gid || 0,
} }), {
  immediate: false,
  middleware: createGlobalLoadingMiddleware({
    loadingText: '校验中...',
    delay: 200,
  }),
}).onSuccess(() => {
  router.push({ name: 'pick-edit' })
}).onError((err) => {
  globalLoading.close()
  showError(err.error.message || '校验失败')
})

/**
 * 自动回货
 */
const { send: autoArv } = useRequest(Apis.vehsaleusesignarvInterface.autoArvUsingPOST({ }), {
  immediate: false,
  middleware: createGlobalLoadingMiddleware({
    loadingText: '自动回货中...',
    delay: 200,
  }),
}).onSuccess(() => {
  globalLoading.close()
  showSuccess('自动回货成功')
}).onError((err) => {
  globalLoading.close()
  showError(err.error.message || '自动回货失败')
})

/**
 * 回货前校验
 * 校验是否存在未签到的门店数据和已申请的车销领货单
 */
const { send: verifyBeforeReturn, data: verifyResult } = useRequest(Apis.vehsaleusesignarvInterface.verifyUsingPOST({}), {
  immediate: false,
  middleware: createGlobalLoadingMiddleware({
    loadingText: '校验中...',
    delay: 200,
  }),
}).onSuccess((resp) => {
  if (resp.data?.data?.storeGid || (resp.data?.data?.nums && resp.data.data.nums.length > 0)) {
    // 校验未通过，显示弹框
    showVerifyDialog.value = true
  }
  else {
    // 校验通过，继续执行原有的回货校验
    continueWithReturnCheck()
  }
}).onError((err) => {
  globalLoading.close()
  showError(err.error.message || '校验失败')
})

/**
 * 继续执行原有的回货校验逻辑
 */
function continueWithReturnCheck() {
  navigateToReturnCheck()
}

/**
 * 回货校验
 * 校验通过则跳转到回货页面
 * 校验失败则提示
 */
const { send: navigateToReturnCheck } = useRequest(Apis.vehsaleusesignarvInterface.hasArvGoodsUsingGET({ }), {
  immediate: false,
  middleware: createGlobalLoadingMiddleware({
    loadingText: '校验中...',
    delay: 200,
  }),
}).onSuccess((resp) => {
  if (resp.data && resp.data.data) {
    router.push({ name: 'back-edit' })
  }
  else {
    showConfirm({
      msg: '商品已售空，确认后系统将自动完成回货',
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      success: () => {
        autoArv()
      },
    })
  }
}).onError((err) => {
  globalLoading.close()
  showError(err.error.message || '校验失败')
})

/**
 * 回货校验入口
 * 首先进行回货前校验，再进行回货校验
 */
function navigateToReturn() {
  verifyBeforeReturn()
}

// 跳转到历史领货页面
function navigateToPickHistory() {
  router.push({
    name: 'pick-list',
    params: { status: 'audited' },
  })
}

// 跳转到历史回货页面
function navigateToReturnHistory() {
  router.push({
    name: 'back-list',
    params: { status: 'audited' },
  })
}

/**
 * 跳转到回货差异列表页面
 */
function navToBackDiff() {
  router.push({ name: 'back-diff-list' })
}

/**
 * 处理跳转到门店详情进行签退
 */
function handleGoToStoreDetail() {
  if (verifyResult.value?.data?.storeGid) {
    // 跳转到门店详情页面
    router.push({
      name: 'sales-store-detail',
      params: {
        id: String(verifyResult.value.data.storeGid!),
      },
    })
  }
  showVerifyDialog.value = false
}

/**
 * 处理跳转到车销领货单
 */
function handleGoToPickOrder() {
  if (verifyResult.value?.data?.nums && verifyResult.value.data.nums.length > 0) {
    // 跳转到领货单详情页面，取第一个单号
    router.push({
      name: 'pick-list',
    })
  }
  showVerifyDialog.value = false
}

/**
 * 关闭校验弹框
 */
function closeVerifyDialog() {
  showVerifyDialog.value = false
}
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <view class="home-cards-container overflow-hidden">
    <!-- 数据统计卡片 - 使用渐变背景 -->
    <view class="stats-cards relative flex items-center justify-between px-5 py-3.5">
      <!-- 背景渐变效果 -->
      <view class="bg-stat-card absolute inset-0" />

      <view class="stat-card z-10">
        <view class="stat-label flex items-center">
          <text class="text-3 text-[#636D78]">
            货值
          </text>
        </view>
        <text class="text-5 text-[#343A40] font-bold">
          ¥ {{ statsData.total || '0.00' }}
        </text>
      </view>

      <view class="stat-card z-10">
        <view class="stat-label flex items-center">
          <text class="text-3 text-[#636D78]">
            品项
          </text>
        </view>
        <text class="text-5 text-[#343A40] font-bold">
          {{ statsData.skuCount || '0' }}
        </text>
      </view>

      <view class="stat-card z-10">
        <view class="stat-label flex items-center">
          <text class="mr-1 text-3 text-[#636D78]">
            不可用
          </text>
          <wd-tooltip placement="bottom-end" content="历史回货存在差异，且差异还未处理完成的商品总额">
            <view class="flex items-center">
              <view class="i-carbon-information text-3 text-[#B2B2B2]" />
            </view>
          </wd-tooltip>
        </view>
        <text class="text-5 text-[#343A40] font-bold" @click="navToBackDiff">
          ¥ {{ statsData.unusableTotal || '0.00' }}
        </text>
      </view>
    </view>

    <!-- 领货回货卡片 -->
    <view class="action-cards px-4 pb-2">
      <view class="flex">
        <!-- 领货卡片 -->
        <view class="action-card pickup-card mr-2 flex-1 border border-white border-opacity-60 rounded-lg border-solid p-3 shadow-sm">
          <view class="flex items-center justify-between">
            <view class="icon-box h-6 w-6 flex items-center justify-center">
              <image
                src="@/static/icon/ic_pick.svg"
                mode="scaleToFill"
                class="h-6 w-6"
              />
            </view>

            <view class="flex cursor-pointer items-center" @click="checkPickPermission() && navigateToPick()">
              <text class="text-4 text-[#2D2D2D] font-medium">
                去领货
              </text>
              <view class="i-carbon-chevron-right ml-1 text-[#2D2D2D]" />
            </view>
          </view>

          <view class="mt-2">
            <text class="text-3 text-[#5C5C5C]">
              上次领货信息
            </text>
          </view>

          <view v-if="statsData.vehSaleUseSignTotal || statsData.lastVehSaleUseSignDate" class="mt-1 flex items-center justify-between">
            <text class="break-all text-3 text-[#2D2D2D] font-medium">
              ¥ {{ statsData.vehSaleUseSignTotal || '0.00' }}
            </text>
            <text class="text-3 text-[#5C5C5C]">
              {{ formatDate(statsData.lastVehSaleUseSignDate) }}
            </text>
          </view>
          <view v-else class="mt-1 flex items-center justify-between">
            <text class="text-3 text-[#2D2D2D] font-medium">
              暂无领货信息
            </text>
          </view>

          <view class="history-btn mt-2 inline-flex items-center rounded-md bg-[#E5EDFF] px-2 py-1" @click="navigateToPickHistory">
            <text class="text-3 text-[#1C64FD] font-medium">
              历史领货
            </text>
          </view>
        </view>

        <!-- 回货卡片 -->
        <view class="action-card return-card flex-1 border border-white border-opacity-60 rounded-lg border-solid p-3 shadow-sm">
          <view class="flex items-center justify-between">
            <view class="icon-box h-6 w-6 flex items-center justify-center">
              <image
                src="@/static/icon/ic_back.svg"
                mode="scaleToFill"
                class="h-6 w-6"
              />
            </view>

            <view class="flex cursor-pointer items-center" @click="checkBackPermission() && navigateToReturn()">
              <text class="text-4 text-white font-medium">
                去回货
              </text>
              <view class="i-carbon-chevron-right ml-1 text-white" />
            </view>
          </view>

          <view class="mt-2">
            <text class="text-3 text-white text-opacity-60">
              上次回货信息
            </text>
          </view>

          <view v-if="statsData.vehSaleUseSignArvTotal || statsData.lastVehSaleUseSignArvDate" class="mt-1 flex items-center justify-between">
            <text class="break-all text-3 text-white font-medium">
              ¥ {{ statsData.vehSaleUseSignArvTotal || '0.00' }}
            </text>
            <text class="text-3 text-white text-opacity-60">
              {{ formatDate(statsData.lastVehSaleUseSignArvDate) }}
            </text>
          </view>
          <view v-else class="mt-1 flex items-center justify-between">
            <text class="text-3 text-white font-medium">
              暂无回货信息
            </text>
          </view>

          <view class="history-btn mt-2 inline-flex items-center rounded-md bg-[#7CA4FF] px-2 py-1" @click="navigateToReturnHistory">
            <text class="text-3 text-white font-medium">
              历史回货
            </text>
          </view>
        </view>
      </view>
    </view>

    <!-- 回货前校验弹框 -->
    <ReturnVerifyDialog
      v-model="showVerifyDialog"
      :verify-data="verifyResult?.data || {}"
      @go-to-store="handleGoToStoreDetail"
      @go-to-order="handleGoToPickOrder"
      @closed="closeVerifyDialog"
    />
  </view>
</template>

<style lang="scss" scoped>
.action-card {
  overflow: hidden;
}

.bg-stat-card {
  background: linear-gradient(180deg, rgba(240, 246, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 100%);
}

.pickup-card {
  background: linear-gradient(180deg, #DBECFF 0%, #FFFFFF 100%);
}

.return-card {
  background: linear-gradient(180deg, #7CA4FF 0%, #4480FF 100%);
}

/* 确保组件不会导致高度异常 */
.stats-cards {
  min-height: auto;
  height: auto;
}

.action-cards {
  min-height: auto;
  height: auto;
}

/* 修复可能的flex布局问题 */
.action-card {
  flex-shrink: 0;
  min-height: 0;
}

/* 根容器样式 */
.home-cards-container {
}
</style>
