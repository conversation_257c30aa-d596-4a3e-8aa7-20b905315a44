<!--
 * @Description: 首页骨架屏组件
-->
<script setup lang="ts">
// 统计卡片骨架屏配置
const statsSkeletonConfig = [
  [{ width: '30%', height: '16px' }, { width: '30%', height: '16px' }, { width: '30%', height: '16px' }],
  [{ width: '30%', height: '24px' }, { width: '30%', height: '24px' }, { width: '30%', height: '24px' }],
]

// 领货回货卡片骨架屏配置
const actionCardsSkeletonConfig = [
  [{ width: '48%', height: '120px' }, { width: '48%', height: '120px' }],
]

// 商品列表骨架屏配置
const productSkeletonConfig = [
  [{ width: '86px', height: '86px', type: 'rect' }, { width: '65%', marginLeft: '12px' }],
  [{ width: '86px', height: '86px', type: 'rect' }, { width: '65%', marginLeft: '12px' }],
  [{ width: '86px', height: '86px', type: 'rect' }, { width: '65%', marginLeft: '12px' }],
]
</script>

<template>
  <view class="home-skeleton h-screen overflow-hidden">
    <!-- 数据统计卡片骨架屏 -->
    <view class="stats-cards px-5 py-3">
      <wd-skeleton :row-col="statsSkeletonConfig" animation="flashed" />
    </view>

    <!-- 领货回货卡片骨架屏 -->
    <view class="action-cards px-4 pb-2">
      <wd-skeleton :row-col="actionCardsSkeletonConfig" animation="flashed" />
    </view>

    <!-- 商品清单头部骨架屏 -->
    <view class="header mx-2.5 rounded-lg bg-white p-3">
      <wd-skeleton :row-col="[{ width: '30%', height: '20px' }, { width: '40%', height: '16px' }]" animation="flashed" />
    </view>

    <!-- 标签页骨架屏 -->
    <view class="tabs-container mx-2.5">
      <wd-skeleton :row-col="[{ height: '40px' }]" animation="flashed" />
    </view>

    <!-- 商品列表骨架屏 -->
    <view class="product-list mt-3 px-2.5">
      <wd-skeleton :row-col="productSkeletonConfig" animation="flashed" />
    </view>
  </view>
</template>

<style lang="scss" scoped>
/* 骨架屏样式 */
</style>
