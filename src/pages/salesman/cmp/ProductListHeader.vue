<!--
 * @Description: 商品清单头部组件 - 支持互斥排序
 * @FilePath: /lsym-cx-mini/src/pages/salesman/cmp/ProductListHeader.vue
-->
<script setup lang="ts">
import type { SortItem } from '@/composables/useDraft'

interface SortButton {
  title: string
  field: string
  asc: 0 | 1 | -1
}

const props = defineProps({
  // 标题
  title: {
    type: String,
    default: '商品清单',
  },
  // 排序按钮配置
  sortButtons: {
    type: Array as PropType<SortButton[]>,
    default: () => [
      { title: '名称', field: 'name', asc: 0 },
      { title: '数量', field: 'vehSaleWrhQty', asc: 0 },
    ],
  },
})

const emits = defineEmits<{
  (e: 'update:sortButtons', value: SortButton[]): void
  (e: 'sort-change', value: SortItem[]): void
}>()

const sortButtonsList = ref<SortButton[]>([...props.sortButtons].map(button => ({
  ...button,
  asc: button.asc ? -button.asc as 0 | 1 | -1 : 0,
})))

// 深度比较函数
function isEqual(a: any, b: any): boolean {
  return JSON.stringify(a) === JSON.stringify(b)
}

// 监听 props 变化
watch(() => props.sortButtons, (newVal) => {
  const oldVal = sortButtonsList.value.map(button => ({
    ...button,
    asc: button.asc ? -button.asc as 0 | 1 | -1 : 0,
  }))
  if (!isEqual(oldVal, newVal)) {
    sortButtonsList.value = newVal.map(button => ({
      ...button,
      asc: button.asc ? -button.asc as 0 | 1 | -1 : 0,
    }))
  }
}, { deep: true })

// 使用防抖来避免频繁更新
let updateTimer: ReturnType<typeof setTimeout> | null = null

watch(sortButtonsList, (newVal, oldVal) => {
  // 清除之前的定时器
  if (updateTimer) {
    clearTimeout(updateTimer)
  }

  // 检查是否真的发生了变化
  if (isEqual(newVal, oldVal)) {
    return
  }

  // 使用防抖延迟执行
  updateTimer = setTimeout(() => {
    emits('update:sortButtons', newVal.map(button => ({
      ...button,
      asc: button.asc ? -button.asc as 0 | 1 | -1 : 0,
    })))

    // 发出排序变化事件
    const sortItems: SortItem[] = newVal
      .filter(button => button.asc !== 0)
      .map(button => ({
        field: button.field,
        name: button.title,
        asc: button.asc ? -button.asc as 0 | 1 | -1 : 0,
      }))

    emits('sort-change', sortItems)
  }, 0)
}, { deep: true, flush: 'post' })

// 处理排序按钮变化 - 实现互斥排序逻辑
function handleSortChange(index: number, asc: 0 | 1 | -1) {
  const newSortButtons = [...sortButtonsList.value]

  // 如果当前排序不是重置操作，则重置其他所有排序条件
  if (asc !== 0) {
    newSortButtons.forEach((button, idx) => {
      if (idx !== index) {
        button.asc = 0
      }
    })
  }

  // 设置当前排序条件
  newSortButtons[index] = { ...newSortButtons[index], asc }
  sortButtonsList.value = newSortButtons
}
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <view class="header mx-2.5 rounded-lg bg-white px-3">
    <view class="flex items-center justify-between">
      <text class="text-15px text-black font-500">
        {{ title }}
      </text>
      <view class="flex">
        <view
          v-for="(sortButton, index) in sortButtonsList"
          :key="sortButton.field"
          :class="[index === sortButtonsList.length - 1 ? '' : 'mr-4']"
        >
          <wd-sort-button
            :model-value="sortButton.asc"
            :title="sortButton.title"
            allow-reset
            :line="false"
            @update:model-value="(asc) => handleSortChange(index, asc)"
          />
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>

</style>
