<script setup lang="ts">
import { useQueue } from 'wot-design-uni'
import HomeSkeleton from './cmp/HomeSkeleton.vue'
import HomeSkuCard from './cmp/HomeSkuCard.vue'
import HomeCards from './cmp/HomeCards.vue'
import ProductListHeader from './cmp/ProductListHeader.vue'
import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
import type { QuerySort } from '@/api/globals'
import type { SortItem } from '@/composables/useDraft'

const router = useRouter()

const globalToast = useGlobalToast()
const { closeOutside } = useQueue()
// 页面初始加载状态 - 只在首次加载时显示
const initialLoading = ref(true)

// 使用 useRequest 加载顶部统计数据
const { data: stats, send: getStats } = useRequest(Apis.vehsalewrhinvInterface.getTemplatesUsingPOST())

// 监听刷新数据事件
useWatchRefreshData({
  callback: () => {
    getStats()
  },
  name: 'salesman-home',
})

const statsData = computed(() => {
  return stats.value?.data || {}
})

// 当前活跃标签
const activeTab = ref('')

// 分类列表
const categories = ref([{ code: '', name: '全部' }])

// 排序按钮配置
const sortButtons = ref([
  { title: '名称', field: 'name', asc: 0 as 0 | 1 | -1 },
  { title: '数量', field: 'vehSaleWrhQty', asc: 0 as 0 | 1 | -1 },
])

const sorts = computed<QuerySort[]>(() => {
  const sorts: QuerySort[] = []
  sortButtons.value.forEach((button) => {
    if (button.asc !== 0) {
      sorts.push({
        field: button.field,
        asc: button.asc === 1,
      })
    }
  })
  return sorts
})

const { mescrollInit, getMescroll, downCallback } = useMescroll(onPageScroll, onReachBottom)

// 使用 usePagination 实现分页查询
const {
  // 数据列表
  data: skuList,
  reload: reloadProducts,
  page,
  total,
} = usePagination(
  page => Apis.vehsalewrhinvInterface.queryGoodsUsingPOST_1({
    data: {
      page: page - 1, // 后端页码从0开始，usePagination从1开始
      pageSize: 10,
      sortCode: activeTab.value || undefined,
      sorts: sorts.value,
    },
  }),
  {
    immediate: false,
    append: true,
    initialData: [],
    total: response => response.total || 0,
    data: response => response.data || [],
  },
).onSuccess((resp) => {
  getMescroll().endBySize(resp.data.data?.length || 0, total.value)
}).onError((error) => {
  globalToast.error(error.error?.message || '加载商品列表失败')
  getMescroll().endErr()
})

// 上拉加载更多 - 结合usePagination使用
async function upCallback(mescroll: any) {
  try {
    if (mescroll.num === 1) {
      await reloadProducts()
    }
    else {
      page.value = mescroll.num
    }
  }
  catch (error: any) {
    console.error('加载商品列表失败:', error)
    mescroll.endErr() // 请求失败，结束加载
  }
}

// 加载分类
async function loadCategories() {
  try {
    const res = await Apis.mdataInterface.queryCategoryUsingPOST({
      data: {
      },
    }).send()
    if (res.code === 2000 && res.data) {
      categories.value = [{ code: '', name: '全部' }, ...res.data.map(item => ({ code: item.code, name: item.name }))]
    }
  }
  catch {}
}

// tab切换时更新当前活跃标签
function handleTabChange(code: string) {
  activeTab.value = code
  // 标签页内部的TabContent组件会监听active属性的变化并自行处理数据加载
}

// 搜索关键词
const searchKeyword = ref('')

// 处理排序变化
function handleSortChange(_sortItems: SortItem[]) {
  // 根据排序变化重新加载数据
  if (getMescroll()) {
    getMescroll().resetUpScroll()
  }
}

function handleSearchClick() {
  router.push({
    name: 'salesman-search',
  })
}
function handleScan(code: string) {
  router.push({
    name: 'salesman-search',
    params: {
      keyword: code,
    },
  })
}

// 页面加载时获取数据
onMounted(() => {
  // 加载分类
  loadCategories().then(() => {
    activeTab.value = categories.value[0]?.code || ''
    // 初始化完成后，关闭初始加载状态
    setTimeout(() => {
      initialLoading.value = false
    }, 500)
  })
})

onShow(() => {
  getStats()
})

const scrollTop = ref(0)

onPageScroll((event) => {
  scrollTop.value = event.scrollTop
})

const navbarStyle = computed(() => {
  const opacity = Math.min(scrollTop.value / 100, 1)
  return `background-color: rgba(255, 255, 255, ${opacity}); transition: background-color 0.3s;`
})
</script>

<template>
  <view class="salesman-home relative box-border w-screen flex flex-col bg-[#F9F9F9]" @click="closeOutside">
    <!-- 搜索栏 -->
    <nav-search-bar
      v-model="searchKeyword"
      :custom-style="navbarStyle"
      readonly
      placeholder="请输入商品信息"
      @click="handleSearchClick"
      @scan="handleScan"
    />

    <!-- 骨架屏 - 只在初始加载时显示 -->
    <HomeSkeleton v-if="initialLoading" />

    <!-- 实际内容 -->
    <block v-else>
      <!-- 数据统计卡片和领货回货卡片 -->
      <HomeCards :stats-data="statsData" />

      <!-- 商品清单头部 -->
      <ProductListHeader
        v-model:sort-buttons="sortButtons"
        title="商品清单"
        @sort-change="handleSortChange"
      />

      <!-- 标签页切换 -->
      <view class="tabs-container relative box-border w-screen flex items-center px-2.5">
        <wd-tabs v-model="activeTab" custom-class="w-702rpx" @change="handleTabChange">
          <wd-tab v-for="cat in categories" :key="cat.code" lazy :title="cat.name" :name="cat.code">
            <!-- 商品列表 - 使用mescroll实现上拉加载更多 -->
            <mescroll-body
              v-if="activeTab === cat.code"
              :down="downOption"
              height="100%"
              :up="upOption"
              @init="mescrollInit"
              @down="downCallback"
              @up="upCallback"
            >
              <view class="product-list px-2.5">
                <HomeSkuCard v-for="(sku, index) in skuList" :key="sku.gid || index" :sku="sku" />
              </view>
            </mescroll-body>
          </wd-tab>
        </wd-tabs>
      </view>
    </block>
  </view>
</template>

<style lang="scss" scoped>
.salesman-home {
  height: auto;

  :deep(){
    .wd-navbar__left{
      display: none;
    }
    .wd-navbar__title{
      margin: 0;
      max-width: 100%;
    }

    &::-webkit-scrollbar {
      display: none; /* Chrome, Safari, Opera */
    }
    .wd-tabs__map-nav-btn{
      width: 206rpx !important;
    }

    .mescroll-body{
      // 减去搜索栏、数据统计卡片、领货回货卡片、商品清单头部、tabbar的高度
      min-height: calc(100vh - var(--navbar-total-height) - 440rpx - 96rpx - 50px) !important;
      min-height: calc(100vh - var(--navbar-total-height) - 440rpx - 96rpx - 50px - constant(safe-area-inset-bottom)) !important;
      min-height: calc(100vh - var(--navbar-total-height) - 440rpx - 96rpx - 50px - env(safe-area-inset-bottom)) !important;
    }
  }

}
</style>

<route lang="json">
  {
    "layout": "tabbar",
    "name": "salesman-home",
    "style": {
      "navigationBarTitleText": "库存",
      "navigationStyle": "custom"
    }
  }
</route>
