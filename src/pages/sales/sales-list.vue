<!--
 * @Author: claude
 * @Date: 2025-04-19 16:55:00
 * @LastEditTime: 2025-06-24 10:38:42
 * @Description: 历史车销单页面
 * @FilePath: /lsym-cx-mini/src/pages/sales/sales-list.vue
-->
<script setup lang="ts">
import { isArray } from 'wot-design-uni/components/common/util'
import SalesOrderCard from './cmp/SalesOrderCard.vue'
import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
import { useGlobalToast } from '@/composables/useGlobalToast'
import TimeFilter, { type TimeFilterType } from '@/components/TimeFilter.vue'
import { useVehSaleEmpSelect } from '@/composables/useVehSaleEmpSelect'
import type { VehSaleBckQueryResponseDTO, VehSaleQueryFilter } from '@/api/globals'

const router = useRouter()
const userStore = useUserStore()
const { currentWms } = useWmsStore()
const { checkPermission, checkAllPermissions, checkAnyPermission } = usePermissionChecker()

const hasAllPermissions = computed(() => {
  return checkAllPermissions(['vehSaleView', 'vehSaleBckView'])
})

const hasAnyPermission = computed(() => {
  return checkAnyPermission(['vehSaleView', 'vehSaleBckView'])
})

// 角色判断
const isSalesman = computed(() => userStore.userRole === 'salesman')
const isWarehouseManager = computed(() => userStore.userRole === 'warehouse')

// 全局提示
const globalToast = useGlobalToast()
const timeFilterRef = ref<InstanceType<typeof TimeFilter>>()

// 门店选择（业务员端使用）
const { storeList, storeLoading, handleStoreSelect, storeGid, currentStore } = useStoreSelect({
  vehSaleEmpGid: isSalesman.value ? userStore.vehSaleEmp?.gid : undefined,
  wmsGid: currentWms?.gid,
})

// 业务员选择（仓管端使用）
const { empList, empLoading, handleEmpSelect, empId, currentEmp } = useVehSaleEmpSelect(currentWms?.gid)

// 状态筛选相关
const statusList = [
  { label: '全部状态', value: '300,310' },
  { label: '已完成', value: '300' },
  { label: '已作废', value: '310' },
]

const selectedStatus = ref('300,310')
const currentStatus = computed(() => {
  return statusList.find(item => item.value === selectedStatus.value) || statusList[0]
})

// 处理状态选择
function handleSelectStatus(event: { value: string, selectedItems: { label: string, value: string } }) {
  selectedStatus.value = event.value
  handleRefresh()
}

// 监听刷新数据事件
useWatchRefreshData({
  callback: () => {
    handleRefresh()
  },
  name: 'sales-list',
})

function handleSelectStore(event: { value: string, selectedItems: { label: string, value: string } }) {
  handleStoreSelect(event)
  handleRefresh()
}

// 处理业务员选择（仓管端使用）
function handleSelectEmp(event: { value: string, selectedItems: { label: string, value: string, code: string, name: string } }) {
  handleEmpSelect(event)
  handleRefresh()
}

// 当前选中的Tab
const currentTab = ref<'sales' | 'return'>('sales')

// 查询参数
const queryParams = reactive<Omit<VehSaleQueryFilter, 'page' | 'pageSize' | 'stat'>>({
  keyword: '',
  beginDate: '',
  finishDate: '',
  sorts: [
    {
      field: 'lstupdTime',
      asc: false,
    },
  ],
})

const { mescrollInit, getMescroll, downCallback } = useMescroll(onPageScroll, onReachBottom)

const {
  // 数据列表
  data: orderList,
  reload: reloadOrders,
  total,
  page,
} = usePagination(
  (page, pageSize) => {
    // 构建状态筛选参数
    const stats = selectedStatus.value ? selectedStatus.value.split(',').map(s => Number(s)) : undefined

    // 根据角色设置业务员参数
    let vehSaleEmpGid: number | undefined
    if (isSalesman.value) {
      // 业务员角色：使用当前登录业务员的GID
      vehSaleEmpGid = userStore.vehSaleEmp?.gid
    }
    else if (isWarehouseManager.value) {
      // 仓管角色：使用选择的业务员GID（如果有选择）
      if (empId.value) {
        vehSaleEmpGid = Number(empId.value)
      }
    }

    // 根据当前选中的Tab决定调用不同的接口
    if (currentTab.value === 'return') {
      // 车销退接口
      return Apis.vehsalebckInterface.queryUsingPOST({
        data: {
          ...queryParams,
          keyword: queryParams.keyword || undefined,
          beginDate: queryParams.beginDate || undefined,
          finishDate: queryParams.finishDate || undefined,
          storeGid: storeGid.value ? Number(storeGid.value) : undefined,
          vehSaleEmpGid,
          wmsGid: isWarehouseManager.value ? currentWms?.gid : undefined,
          stats,
          page: page - 1, // 后端页码从0开始，usePagination从1开始
          pageSize,
        },
      })
    }
    else {
      // 车销开接口
      return Apis.vehsaleInterface.queryUsingPOST_1({
        data: {
          ...queryParams,
          keyword: queryParams.keyword || undefined,
          beginDate: queryParams.beginDate || undefined,
          finishDate: queryParams.finishDate || undefined,
          storeGid: storeGid.value ? Number(storeGid.value) : undefined,
          vehSaleEmpGid,
          stats,
          wmsGid: isWarehouseManager.value ? currentWms?.gid : undefined,
          page: page - 1, // 后端页码从0开始，usePagination从1开始
          pageSize,
        },
      })
    }
  }
  ,
  {
    immediate: false,
    append: true,
    initialData: [],
    initialPageSize: 10,
    initialPage: 1,
    total: response => response.total,
    data: response => (response.data || []),
  },
).onSuccess((resp) => {
  getMescroll().endBySize(resp.data.data?.length || 0, total.value)
}).onError((error) => {
  globalToast.error(error.error?.message || '加载车销单列表失败')
  getMescroll().endErr()
})

// 上拉加载更多 - 结合usePagination使用
async function upCallback(mescroll: any) {
  if (mescroll.num === 1) {
    await reloadOrders()
  }
  else {
    page.value = mescroll.num
  }
}

function handleRefresh() {
  // 初始加载数据
  if (getMescroll()) {
    getMescroll().resetUpScroll()
  }
}

// 是否初始化
const inited = ref(false)

const defaultType = ref<TimeFilterType >()

// 处理路由参数
onLoad((options: any) => {
  if (options.id) {
    storeGid.value = Number(options.id)
  }
  // 如果有时间范围参数，设置查询参数
  if (options.beginDate) {
    queryParams.beginDate = options.beginDate as string
  }

  if (options.endDate) {
    queryParams.finishDate = options.endDate as string

    if (options.beginDate && options.endDate && options.beginDate === options.endDate) {
      defaultType.value = '今日'
    }
  }

  let type: 'sales' | 'return' | undefined = void 0
  if (!hasAllPermissions.value && hasAnyPermission.value) {
    type = checkPermission('vehSaleView') ? 'sales' : 'return'
  }

  currentTab.value = type || options.type || 'sales'
  inited.value = true
})

// 切换Tab
function changeTab(event: { index: number, name: 'sales' | 'return' }) {
  currentTab.value = event.name
  handleRefresh()
}

/**
 * 筛选时间的回调
 */
function handleTimeFilterConfirm(payload: { range: string | [string, string] }) {
  if (isArray(payload.range)) {
    queryParams.beginDate = payload.range[0]
    queryParams.finishDate = payload.range[1]
  }
  else {
    queryParams.beginDate = payload.range
    queryParams.finishDate = payload.range
  }
  handleRefresh()
}

// 查看订单详情
function viewOrderDetail(order: VehSaleBckQueryResponseDTO) {
  // 确保 order.id 存在
  if (!order.num) {
    globalToast.error('订单ID不存在')
    return
  }

  // 根据当前选中的Tab决定跳转到不同的详情页面
  if (currentTab.value === 'return') {
    router.push({
      name: 'sales-back-detail',
      params: {
        id: order.num,
      },
    })
  }
  else {
    router.push({
      name: 'sales-detail',
      params: {
        id: order.num,
      },
    })
  }
}
</script>

<template>
  <view class="sales-list box-border min-h-screen bg-[#F9F9F9]" style="--wot-search-input-height:36px">
    <!-- 搜索和筛选区域 - 固定在顶部 -->
    <view class="fixed-top-section">
      <SearchBar
        v-model="queryParams.keyword"
        placeholder="单号/门店/商品"
        :has-filter="!!queryParams.beginDate || !!queryParams.finishDate"
        @filter="timeFilterRef?.open()"
        @clear="handleRefresh"
        @search="handleRefresh"
      />

      <!-- 类型选择Tab -->
      <wd-tabs v-model="currentTab" auto-line-width @change="changeTab">
        <wd-tab title="车销" name="sales" :disabled="!checkPermission('vehSaleView')" />
        <wd-tab title="车销退" name="return" :disabled="!checkPermission('vehSaleBckView')" />
      </wd-tabs>

      <!-- 筛选区域 -->
      <view class="h-88rpx flex items-center px-3">
        <!-- 门店筛选 - 业务员端显示 -->
        <wd-select-picker
          v-if="inited && isSalesman"
          v-model="storeGid"
          :columns="storeList"
          :loading="storeLoading"
          title="选择门店"
          label="门店"
          type="radio"
          use-default-slot filterable hide-label
          @confirm="handleSelectStore"
        >
          <template #default>
            <view
              class="flex items-center rounded bg-white px-3 py-1"
            >
              <text class="max-w-32 truncate text-28rpx text-[#2D2D2D] font-500">
                {{ currentStore ? currentStore.label : '全部门店' }}
              </text>
              <text class="i-carbon-caret-down text-4 text-[var(--textapplication-text-3)]" />
            </view>
          </template>
        </wd-select-picker>

        <!-- 业务员筛选 - 仓管端显示 -->
        <wd-select-picker
          v-if="inited && isWarehouseManager"
          v-model="empId"
          :columns="empList"
          :loading="empLoading"
          title="选择业务员"
          label="业务员"
          type="radio"
          use-default-slot filterable hide-label
          @confirm="handleSelectEmp"
        >
          <template #default>
            <view
              class="flex items-center rounded bg-white px-3 py-1"
            >
              <text class="max-w-32 truncate text-28rpx text-[#2D2D2D] font-500">
                {{ currentEmp ? currentEmp.label : '全部业务员' }}
              </text>
              <text class="i-carbon-caret-down text-4 text-[var(--textapplication-text-3)]" />
            </view>
          </template>
        </wd-select-picker>

        <!-- 状态筛选 - 业务员端显示 -->
        <wd-select-picker
          v-if="inited"
          v-model="selectedStatus"
          custom-class="ml-3!"
          :columns="statusList"
          title="选择状态"
          label="状态"
          type="radio"
          use-default-slot hide-label
          @confirm="handleSelectStatus"
        >
          <template #default>
            <view
              class="flex items-center rounded bg-white px-3 py-1"
            >
              <text class="max-w-32 truncate text-28rpx text-[#2D2D2D] font-500">
                {{ currentStatus.label }}
              </text>
              <text class="i-carbon-caret-down text-4 text-[var(--textapplication-text-3)]" />
            </view>
          </template>
        </wd-select-picker>
      </view>
    </view>

    <!-- 历史车销列表 -->
    <mescroll-body
      v-if="inited"
      :down="downOption" :up="upOption" height="100%" safearea @init="mescrollInit"
      @down="downCallback" @up="upCallback"
    >
      <view class="mx-3">
        <SalesOrderCard
          v-for="order in orderList"
          :key="order.num"
          :order="order"
          @click="viewOrderDetail(order)"
        />
      </view>
    </mescroll-body>

    <!-- 筛选弹窗 -->
    <TimeFilter v-if="inited" ref="timeFilterRef" :default-type="defaultType" @confirm="handleTimeFilterConfirm" />
  </view>
</template>

<style lang="scss" scoped>
.sales-list {
  padding-top: calc(52px + 42px + 88rpx);

  :deep(.mescroll-body) {
    min-height: calc(100vh - 52px - 42px - 88rpx) !important;
    min-height: calc(100vh - 52px - 42px - 88rpx - constant(safe-area-inset-bottom)) !important;
    min-height: calc(100vh - 52px - 42px - 88rpx - env(safe-area-inset-bottom)) !important;
  }
}

/* 固定在顶部的搜索栏和筛选条件 */
.fixed-top-section {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 10;
  background-color: #F9F9F9;
}
</style>

<route lang="json">
{
  "name": "sales-list",
  "style": {
    "navigationBarTitleText": "历史车销单"
  },
  "meta": {
    "permissions": ["vehSaleView", "vehSaleBckView"]
  }
}
</route>
