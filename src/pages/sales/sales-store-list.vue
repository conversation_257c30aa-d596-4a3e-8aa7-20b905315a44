<script setup lang="ts">
import { ref } from 'vue'
import SalesStoreCard from './cmp/SalesStoreCard.vue'
import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
import type { StoreDTO } from '@/api/globals'
// 全局提示
const globalToast = useGlobalToast()
const router = useRouter()
const userStore = useUserStore()

// 监听刷新数据事件
useWatchRefreshData({
  callback: () => {
    handleRefresh()
  },
  name: 'sales-store-list',
})

// 搜索关键字
const searchKeyword = ref('')

const { sortLineList, sortLineLoading, handleSortLineSelect, getSortLine, sortLineId, currentSortLine } = useSortLineSelect(userStore.vehSaleEmp?.gid)
const { mescrollInit, getMescroll, downCallback } = useMescroll(onPageScroll, onReachBottom)
const {
  // 数据列表
  data: storeList,
  reload: reloadStore,
  total,
  page,
} = usePagination(
  (page, pageSize) => Apis.mdataInterface.queryStoreUsingPOST({
    data: {
      sortLineUuid: sortLineId.value || undefined,
      page: page - 1, // 后端页码从0开始，usePagination从1开始
      pageSize,
      keyword: searchKeyword.value || undefined,
      vehSaleEmpGid: userStore.vehSaleEmp?.gid,
    },
  }),
  {
    immediate: false,
    append: true,
    initialData: [],
    initialPageSize: 10,
    initialPage: 1,
    total: response => response.total,
    data: response => (response.data || []),
  },
).onSuccess((resp) => {
  getMescroll().endBySize(resp.data.data?.length || 0, total.value)
}).onError((error) => {
  globalToast.error(error.error?.message || '加载门店列表失败')
  getMescroll().endErr()
})

// 上拉加载更多 - 结合usePagination使用
async function upCallback(mescroll: any) {
  if (mescroll.num === 1) {
    await reloadStore()
  }
  else {
    page.value = mescroll.num
  }
}

// 跳转到门店详情
function goToStoreDetail(store: StoreDTO) {
  router.push({
    name: 'sales-store-detail',
    params: {
      id: String(store.gid!),
    },
  })
}

function handleRefresh() {
  const mescroll = getMescroll()
  if (mescroll && mescroll.resetUpScroll) {
    mescroll.resetUpScroll()
  }
}

function handleSortSelect(event: { value: string, selectedItems: { label: string, value: string } }) {
  handleSortLineSelect(event)
  handleRefresh()
}

onShow(() => {
  if (!sortLineLoading.value) {
    getSortLine()
  }
})
</script>

<template>
  <view class="sales-store-list relative box-border w-screen flex flex-col bg-[#F9F9F9] pt-[calc(52px+88rpx)]">
    <view class="fixed-top-section">
      <SearchBar
        v-model="searchKeyword"
        :show-filter="false"
        placeholder="门店/地址"
        @clear="handleRefresh"
        @search="handleRefresh"
      />

      <!-- 线路选择 -->
      <view class="h-88rpx flex items-center px-3">
        <wd-select-picker
          v-model="sortLineId"
          :columns="sortLineList"
          :loading="sortLineLoading"
          title="选择线路"
          type="radio"
          filterable use-default-slot hide-label
          @confirm="handleSortSelect"
        >
          <template #default>
            <view
              class="flex items-center rounded bg-white px-3 py-1"
            >
              <text class="max-w-32 truncate text-28rpx text-[#2D2D2D] font-500">
                {{ currentSortLine?.label || '选择线路' }}
              </text>
              <text class="i-carbon-caret-down text-4 text-[var(--textapplication-text-3)]" />
            </view>
          </template>
        </wd-select-picker>
      </view>
    </view>

    <!-- 门店列表 -->
    <mescroll-body
      :down="downOption"
      :up="upOption"
      safearea
      height="100%"
      @init="mescrollInit"
      @down="downCallback"
      @up="upCallback"
    >
      <view class="mx-3">
        <SalesStoreCard
          v-for="store in storeList"
          :key="store.code"
          :store="store"
          @click="goToStoreDetail"
        />
      </view>
    </mescroll-body>
  </view>
</template>

<style lang="scss" scoped>
.sales-store-list {
  min-height: calc(100vh - 50px) !important;
  min-height: calc(100vh - 50px - constant(safe-area-inset-bottom)) !important;
  min-height: calc(100vh - 50px - env(safe-area-inset-bottom)) !important;

  :deep(.wd-action-sheet) {
    padding-bottom: 50px !important;
  }

  :deep(.mescroll-body) {
    min-height: calc(100vh - 88rpx - 52px - 50px) !important;
    min-height: calc(100vh - 88rpx - 52px - 50px - constant(safe-area-inset-bottom)) !important;
    min-height: calc(100vh - 88rpx - 52px - 50px - env(safe-area-inset-bottom)) !important;
  }
}

/* 固定在顶部的搜索栏和线路选择 */
.fixed-top-section {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 10;
  background-color: #F9F9F9;
}
</style>

<route lang="json">
{
  "layout": "tabbar",
  "name": "sales-store-list",
  "style": {
    "navigationBarTitleText": "车销"
  }
}
</route>
