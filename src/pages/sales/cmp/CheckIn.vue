<script setup lang="ts">
import { onMounted } from 'vue'
import { useRaf } from 'wot-design-uni/components/composables/useRaf'
import dayjs from 'dayjs'
import UploadImgPop from '../../../business/UploadImgPop.vue'
import type { AttachmentDTO, BillSubmitAttachDtlRequestDTO, StoreDTO } from '@/api/globals'

const props = defineProps({
  store: {
    type: Object as PropType<StoreDTO>,
    required: true,
    default: () => ({}),
  },
})

/**
 * 更新是否可以开单
 * 设置onShow刷新
 */
const emit = defineEmits<{
  (e: 'update-is-can-open-order', value: boolean): void // 更新是否可以开单
  (e: 'set-onshow-refresh'): void // 设置onShow刷新数据
}>()

const { setStore } = useSalesStore()

const uploadImgPopRef = ref<InstanceType<typeof UploadImgPop>>()

const { initLocation, signIn, signOut, location, loading, checkInState, noSignOutStore, distance, signHistory, canOpenOrder, handleSignOutVerify } = useCheckIn(props.store)
const { ossLoading, ossInfo } = useOssInfo()

const router = useRouter()

const checkInLoading = computed(() => loading.value || ossLoading.value) // 签到loading

watch(canOpenOrder, (newVal) => {
  emit('update-is-can-open-order', newVal)
})
// 当前时间 hh:mm:ss
const time = ref(getTime())

const { start: startRaf } = useRaf(refreshTime)
const hours = computed(() => time.value.split(':')[0])
const minutes = computed(() => time.value.split(':')[1])
const seconds = computed(() => time.value.split(':')[2])

const checkInStateText = computed(() => {
  if (checkInLoading.value) {
    return {
      operation: '打卡签到',
      tip: '获取门店签到状态中...',
    }
  }
  // 写个方法，如果signInTime是今天的时间则只展示时分秒，否则展示年月日时分秒,可以使用dayjs
  const getSignTime = (signInTime: string) => {
    const now = dayjs()
    const signInDate = dayjs(signInTime)
    if (signInDate.isSame(now, 'day')) {
      return signInTime.split(' ')[1]
    }
    else {
      return signInTime
    }
  }
  switch (checkInState.value) {
    case 'canCheckIn':
      // 存在未签退的门店，不可进行再次签到，必须先去签退
      if (CommonUtil.isNumber(noSignOutStore.value?.data)) {
        return {
          operation: '打卡签到',
          notice: '存在未签退的门店，不可进行再次签到',
          noticeClass: 'check-in-error',
          stateClass: 'check-in-disabled',
        }
      }
      else {
        // 不存在未签退的门店，可以进行签到
        return {
          operation: '打卡签到',
          notice: signHistory.value.length > 0 ? '您已离店签退成功' : '您还未到店签到，请及时签到',
          noticeClass: signHistory.value.length > 0 ? 'check-in-success' : 'check-in-warning',
          stateClass: 'can-check-in',
        }
      }
    case 'canCheckOut':
      return {
        operation: '离店签退',
        notice: '您已打卡成功，可进行离店签退',
        noticeClass: 'check-in-primary',
        stateClass: 'can-check-out',
        signTime: signHistory.value[0]?.signInTime ? `${getSignTime(signHistory.value[0]?.signInTime)}` : '',
      }
    case 'outOfRange':
      return {
        operation: '打卡签到',
        tip: CommonUtil.isNumber(distance?.value?.data) ? '不在门店打卡范围' : '',
        stateClass: 'check-in-disabled',
        // 如果存在打卡记录且已签退，显示success样式以便显示查看记录按钮
        notice: signHistory.value.length > 0 ? '您已离店签退成功' : undefined,
        noticeClass: signHistory.value.length > 0 ? 'check-in-success' : undefined,
      }
    default:
      return {
        operation: '打卡签到',
        tip: `不在门店打卡范围`,
        stateClass: 'check-in-disabled',
      }
  }
})

function getTime() {
  const now = new Date()
  const hours = now.getHours()
  const minutes = now.getMinutes()
  const seconds = now.getSeconds()
  return `${CommonUtil.padZero(hours)}:${CommonUtil.padZero(minutes)}:${CommonUtil.padZero(seconds)}`
}

function refreshTime() {
  time.value = getTime()
  startRaf()
}

// 上传附件列表
const fileList = ref<AttachmentDTO[]>([])

/**
 * 打卡签到/签退
 */
async function handleCheckIn() {
  if (checkInLoading.value) {
    return
  }

  if (!location) {
    return initLocation()
  }

  if (checkInStateText.value.stateClass === 'check-in-disabled') {
    return
  }

  if (!ossInfo.value) {
    return
  }

  const isCheckIn = checkInState.value === 'canCheckIn'

  // 如果是签退，先进行校验
  if (!isCheckIn) {
    try {
      await handleSignOutVerify({ attachDetails: [] }, true)
      // 校验成功后，打开上传图片弹框
      openUploadDialog(isCheckIn)
    }
    catch (error) {
      // 校验失败或用户选择跳转，不打开上传弹框
      console.log('签退校验失败或用户选择跳转')
    }
  }
  else {
    // 签到直接打开上传图片弹框
    openUploadDialog(isCheckIn)
  }
}

/**
 * 打开上传图片弹框
 */
function openUploadDialog(isCheckIn: boolean) {
  uploadImgPopRef.value?.open({
    label: isCheckIn ? '到店照片' : '离店照片',
    fileList: fileList.value.map((file) => {
      return {
        url: file.fileUrl!,
        thumb: file.fileUrl!,
        name: file.fileName,
      }
    }),
    ossInfo: ossInfo.value!,
    confirm: (imgList: BillSubmitAttachDtlRequestDTO[]) => {
      fileList.value = imgList.map((item) => {
        return {
          ...item,
          signType: isCheckIn ? 1 : -1,
        }
      })
      if (isCheckIn) {
        signIn({
          attachDetails: fileList.value,
        }).then(() => {
          fileList.value = []
        })
      }
      else {
        // 签退时直接调用signOut，因为校验已经在之前完成
        signOut({
          attachDetails: fileList.value,
        }).then(() => {
          fileList.value = []
        })
      }
    },
  })
}

/**
 * 跳转到打卡记录
 */
function goToHistory() {
  setStore(props.store)
  // 跳转到签到记录页面
  router.push({
    name: 'sales-checkin-history',
  })
}

/**
 * 跳转到离店签退
 */
function goToCheckOut() {
  if (!noSignOutStore.value?.data) {
    return
  }
  // 跳转到其他门店签退，需要刷新数据
  emit('set-onshow-refresh')
  router.push({
    name: 'sales-store-detail',
    params: {
      id: String(noSignOutStore.value?.data),
    },
  })
}

onMounted(() => {
  startRaf()
})

defineExpose({
  initLocation,
})
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <UploadImgPop ref="uploadImgPopRef" />
  <view v-if="checkInStateText.notice" class="box-border w-full px-3">
    <view class="box-border h-76rpx flex items-center justify-between overflow-hidden rounded-t-3 rounded-br-3 px-3 text-26rpx" :class="checkInStateText.noticeClass">
      <view class="flex items-center">
        <image v-if="checkInStateText.noticeClass === 'check-in-success'" src="/static/icon/ic_check-in-success.png" mode="widthFix" class="mr-2 h-36rpx w-36rpx" />
        <image v-if="checkInStateText.noticeClass === 'check-in-primary'" src="/static/icon/ic_check-in-primary.png" mode="widthFix" class="mr-2 h-36rpx w-36rpx" />
        <text>{{ checkInStateText.notice }}</text>
      </view>

      <view v-if="checkInStateText.noticeClass === 'check-in-success'" @click="goToHistory">
        <text class="text-26rpx font-500">
          查看记录
        </text>
        <text class="i-carbon-chevron-right text-4" />
      </view>
      <view v-if="checkInStateText.noticeClass === 'check-in-error'" class="rounded-1 bg-[var(--atom-function-red-red-6)] px-2 py-.5" @click="goToCheckOut">
        <text class="text-3 text-white">
          去离店签退
        </text>
      </view>
    </view>
  </view>
  <view class="check-in w-full flex flex-col items-center py-24">
    <view class="relative h-260rpx w-260rpx flex flex-col items-center justify-center rounded-full" :class="checkInStateText.stateClass" @click="handleCheckIn">
      <block v-if="!checkInLoading">
        <block v-if="location">
          <text class="flex items-center text-36rpx text-white font-700">
            <text class="text-56rpx">
              {{ hours }}:{{ minutes }}
            </text>
            <text>:</text>
            <text>
              {{ seconds }}
            </text>
          </text>
          <text class="mt-20rpx text-4 text-white">
            {{ checkInStateText.operation }}
          </text>
        </block>
        <block v-else>
          <text class="text-5 text-white font-700">
            授权定位
          </text>
        </block>
      </block>
      <wd-loading v-else />
    </view>
    <view v-if="checkInStateText.tip" class="mt-20rpx text-3 text-[var(--frequentapplication-red-content)]">
      {{ checkInStateText.tip }}
    </view>

    <view v-if="checkInStateText.signTime" class="mt-20rpx text-28rpx text-3 text-#333">
      <image src="/static/icon/ic_sign_time.svg" mode="widthFix" class="h-2 w-2" />
      <text class="text-#999">
        签到时间
      </text>
      <text>{{ checkInStateText.signTime }}</text>
    </view>
  </view>
</template>

<style scoped lang="scss">
.check-in-warning {
  background: var(--frequentapplication-orange-background);
  color: var(--frequentapplication-orange-content);
}

.check-in-primary {
  background: var(--atom-primary-primary-1);
  color: var(--frequentapplication-primary-content);
}

.check-in-success {
  background: var(--frequentapplication-green-background);
  color: var(--frequentapplication-green-content);
}

.check-in-error {
  background: var(--frequentapplication-red-background);
  color: var(--frequentapplication-red-content);
}

.check-in {
  .can-check-in {
    background: linear-gradient(129deg, var(--atom-primary-primary-5, #4480FF) 4.41%, var(--frequent-application-primary-content, #1C64FD) 87.8%);
  }
  .can-check-out {
    background: linear-gradient(129deg, var(--atom-function-green-green-5, #59CDAA) 4.41%, var(--frequentapplication-green-content, #12B886) 87.8%);
  }
  .check-in-disabled {
    background: #CCC;
  }
}
</style>
