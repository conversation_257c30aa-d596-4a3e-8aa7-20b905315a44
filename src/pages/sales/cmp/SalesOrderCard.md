# SalesOrderCard 销售单据卡片组件

## 功能概述

销售单据卡片组件用于在销售单据列表中展示单个订单的基本信息，支持显示单据状态、门店信息、业务员信息和统计数据。

## 主要特性

- ✅ **单据信息**: 显示单据号、门店、业务员、时间等基本信息
- ✅ **统计数据**: 展示商品种类数和订单总金额
- ✅ **状态显示**: 支持显示"已完成"和"已作废"状态
- ✅ **点击事件**: 支持点击整个卡片触发事件

## 使用方法

### 1. 基本用法

```vue
<script setup>
import SalesOrderCard from './cmp/SalesOrderCard.vue'

const orderData = {
  num: '0000191204009',
  stat: 300, // 状态码
  store: {
    name: '小二小店',
    code: '1001'
  },
  vehSaleEmp: {
    name: '王二小'
  },
  lstupdTime: '昨天',
  goodsCount: 99,
  total: 99999.99
}

function handleOrderClick() {
  // 处理点击事件
  console.log('订单被点击')
}
</script>

<template>
  <SalesOrderCard
    :order="orderData"
    @click="handleOrderClick"
  />
</template>
```

## API 接口

### 组件属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `order` | `VehSaleQueryResponseDTO` | - | 订单数据（必需） |

### 组件事件

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| `click` | 点击卡片时触发 | - |

### 订单数据结构

```typescript
interface VehSaleQueryResponseDTO {
  num?: string // 单据号
  stat?: number // 状态码
  store?: { // 门店信息
    name?: string // 门店名称
    code?: string // 门店代码
  }
  vehSaleEmp?: { // 业务员信息
    name?: string // 业务员姓名
  }
  lstupdTime?: string // 操作时间
  goodsCount?: number // 商品种类数
  total?: number // 订单总金额
}
```

## 状态显示

组件会根据订单的`stat`状态码自动显示对应的状态标签：

### 支持的状态

| 状态码 | 显示文字 | 颜色 | 说明 |
|--------|----------|------|------|
| `300` | 已完成 | `#12B886` (绿色) | 订单已完成 |
| `110` | 已作废 | `#8A8A8A` (灰色) | 审核后作废 |
| `1310` | 已作废 | `#8A8A8A` (灰色) | 申请后作废 |
| `310` | 已作废 | `#8A8A8A` (灰色) | 已完成后作废 |
| 其他 | 不显示 | - | 无状态标签 |

### 状态标签样式
- **位置**: 位于单据号右侧
- **背景**: 浅灰色圆角背景 `#F5F5F5`
- **字体**: 14px，medium字重
- **内边距**: 8px水平，4px垂直

## 样式特点

- **卡片设计**: 白色背景，8px圆角，轻微阴影
- **响应式布局**: 适配不同屏幕尺寸
- **UnoCSS样式**: 使用原子化CSS类名
- **间距设计**: 合理的内边距和间距

## 布局结构

```
┌─────────────────────────────────┐
│ 单据号              [状态]       │
│                                 │
│ 门店：xxx    业务员：xxx        │
│ 时间：xxx                       │
│                                 │
│ ┌─────────────────────────────┐ │
│ │ 共 99 种商品     共 ¥999.99 │ │
│ └─────────────────────────────┘ │
└─────────────────────────────────┘
```

## 使用场景

1. **销售单据列表**: 在历史车销单列表中展示订单信息
2. **订单筛选**: 配合筛选功能展示不同状态的订单
3. **快速预览**: 提供订单的关键信息预览

## 注意事项

- 确保传入的`order`数据结构正确
- 状态显示基于`stat`字段，请确保状态码准确
- 点击事件会冒泡到整个卡片区域
- 组件使用`virtualHost: true`优化小程序渲染性能

## 相关组件

- `sales-list.vue` - 使用该组件的销售单据列表页面
- 其他订单列表页面可复用此组件
