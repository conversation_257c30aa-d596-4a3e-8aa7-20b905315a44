<script lang="ts" setup>
import type { StoreDTO } from '@/api/globals'

const props = defineProps<{
  store: StoreDTO
}>()

const emit = defineEmits<{
  (e: 'click', payload: StoreDTO): void
}>()
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <view class="sales-store-card mb-2 w-[calc(100%-48rpx)] rounded-lg bg-white p-3" @click="emit('click', props.store)">
    <!-- 门店标题 -->
    <view class="mb-3 flex items-center justify-between">
      <text class="text-4 text-[#2D2D2D] font-medium">
        {{ props.store.name }}[{{ props.store.code }}]
      </text>
    </view>
    <!-- 门店地址 -->
    <view class="mb-2">
      <text class="text-3 text-[#8A8A8A]">
        地址：
      </text>
      <text class="text-3 text-[#2D2D2D]">
        {{ store.address || '--' }}
      </text>
    </view>
    <!-- 订单信息 -->
    <view v-if="store.lastVehSaleTime || store.lastVehSaleTotal" class="flex items-center justify-between rounded bg-[#F5F5F5] px-3 py-1">
      <view class="flex items-center">
        <text class="text-28rpx text-[#8A8A8A]">
          上次订单：
        </text>
        <text class="text-28rpx text-[#2D2D2D] font-medium">
          {{ formatDate(store.lastVehSaleTime) }}
        </text>
      </view>
      <view class="flex items-center">
        <text class="text-28rpx text-[#8A8A8A]">
          共
        </text>
        <view class="mx-1 flex items-center">
          <text class="text-3 text-[#8A8A8A]">
            ¥
          </text>
          <text class="text-4 text-[#1A1A1A] font-medium">
            {{ store.lastVehSaleTotal }}
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.sales-store-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

  &:not(:last-child) {
    margin-bottom: 16rpx;
  }
}
</style>
