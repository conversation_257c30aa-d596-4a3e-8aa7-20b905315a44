<!--
 * @Author: claude
 * @Date: 2025-04-19 16:48:00
 * @Description: 销售单据卡片组件 - 支持状态显示
 * @FilePath: /lsym-cx-mini/src/pages/sales/cmp/SalesOrderCard.vue
-->

<script setup lang="ts">
import { computed } from 'vue'
import type { VehSaleQueryResponseDTO } from '@/api/globals'

const props = defineProps({
  order: {
    type: Object as PropType<VehSaleQueryResponseDTO>,
    required: true,
  },
  // 单据类型：销开/销退
  type: {
    type: String,
    default: '',
  },
})

// 定义点击事件
const emit = defineEmits(['click'])

// 处理点击事件
function handleClick() {
  emit('click')
}

// 获取单据状态显示信息
const statusInfo = computed(() => {
  const order = props.order
  if (!order || !order.stat) {
    return null
  }

  // 根据状态码直接判断显示状态
  switch (order.stat) {
    case 300: // 已完成
      return {
        text: '已完成',
        class: 'text-[#12B886]',
      }
    case 110: // 审核后作废
    case 1310: // 申请后作废
    case 310: // 已完成后作废
      return {
        text: '已作废',
        class: 'text-[#8A8A8A]',
      }
    default:
      return null
  }
})
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <view class="sales-order-card relative mb-2 w-[calc(100%-48rpx)] rounded-lg bg-white p-3" @click="handleClick">
    <!-- 单号和状态 -->
    <view class="mb-3 flex items-center justify-between">
      <text class="text-4 text-[#2D2D2D] font-medium">
        {{ order.num }}
      </text>
      <view
        v-if="statusInfo"
        class="rounded px-2 py-1"
      >
        <text class="text-28rpx font-medium" :class="statusInfo.class">
          {{ statusInfo.text }}
        </text>
      </view>
    </view>

    <!-- 基本信息 -->
    <view class="mb-1 flex flex-wrap">
      <view class="mb-2 min-w-[50%] flex break-all">
        <text class="flex-none text-3 text-[#8A8A8A]">
          门店：
        </text>
        <text class="text-3 text-[#2D2D2D]">
          {{ order.store?.name }}[{{ order.store?.code }}]
        </text>
      </view>

      <view class="mb-2 min-w-[50%] flex break-all">
        <text class="flex-none text-3 text-[#8A8A8A]">
          业务员：
        </text>
        <text class="text-3 text-[#2D2D2D]">
          {{ order.vehSaleEmpName }}[{{ order.vehSaleEmp?.code }}]
        </text>
      </view>

      <view class="mb-2 min-w-[50%] flex break-all">
        <text class="flex-none text-3 text-[#8A8A8A]">
          操作时间：
        </text>
        <text class="text-3 text-[#2D2D2D] font-500">
          {{ formatDate(order.lstupdTime) }}
        </text>
      </view>
    </view>

    <!-- 统计信息 -->
    <view class="flex items-center justify-between rounded bg-[#F5F5F5] px-3 py-1">
      <view class="flex items-center">
        <text class="text-28rpx text-[#8A8A8A]">
          共
        </text>
        <text class="mx-1 text-4 text-[#1A1A1A] font-medium">
          {{ order.goodsCount }}
        </text>
        <text class="text-28rpx text-[#8A8A8A]">
          种商品
        </text>
      </view>
      <view class="flex items-center">
        <text class="text-28rpx text-[#8A8A8A]">
          共
        </text>
        <view class="mx-1 flex items-center">
          <text class="text-3 text-[#8A8A8A]">
            ¥
          </text>
          <text class="text-4 text-[#1A1A1A] font-medium">
            {{ order.total }}
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.sales-order-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

  &:not(:last-child) {
    margin-bottom: 16rpx;
  }
}
</style>
