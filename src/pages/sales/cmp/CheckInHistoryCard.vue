<script setup lang="ts">
import type { AttachmentDTO, StoreSignDTO } from '@/api/globals'

const props = defineProps({
  record: {
    type: Object as PropType<StoreSignDTO>,
    required: true,
    default: () => ({}),
  },
})

const signInImgList = computed(() => {
  return props.record.attachDetails ? props.record.attachDetails.filter(item => item.signType === 1) : []
})

const signOutImgList = computed(() => {
  return props.record.attachDetails ? props.record.attachDetails.filter(item => item.signType === -1) : []
})

// 格式化时间显示
function formatTime(timeStr: string) {
  if (!timeStr)
    return '--'
  try {
    const date = new Date(timeStr)
    return date.toTimeString().split(' ')[0] // 获取 HH:mm:ss 格式
  }
  catch {
    return timeStr
  }
}

/**
 * 预览图片
 * @param imgList 图片列表
 * @param index 当前图片索引
 */
function handlePreview(imgList: AttachmentDTO[], index: number) {
  uni.previewImage({
    urls: imgList.map(item => item.fileUrl!),
    current: index,
  })
}
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <view class="check-in-history-card relative mx-3 rounded-3 rounded-3 bg-white p-0">
    <!-- 签到记录 -->
    <view class="flex flex-col">
      <!-- 签到信息 -->
      <view class="flex flex-col py-3">
        <!-- 签到时间行 -->
        <view class="flex items-center px-3">
          <view class="mr-15rpx h-28rpx w-28rpx flex items-center justify-center">
            <!-- 签到成功图标 -->
            <image
              src="@/static/icon/ic_success.svg"
              class="h-28rpx w-28rpx"
              mode="widthFix"
            />
          </view>

          <text class="text-3 text-[#666666]">
            签到时间：{{ formatTime(record.signInTime!) }}
          </text>
        </view>

        <!-- 签到照片 -->
        <view v-if="signInImgList.length" class="mt-3 px-3">
          <view class="flex flex-wrap">
            <!-- 这里显示签到时上传的照片，目前显示占位图 -->
            <image
              v-for="(item, index) in signInImgList"
              :key="`signin-${item.fileId}`"
              :src="item.fileUrl"
              mode="aspectFit"
              class="h-118rpx w-118rpx flex items-center justify-center border border-[#E2E3E5] rounded-2rpx bg-gray-100"
              @click="handlePreview(signInImgList, index)"
            />
          </view>
        </view>
      </view>

      <!-- 签退记录（如果存在） -->
      <view v-if="record.signOutTime" class="flex flex-col border-t border-[#F0F0F0] py-3">
        <!-- 签退时间行 -->
        <view class="flex items-center px-3">
          <view class="mr-15rpx h-28rpx w-28rpx flex items-center justify-center">
            <!-- 签退成功图标 -->
            <image
              src="@/static/icon/ic_success.svg"
              class="h-28rpx w-28rpx"
              mode="widthFix"
            />
          </view>

          <text class="text-3 text-[#666666]">
            签退时间：{{ formatTime(record.signOutTime) }}
          </text>
        </view>

        <!-- 签退照片 -->
        <view v-if="signOutImgList.length" class="mt-3 px-3">
          <view class="flex flex-wrap">
            <image
              v-for="(item, index) in signOutImgList"
              :key="`signout-${item.fileId}`"
              :src="item.fileUrl"
              mode="aspectFit"
              class="h-118rpx w-118rpx flex items-center justify-center border border-[#E2E3E5] rounded-2rpx bg-gray-100"
              @click="handlePreview(signOutImgList, index)"
            />
          </view>
        </view>
      </view>

      <!-- 未签退状态 -->
      <view v-else class="flex items-center border-t border-[#F0F0F0] px-3 py-3">
        <view class="mr-15rpx h-28rpx w-28rpx flex items-center justify-center">
          <!-- 未签退图标 -->
          <view class="h-16rpx w-16rpx border-4rpx border-[#F57F00] rounded-full" />
        </view>

        <view class="flex items-center gap-2">
          <text class="text-3 text-[#999999]">
            未签退
          </text>
          <text class="text-28rpx text-[#333333] font-medium">
            {{ formatTime(record.signInTime!) }}
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.check-in-history-card {
  &:not(:last-child) {
    margin-bottom: 16rpx;
  }
}
</style>
