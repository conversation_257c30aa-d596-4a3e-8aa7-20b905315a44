<script setup lang="ts">
import { isDef } from 'wot-design-uni/components/common/util'
import type { VehSaleDtlResponseDTO } from '@/api/globals'

const props = defineProps({
  sku: {
    type: Object as PropType<VehSaleDtlResponseDTO>,
    required: true,
    default: () => ({}),
  },
})

/**
 * 商品图片列表
 */
const skuImgList = computed(() => {
  const imgList = props.sku.imageDetails && props.sku.imageDetails.length > 0 ? props.sku.imageDetails.map(item => item.fileUrl) : []
  return imgList.filter(item => isDef(item))
})

/**
 * 商品主图
 */
const skuMainImg = computed(() => {
  return skuImgList.value.length > 0 ? skuImgList.value[0] : ''
})

/**
 * 规格详情列表
 */
const qpcDetails = computed(() => {
  // 如果有qpcDetails直接使用
  if (props.sku.qpcDetails && props.sku.qpcDetails.length > 0) {
    return props.sku.qpcDetails
  }
  return []
})
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <view class="overflow-hidden">
    <view class="py-3">
      <view class="mb-2 flex">
        <!-- 商品图片 -->
        <SkuImage
          :src="skuMainImg"
          :preview-src="skuImgList"
          mode="widthFix"
          custom-class="mr-3 flex-none"
        />
        <view class="flex-auto">
          <view class="line-clamp-2 break-all text-28rpx text-[#2D2D2D] font-medium leading-[1.5]">
            {{ sku.goods?.name }}
          </view>
          <view class="mt-1 inline-block break-all rounded bg-[#F5F6F7] px-2 py-0 text-3 text-[#5C5C5C]">
            {{ sku.goods?.code }}
          </view>
        </view>
      </view>

      <view class="mt-2">
        <view
          v-for="(qpc, index) in qpcDetails"
          :key="index"
          class="box-border w-full flex items-center justify-between px-3 py-2"
        >
          <text class="w-1/2">
            <text class="h-5 break-all rounded bg-[#F5F6F7] px-1 text-[26rpx] text-[var(--textapplication-text-2)] leading-[1]">
              {{ qpc.qpcStr }}
            </text>
          </text>
          <text class="w-1/2 break-all text-right text-[26rpx] text-[var(--frequentapplication-primary-content)]">
            {{ qpc.qtyStr }}{{ qpc.munit }}
          </text>
        </view>
      </view>
    </view>
  </view>
</template>
