<script setup lang="ts">
import CheckIn from './cmp/CheckIn.vue'
import { createGlobalLoadingMiddleware } from '@/api/core/middleware'
import type { StoreDTO } from '@/api/globals'

const userStore = useUserStore()
const router = useRouter()
const { setStore, clearStore } = useSalesStore()
const { error: showError, success: showSuccess } = useGlobalToast()
const globalLoading = useGlobalLoading()
const checkInRef = ref<InstanceType<typeof CheckIn> | null>(null)

const { updateStore, getDistance, getLocation, maxDistance } = useCheckIn()

const { checkAllPermissions } = usePermissionChecker()

// 检查开单权限（需要查看权限和创建权限）
const hasCreatePermission = computed(() => {
  return checkAllPermissions(['vehSaleView', 'vehSaleCreate'])
})

// 检查退单权限（需要查看权限和创建权限）
const hasReturnPermission = computed(() => {
  return checkAllPermissions(['vehSaleBckView', 'vehSaleBckCreate'])
})

// 检查是否有任意一个权限（用于控制底部按钮区域显示）
const hasPermission = computed(() => {
  return hasCreatePermission.value || hasReturnPermission.value
})

// 门店gid
const storeGid = ref<number | null>(null)
// 门店信息
const storeInfo = ref<StoreDTO | null>(null)
// 是否可以开单
const canOpenOrder = ref(false)

/**
 * 更新是否可以开单
 * @param newVal 是否可以开单
 */
function updateCanOpenOrder(newVal: boolean) {
  canOpenOrder.value = newVal
}

/**
 * 获取门店信息
 */
const { send: getStore } = useRequest((storeGid: number) => Apis.mdataInterface.getStoreUsingGET({ params: {
  storeGid,
  vehSaleEmpGid: userStore.vehSaleEmp?.gid || 0,
} }), {
  immediate: false,
  middleware: createGlobalLoadingMiddleware({
    loadingText: '获取门店信息中...',
  }),
}).onSuccess((resp) => {
  storeInfo.value = resp.data.data || null
  updateStore(storeInfo.value!)
  const timer = setTimeout(() => {
    clearTimeout(timer)
    checkInRef.value?.initLocation()
  }, 200)
}).onError((err) => {
  globalLoading.close()
  showError(err.error.message || '查询门店信息失败')
})

onLoad((options: any) => {
  clearStore()
  storeGid.value = options.id as number
  getStore(storeGid.value)
})

// 是否onShow刷新数据
const onShowRefresh = ref(false)

onShow(() => {
  if (onShowRefresh.value) {
    onShowRefresh.value = false
    getStore(storeGid.value!)
  }
})

/**
 * 跳转历史车销
 */
function goToHistory() {
  // 存储store信息到全局store
  setStore(storeInfo.value!)
  router.push({
    name: 'sales-list',
    params: {
      id: String(storeInfo.value!.gid!),
    },
  })
}

// 开启导航
function openNavigation() {
  uni.openLocation({
    latitude: Number(storeInfo.value!.latitude),
    longitude: Number(storeInfo.value!.longitude),
    success: () => {
      showSuccess('导航已开启')
    },
    fail: () => {
      showError('导航开启失败')
    },
  })
}

/**
 * 车销开单前校验
 * 校验通过则跳转到领货页面
 * 校验失败则提示
 */
const { send: verifyCreate } = useRequest(() => Apis.vehsaleInterface.verifyCreateUsingPOST_1({ params: {
  vehSaleEmpGid: userStore.vehSaleEmp?.gid || 0,
} }), {
  immediate: false,
}).onSuccess(() => {
  setStore(storeInfo.value!)
  router.push({ name: 'sales-edit' })
}).onError((err) => {
  globalLoading.close()
  showError(err.error.message || '校验失败')
})

/**
 * 开单
 */
async function createOrder() {
  try {
    globalLoading.loading('校验中...')
    await getLocation()
    const distance = await getDistance()
    const inRange = Number(distance?.data) <= maxDistance.value
    if (inRange) {
      await verifyCreate()
    }
    else {
      globalLoading.close()
      checkInRef.value?.initLocation()
      throw new Error(`请在门店${userStore.getOption('PS4_StoreSignMaxDistance') || 200}m范围内进行开单~`)
    }
    globalLoading.close()
  }
  catch (error: any) {
    globalLoading.close()
    showError(error.message || '开单失败')
  }
}

/**
 * 车销退单前校验
 * 校验通过则跳转到车销退单页面
 * 校验失败则提示
 */
const { send: verifyCreateBck } = useRequest(() => Apis.vehsalebckInterface.verifyCreateUsingPOST({ params: {
  vehSaleEmpGid: userStore.vehSaleEmp?.gid || 0,
} }), {
  immediate: false,
}).onSuccess(() => {
  setStore(storeInfo.value!)
  router.push({ name: 'sales-back-edit' })
}).onError((err) => {
  globalLoading.close()
  showError(err.error.message || '校验失败')
})

// 退单
async function returnOrder() {
  try {
    globalLoading.loading('校验中...')
    await getLocation()
    const distance = await getDistance()
    const inRange = Number(distance?.data) <= maxDistance.value
    if (inRange) {
      await verifyCreateBck()
    }
    else {
      globalLoading.close()
      checkInRef.value?.initLocation()
      throw new Error(`请在门店${userStore.getOption('PS4_StoreSignMaxDistance') || 200}m范围内进行退单~`)
    }
    globalLoading.close()
  }
  catch (error: any) {
    globalLoading.close()
    showError(error.message || '退单失败')
  }
}
</script>

<template>
  <view v-if="storeInfo" class="store-detail box-border min-h-screen bg-[#F9F9F9]" :class="canOpenOrder && hasPermission ? 'store-detail--can-open-order' : ''">
    <!-- 门店头部信息 -->
    <view class="h-92rpx flex items-center bg-white px-4">
      <image
        src="@/static/icon/ic_shop_index.svg"
        mode="widthFix"
        class="mr-18rpx h-auto w-32rpx flex-shrink-0"
      />
      <text class="flex-auto truncate text-4 font-semibold">
        {{ storeInfo!.name }}[{{ storeInfo!.code }}]
      </text>
    </view>

    <!-- 门店详细信息 -->
    <view class="bg-white px-3 py-2">
      <view class="rounded-lg bg-white from-[var(--atom-primary-primary-1)] to-white bg-gradient-to-b p-3">
        <!-- 地址 -->
        <view class="flex items-start justify-between py-2">
          <text class="text-28rpx text-[#5C5C5C] font-normal">
            地址
          </text>
          <text class="max-w-60 text-right text-28rpx text-[#2D2D2D]">
            {{ storeInfo!.address || '--' }}
          </text>
        </view>

        <!-- 开启导航 -->
        <view class="flex items-center justify-end py-2" @click="openNavigation">
          <image
            src="@/static/icon/location_icon.svg"
            mode="widthFix"
            class="mr-1 h-auto w-4"
          />
          <text class="text-28rpx text-[var(--frequentapplication-primary-content)] font-medium">
            开启导航
          </text>
        </view>

        <!-- 所属线路 -->
        <view class="flex items-start justify-between py-2">
          <text class="flex-none text-28rpx text-[#5C5C5C] font-normal">
            所属线路
          </text>
          <view class="max-w-60 flex flex-col">
            <text v-for="(line, index) in storeInfo!.sortLines" :key="index" class="text-28rpx text-[#2D2D2D] leading-42rpx">
              {{ line.name }}
            </text>
          </view>
        </view>

        <!-- 上次订单时间 -->
        <view class="flex items-start justify-between py-2">
          <text class="text-28rpx text-[#5C5C5C] font-normal">
            上次订单时间
          </text>
          <text class="text-28rpx text-[#2D2D2D]">
            {{ formatDate(storeInfo.lastVehSaleTime) }}
          </text>
        </view>

        <!-- 上次订单金额 -->
        <view class="flex items-start justify-between py-2">
          <text class="text-28rpx text-[#5C5C5C] font-normal">
            上次订单金额
          </text>
          <text class="text-28rpx text-[#2D2D2D]">
            ¥ {{ storeInfo.lastVehSaleTotal || '--' }}
          </text>
        </view>

        <!-- 历史单据 -->
        <view class="flex items-center justify-end py-2" @click="goToHistory">
          <image
            src="@/static/icon/ic_danju.svg"
            mode="widthFix"
            class="mr-1 h-auto w-4"
          />
          <text class="text-28rpx text-[var(--frequentapplication-primary-content)] font-medium">
            历史单据
          </text>
          <image
            src="@/static/icon/arrow_icon.svg"
            mode="widthFix"
            class="ml-1 h-auto w-4"
          />
        </view>
      </view>
    </view>

    <CheckIn v-if="storeInfo" ref="checkInRef" :store="storeInfo" @update-is-can-open-order="updateCanOpenOrder" @set-onshow-refresh="onShowRefresh = true" />
    <!-- 底部按钮 -->
    <view v-if="canOpenOrder && hasPermission" class="fixed bottom-0 left-0 right-0 box-border w-screen bg-white shadow-md">
      <view class="box-border h-136rpx w-full flex items-center justify-between px-3">
        <wd-button
          v-if="hasReturnPermission"
          plain
          block
          :custom-class="hasCreatePermission ? 'w-50% mr-2!' : 'flex-1'"
          type="error"
          size="large"
          :round="false"
          @click="returnOrder"
        >
          退单
        </wd-button>
        <wd-button
          v-if="hasCreatePermission"
          type="primary"
          size="large"
          :custom-class="hasReturnPermission ? 'w-50%' : 'flex-1'"
          block
          :round="false"
          @click="createOrder"
        >
          开单
        </wd-button>
      </view>
      <wd-gap :height="0" safe-area-bottom />
    </view>
  </view>
</template>

<style lang="scss" scoped>
.store-detail {
  &--can-open-order {
    padding-bottom: 136rpx !important;
    padding-bottom: calc(136rpx + constant(safe-area-inset-bottom)) !important;
    padding-bottom: calc(136rpx + env(safe-area-inset-bottom)) !important;
  }
}
</style>

<route lang="json">
{
  "name": "sales-store-detail",
  "style": {
    "navigationBarTitleText": "门店详情"
  }
}
</route>
