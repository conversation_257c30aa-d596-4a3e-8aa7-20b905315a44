<script setup lang="ts">
import CheckInHistoryCard from './cmp/CheckInHistoryCard.vue'

const { store } = storeToRefs(useSalesStore())

const { signHistory, getSignHistory } = useCheckIn(store.value!)

// 页面挂载时获取门店信息和签到记录
onMounted(async () => {
  await getSignHistory()
})
</script>

<template>
  <view class="checkin-history box-border min-h-screen bg-[#F9F9F9] pt-3">
    <!-- 记录列表 -->
    <CheckInHistoryCard
      v-for="(record, index) in signHistory"
      :key="index"
      :record="record"
    />
  </view>
</template>

<style lang="scss" scoped>
.checkin-history {
  padding-bottom: 0 !important;
  padding-bottom: constant(safe-area-inset-bottom) !important;
  padding-bottom: env(safe-area-inset-bottom) !important;
}
</style>

<route lang="json">
{
  "name": "sales-checkin-history",
  "style": {
    "navigationBarTitleText": "门店签到记录"
  }
}
</route>
