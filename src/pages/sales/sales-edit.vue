<script setup lang="ts">
import UploadImgPop from '../../business/UploadImgPop.vue'
import SalesEditSkuCard from './cmp/SalesEditSkuCard.vue'
import CartSkeleton from '@/business/CartSkeleton.vue'
import SkuListSkeleton from '@/business/SkuListSkeleton.vue'
import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
import type { VehSaleGetDraftGoodsResponseDTO, VehSaleGetDraftQpcResponseDTO, VehSaleSubmitRequestDTO } from '@/api/globals'
import { createGlobalLoadingMiddleware } from '@/api/core/middleware'
import CartNavBar from '@/business/CartNavBar.vue'
import DraftSubmitVerify from '@/business/DraftSubmitVerify.vue'

const { store } = storeToRefs(useSalesStore())

const { navBarTotalHeight } = storeToRefs(useDeviceInfo())

const userStore = useUserStore()
const globalToast = useGlobalToast()
const globalLoading = useGlobalLoading()
const router = useRouter()
const { mescrollInit, downCallback, getMescroll } = useMescroll(onPageScroll, onReachBottom)
const { getDraftInfo, draftInfo, skuQueryParams, skuList, skuListLoading, skuTotal, reloadSkuList, skuPage, onSkuListSuccess, onSkuListError, draftInfoLoading, submitDraft, verifySubmitData, verifySubmit } = useDraft('vehSale')
skuQueryParams.sorts = [
  {
    field: 'vehSaleWrhQty',
    asc: 0,
    name: '库存',
  },
]
skuQueryParams.storeGid = store.value?.gid
const { categoryList: categoryListData, getCategoryList, categoryLoading } = useCategorySelect(false)
const { emitRefreshData: emitRefreshStoreList } = useSendRefreshData('sales-store-list') // 刷新列表，本页面提交成功需要刷新门店列表
const { lockPage, unlockPage, pageStyle } = useLockPage()
const draftSubmitVerifyRef = ref<InstanceType<typeof DraftSubmitVerify>>()
const uploadImgPopRef = ref<InstanceType<typeof UploadImgPop>>()

// OSS 信息
const { ossInfo } = useOssInfo()

// 监听刷新数据事件
useWatchRefreshData({
  callback: () => {
    getDraftInfo()
    handleRefresh()
  },
  name: 'sales-edit',
})

/**
 * 监听跳转事件
 */
useWatchReditct({
  callback: (id) => {
    router.replace({
      name: 'sales-detail',
      params: { id },
    })
  },
  name: 'sales-edit',
})

const categoryList = computed(() => {
  return categoryListData.value.map((item) => {
    const count = draftInfo.value?.data?.categorySkuCount?.[item.code] || 0
    return {
      label: item.label,
      value: item.value,
      code: item.code,
      count: item.label === '全部' ? draftInfo.value?.data?.skuCount : count,
    }
  })
})

const loading = computed(() => {
  return categoryLoading.value || draftInfoLoading.value
})

function handleRefresh() {
  const mescroll = getMescroll()
  if (mescroll && mescroll.resetUpScroll) {
    mescroll.resetUpScroll()
  }
}

onSkuListSuccess((resp) => {
  getMescroll().endBySize(resp.data.data?.length || 0, skuTotal.value)
  if (skuTotal.value === 0) {
    getMescroll().showEmpty()
  }
})

onSkuListError((err) => {
  globalToast.error(err.error?.message || '加载领货商品失败')
  getMescroll().endErr()
})

// 上拉加载商品数据
async function upCallback(mescroll: any) {
  if (mescroll.num === 1) {
    await reloadSkuList()
  }
  else {
    skuPage.value = mescroll.num
  }
}

onLoad(async () => {
  // 获取draftInfo
  await Promise.all([
    getDraftInfo(),
    getCategoryList(),
  ])
})

// 购物车品项数量
const skuCount = computed(() => {
  return draftInfo.value?.data?.skuCount || 0
})

// 购物车总金额
const total = computed(() => {
  return draftInfo.value?.data?.total || 0
})

// 改变商品数量
async function handleSpecChange(spec: VehSaleGetDraftQpcResponseDTO, sku: VehSaleGetDraftGoodsResponseDTO) {
  const index = skuList.value.findIndex(item => item.goods.gid === sku.goods.gid)
  const oldSku = CommonUtil.deepClone(skuList.value[index])
  skuList.value[index] = CommonUtil.deepClone(sku)
  const verifyIndex = verifySubmitData.value && verifySubmitData.value.data && verifySubmitData.value.data.length > 0 ? verifySubmitData.value.data.findIndex(item => item.goods.gid === sku.goods.gid) : -1
  if (verifyIndex !== -1) {
    verifySubmitData.value.data![verifyIndex] = sku
  }
  try {
    const result = await submitDraft({
      gdCode: sku.goods.gdCode!,
      goods: {
        gid: sku.goods.gid,
        name: sku.goods.name,
        code: sku.goods.code,
      },
      munit: spec.munit,
      price: spec.price,
      qpc: spec.qpc,
      qpcStr: spec.qpcStr,
      qty: spec.qty,
      qtyStr: spec.qtyStr,
      singlePrice: spec.singlePrice,
      total: spec.singlePrice.multiply(spec.qty).scale(2),
      uuid: spec.uuid,
      version: sku.version,
      store: store.value!,
    })
    skuList.value[index].version = result.data?.version || skuList.value[index].version
    if (verifyIndex !== -1) {
      verifySubmitData.value.data![verifyIndex].version = result.data?.version || verifySubmitData.value.data![verifyIndex].version
    }
  }
  catch (error) {
    console.log(error)
    skuList.value[index] = oldSku
    if (verifyIndex !== -1) {
      verifySubmitData.value.data![verifyIndex] = oldSku
    }
  }
}

// 前往购物车页面
function goToCart() {
  // 检查购物车是否为空
  if (skuCount.value === 0) {
    globalToast.info('购物车还是空的')
    return
  }
  router.push({ name: 'sales-cart' })
}

// 提交订单请求
const { send: submit } = useRequest(
  (data: VehSaleSubmitRequestDTO) => Apis.vehsaleInterface.submitUsingPOST_1({
    data,
  }),
  {
    immediate: false,
    middleware: createGlobalLoadingMiddleware({
      loadingText: '提交中...',
    }),
  },
).onSuccess((resp) => {
  emitRefreshStoreList()
  globalLoading.close()
  globalToast.success({
    msg: '提交成功',
    duration: 500,
    closed: () => {
      router.replace({
        name: 'sales-detail',
        params: {
          id: resp.data.data!,
        },
      })
    },
  })
}).onError((err) => {
  globalLoading.close()
  globalToast.error(err.error?.message || '提交失败')
})

/**
 * 提交订单
 */
async function handleSubmit() {
  return new Promise<boolean>((resolve) => {
    if (skuCount.value === 0) {
      globalToast.info('请先添加商品')
      resolve(false)
    }
    else {
      verifySubmit().then((result) => {
        if (!result || !result.data || !result.data.length) {
          // 校验通过后，先上传图片
          handleImageSubmit()
          resolve(true)
        }
        else {
          resolve(false)
          globalLoading.close()
          globalToast.warning('部分商品库存不足，请调整数量!')
          draftSubmitVerifyRef.value?.open({
            beforeConfirm: async () => {
              const res = await handleSubmit()
              return res
            },
          })
        }
      }).catch((err) => {
        globalLoading.close()
        globalToast.error(err.error?.message || '校验失败')
        resolve(false)
      })
    }
  })
}

// 跳转到搜索页面
function goToSearch() {
  router.push({ name: 'sales-edit-search', params: {
    store: JSON.stringify({
      gid: store.value!.gid,
      code: store.value!.code,
      name: store.value!.name,
    }),
  } })
}

/**
 * 重新加载数据
 */
function reload() {
  getDraftInfo()
  handleRefresh()
}

/**
 * 处理图片上传提交
 */
function handleImageSubmit() {
  if (!ossInfo.value) {
    globalToast.error('获取上传配置失败')
    return
  }

  uploadImgPopRef.value?.open({
    title: '上传图片信息',
    subTitle: '交货信息',
    label: '上传门店照片',
    description: '请拍照上传门店陈列、交货照片',
    fileList: [],
    ossInfo: ossInfo.value,
    confirm: (imgList) => {
      // 这里可以将图片信息附加到提交数据中
      const submitData: VehSaleSubmitRequestDTO = {
        vehSaleEmp: userStore.vehSaleEmp,
        store: store.value!,
        attachDetails: imgList,
      }
      performActualSubmit(submitData)
    },
  })
}

/**
 * 执行实际的提交操作
 */
function performActualSubmit(submitData: VehSaleSubmitRequestDTO) {
  submit(submitData)
}
</script>

<template>
  <PrivacyPopup />
  <DraftSubmitVerify ref="draftSubmitVerifyRef" @opened="lockPage" @closed="unlockPage" @confirm="reload" @cancel="reload">
    <template #body>
      <template v-if="verifySubmitData && verifySubmitData.data">
        <SalesEditSkuCard
          v-for="sku in verifySubmitData.data"
          :key="sku.goods.gid"
          :sku="sku"
          @change="handleSpecChange"
        />
      </template>
    </template>
  </DraftSubmitVerify>

  <UploadImgPop ref="uploadImgPopRef" @opened="lockPage" @closed="unlockPage" />

  <view class="pick-edit-page relative box-border min-h-screen w-screen overflow-x-hidden" :style="pageStyle">
    <!-- 自定义导航栏 -->
    <CartNavBar mode="common">
      <template #title>
        <view class="absolute left-50% top-50% w-full flex transform items-center justify-center -translate-x-1/2 -translate-y-1/2">
          <view class="inline-flex items-center justify-center w-auto!" @click="goToSearch">
            <text class="text-lg text-black font-medium">
              车销单开单
            </text>
            <text class="i-carbon-search ml-2 text-4 text-black" />
          </view>
        </view>
      </template>
    </CartNavBar>

    <!-- 骨架屏 -->
    <CartSkeleton v-if="loading" custom-class="top-[var(--navbar-total-height)]" />
    <view v-else class="main relative z-1 w-screen bg-white">
      <!-- 分类Tabs -->
      <view class="fixed top-0 z-3 w-screen bg-white" :style="`top:${navBarTotalHeight}px`">
        <wd-tabs v-model="skuQueryParams.sortCode" custom-class="tabs-custom" @change="handleRefresh">
          <wd-tab v-for="cat in categoryList" :key="cat.code" :title="cat.label" :name="cat.code" :badge-props="cat.count ? { isDot: true } : undefined" />
        </wd-tabs>
        <EditSort v-model:sorts="skuQueryParams.sorts" @change="handleRefresh" />
      </view>
      <wd-gap height="calc(42px + 36px)" />

      <!-- 主内容区域 -->
      <view class="box-border flex flex-auto flex-col bg-white">
        <mescroll-body
          v-if="!categoryLoading"
          :down="downOption"
          :up="upOption"
          @init="mescrollInit"
          @down="downCallback"
          @up="upCallback"
        >
          <SkuListSkeleton v-if="!loading && skuPage === 1 && skuListLoading" />
          <template v-else>
            <SalesEditSkuCard
              v-for="sku in skuList"
              :key="sku.goods.gid"
              :sku="sku"
              @change="handleSpecChange"
            />
          </template>
        </mescroll-body>
      </view>
    </view>
    <!-- 底部购物车 -->
    <CartBar
      :count="skuCount"
      :amount="total"
      safe-area-bottom fixed
      button-text="提交"
      @click-cart="goToCart"
      @click-button="handleSubmit"
    />
  </view>
</template>

<style lang="scss" scoped>
.pick-edit-page {
  min-height: 100vh;
  padding-bottom: 120rpx;
  padding-bottom: calc(120rpx + const(safe-area-inset-bottom)) !important;
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom)) !important;

   :deep(.mescroll-body) {
    min-height: calc(100vh - var(--navbar-total-height) - 42px - 36px - 120rpx) !important;
    min-height: calc(100vh - var(--navbar-total-height) - 42px - 36px - 120rpx - const(safe-area-inset-bottom)) !important;
    min-height: calc(100vh - var(--navbar-total-height) - 42px - 36px - 120rpx - env(safe-area-inset-bottom)) !important;
  }
}

// tabs样式
:deep(.tabs-custom){
  // 修复徽标导致文字溢出的问题
  .wd-tabs__nav-item-badge {
    display: flex;
    max-width: 100%;
    min-width: 0;

    .wd-badge {
      display: flex;
      max-width: 100%;
      min-width: 0;

      .wd-tabs__nav-item-text {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        flex: 1;
        min-width: 0;
      }
    }
  }
}
</style>

<route lang="json">
{
  "name": "sales-edit",
  "style": {
    "navigationStyle": "custom",
    "backgroundColor": "#F9F9F9"
  },
  "meta": {
    "permissions": ["vehSaleCreate"]
  }
}
</route>
