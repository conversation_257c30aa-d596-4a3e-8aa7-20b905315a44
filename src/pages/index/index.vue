<!--
 * @Author: weish<PERSON>
 * @Date: 2025-05-15 12:00:00
 * @LastEditTime: 2025-05-15 16:20:00
 * @LastEditors: weisheng
 * @Description: 首页角色判断跳转页
 * @FilePath: /lsym-cx-mini/src/pages/index/index.vue
-->
<script setup lang="ts">
import { onMounted } from 'vue'
import { useUserStore } from '@/store/useUserStore'
import { useWmsStore } from '@/store/useWmsStore'

const router = useRouter()
const userStore = useUserStore()
const wmsStore = useWmsStore()

// 跳转逻辑
function redirectToHomePage() {
  const userRole = userStore.userRole

  if (userRole === 'warehouse') {
    // 仓管角色：如果未选择物流中心，则跳转到选择页面
    if (!wmsStore.isWmsSelected) {
      router.replaceAll({
        name: 'wms-select',
      })
    }
    else {
      // 已选择物流中心，跳转到工作台
      router.replaceAll({
        name: 'warehouse-dashboard',
      })
    }
  }
  else if (userRole === 'salesman') {
    // 业务员角色
    router.replaceAll({
      name: 'salesman-home',
    })
  }
  else {
    // 未登录或角色不明确，跳转到登录页
    if (!userStore.isLoggedIn) {
      router.replaceAll({
        name: 'login',
      })
    }
    else {
      // 默认跳转到首页
      router.replaceAll({
        name: 'salesman-home',
      })
    }
  }
}

onMounted(() => {
  redirectToHomePage()
})
</script>

<template>
  <view class="redirect-page">
    <!-- 使用一个空白页面，避免闪烁 -->
    <view class="loading-container">
      <!-- 可以选择不显示任何内容，或者显示一个简单的加载指示器 -->
      <view class="loading-spinner" />
    </view>
  </view>
</template>

<style lang="scss">
.redirect-page {
  width: 100%;
  height: 100vh;
  background-color: #F9F9F9; // 与应用背景色保持一致

  .loading-container {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .loading-spinner {
      width: 40rpx;
      height: 40rpx;
      border: 4rpx solid rgba(0, 0, 0, 0.1);
      border-radius: 50%;
      border-top: 4rpx solid transparent;
      // 不显示加载动画，以减少闪烁
      // animation: spin 0.8s linear infinite;
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>

<route lang="json">
{
  "name": "index",
  "type": "home",
  "style": {
    "navigationBarTitleText": "",
    "navigationStyle": "custom",
    "backgroundColor": "#F9F9F9"
  }
}
</route>
