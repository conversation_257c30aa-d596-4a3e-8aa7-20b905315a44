<!--
 * @Author: weish<PERSON>
 * @Date: 2025-03-27 17:06:25
 * @LastEditTime: 2025-05-23 00:08:09
 * @LastEditors: weisheng
 * @Description:
 * @FilePath: /lsym-cx-mini/src/layouts/default.vue
 * 记得注释
-->
<script lang="ts" setup>
const { themeVars, customThemeVars } = useTheme()
</script>

<script lang="ts">
export default {
  options: {
    addGlobalClass: true,
    virtualHost: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <wd-config-provider :theme-vars="themeVars" :custom-style="`background-color: #f5f5f5;min-height: 100vh;${customThemeVars}`">
    <slot />
    <wd-notify />
    <wd-message-box />
    <global-loading />
    <global-toast />
    <global-message />
    <wd-toast />
  </wd-config-provider>
</template>
