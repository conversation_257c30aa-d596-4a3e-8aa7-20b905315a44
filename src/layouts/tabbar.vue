<script lang="ts" setup>
const router = useRouter()
const route = useRoute()
const { themeVars, customThemeVars } = useTheme()

const { activeTabbar, getTabbarItemValue, setTabbarItemActive, tabbarList } = useTabbar()

function handleTabbarChange({ value }: { value: string }) {
  setTabbarItemActive(value)
  router.pushTab({ name: value })
}

onMounted(() => {
  nextTick(() => {
    if (route.name && route.name !== activeTabbar.value.name) {
      setTabbarItemActive(route.name)
    }
  })
})

onShow(() => {
  // #ifdef APP-PLUS
  uni.hideTabBar()
  // #endif
})
</script>

<script lang="ts">
export default {
  options: {
    addGlobalClass: true,
    virtualHost: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <wd-config-provider :theme-vars="themeVars" :custom-style="`min-height: 100vh;${customThemeVars}`">
    <slot />
    <wd-tabbar :model-value="activeTabbar.name" placeholder safe-area-inset-bottom bordered fixed @change="handleTabbarChange">
      <wd-tabbar-item v-for="(item, index) in tabbarList" :key="index" :name="item.name" :value="getTabbarItemValue(item.name)" :title="item.title" :icon="item.icon">
        <template #icon="{ active }">
          <image
            class="h-6 w-6"
            :src="active ? item.activeIcon : item.icon"
            mode="scaleToFill"
          />
        </template>
      </wd-tabbar-item>
    </wd-tabbar>
    <wd-notify />
    <wd-message-box />
    <wd-toast />
    <global-loading />
    <global-toast />
    <global-message />
  </wd-config-provider>
</template>
