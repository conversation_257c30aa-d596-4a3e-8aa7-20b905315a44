<script setup lang="ts">
import WarehousePickEditSkuCard from './cmp/WarehousePickEditSkuCard.vue'
import WarehousePickeEditHeader from './cmp/WarehousePickeEditHeader.vue'
import WarehousePickEditSkeleton from './cmp/WarehousePickEditSkeleton.vue'
import SkuListSkeleton from '@/business/SkuListSkeleton.vue'
import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
import type { GCN, VehSaleEmp, VehSaleGetDraftGoodsResponseDTO, VehSaleGetDraftQpcResponseDTO, VehSaleUseSignSubmitRequestDTO } from '@/api/globals'
import { createGlobalLoadingMiddleware } from '@/api/core/middleware'
import CartNavBar from '@/business/CartNavBar.vue'
import DraftSubmitVerify from '@/business/DraftSubmitVerify.vue'

// const userStore = useUserStore()
const globalToast = useGlobalToast()
const globalLoading = useGlobalLoading()
const router = useRouter()
const { mescrollInit, downCallback, getMescroll } = useMescroll(onPageScroll, onReachBottom)
const warehousePickeEditHeaderRef = ref<InstanceType<typeof WarehousePickeEditHeader>>()
const { getDraftInfo, clearDraft, draftInfo, skuQueryParams, skuList, skuListLoading, skuTotal, reloadSkuList, skuPage, onSkuListSuccess, onSkuListError, draftInfoLoading, submitDraft, seleEmp, verifySubmit, verifySubmitData } = useDraft('vehSaleUseSign')
const { categoryList, getCategoryList, categoryLoading } = useCategorySelect(false)

const { lockPage, unlockPage, pageStyle } = useLockPage()

const draftSubmitVerifyRef = ref<InstanceType<typeof DraftSubmitVerify>>()
const { confirm: showConfirm } = useGlobalMessage()

// 监听刷新数据事件
useWatchRefreshData({
  callback: () => {
    getDraftInfo()
    handleRefresh()
  },
  name: 'warehouse-pick-edit',
})

/**
 * 监听跳转事件
 */
useWatchReditct({
  callback: (id) => {
    router.replace({
      name: 'pick-detail',
      params: { id },
    })
  },
  name: 'warehouse-pick-edit',
})

/**
 * 重新加载数据
 */
function reload() {
  getDraftInfo()
  handleRefresh()
}

const categoryListFilter = computed(() => {
  return categoryList.value.map((item) => {
    const count = draftInfo.value?.data?.categorySkuCount?.[item.code] || 0
    return {
      label: item.label,
      value: item.value,
      code: item.code,
      count: item.label === '全部' ? draftInfo.value?.data?.skuCount : count,
    }
  })
})

const loading = computed(() => {
  return categoryLoading.value || draftInfoLoading.value
})

const sorts = ref(JSON.parse(JSON.stringify(skuQueryParams.sorts || [])))
function handleRefresh() {
  sorts.value.forEach((sort: { asc: 0 | -1 | 1 }, index: number) => {
    if (sort.asc !== 0) {
      // 默认倒叙排序，翻转排序参数
      skuQueryParams.sorts[index].asc = sort.asc === 1 ? -1 : 1
    }
    else {
      skuQueryParams.sorts[index].asc = 0
    }
  })

  const mescroll = getMescroll()
  if (mescroll && mescroll.resetUpScroll) {
    mescroll.resetUpScroll()
  }
}

onSkuListSuccess((resp) => {
  getMescroll().endBySize(resp.data.data?.length || 0, skuTotal.value)
  if (skuTotal.value === 0) {
    getMescroll().showEmpty()
  }
})

onSkuListError((err) => {
  globalToast.error(err.error?.message || '加载领货商品失败')
  getMescroll().endErr()
})

// 上拉加载商品数据
async function upCallback(mescroll: any) {
  if (mescroll.num === 1) {
    await reloadSkuList()
  }
  else {
    skuPage.value = mescroll.num
  }
}

/**
 * 领货校验
 * 校验通过则跳转到领货页面
 * 校验失败则提示
 */
// function sendCreate()
const { send: create } = useRequest(() => Apis.vehsaleusesignInterface.verifyCreateUsingPOST_3({ params: {
  vehSaleEmpGid: (() => skuQueryParams?.vehSaleEmpGid ? skuQueryParams.vehSaleEmpGid : 0)(),
} }), {
  immediate: false,
})

// 清除草稿并更新
async function clearAndUpdate() {
  await clearDraft()
  await create()
  await getDraftInfo()
}

// 在组件挂载时获取状态栏高度和初始化数据
onMounted(async () => {
  // 先获取draftInfo
  // // 然后获取分类数据
  // if (!skuQueryParams.wrhGid) {
  //   warehousePickeEditHeaderRef.value?.openSelect()
  // }
  await getCategoryList()

  if (skuQueryParams) {
    skuQueryParams.sortCode = ''
  }

  // warehouseEmployeePickeEditHeaderRef.value?.openSelect()

  warehousePickeEditHeaderRef.value?.openSelect()
})

// 购物车品项数量
const skuCount = computed(() => {
  return draftInfo.value?.data?.skuCount || 0
})

// 购物车总金额
const total = computed(() => {
  return draftInfo.value?.data?.total || 0
})

// 改变商品数量
async function handleSpecChange(spec: VehSaleGetDraftQpcResponseDTO, sku: VehSaleGetDraftGoodsResponseDTO) {
  const index = skuList.value.findIndex(item => item.goods.gid === sku.goods.gid)
  const oldSku = CommonUtil.deepClone(skuList.value[index])
  skuList.value[index] = CommonUtil.deepClone(sku)

  seleEmp.value.gid = skuQueryParams.vehSaleEmpGid!
  try {
    const result = await submitDraft({
      gdCode: sku.goods.gdCode!,
      goods: {
        gid: sku.goods.gid,
        name: sku.goods.name,
        code: sku.goods.code,
      },
      munit: spec.munit,
      price: spec.price,
      qpc: spec.qpc,
      qpcStr: spec.qpcStr,
      qty: spec.qty,
      qtyStr: spec.qtyStr,
      singlePrice: spec.singlePrice,
      total: spec.singlePrice.multiply(spec.qty).scale(2),
      uuid: spec.uuid,
      version: sku.version,
      // vehSaleEmp: skuQueryParams.vehSaleEmpGid,
      wrh: {
        gid: skuQueryParams.wrhGid!,
      },
    })
    skuList.value[index].version = result.data?.version || skuList.value[index].version
  }
  catch (error) {
    console.log(error)
    skuList.value[index] = oldSku
  }
}

// 前往购物车页面
function goToCart() {
  // 检查购物车是否为空
  if (skuCount.value === 0) {
    globalToast.info('购物车还是空的')
    return
  }
  router.push({ name: 'warehouse-pick-cart', params: {
    wrhGid: `${skuQueryParams.wrhGid}`,
    vehSaleEmpGid: `${skuQueryParams.vehSaleEmpGid}`,
  } })
}

// 提交订单请求
const { send: submit } = useRequest(
  (data: VehSaleUseSignSubmitRequestDTO) => Apis.vehsaleusesignInterface.submitUsingPOST_4({
    data,
  }),
  {
    immediate: false,
    middleware: createGlobalLoadingMiddleware({
      loadingText: '提交中...',
    }),
  },
).onSuccess((resp) => {
  globalLoading.close()
  globalToast.success({
    msg: '提交成功',
    duration: 500,
    closed: () => {
      router.replace({
        name: 'pick-detail',
        params: {
          id: resp.data.data!,
        },
      })
    },
  })
}).onError((err) => {
  globalLoading.close()
  globalToast.error(err.error?.message || '提交失败')
})

const currentEmp = ref<GCN>()
const currentWrh = ref<GCN>()

/**
 * 提交订单
 */
async function handleSubmit() {
  return new Promise<boolean>((resolve) => {
    if (skuCount.value === 0) {
      globalToast.info('请先添加商品')
      resolve(false)
    }
    else {
      showConfirm({
        msg: '确认提交本次领货',
        confirmButtonText: '确认',
        cancelButtonText: '再想想',
        success: (res) => {
          if (res.action === 'confirm') {
            const submitData = {
              vehSaleEmp: currentEmp.value, // userStore.vehSaleEmp,
              wrh: currentWrh.value,
              attachDetails: [],
            }
            verifySubmit(submitData).then((result) => {
              if (!result || !result.data || !result.data.length) {
                submit(submitData)
                resolve(true)
              }
              else {
                resolve(false)
                globalLoading.close()
                globalToast.warning('部分商品库存不足，请调整数量!')
                draftSubmitVerifyRef.value?.open({
                  beforeConfirm: async () => {
                    const res = await handleSubmit()
                    return res
                  },
                })
              }
            }).catch((err) => {
              globalLoading.close()
              globalToast.error(err.error?.message || '校验失败')
              resolve(false)
            })
          }
        },
      })
    }
  })
}

// 跳转到搜索页面
function goToSearch() {
  router.push({ name: 'warehouse-pick-edit-search', params: {
    wrhGid: `${skuQueryParams.wrhGid}`,
    vehSaleEmpGid: `${skuQueryParams.vehSaleEmpGid}`,
  } })
}

// 是否是第一次加载，第一次加载需调用草稿
const isFirstInit = ref(true)
async function changeWrhOrEmp(emp: GCN, wrh: GCN) {
  currentEmp.value = emp
  currentWrh.value = wrh
  skuQueryParams.vehSaleEmpGid = emp.gid | 0
  skuQueryParams.wrhGid = wrh.gid | 0
  if (!seleEmp.value) {
    seleEmp.value = {} as VehSaleEmp
  }

  if (skuQueryParams.vehSaleEmpGid) {
    seleEmp.value.gid = skuQueryParams.vehSaleEmpGid
  }

  if (isFirstInit.value) {
    const draftInfo = await Promise.all([
      getDraftInfo(),
    ])

    if (draftInfo[0].data?.wrh?.gid) {
      const wrhGid = draftInfo[0].data.wrh.gid
      if (wrh.gid?.toString() !== wrhGid?.toString()) {
        // skuQueryParams.wrhGid = wrhGid;
        // 前后两次仓库不一致，清空购物车
        clearAndUpdate()
      }
    }

    console.log('draftInfo', draftInfo)
    isFirstInit.value = false
  }

  handleRefresh()
}
</script>

<template>
  <DraftSubmitVerify ref="draftSubmitVerifyRef" title="请确认商品数量" @opened="lockPage" @closed="unlockPage" @confirm="reload" @cancel="reload">
    <template #body>
      <template v-if="verifySubmitData && verifySubmitData.data">
        <view class="mx-3 mb-3 box-border flex items-center rounded-2 bg-[var(--frequentapplication-orange-background)] px-20rpx py-2 text-26rpx text-[var(--frequentapplication-orange-content)]">
          以下商品库存不足，已自动更新为最大可领货数
        </view>
        <WarehousePickEditSkuCard
          v-for="sku in verifySubmitData.data"
          :key="sku.goods.gid"
          :sku="sku"
          @change="handleSpecChange"
        />
      </template>
    </template>
  </DraftSubmitVerify>
  <!-- <PickEditImport ref="pickEditImportRef" @import="handleImport" @opened="lockPage()" @closed="unlockPage" /> -->
  <view class="pick-edit-page relative box-border min-h-screen w-screen overflow-x-hidden" :style="pageStyle">
    <!-- 自定义导航栏 -->
    <CartNavBar mode="common">
      <template #title>
        <view class="absolute left-50% top-50% w-full flex transform items-center justify-center -translate-x-1/2 -translate-y-1/2">
          <view class="inline-flex items-center justify-center w-auto!" @click="goToSearch">
            <text class="text-lg text-black font-medium">
              领货录入
            </text>
            <text class="i-carbon-search ml-2 text-4 text-black" />
          </view>
        </view>
      </template>
    </CartNavBar>

    <!-- 仓位 和 业务员 -->
    <WarehousePickeEditHeader ref="warehousePickeEditHeaderRef" :current-wrh="skuQueryParams.wrhGid" :current-emp="skuQueryParams.vehSaleEmpGid" :clear-and-update="clearAndUpdate" custom-class="fixed z-2" @submit="changeWrhOrEmp" />
    <!-- 骨架屏 -->
    <WarehousePickEditSkeleton v-if="loading" custom-class="top-[var(--navbar-total-height)]" />
    <view v-else class="main relative z-1 w-screen bg-white">
      <!-- 分类tabs -->
      <view class="fixed z-2 w-full" style="top: calc(var(--navbar-total-height) + 192rpx)">
        <wd-tabs v-model="skuQueryParams.sortCode" custom-class="w-full tabs-custom" @change="handleRefresh">
          <wd-tab
            v-for="(category, index) in categoryListFilter"
            :key="index"
            :title="category.label || ''"
            :name="category.value"
            :badge-props="category.count ? { isDot: true } : undefined"
          />
        </wd-tabs>
        <!-- 头部区域 -->
        <view class="box-border w-full pl-3">
          <EditSort v-model:sorts="skuQueryParams.sorts" @change="handleRefresh" />
        </view>
      </view>

      <!-- 主内容区域 -->
      <view class="box-border flex flex-auto flex-col bg-white">
        <wd-gap height="calc(74rpx + 42px + 36px)" />

        <mescroll-body
          v-if="skuQueryParams.vehSaleEmpGid && skuQueryParams.wrhGid && !categoryLoading"
          :down="downOption"
          :up="upOption"
          @init="mescrollInit"
          @down="downCallback"
          @up="upCallback"
        >
          <view class="mt-15 p-3 pb-0">
            <SkuListSkeleton v-if="!loading && skuPage === 1 && skuListLoading" />
            <template v-else>
              <WarehousePickEditSkuCard
                v-for="sku in skuList"
                :key="sku.goods.gid"
                :sku="sku"
                @change="handleSpecChange"
              />
            </template>
          </view>
        </mescroll-body>
      </view>
    </view>
    <!-- 底部购物车 -->
    <CartBar
      :count="skuCount"
      :amount="total"
      safe-area-bottom fixed
      button-text="提交"
      @click-cart="goToCart"
      @click-button="handleSubmit"
    />
  </view>
</template>

<style lang="scss" scoped>
.pick-edit-page {
  min-height: 100vh;
  padding-bottom: 120rpx;
  padding-bottom: calc(120rpx + const(safe-area-inset-bottom)) !important;
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom)) !important;

   :deep(.mescroll-body) {
    min-height: calc(100vh - var(--navbar-total-height) - 192rpx - 42px - 36px - 120rpx) !important;
    min-height: calc(100vh - var(--navbar-total-height) - 192rpx - 42px - 36px - 120rpx - const(safe-area-inset-bottom)) !important;
    min-height: calc(100vh - var(--navbar-total-height) - 192rpx - 42px - 36px - 120rpx - env(safe-area-inset-bottom)) !important;
  }

  // tabs样式
  :deep(.tabs-custom){
    // 修复徽标导致文字溢出的问题
    .wd-tabs__nav-item-badge {
      display: flex;
      max-width: 100%;
      min-width: 0;

      .wd-badge {
        display: flex;
        max-width: 100%;
        min-width: 0;

        .wd-tabs__nav-item-text {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          flex: 1;
          min-width: 0;
        }
      }
    }
  }
}
</style>

<route lang="json">
{
  "name": "warehouse-pick-edit",
  "style": {
    "navigationStyle": "custom",
    "navigationBarTitleText": "全部商品",
    "backgroundColor": "#F9F9F9"
  },
  "meta": {
    "permissions": ["vehSaleUseSignCreate"]
  }
}
</route>
