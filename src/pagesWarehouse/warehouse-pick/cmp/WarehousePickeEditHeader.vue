<!--
 * @Author: weish<PERSON>
 * @Date: 2025-05-20 16:44:22
 * @LastEditTime: 2025-06-19 18:04:20
 * @LastEditors: weisheng
 * @Description:
 * @FilePath: /lsym-cx-mini/src/pagesWarehouse/warehouse-pick/cmp/WarehousePickeEditHeader.vue
 * 记得注释
-->
<script setup lang="ts">
import type { SelectPickerBeforeConfirm, SelectPickerInstance } from 'wot-design-uni/components/wd-select-picker/types'
import type { GCN } from '@/api/globals'

const props = defineProps({
  // 当前业务员
  currentEmp: {
    type: [Number, String],
    default: '',
  },
  // 当前仓位
  currentWrh: {
    type: [Number, String],
    default: '',
  },
  // 自定义类名
  customClass: {
    type: String,
    default: '',
  },
  // 自定义样式
  customStyle: {
    type: String,
    default: '',
  },
  // 清空草稿
  clearAndUpdate: {
    type: Function,
  },
})

const emit = defineEmits<{
  (e: 'submit', emp: GCN, wrh: GCN): void
}>()

const { confirm: showConfirm } = useGlobalMessage()
const globalLoading = useGlobalLoading()
const globalToast = useGlobalToast()

const { currentWms } = useWmsStore()

const { wrhId, wrhList, wrhLoading, currentWrh } = useWrhSelect(void 0, currentWms?.gid)

const wrhListData = computed(() => {
  return wrhList.value.filter(item => !!item.value)
})

const wrhSelectRef = ref<SelectPickerInstance>()

// 添加状态跟踪门店弹框是否由业务员选择触发
const isStoreOpenedByEmpSelection = ref(false)

// const confirmedWrhId = ref<string | number>(props.currentWrh)

// const currentWrh = computed(() => {
//   return wrhList.value.find(item => item.value === confirmedWrhId.value)
// })

const { empList, empLoading, currentEmp, empId } = useVehSaleEmpSelect(currentWms?.gid)
const confirmedEmpId = ref<string | number>(props.currentEmp)
const empSelectRef = ref<SelectPickerInstance>()
const empListData = computed(() => {
  return empList.value.filter(item => item.label !== '全部业务员')
})

const onlyOneWrh = computed(() => {
  return wrhListData.value.length === 1
})

function handleSelectEmp(event: { value: string, selectedItems: { label: string, value: string } }) {
  confirmedEmpId.value = event.value
  empId.value = event.value
  // 如果只有一个仓位，直接设置为选择该仓位
  if (onlyOneWrh.value) {
    // confirmedWrhId.value = wrhListData.value[0].value
    // handleSelect({ value: wrhListData.value[0].value as string, selectedItems: { label: wrhListData.value[0].label, value: wrhListData.value[0].value as string } })
    handleSelect({
      value: wrhListData.value[0].value as string,
      selectedItems: {
        label: wrhListData.value[0].label,
        value: wrhListData.value[0].value as string,
        name: wrhListData.value[0].name,
        code: wrhListData.value[0].code,
      },
    })
  }
  else {
    wrhSelectRef.value?.open()
    // 标记门店弹框是由业务员选择触发的
    isStoreOpenedByEmpSelection.value = true
  }
}

function handleSelect(event: { value: string, selectedItems: { label: string, value: string, name: string, code: string } }) {
  // 重置标记
  isStoreOpenedByEmpSelection.value = false
  const currentEmpOrigin: GCN = {
    code: currentEmp.value?.code,
    gid: currentEmp.value?.value,
    name: currentEmp.value?.name,
  } as unknown as GCN

  const currentWrhOrigin: GCN = {
    code: event.selectedItems?.code,
    gid: event.selectedItems?.value as any as number,
    name: event.selectedItems?.name,
  } as GCN

  emit('submit', currentEmpOrigin, currentWrhOrigin)
}

const beforeConfirm: SelectPickerBeforeConfirm = (value, resolve) => {
  if (!value) {
    globalToast.warning('请选择仓位')
    resolve(false)
  }

  else if (value !== wrhId.value && wrhId.value) {
    showConfirm({
      title: '提示',
      msg: '切换仓位将会清空购物车，确认切换吗？',
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      async success(res) {
        if (res.action === 'confirm') {
          try {
            if (CommonUtil.isFunction(props.clearAndUpdate)) {
              await props.clearAndUpdate()
            }
            resolve(true)
            globalLoading.close()
            globalToast.success(`已切换至${currentWrh.value?.label}仓位`)
          }
          catch (error) {
            resolve(false)
            globalLoading.close()
            globalToast.error('切换仓位失败')
          }
        }
        else {
          resolve(false)
        }
      },
    })
  }
  else {
    resolve(true)
  }
}

const beforeEmpConfirm: SelectPickerBeforeConfirm = (value, resolve) => {
  if (!value) {
    globalToast.warning('请选择业务员')
    resolve(false)
  }
  else if (value !== confirmedEmpId.value && confirmedEmpId.value) {
    showConfirm({
      title: '提示',
      msg: '切换业务员将会清空购物车，确认切换吗？',
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      async success(res) {
        if (res.action === 'confirm') {
          try {
            if (CommonUtil.isFunction(props.clearAndUpdate)) {
              await props.clearAndUpdate()
            }
            resolve(true)
            globalLoading.close()
            globalToast.success(`已切换至${currentEmp.value?.label}业务员`)
          }
          catch (error) {
            resolve(false)
            globalLoading.close()
            globalToast.error('切换业务员失败')
          }
        }
        else {
          resolve(false)
        }
      },
    })
  }
  else {
    resolve(true)
  }
}

watch(() => props.currentWrh, (newVal) => {
  if (!CommonUtil.isEqual(newVal, wrhId.value)) {
    wrhId.value = newVal
  }
})

// 处理业务员弹框关闭
function handleEmpClose() {
  // 如果已经有完整的业务员和门店选择，恢复原来的状态
  if (props.currentEmp && props.currentWrh) {
    confirmedEmpId.value = props.currentEmp
    wrhId.value = props.currentWrh
    empId.value = String(props.currentEmp)
  }
  else {
    // 如果没有完整选择，重新弹出业务员选择弹框
    nextTick(() => {
      globalToast.warning('请选择业务员')
      empSelectRef.value?.open()
    })
  }
}

// 处理仓位弹框关闭
function handleWrhClose() {
  // 如果门店弹框是由业务员选择触发的，则重新打开业务员选择弹框
  if (isStoreOpenedByEmpSelection.value) {
    nextTick(() => {
      empSelectRef.value?.open()
    })
  }
  // 重置标记
  isStoreOpenedByEmpSelection.value = false
}

// 直接打开仓位选择
function handleDirectWrhOpen() {
  // 标记为非业务员选择触发
  isStoreOpenedByEmpSelection.value = false
  wrhSelectRef.value?.open()
}

defineExpose({
  openSelect: () => {
    // 先选择业务员
    empSelectRef.value?.open()
  },
})
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <view class="pick-edit-header z-3 box-border h-25 w-full bg-[var(--greyapplication-bottomlayer)]" :style="customStyle" :class="customClass">
    <view class="px-28rpx">
      <wd-select-picker
        ref="empSelectRef"
        v-model="confirmedEmpId"
        :close-on-click-modal="false"
        :columns="empListData"
        :loading="empLoading"
        title="选择业务员"
        filter-placeholder="业务员名称/代码"
        label="业务员"
        type="radio"

        filterable use-default-slot hide-label
        :before-confirm="beforeEmpConfirm"
        @confirm="handleSelectEmp"
        @cancel="handleEmpClose"
      >
        <template #default>
          <view class="h-88rpx flex items-center justify-between text-28rpx text-[var(--textapplication-text-2)]">
            <view class="w-[calc(100%-60rpx)] flex">
              <view class="w-20 shrink-0">
                <text class="i-carbon-user mr-1" />
                <text class="mr-2">
                  业务员
                </text>
              </view>
              <text class="text-overflow-ellipsis mr-2 w-[calc(100%-160rpx)] flex-1 truncate text-[var(--textapplication-text-1)]">
                {{ currentEmp?.label || '请选择业务员' }}
              </text>
            </view>
            <text class="i-carbon-chevron-right text-[var(--textapplication-text-1)]" />
          </view>
        </template>
      </wd-select-picker>

      <wd-select-picker
        ref="wrhSelectRef"
        v-model="wrhId"
        :close-on-click-modal="false"
        :columns="wrhListData"
        :loading="wrhLoading"
        title="选择仓位"
        label="仓位"
        type="radio"
        hide-label
        filterable
        filter-placeholder="仓位名称/代码"
        use-default-slot
        :before-confirm="beforeConfirm"
        class="h-11"
        :disabled="onlyOneWrh"
        @confirm="handleSelect"
        @cancel="handleWrhClose"
      >
        <template #default>
          <view class="h-88rpx flex items-center justify-between text-28rpx text-[var(--textapplication-text-2)]" @click="handleDirectWrhOpen">
            <view class="w-[calc(100%-60rpx)] flex">
              <view class="w-20 shrink-0">
                <text class="i-carbon-location mr-1" />
                <text class="mr-2">
                  仓位
                </text>
              </view>
              <text class="text-overflow-ellipsis mr-2 truncate text-[var(--textapplication-text-1)]">
                {{ currentWrh?.label || '请选择仓位' }}
              </text>
            </view>
            <text class="i-carbon-chevron-right text-[var(--textapplication-text-1)]" />
          </view>
        </template>
      </wd-select-picker>
    </view>

    <view class="pick-edit-header-bottom h-1 w-full" />
  </view>
</template>

<style lang="scss" scoped>
.pick-edit-header {
  background-color: #fff;

  :deep() {
    // .wd-action-sheet__close {
    //   display: none;
    // }

    .wd-select-picker__header {
      height: 112rpx;
      line-height: 112rpx;
    }
  }

  &-bottom {
    background: var(--wot-sidebar-bg, var(--wot-color-gray-1, #f7f8fa));
  }

  .text-overflow-ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  :deep() {
    .wd-radio__label {
      width: calc(100% - 60rpx);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      text-align: left;
    }
  }
}
</style>
