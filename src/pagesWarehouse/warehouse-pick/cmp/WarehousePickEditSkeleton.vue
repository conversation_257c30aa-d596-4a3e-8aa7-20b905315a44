<!--
 * @Author: weisheng
 * @Date: 2025-05-21 11:04:06
 * @LastEditTime: 2025-05-24 18:30:45
 * @LastEditors: weisheng
 * @Description:
 * @FilePath: /lsym-cx-mini/src/pagesSalesMan/pick/cmp/PickEditSkeleton.vue
 * 记得注释
-->
<!--
 * @Description: 领货单编辑页骨架屏组件
-->
<script setup lang="ts">
// 组件不需要任何props或状态
defineProps({
  customClass: {
    type: String,
    default: '',
  },
  customStyle: {
    type: String,
    default: '',
  },
})
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <view class="pick-edit-skeleton flex">
    <!-- 侧边分类栏骨架屏 -->
    <view class="main-sidebar w-96px bg-[#F9F9F9]">
      <view class="flex flex-col gap-2 p-2">
        <wd-skeleton v-for="i in 8" :key="i" :row-col="[{ width: '100%', height: '60px' }]" animation="flashed" />
      </view>
    </view>

    <!-- 主内容区域骨架屏 -->
    <view class="w-[calc(100vw-96px)] flex flex-auto flex-col">
      <!-- 头部排序工具栏骨架屏 -->
      <view class="header-section z-1 bg-white">
        <view class="sort-toolbar flex items-center justify-end border-t border-[#E5E5E5] px-3 py-2">
          <wd-skeleton :row-col="[{ width: '60px', height: '20px' }]" animation="flashed" />
          <wd-skeleton :row-col="[{ width: '60px', height: '20px' }]" animation="flashed" />
        </view>
      </view>

      <!-- 商品列表骨架屏 -->
      <view class="flex-1 p-3">
        <view v-for="i in 3" :key="i" class="mb-3">
          <!-- 商品卡片骨架屏 -->
          <view class="rounded-lg bg-white p-3">
            <!-- 商品基本信息 -->
            <view class="flex items-start gap-3">
              <wd-skeleton :row-col="[{ width: '120px', height: '120px' }]" animation="flashed" />
              <view class="flex-1">
                <wd-skeleton :row-col="[{ width: '80%', height: '20px' }, { width: '60%', height: '16px' }, { width: '40%', height: '16px' }]" animation="flashed" />
              </view>
            </view>
            <!-- 商品规格 -->
            <view class="mt-3">
              <wd-skeleton :row-col="[{ width: '100%', height: '40px' }]" animation="flashed" />
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.pick-edit-skeleton {
  width: 100vw;
  min-height: 100vh;
  background-color: #F9F9F9;
}
</style>
