<!--
 * @Author: weish<PERSON>
 * @Date: 2025-04-20 20:11:51
 * @LastEditTime: 2025-06-25 16:36:50
 * @LastEditors: weisheng
 * @Description:
 * @FilePath: /lsym-cx-mini/src/pagesWarehouse/back-confirm/cmp/BackConfirmCard.vue
 * 记得注释
-->
<script setup lang="ts">
import type { VehSaleUseSignBckQueryResponseDTO } from '@/api/globals'

const props = defineProps({
  record: {
    type: Object as PropType<VehSaleUseSignBckQueryResponseDTO>,
    default: () => ({}),
    required: true,
  },
})

const emit = defineEmits(['click'])

// 点击卡片
function handleClick() {
  emit('click', props.record.num)
}
</script>

<template>
  <view class="record-card mb-2 w-[calc(100%-48rpx)] rounded-lg bg-white p-3" @click="handleClick">
    <!-- 单号 -->
    <view class="mb-3 flex items-center justify-between">
      <text class="text-4 text-[#2D2D2D] font-medium">
        {{ `${record.vehSaleEmpName}[${record.vehSaleEmp?.code}]` }}
      </text>
    </view>

    <!-- 基本信息 -->
    <view class="mb-1 flex flex-wrap">
      <view class="mb-2 min-w-[50%] flex break-all">
        <text class="flex-none text-3 text-[#8A8A8A]">
          出货仓位：
        </text>
        <text class="text-3 text-[#2D2D2D]">
          {{ record.wrh?.name }}
        </text>
      </view>

      <view class="mb-2 min-w-[50%] flex break-all">
        <text class="flex-none text-3 text-[#8A8A8A]">
          单号：
        </text>
        <text class="text-3 text-[#2D2D2D]">
          {{ record.num }}
        </text>
      </view>

      <view class="mb-2 min-w-[50%] flex break-all">
        <text class="flex-none text-3 text-[#8A8A8A]">
          操作时间：
        </text>
        <text class="text-3 text-[#2D2D2D] font-500">
          {{ formatDate(record.lstupdTime) }}
        </text>
      </view>
    </view>

    <!-- 统计信息 -->
    <view class="flex items-center justify-between rounded bg-[#F5F5F5] px-3 py-1">
      <view class="flex items-center">
        <text class="text-28rpx text-[#8A8A8A]">
          共
        </text>
        <text class="mx-1 text-4 text-[#1A1A1A] font-medium">
          {{ record.goodsCount }}
        </text>
        <text class="text-28rpx text-[#8A8A8A]">
          种商品
        </text>
      </view>

      <view class="flex items-center">
        <text class="text-28rpx text-[#8A8A8A]">
          共
        </text>
        <view class="mx-1 flex items-center">
          <text class="text-3 text-[#8A8A8A]">
            ¥
          </text>
          <text class="text-4 text-[#1A1A1A] font-medium">
            {{ record.total }}
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped>
</style>
