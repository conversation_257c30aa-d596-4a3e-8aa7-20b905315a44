<script setup lang="ts">
import { callInterceptor } from 'wot-design-uni/components/common/interceptor'
import DetailTableHeader from '@/business/DetailTableHeader.vue'
import type { VehSaleUseSignArvDtlResponseDTO } from '@/api/globals'

interface Props {
  title?: string
}

withDefaults(defineProps<Props>(), {
  title: '回货商品差异清单',
})

const emit = defineEmits(['opened', 'closed', 'confirm', 'cancel'])

type BeforeConfirm = () => boolean | Promise<boolean>

interface OpenOptions {
  beforeConfirm?: BeforeConfirm
  diffSkus: VehSaleUseSignArvDtlResponseDTO[]
  diffCount: number
  diffAmount: number
}

const state = reactive({
  show: false, // 是否打开
  beforeConfirm: null as BeforeConfirm | null, // 确认前回调
  diffSkus: [] as VehSaleUseSignArvDtlResponseDTO[], // 差异商品列表
  diffCount: 0, // 差异品项数
  diffAmount: 0, // 差异总金额
})

// 表头配置
const tableHeaders = ref([
  { title: '商品信息', field: 'goods', sortable: false, asc: 0 as 0 | 1 | -1 },
  { title: '差异信息', field: 'diff', sortable: false, asc: 0 as 0 | 1 | -1 },
])

function open(options: OpenOptions) {
  state.beforeConfirm = options.beforeConfirm || null
  state.diffSkus = options.diffSkus
  state.diffCount = options.diffCount
  state.diffAmount = options.diffAmount
  state.show = true
}

function close() {
  state.show = false
  // 清空数据
  state.diffSkus = []
  state.diffCount = 0
  state.diffAmount = 0
  state.beforeConfirm = null
}

function handleCancel() {
  close()
  emit('cancel')
}

/**
 * 确认
 */
function handleConfirm() {
  if (state.beforeConfirm) {
    callInterceptor(() => state.beforeConfirm?.(), {
      done: () => {
        close()
        emit('confirm')
      },
    })
  }
  else {
    close()
    emit('confirm')
  }
}

/**
 * 计算差异信息
 */
function getDiffInfo(sku: VehSaleUseSignArvDtlResponseDTO) {
  const diff = Number(sku.qty).add(Number(sku.tgpQty)).minus(Number(sku.shouldQty))
  const diffAmount = diff.multiply(sku.price || 0).divide(sku.qpc || 1).scale(2)

  if (diff > 0) {
    return `多${Math.abs(diff)}/计¥${Math.abs(diffAmount)}`
  }
  else if (diff < 0) {
    return `少${Math.abs(diff)}${sku.minMunit}/计¥${Math.abs(diffAmount)}`
  }
  return ''
}

defineExpose({
  open,
  close,
})
</script>

<template>
  <wd-action-sheet
    v-model="state.show"
    :title="title"
    :z-index="9999"
    @opened="emit('opened')"
    @closed="emit('closed')"
    @close="handleCancel"
  >
    <!-- 提示信息 -->
    <view class="mx-3 mt-2 border border-white/60 rounded-[0px] bg-[#FFF6EB] p-2 backdrop-blur-[6px]">
      <view class="flex items-center">
        <view class="mr-2 h-4 w-4 flex items-center justify-center rounded-full bg-[#F86E21]">
          <text class="text-26rpx text-white">
            !
          </text>
        </view>
        <text class="text-28rpx text-[#636D78]">
          确认后将按照如下信息生成差异单，请再次核对。
        </text>
      </view>
    </view>

    <!-- 差异统计卡片 -->
    <view class="mx-3 mt-2 flex">
      <view class="mr-3 flex-1 border border-[#E6E6E6] rounded-md bg-white p-3">
        <view class="mb-1.5 flex items-center">
          <text class="text-28rpx text-[#636D78]">
            差异品项数
          </text>
        </view>
        <text class="text-xl text-[#343A40] font-bold">
          {{ state.diffCount }}
        </text>
      </view>
      <view class="flex-1 border border-[#E6E6E6] rounded-md bg-white p-3">
        <view class="mb-1.5 flex items-center">
          <text class="text-28rpx text-[#636D78]">
            差异总金额
          </text>
        </view>
        <text class="text-xl text-[#343A40] font-bold">
          ¥{{ Math.abs(state.diffAmount) }}
        </text>
      </view>
    </view>

    <!-- 表头 -->
    <view class="mx-3 mt-2">
      <DetailTableHeader
        :headers="tableHeaders"
        :show-title="false"
      />
    </view>

    <!-- 差异商品列表 -->
    <scroll-view class="box-border h-36vh w-full px-3" scroll-y>
      <view
        v-for="(sku, index) in state.diffSkus"
        :key="sku.gdCode || index"
        class="rounded-lg bg-white p-3"
        :class="{ 'mb-2': index < state.diffSkus.length - 1 }"
      >
        <!-- 商品卡片：上中下结构 -->
        <view class="flex flex-col">
          <!-- 上部分：商品名称 -->
          <text class="line-clamp-2 mb-1.5 break-all text-28rpx text-[#2D2D2D] font-medium">
            {{ sku.goods?.name || '未知商品' }}
          </text>

          <!-- 中部分：商品标签和价格信息 -->
          <view class="mb-1.5 flex">
            <view class="mr-2 flex items-center break-all rounded bg-[#F5F6F7] px-1">
              <text class="text-26rpx text-[#5C5C5C]">
                {{ sku.gdCode }}
              </text>
            </view>
            <view class="flex flex-none items-center">
              <text class="mr-0.5 text-28rpx text-[#F57F00]">
                ¥
              </text>
              <text class="text-36rpx text-[#F57F00] font-medium">
                {{ Number(sku.price).divide(sku.qpc || 1).scale(4) }}
              </text>
              <text class="text-26rpx text-[#8A8A8A]">
                /{{ sku.minMunit }}
              </text>
            </view>
          </view>

          <!-- 下部分：差异信息 -->
          <view class="flex items-center justify-between">
            <text class="text-28rpx text-[#2D2D2D]">
              差异量/差异额
            </text>
            <text class="text-28rpx text-[#F14646] font-medium">
              {{ getDiffInfo(sku) }}
            </text>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 底部按钮 -->
    <view class="flex items-center justify-between bg-white px-3 py-3">
      <wd-button
        type="primary"
        plain
        :round="false"
        size="large"
        custom-class="flex-1 mr-2!"
        @click="handleCancel"
      >
        上一步
      </wd-button>
      <wd-button
        type="primary"
        :round="false"
        size="large"
        custom-class="flex-1"
        @click="handleConfirm"
      >
        确认回货
      </wd-button>
    </view>
  </wd-action-sheet>
</template>

<style scoped lang="scss">
</style>
