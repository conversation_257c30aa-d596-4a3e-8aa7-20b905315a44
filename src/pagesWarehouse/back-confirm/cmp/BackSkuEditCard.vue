<script setup lang="ts">
import { deepClone, isDef, isEqual } from 'wot-design-uni/components/common/util'
import type { VehSaleUseSignArvDtlResponseDTO } from '@/api/globals'

const props = defineProps({
  sku: {
    type: Object as PropType<VehSaleUseSignArvDtlResponseDTO>,
    required: true,
  },
})

const emit = defineEmits<{
  'update:sku': [{ sku: VehSaleUseSignArvDtlResponseDTO, isModified: boolean }]
}>()

// 商品数据的本地副本
const innerSku = ref<VehSaleUseSignArvDtlResponseDTO>(deepClone(props.sku))

// 整件数量（规格数量）
const wholeQty = ref(getMNQty()[0])
// 单品数量（最小规格数量）
const singleQty = ref(getMNQty()[1])

function getMNQty() {
  const qtyStr = innerSku.value.qtyStr
  if (!qtyStr) {
    return [0, 0]
  }
  const [whole, single] = qtyStr.split('+')
  return [Number(whole || 0), Number(single || 0)]
}

const singleMax = computed(() => {
  // 取整
  return Number((innerSku.value.shouldQty || 0).minus(wholeQty.value.multiply((innerSku.value.qpc || 0)))).minus(innerSku.value.tgpQty || 0).floorScale(0)
})

const wholeMax = computed(() => {
  return Number((innerSku.value.shouldQty || 0).minus(singleQty.value).minus(innerSku.value.tgpQty || 0).divide((innerSku.value.qpc || 1))).floorScale(0)
})

/**
 * 兑奖物数量最大值
 */
const tgpQtyMax = computed(() => {
  return Number((innerSku.value.shouldQty || 0).minus(singleQty.value).minus(wholeQty.value.multiply((innerSku.value.qpc || 0)))).floorScale(0)
})

// 原始数据
const originData = ref(deepClone(props.sku))

// 是否修改
const isModified = computed(() => {
  return !isEqual(innerSku.value, originData.value)
})

// 监听props变化，同步到本地数据
watch(() => props.sku, (newSku) => {
  if (isEqual(innerSku.value, newSku)) {
    return
  }
  innerSku.value = newSku
}, { deep: true })

/**
 * 处理整件数量变化
 */
function handleWholeQtyChange({ value }: { value: number }) {
  wholeQty.value = value
  updateQty()
}

/**
 * 处理单品数量变化
 */
function handleSingleQtyChange({ value }: { value: number }) {
  singleQty.value = value
  updateQty()
}

/**
 * 处理兑奖物数量变化
 */
function handleGiftQtyChange({ value }: { value: number }) {
  innerSku.value.tgpQty = value || 0
  emit('update:sku', { sku: innerSku.value, isModified: isModified.value })
}

/**
 * 更新数量
 */
function updateQty() {
  innerSku.value.qty = Number((wholeQty.value * (innerSku.value.qpc || 0) + singleQty.value).toFixed(4))
  innerSku.value.qtyStr = `${wholeQty.value}+${singleQty.value}`
  emit('update:sku', { sku: innerSku.value, isModified: isModified.value })
}

/**
 * 商品图片列表
 */
const skuImgList = computed(() => {
  const imgList = props.sku.imageDetails && props.sku.imageDetails.length > 0 ? props.sku.imageDetails.map(item => item.fileUrl) : []
  return imgList.filter(item => isDef(item))
})

/**
 * 商品主图
 */
const skuMainImg = computed(() => {
  return skuImgList.value.length > 0 ? skuImgList.value[0] : ''
})

/**
 * 计算差异显示
 */
function getDifferenceText(): { total: string, diff: string } {
  const shouldQty = innerSku.value.shouldQty || 0
  const actualQty = innerSku.value.qty || 0
  const tgpQty = innerSku.value.tgpQty || 0
  const difference = actualQty.add(tgpQty).minus(shouldQty).scale(4)
  return {
    total: `共${actualQty.add(tgpQty)}${innerSku.value.minMunit}`,
    diff: difference === 0 ? '' : difference > 0 ? `多${difference}${innerSku.value.minMunit}` : `少${Math.abs(difference)}${innerSku.value.minMunit}`,
  }
}

/**
 * 检查是否含有兑奖物
 */
const hasGiftItem = computed(() => {
  return !!innerSku.value.tgpQty
})
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <view class="back-sku-edit-card rounded-lg bg-white p-3">
    <view class="flex">
      <!-- 商品信息 -->
      <view class="flex-1">
        <view class="mb-2 flex items-center">
          <!-- 商品图片 -->
          <SkuImage
            :src="skuMainImg"
            :preview-src="skuImgList"
            mode="aspectFill"
            custom-class="mr-3 flex-none"
          />
          <view class="flex-auto">
            <!-- 商品名称 -->
            <text class="line-clamp-2 break-all text-28rpx text-[#2D2D2D] font-medium">
              {{ innerSku.goods?.name || '--' }}
            </text>

            <!-- 商品标签 -->
            <view class="mt-1 flex flex-wrap">
              <view class="break-all rounded bg-[#F5F6F7] px-2 py-1">
                <text class="text-26rpx text-[#5C5C5C]">
                  {{ innerSku.gdCode }}
                </text>
              </view>
              <!-- <view class="ml-2 rounded bg-[#F5F6F7] px-2 py-1">
                <text class="text-26rpx text-[#5C5C5C]">
                  {{ innerSku.qpcStr }}
                </text>
              </view> -->
              <view v-if="hasGiftItem" class="ml-2 rounded bg-[#FFF5F5] px-2 py-1">
                <text class="text-26rpx text-[#F14646]">
                  含兑奖物
                </text>
              </view>
            </view>
          </view>
        </view>

        <!-- 价格和应回信息 -->
        <view class="flex items-center">
          <!-- 价格 -->
          <view class="w-1/3 flex items-center">
            <text class="text-26rpx text-[#F57F00]">
              ¥
            </text>
            <text class="text-lg text-[#F57F00] font-medium">
              {{ innerSku.price }}
            </text>
            <text class="text-26rpx text-[#8A8A8A]">
              /{{ innerSku.munit }}
            </text>
          </view>

          <!-- 应回数量 -->
          <view class="w-1/3 text-center">
            <text class="text-center text-26rpx text-[#2D2D2D] font-medium">
              {{ getQpcQty(innerSku.shouldQtyStr!, innerSku.munit!, innerSku.minMunit!) }}
            </text>
          </view>
        </view>

        <!-- 数量编辑区域 -->
        <view class="mt-2 flex flex-col items-end">
          <!-- 整件数量编辑 -->
          <view class="mb-2 flex items-center justify-between">
            <view class="flex items-center">
              <text class="mr-2 text-26rpx text-[#8A8A8A]">
                {{ innerSku.qpcStr }}
              </text>
              <wd-input-number
                :model-value="wholeQty"
                :min="0"
                :max="wholeMax"
                :step="1"
                input-width="100%"
                custom-class="input-number-adjust"
                @change="handleWholeQtyChange"
              />
            </view>
          </view>

          <!-- 单品数量编辑 -->
          <view v-if="innerSku.qpcStr !== '1*1'" class="mb-2 flex items-center justify-between">
            <view class="flex items-center">
              <text class="mr-2 text-26rpx text-[#8A8A8A]">
                1*1
              </text>
              <wd-input-number
                :model-value="singleQty"
                :min="0"
                :max="singleMax"
                :step="1"
                input-width="100%"
                custom-class="input-number-adjust"
                @change="handleSingleQtyChange"
              />
            </view>
          </view>

          <!-- 兑奖物数量编辑（如果有的话） -->
          <view class="mb-2 flex items-center justify-between">
            <view class="flex items-center">
              <text class="mr-2 text-26rpx text-[#8A8A8A]">
                兑奖物
              </text>
              <wd-input-number
                :model-value="innerSku.tgpQty!"
                :min="0"
                :max="tgpQtyMax"
                :step="1"
                input-width="100%"
                custom-class="input-number-adjust"
                @change="handleGiftQtyChange"
              />
            </view>
          </view>

          <!-- 总数显示和差异 -->
          <view class="flex items-center justify-end">
            <text class="text-26rpx text-[#1C64FD] font-medium">
              {{ getDifferenceText().total }}
            </text>
            <text v-if="getDifferenceText().diff" class="text-26rpx text-[#F14646] font-medium">
              {{ getDifferenceText().diff }}
            </text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.back-sku-edit-card {

}

// 自定义数量输入步进器样式
:deep(.input-number-adjust) {
  // 按钮
  .wd-input-number__action {
    width: 56rpx;
    height: 56rpx;
  }
  // 输入框
  .wd-input-number__inner{
    width: 100rpx;
    height: 56rpx;
    .wd-input-number__input{
      height: 100%;
      font-size: 30rpx;
      font-weight: 500;
    }
  }
}
</style>
