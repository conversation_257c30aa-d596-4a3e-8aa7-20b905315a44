<script setup lang="ts">
import { deepClone } from 'wot-design-uni/components/common/util'
import DetailSkeleton from '../pick-confirm/cmp/DetailSkeleton.vue'
import BackSkuEditCard from './cmp/BackSkuEditCard.vue'
import BackDiffConfirm from './cmp/BackDiffConfirm.vue'
import { createGlobalLoadingMiddleware } from '@/api/core/middleware'
import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
import type { QuerySort, VehSaleUseSignArvAuditRequestDTO, VehSaleUseSignArvDtlResponseDTO } from '@/api/globals'
import type { SortItem } from '@/composables/useDraft'

const { mescrollInit, getMescroll, downCallback } = useMescroll(onPageScroll, onReachBottom)
const { success: showSuccess, error: showError } = useGlobalToast()
const globalLoading = useGlobalLoading()
const { confirm: showConfirm } = useGlobalMessage()
const router = useRouter()

const { emitRefreshData: emitRefreshBackConfirmList } = useSendRefreshData('back-confirm-list') // 刷新列表，本页面提交成功需要刷新列表

const id = ref<string>('')

// 获取单头详情
const {
  data: order,
  loading: isLoading,
  send: loadOrderDetail,
} = useRequest(
  (num: string) => Apis.vehsaleusesignarvInterface.getUsingGET_2({
    params: {
      num,
      fetchDetail: false,
    },
  }),
  {
    immediate: false,
    middleware: createGlobalLoadingMiddleware({
      loadingText: '加载中...',
    }),
  },
).onError((error) => {
  showError(error.error.message || '获取回货单详情失败')
})

// 单头详情
const orderDetail = computed(() => {
  return order.value?.data
})

// 获取分类相关数据和方法
const { categoryList: categoryListData, categoryId } = useCategorySelect()

// 表头配置
const tableHeaders = ref([
  { title: '价格', field: 'price' },
  { title: '应回', field: 'shouldQty', sortable: true, asc: 0 as 0 | 1 | -1 },
  { title: '实回', field: 'qty', sortable: true, asc: 0 as 0 | 1 | -1 },
])

// 当前排序条件
const currentSort = ref<SortItem[]>([])

// 存储修改过的商品数据
const modifiedSkus = ref<VehSaleUseSignArvDtlResponseDTO[]>([])

// 处理分类列表，添加数量显示
const categoryList = computed(() => {
  return categoryListData.value.map((item) => {
    // 计算该分类下有输入数量的商品数
    let count = 0
    if (item.code === '') {
      // 全部分类：统计所有有修改的商品
      count = modifiedSkus.value.filter(sku =>
        (sku.qty || 0) > 0 || (sku.tgpQty || 0) > 0,
      ).length
    }
    else {
      // 具体分类：统计该分类下有修改的商品
      count = modifiedSkus.value.filter(sku =>
        sku.category?.code === item.code
        && ((sku.qty || 0) > 0 || (sku.tgpQty || 0) > 0),
      ).length
    }
    return {
      label: item.label,
      value: item.value,
      code: item.code,
      count,
    }
  })
})

// 差异弹窗引用
const diffConfirmRef = ref<InstanceType<typeof BackDiffConfirm>>()

/**
 * 是否存在差异
 */
const hasDiff = computed(() => {
  return modifiedSkus.value.some(item => Number(item.qty).add(item.tgpQty || 0) !== Number(item.shouldQty))
})

/**
 * 获取存在差异的商品
 */
const diffSkus = computed(() => {
  return modifiedSkus.value.filter(item => Number(item.qty).add(item.tgpQty || 0) !== Number(item.shouldQty))
})

/**
 * 计算差异统计
 */
const diffStats = computed(() => {
  const skus = diffSkus.value
  const count = skus.length
  const amount = skus.reduce((sum, sku) => {
    const diff = (sku.shouldQty || 0).minus(sku.qty || 0).minus(sku.tgpQty || 0)
    const price = sku.price || 0
    return sum.add(diff.multiply(price).divide(sku.qpc || 1))
  }, 0)

  return { count, amount: amount.scale(2) }
})

// 分页查询商品
const {
  // 数据列表
  data: skuList,
  reload: reloadSku,
  total,
  page,
} = usePagination(
  page => Apis.vehsaleusesignarvInterface.getDetailsUsingPOST({
    data: {
      num: id.value!,
      page: page - 1, // 后端页码从0开始
      pageSize: 10,
      sorts: getSortParams(), // 添加排序参数
      category: categoryId.value
        ? {
            code: categoryId.value,
          }
        : undefined,
    },
  }),
  {
    immediate: false,
    append: true,
    initialData: [],
    initialPageSize: 10,
    initialPage: 1,
    total: response => response.total,
    data: (response) => {
      const data = response.data || []
      // 与modifiedSkus中的数据进行同步
      if (modifiedSkus.value.length > 0) {
        data.forEach((item) => {
          // 查找是否有修改过的相同商品
          const modifiedItem = modifiedSkus.value.find(
            modified =>
              modified.goods?.gid === item.goods?.gid,
          )
          // 如果找到已修改的商品，则更新其数量
          if (modifiedItem) {
            item.qty = modifiedItem.qty
            item.tgpQty = modifiedItem.tgpQty
          }
        })
      }

      return data
    },
  },
).onSuccess((resp) => {
  getMescroll().endBySize(resp.data.data?.length || 0, total.value)
})
  .onError((error) => {
    showError(error.error?.message || '加载回货确认列表失败')
    getMescroll().endErr()
  })

/**
 * 获取排序参数
 */
function getSortParams() {
  const sortParams: QuerySort[] = []

  // 遍历当前排序条件
  currentSort.value.forEach((sort) => {
    sortParams.push({
      field: sort.field,
      asc: sort.asc === 1,
    })
  })

  return sortParams
}

/**
 * 处理商品数量变更
 */
function handleQuantityUpdate({ sku, isModified }: { sku: VehSaleUseSignArvDtlResponseDTO, isModified: boolean }) {
  // 更新修改状态
  if (isModified) {
    // 如果已修改，检查是否已在修改列表中
    const existIndex = modifiedSkus.value.findIndex(
      item => item.goods?.gid === sku.goods?.gid,
    )

    if (existIndex > -1) {
      // 已存在则更新
      modifiedSkus.value[existIndex] = deepClone(sku)
    }
    else {
      // 不存在则添加
      modifiedSkus.value.push(deepClone(sku))
    }
  }
  else {
    // 如果未修改，从修改列表中移除
    modifiedSkus.value = modifiedSkus.value.filter(
      item => !(item.goods?.gid === sku.goods?.gid),
    )
  }
}

// 上拉加载更多 - 结合usePagination使用
async function upCallback(mescroll: any) {
  if (mescroll.num === 1) {
    await reloadSku()
  }
  else {
    page.value = mescroll.num
  }
}

// 审核回货单
const {
  send: handleAuditRequest,
} = useRequest(
  (data: VehSaleUseSignArvAuditRequestDTO) => Apis.vehsaleusesignarvInterface.auditUsingPOST({
    data,
  }),
  {
    immediate: false,
    middleware: createGlobalLoadingMiddleware({
      loadingText: '审核中...',
    }),
  },
).onSuccess(() => {
  globalLoading.close()
  emitRefreshBackConfirmList()
  showSuccess({
    msg: '审核成功',
    duration: 500,
    closed() {
      router.replace({
        name: 'back-detail',
        params: {
          id: id.value,
        },
      })
    },
  })
}).onError((error) => {
  globalLoading.close()
  showError(error.error?.message || '审核失败')
})

// 作废回货单
const {
  send: handleAbortRequest,
} = useRequest(
  (num: string, version) => Apis.vehsaleusesignarvInterface.abortUsingPOST_2({
    data: {
      num,
      version,
    },
  }),
  {
    immediate: false,
    middleware: createGlobalLoadingMiddleware({
      loadingText: '作废中...',
    }),
  },
).onError((error) => {
  globalLoading.close()
  showError(error.error?.message || '作废失败')
}).onSuccess(() => {
  emitRefreshBackConfirmList()
  globalLoading.close()
  showSuccess({
    msg: '作废成功',
    duration: 500,
    closed() {
      router.replace({
        name: 'back-detail',
        params: {
          id: id.value,
        },
      })
    },
  })
})

// 审核操作
function auditOrder() {
  if (!id.value) {
    showError('缺少单号')
    return
  }

  // 如果存在差异，先显示差异确认弹窗
  if (hasDiff.value) {
    diffConfirmRef.value?.open({
      beforeConfirm: () => {
        return new Promise((resolve) => {
          // 没有差异直接确认审核
          showConfirm({
            title: '提示',
            msg: '请确认数量核对无误',
            zIndex: 10000,
            confirmButtonText: '确认',
            cancelButtonText: '再想想',
            success: (res) => {
              if (res.action === 'confirm') {
                performAudit()
                resolve(true)
              }
            },
          })
        })
      },
      diffSkus: diffSkus.value,
      diffCount: diffStats.value.count,
      diffAmount: diffStats.value.amount,
    })
    return
  }

  // 没有差异直接确认审核
  showConfirm({
    title: '提示',
    msg: '请确认数量核对无误',
    confirmButtonText: '确认',
    cancelButtonText: '再想想',
    success: (res) => {
      if (res.action === 'confirm') {
        performAudit()
      }
    },
  })
}

/**
 * 执行审核操作
 */
function performAudit() {
  const data: VehSaleUseSignArvAuditRequestDTO = {
    num: id.value!,
    version: orderDetail.value?.version || 1,
    details: modifiedSkus.value.map(item => ({
      gdGid: item.goods?.gid as number,
      qpc: item.qpc!,
      qpcStr: item.qpcStr!,
      qty: item.qty!,
      tgpQty: item.tgpQty!,
      shouldQty: item.shouldQty!,
    })),
  }
  handleAuditRequest(data)
}

// 作废操作
function abortOrder() {
  if (!id.value) {
    showError('缺少单号')
    return
  }

  showConfirm({
    title: '提示',
    msg: '确认作废该单据吗？',
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    success: (res) => {
      if (res.action === 'confirm') {
        handleAbortRequest(id.value, orderDetail.value?.version || 1)
      }
    },
  })
}

/**
 * 处理排序变化
 * @param sortItems 排序项列表
 */
function handleSortChange(sortItems: SortItem[]) {
  currentSort.value = sortItems
  // 重新加载数据
  handleRefresh()
}

function handleRefresh() {
  if (getMescroll()) {
    getMescroll().resetUpScroll()
  }
}

const { lockPage, unlockPage, pageStyle } = useLockPage()

// 权限检查
const { checkPermission } = usePermissionChecker()

// 检查权限状态
const hasAuditPermission = computed(() => checkPermission('vehSaleUseSignArvAudit'))
const hasAbortPermission = computed(() => checkPermission('vehSaleUseSignArvAbort'))

// 判断是否显示按钮区域
const showButtonArea = computed(() => hasAuditPermission.value || hasAbortPermission.value)

onLoad((options: any) => {
  if (options.id) {
    id.value = options.id as string
    loadOrderDetail(id.value)
  }
})
</script>

<template>
  <view class="back-confirm-detail box-border min-h-screen w-screen bg-[#F9F9F9]" :style="pageStyle">
    <DetailSkeleton v-if="isLoading" />
    <view v-else>
      <!-- 分类Tabs - 固定在顶部 -->
      <view class="fixed top-0 z-3 w-screen bg-white">
        <wd-tabs v-model="categoryId" custom-class="tabs-custom" @change="handleRefresh">
          <wd-tab v-for="cat in categoryList" :key="cat.code" :title="cat.label" :name="cat.code" :badge-props="cat.count ? { isDot: true } : undefined" />
        </wd-tabs>

        <!-- 表头 - 固定在tabs下方 -->
        <view class="border-t border-[#F5F5F5] px-3 py-2">
          <DetailTableHeader :headers="tableHeaders" :show-title="false" @sort-change="handleSortChange" />
        </view>
      </view>

      <!-- 占位空间 -->
      <wd-gap height="calc(42px + 41px + 32rpx)" />

      <!-- 商品列表 - 独立的mescroll -->
      <view class="rounded-lg bg-white">
        <mescroll-body :down="downOption" :up="upOption" @init="mescrollInit" @down="downCallback" @up="upCallback">
          <view class="product-list">
            <view
              v-for="(sku, index) in skuList" :key="sku.gdCode || index"
              class="border-b border-[#F5F5F5] last:border-b-0"
            >
              <BackSkuEditCard :sku="sku" @update:sku="handleQuantityUpdate" />
            </view>
          </view>
        </mescroll-body>
        <wd-gap v-if="showButtonArea" height="calc(44px + 48rpx)" safe-area-bottom />
        <wd-gap v-else height="0" safe-area-bottom />
      </view>

      <!-- 底部按钮 -->
      <view
        v-if="showButtonArea"
        class="fixed bottom-0 left-0 right-0 z-10 box-border w-full border-t border-[#F5F5F5] bg-white"
        style="--wot-button-medium-height: 44px; box-shadow: 0px -4px 12px 0px rgba(0, 0, 0, 0.05);"
      >
        <view class="flex items-center justify-between p-3">
          <wd-button
            v-if="hasAbortPermission" :custom-class="hasAuditPermission ? 'w-50% mr-2!' : 'flex-1'" type="error" plain block :round="false"
            @click="abortOrder()"
          >
            作废
          </wd-button>
          <wd-button
            v-if="hasAuditPermission" :custom-class="hasAbortPermission ? 'w-50%' : 'flex-1'"
            block type="primary" :round="false" @click="auditOrder()"
          >
            {{ hasDiff ? '下一步' : '确认回货' }}
          </wd-button>
        </view>
        <wd-gap height="0" safe-area-bottom />
      </view>
    </view>

    <!-- 差异确认弹窗 -->
    <BackDiffConfirm
      ref="diffConfirmRef" @opened="lockPage" @closed="unlockPage" @confirm="() => { }"
      @cancel="() => { }"
    />
  </view>
</template>

<style lang="scss" scoped>
.back-confirm-detail {

  :deep(.mescroll-body) {
    // 减去顶部分类tabs、表头、底部按钮的高度
    min-height: calc(100vh - 42px - 41px - 36rpx - 44px - 48rpx) !important;
    min-height: calc(100vh - 42px - 41px - 36rpx - 44px - 48rpx - constant(safe-area-inset-bottom)) !important;
    min-height: calc(100vh - 42px - 41px - 36rpx - 44px - 48rpx - env(safe-area-inset-bottom)) !important;
  }
}

// tabs样式
:deep(.tabs-custom){
  // 修复徽标导致文字溢出的问题
  .wd-tabs__nav-item-badge {
    display: flex;
    max-width: 100%;
    min-width: 0;

    .wd-badge {
      display: flex;
      max-width: 100%;
      min-width: 0;

      .wd-tabs__nav-item-text {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        flex: 1;
        min-width: 0;
      }
    }
  }
}
</style>

<route lang="json">
{
  "name": "back-confirm-detail",
  "style": {
    "navigationBarTitleText": "回货商品清单"
  },
  "meta": {
    "permissions": [
      "vehSaleUseSignArvView"
    ]
  }
}
</route>
