<script setup lang="ts">
import { reactive, ref } from 'vue'
import { isArray } from 'wot-design-uni/components/common/util'
import BackConfirmCard from './cmp/BackConfirmCard.vue'
import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
import { useGlobalToast } from '@/composables/useGlobalToast'
import TimeFilter from '@/components/TimeFilter.vue'
import type { OrderStatus } from '@/utils/bill'
import type { VehSaleUseSignBckQueryResponseDTO } from '@/api/globals'

// mescroll相关方法 - 使用hooks
const { mescrollInit, getMescroll, downCallback } = useMescroll(onPageScroll, onReachBottom)

/**
 * 监听下游数据是否存在更新，如果存在则更新列表数据
 */
useWatchRefreshData({
  callback: () => {
    handleRefresh()
  },
  name: 'back-confirm-list',
})

const router = useRouter()
const globalToast = useGlobalToast()
const timeFilterRef = ref()
const { currentWms } = storeToRefs(useWmsStore())

// 当前选中的Tab - 已申请/已审核/已作废
// 1300:已申请
// 100:已审核
// 1310:审核后作废
const currentTab = ref<OrderStatus>('applied')

// 查询参数
const queryParams = reactive({
  keyword: '',
  stat: 1300 as (100 | 1300 | 1310 | undefined),
  beginDate: '',
  finishDate: '',
  sorts: [
    {
      field: 'lstupdTime',
      asc: false,
    },
  ],
})

// 使用 usePagination 实现分页查询
const {
  // 数据列表
  data: recordList,
  reload: reloadRecords,
  total,
  page,
} = usePagination(
  (page, pageSize) => Apis.vehsaleusesignarvInterface.queryUsingPOST_2({
    data: {
      ...queryParams,
      page: page - 1, // 后端页码从0开始，usePagination从1开始
      pageSize,
      wmsGid: currentWms.value?.gid,
    },
  }),
  {
    immediate: false,
    append: true,
    initialData: [],
    initialPageSize: 10,
    initialPage: 1,
    total: response => response.total,
    data: response => (response.data || []),
  },
).onSuccess((resp) => {
  getMescroll().endBySize(resp.data.data?.length || 0, total.value)
}).onError((error) => {
  globalToast.error(error.error?.message || '加载回货列表失败')
  getMescroll().endErr()
})

// 上拉加载更多 - 结合usePagination使用
async function upCallback(mescroll: any) {
  if (mescroll.num === 1) {
    await reloadRecords()
  }
  else {
    page.value = mescroll.num
  }
}

// 处理路由参数
onLoad((options: any) => {
  // 设置初始Tab状态
  // 当前选中的Tab - 已申请/已审核/已作废
  // 1300:已申请
  // 100:已审核
  // 1310:申请后作废
  // 1310:审核后作废
  if (options.stat) {
    switch (options.stat) {
      case 1300:
        currentTab.value = 'applied'
        queryParams.stat = 1300
        break
      case 100:
        currentTab.value = 'audited'
        queryParams.stat = 100
        break
      case 1310:
        currentTab.value = 'canceled'
        queryParams.stat = 1310
        break

      default:
        currentTab.value = 'applied'
        queryParams.stat = 1300
        break
    }
  }

  // 如果有时间范围参数，设置查询参数
  if (options.beginDate) {
    queryParams.beginDate = options.beginDate
  }

  if (options.endDate) {
    queryParams.finishDate = options.endDate
  }
})

function handleRefresh() {
  // 初始加载数据
  if (getMescroll()) {
    getMescroll().resetUpScroll()
  }
}

// 切换Tab
function changeTab(event: { index: number, name: OrderStatus }) {
  currentTab.value = event.name
  // 根据当前选中的Tab设置查询参数
  switch (event.name) {
    case 'applied':
      queryParams.stat = 1300
      break
    case 'audited':
      queryParams.stat = 100
      break
    case 'canceled':
      queryParams.stat = 1310
      break
    default:
      queryParams.stat = 1300
      break
  }

  handleRefresh()
}

/**
 * 筛选时间的回调
 */
function handleTimeFilterConfirm(payload: { range: string | [string, string] }) {
  if (isArray(payload.range)) {
    queryParams.beginDate = payload.range[0]
    queryParams.finishDate = payload.range[1]
  }
  else {
    queryParams.beginDate = payload.range
    queryParams.finishDate = payload.range
  }
}

// 查看回货记录详情
function viewRecordDetail(record: VehSaleUseSignBckQueryResponseDTO) {
  if (!record || !record.num) {
    globalToast.error('记录ID不存在')
    return
  }
  // 已申请/未审核的单据，跳转至回货确认详情，否则跳转至回货详情
  if (['applied', 'initial'].includes(getBillStatus(record.stat!, false))) {
    router.push({
      name: 'back-confirm-detail',
      params: { id: record.num },
    })
  }
  else {
    router.push({
      name: 'back-detail',
      params: { id: record.num },
    })
  }
}
</script>

<template>
  <view class="back-confirm-list box-border box-border min-h-screen bg-[#F9F9F9] pt-[calc(52px+42px)]">
    <!-- 搜索和筛选区域 - 固定在顶部 -->
    <view class="fixed-top-section">
      <SearchBar
        v-model="queryParams.keyword"
        placeholder="业务员/仓位/单号"
        :has-filter="!!queryParams.beginDate || !!queryParams.finishDate"
        @filter="timeFilterRef?.open()"
        @clear="handleRefresh"
        @search="handleRefresh"
      />

      <!-- 类型选择Tab -->
      <wd-tabs v-model="currentTab" auto-line-width @change="changeTab">
        <wd-tab title="已申请" name="applied" />
        <wd-tab title="已审核" name="audited" />
        <wd-tab title="已作废" name="canceled" />
      </wd-tabs>
    </view>

    <!-- 回货记录列表 -->
    <mescroll-body
      :down="downOption" :up="upOption" height="100%" safearea @init="mescrollInit"
      @down="downCallback" @up="upCallback"
    >
      <view class="mx-3 pt-2">
        <BackConfirmCard
          v-for="record in recordList"
          :key="record.num"
          :record="record"
          @click="viewRecordDetail(record)"
        />
      </view>
    </mescroll-body>

    <!-- 筛选弹窗 -->
    <TimeFilter ref="timeFilterRef" @confirm="handleTimeFilterConfirm" />
  </view>
</template>

<style lang="scss" scoped>
.back-confirm-list {
  :deep(.mescroll-body) {
    min-height: calc(100vh - 52px - 42px) !important;
    min-height: calc(100vh - 52px - 42px - constant(safe-area-inset-bottom)) !important;
    min-height: calc(100vh - 52px - 42px - env(safe-area-inset-bottom)) !important;
  }
}

/* 固定在顶部的搜索栏和筛选条件 */
.fixed-top-section {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 10;
  background-color: #F9F9F9;
}
</style>

<route lang="json">
{
  "name": "back-confirm-list",
  "style": {
    "navigationBarTitleText": "回货列表"
  },
  "meta": {
    "permissions": ["vehSaleUseSignArvView"]
  }
}
</route>
