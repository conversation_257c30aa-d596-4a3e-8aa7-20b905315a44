<!--
 * @Author: claude
 * @Date: 2025-01-04 14:30:00
 * @Description: 仓管端买赔单开单页面
 * @FilePath: /lsym-cx-mini/src/pagesWarehouse/warehouse-sales/warehouse-sales-edit.vue
-->
<script setup lang="ts">
import { computed, ref } from 'vue'
import WarehouseSaleEditHeader from './cmp/WarehouseSaleEditHeader.vue'
import WarehouseSalesEditSkuCard from './cmp/WarehouseSalesEditSkuCard.vue'
import WarehouseSalesEditSkeleton from './cmp/WarehouseSalesEditSkeleton.vue'
import DraftSubmitVerify from '@/business/DraftSubmitVerify.vue'
import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
import type { VehSaleEmp, VehSaleGetDraftGoodsResponseDTO, VehSaleGetDraftQpcResponseDTO, VehSaleSubmitRequestDTO } from '@/api/globals'
import { createGlobalLoadingMiddleware } from '@/api/core/middleware'

const router = useRouter()
const globalToast = useGlobalToast()
const globalLoading = useGlobalLoading()
const currentStoreId = ref<number>()

// 使用车销相关的草稿管理
const { getDraftInfo, clearDraft, draftInfo, skuQueryParams, skuList, skuListLoading, skuTotal, reloadSkuList, skuPage, onSkuListSuccess, onSkuListError, draftInfoLoading, submitDraft, seleEmp, verifySubmit, verifySubmitData } = useDraft('vehSale')

skuQueryParams.sorts = [
  {
    field: 'vehSaleWrhQty',
    asc: 0,
    name: '库存',
  },
  {
    field: 'vehSaleDraftQty',
    asc: 0,
    name: '已购',
  },
]
// 页面锁定相关
const { lockPage, unlockPage } = useLockPage()
const draftSubmitVerifyRef = ref<InstanceType<typeof DraftSubmitVerify>>()

const inited = ref(false)

// 分类相关
const { categoryList: categoryListData, getCategoryList, categoryLoading } = useCategorySelect(false)

// 处理分类列表，添加数量显示
const categoryList = computed(() => {
  return categoryListData.value.map((item) => {
    const count = draftInfo.value?.data?.categorySkuCount?.[item.code] || 0
    return {
      label: item.label,
      value: item.value,
      code: item.code,
      count: item.label === '全部' ? draftInfo.value?.data?.skuCount : count,
    }
  })
})

const loading = computed(() => {
  return categoryLoading.value || draftInfoLoading.value
})

// mescroll相关方法
const { mescrollInit, getMescroll, downCallback } = useMescroll(onPageScroll, onReachBottom)

// 上拉加载更多
async function upCallback(mescroll: any) {
  if (mescroll.num === 1) {
    await reloadSkuList()
  }
  else {
    skuPage.value = mescroll.num
  }
}

onSkuListSuccess((resp) => {
  getMescroll().endBySize(resp.data.data?.length || 0, skuTotal.value)
  if (skuTotal.value === 0) {
    getMescroll().showEmpty()
  }
})

onSkuListError((err) => {
  globalToast.error(err.error?.message || '加载商品失败')
  getMescroll().endErr()
})

const warehouseSaleEditHeaderRef = ref<InstanceType<typeof WarehouseSaleEditHeader>>()

// 在组件挂载时初始化
onMounted(async () => {
  warehouseSaleEditHeaderRef.value?.openSelect()
})

// 改变仓位和业务员
async function changeWrhOrEmp(empId: number, storeId: number) {
  skuQueryParams.vehSaleEmpGid = empId
  currentStoreId.value = storeId
  skuQueryParams.storeGid = storeId
  if (!seleEmp.value) {
    seleEmp.value = {} as VehSaleEmp
  }

  if (skuQueryParams.vehSaleEmpGid) {
    seleEmp.value.gid = skuQueryParams.vehSaleEmpGid
  }

  skuQueryParams.sortCode = ''
  const draft = await getDraftInfo()
  if (!inited.value) {
    const isSameStore = draft.data?.store?.gid === currentStoreId.value
    if (!isSameStore && draft.data) {
      await clearAndUpdate()
      await getCategoryList()
    }
    else {
      await getCategoryList()
    }
    inited.value = true
  }
  else {
    handleRefresh()
  }
}

function handleRefresh() {
  const mescroll = getMescroll()
  if (mescroll && mescroll.resetUpScroll) {
    mescroll.resetUpScroll()
  }
}

// 清除草稿并更新
async function clearAndUpdate() {
  await clearDraft()
  await getDraftInfo()
}

// 改变商品数量
async function handleSpecChange(spec: VehSaleGetDraftQpcResponseDTO, sku: VehSaleGetDraftGoodsResponseDTO) {
  const index = skuList.value.findIndex(item => item.goods.gid === sku.goods.gid)
  const oldSku = CommonUtil.deepClone(skuList.value[index])
  skuList.value[index] = CommonUtil.deepClone(sku)

  const verifyIndex = verifySubmitData.value && verifySubmitData.value.data && verifySubmitData.value.data.length > 0 ? verifySubmitData.value.data.findIndex(item => item.goods.gid === sku.goods.gid) : -1
  if (verifyIndex !== -1) {
    verifySubmitData.value.data![verifyIndex] = sku
  }

  seleEmp.value.gid = skuQueryParams.vehSaleEmpGid!

  try {
    const result = await submitDraft({
      gdCode: sku.goods.gdCode!,
      goods: {
        gid: sku.goods.gid,
        name: sku.goods.name,
        code: sku.goods.code,
      },
      munit: spec.munit,
      price: spec.price,
      qpc: spec.qpc,
      qpcStr: spec.qpcStr,
      qty: spec.qty,
      qtyStr: spec.qtyStr,
      singlePrice: spec.singlePrice,
      total: spec.singlePrice.multiply(spec.qty).scale(2),
      uuid: spec.uuid,
      version: sku.version,
      store: {
        gid: currentStoreId.value!,
      },
    })
    skuList.value[index].version = result.data?.version || skuList.value[index].version
    if (verifyIndex !== -1) {
      verifySubmitData.value.data![verifyIndex].version = result.data?.version || verifySubmitData.value.data![verifyIndex].version
    }
  }
  catch (error) {
    console.log(error)
    skuList.value[index] = oldSku
    if (verifyIndex !== -1) {
      verifySubmitData.value.data![verifyIndex] = oldSku
    }
  }
}

// 购物车品项数量
const skuCount = computed(() => {
  return draftInfo.value?.data?.skuCount || 0
})

// 提交订单请求
const { send: submit } = useRequest(
  (data: VehSaleSubmitRequestDTO) => Apis.vehsaleInterface.submitUsingPOST_1({
    data,
  }),
  {
    immediate: false,
    middleware: createGlobalLoadingMiddleware({
      loadingText: '提交中...',
    }),
  },
).onSuccess((resp) => {
  globalLoading.close()
  globalToast.success({
    msg: '提交成功',
    duration: 500,
    closed: () => {
      viewRecordDetail(resp.data.data)
    },
  })
}).onError((err) => {
  globalLoading.close()
  globalToast.error(err.error?.message || '提交失败')
})

function viewRecordDetail(id: string | undefined) {
  if (!id) {
    globalToast.error('单号不存在')
    return
  }
  router.replace({
    name: 'sales-detail',
    params: { id },
  })
}

/**
 * 提交订单
 */
async function handleSubmit() {
  return new Promise<boolean>((resolve) => {
    if (skuCount.value === 0) {
      globalToast.info('请先添加商品')
      resolve(false)
    }
    else {
      verifySubmit().then((result) => {
        if (!result || !result.data || !result.data.length) {
          // 校验通过后直接提交
          const submitData: VehSaleSubmitRequestDTO = {
            vehSaleEmp: seleEmp.value,
            store: {
              gid: currentStoreId.value!,
            },
          }
          submit(submitData)
          resolve(true)
        }
        else {
          resolve(false)
          globalLoading.close()
          globalToast.warning('部分商品库存不足，请调整数量!')
          draftSubmitVerifyRef.value?.open({
            beforeConfirm: async () => {
              const res = await handleSubmit()
              return res
            },
          })
        }
      }).catch((err) => {
        globalLoading.close()
        globalToast.error(err.error?.message || '校验失败')
        resolve(false)
      })
    }
  })
}

/**
 * 重新加载数据
 */
function reload() {
  getDraftInfo()
  handleRefresh()
}
</script>

<template>
  <DraftSubmitVerify ref="draftSubmitVerifyRef" title="请确认商品数量" @opened="lockPage" @closed="unlockPage" @confirm="reload" @cancel="reload">
    <template #body>
      <template v-if="verifySubmitData && verifySubmitData.data">
        <view class="mx-3 mb-3 box-border flex items-center rounded-2 bg-[var(--frequentapplication-orange-background)] px-20rpx py-2 text-26rpx text-[var(--frequentapplication-orange-content)]">
          以下商品库存不足，已自动更新为最大可领货数
        </view>
        <WarehouseSalesEditSkuCard
          v-for="sku in verifySubmitData.data"
          :key="sku.goods.gid"
          :sku="sku"
          @change="handleSpecChange"
        />
      </template>
    </template>
  </DraftSubmitVerify>

  <!-- 主页面内容 -->
  <view class="wholesale-order-edit box-border min-h-screen w-screen overflow-x-hidden">
    <!-- 门店和业务员选择 -->
    <WarehouseSaleEditHeader
      ref="warehouseSaleEditHeaderRef"
      custom-class="w-screen box-border fixed"
      :current-store="currentStoreId"
      :current-emp="skuQueryParams.vehSaleEmpGid"
      :clear-draft="clearDraft"
      @submit="changeWrhOrEmp"
    />
    <wd-gap height="176rpx" />

    <!-- 页面加载骨架屏 -->
    <WarehouseSalesEditSkeleton v-if="loading || !inited || !skuQueryParams.vehSaleEmpGid || !currentStoreId" />

    <!-- 主内容区域 -->
    <view v-if="skuQueryParams.vehSaleEmpGid && currentStoreId && inited && !categoryLoading" class="main relative z-1 w-screen bg-white">
      <!-- 分类tabs -->
      <view class="fixed z-2 w-full" :style="`top: ${warehouseSaleEditHeaderRef ? '176rpx' : '0px'}`">
        <wd-tabs v-model="skuQueryParams.sortCode" custom-class="w-full tabs-custom" @change="handleRefresh">
          <wd-tab
            v-for="(category, index) in categoryList"
            :key="index"
            :title="category.label || ''"
            :name="category.value"
            :badge-props="category.count ? { isDot: true } : undefined"
          />
        </wd-tabs>
        <!-- 头部排序区域 -->
        <view class="box-border w-full pl-3">
          <EditSort v-model:sorts="skuQueryParams.sorts" @change="handleRefresh" />
        </view>
      </view>

      <!-- 主内容区域 -->
      <view class="box-border flex flex-auto flex-col bg-white">
        <wd-gap height="calc(42px + 36px)" />

        <mescroll-body
          :down="downOption"
          :up="upOption"
          @init="mescrollInit"
          @down="downCallback"
          @up="upCallback"
        >
          <view class="p-3 pb-0">
            <SkuListSkeleton v-if="skuPage === 1 && skuListLoading" />
            <template v-else>
              <WarehouseSalesEditSkuCard
                v-for="sku in skuList"
                :key="sku.goods.gid"
                :sku="sku"
                @change="handleSpecChange"
              />
            </template>
          </view>
        </mescroll-body>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view
      class="fixed bottom-0 left-0 right-0 z-1 box-border w-full bg-white p-3"
      style="--wot-button-medium-height: 44px;"
    >
      <view class="flex items-center justify-between">
        <wd-button
          custom-class="flex-auto ml-3!"
          block
          type="primary"
          :round="false"
          @click="handleSubmit"
        >
          提交
        </wd-button>
      </view>
      <wd-gap height="0" safe-area-bottom />
    </view>
  </view>
</template>

<style lang="scss" scoped>
.wholesale-order-edit {
  min-height: 100vh;
  box-sizing: border-box;
  background-color: #fff;
  padding-bottom: calc(44px + 48rpx);
  padding-bottom: calc(44px + 48rpx + const(safe-area-inset-bottom)) !important;
  padding-bottom: calc(44px + 48rpx + env(safe-area-inset-bottom)) !important;

  :deep(.mescroll-body) {
    min-height: calc(100vh - 176rpx - 42px - 36px - 44px - 48rpx) !important;
    min-height: calc(100vh - 176rpx - 42px - 36px - 44px - 48rpx - const(safe-area-inset-bottom)) !important;
    min-height: calc(100vh - 176rpx - 42px - 36px - 44px - 48rpx - env(safe-area-inset-bottom)) !important;
  }

  // tabs样式
  :deep(.tabs-custom){
    // 修复徽标导致文字溢出的问题
    .wd-tabs__nav-item-badge {
      display: flex;
      max-width: 100%;
      min-width: 0;

      .wd-badge {
        display: flex;
        max-width: 100%;
        min-width: 0;

        .wd-tabs__nav-item-text {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          flex: 1;
          min-width: 0;
        }
      }
    }
  }
}
</style>

<route lang="json">
{
  "name": "warehouse-sales-edit",
  "style": {
    "navigationBarTitleText": "车销单开单"
  },
  "meta": {
    "permissions": ["vehSaleCreate"]
  }
}
</route>
