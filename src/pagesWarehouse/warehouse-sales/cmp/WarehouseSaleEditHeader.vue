<!--
 * @Author: claude
 * @Date: 2025-01-04 14:30:00
 * @Description: 买赔单开单页面头部组件（业务员和门店选择）
 * @FilePath: /lsym-cx-mini/src/pagesWarehouse/warehouse-sales/cmp/WarehouseSaleEditHeader.vue
-->
<script setup lang="ts">
import { nextTick } from 'vue'
import type { SelectPickerBeforeConfirm, SelectPickerInstance } from 'wot-design-uni/components/wd-select-picker/types'

const props = defineProps({
  // 当前业务员
  currentEmp: {
    type: [Number, String],
    default: '',
  },
  // 当前门店
  currentStore: {
    type: [Number, String],
    default: '',
  },
  // 自定义类名
  customClass: {
    type: String,
    default: '',
  },
  // 自定义样式
  customStyle: {
    type: String,
    default: '',
  },
  // 清空草稿
  clearDraft: {
    type: Function,
  },
})

const emit = defineEmits<{
  (e: 'submit', empId: number, storeId: number): void
}>()

const { confirm: showConfirm } = useGlobalMessage()
const globalLoading = useGlobalLoading()
const globalToast = useGlobalToast()
const { currentWms } = useWmsStore()

const { storeList, storeLoading, queryStore } = useStoreSelect({ wmsGid: currentWms?.gid }, false)

const storeListData = computed(() => {
  return storeList.value.filter(item => !!item.value)
})

const storeSelectRef = ref<SelectPickerInstance>()

const confirmedStoreId = ref<string | number>(props.currentStore)

// 添加状态跟踪门店弹框是否由业务员选择触发
const isStoreOpenedByEmpSelection = ref(false)

const currentStoreInfo = computed(() => {
  return storeList.value.find(item => item.value === confirmedStoreId.value)
})

const { empList, empLoading, currentEmp, empId } = useVehSaleEmpSelect(currentWms?.gid)
const confirmedEmpId = ref<string | number>(props.currentEmp)
const empSelectRef = ref<SelectPickerInstance>()
const empListData = computed(() => {
  return empList.value.filter(item => item.label !== '全部业务员')
})

function handleSelectEmp(event: { value: string, selectedItems: { label: string, value: string } }) {
  confirmedEmpId.value = event.value
  empId.value = event.value
  queryStore(Number(empId.value))
  // 标记门店弹框是由业务员选择触发的
  isStoreOpenedByEmpSelection.value = true
  storeSelectRef.value?.open()
}

function handleSelect(event: { value: string, selectedItems: { label: string, value: string } }) {
  confirmedStoreId.value = event.value
  // 重置标记
  isStoreOpenedByEmpSelection.value = false
  emit('submit', confirmedEmpId.value as number, confirmedStoreId.value as any as number)
}

// 处理门店弹框关闭
function handleStoreClose() {
  // 如果门店弹框是由业务员选择触发的，则重新打开业务员选择弹框
  if (isStoreOpenedByEmpSelection.value) {
    nextTick(() => {
      empSelectRef.value?.open()
    })
  }
  // 重置标记
  isStoreOpenedByEmpSelection.value = false
}

// 直接打开门店选择
function handleDirectStoreOpen() {
  // 标记为非业务员选择触发
  isStoreOpenedByEmpSelection.value = false
  storeSelectRef.value?.open()
}

// 处理业务员弹框关闭
function handleEmpClose() {
  // 如果已经有完整的业务员和门店选择，恢复原来的状态
  if (props.currentEmp && props.currentStore) {
    confirmedEmpId.value = props.currentEmp
    confirmedStoreId.value = props.currentStore
    empId.value = String(props.currentEmp)
  }
  else {
    // 如果没有完整选择，重新弹出业务员选择弹框
    nextTick(() => {
      empSelectRef.value?.open()
    })
  }
}

const beforeConfirm: SelectPickerBeforeConfirm = (value, resolve) => {
  if (!value) {
    globalToast.warning('请选择门店')
    resolve(false)
  }
  else if (!storeList.value.find(item => item.value === value)) { // 当前门店列表中没有该门店
    globalToast.warning('请选择门店')
    resolve(false)
  }
  else {
    // 检查门店或业务员是否发生变化
    const storeChanged = value !== props.currentStore
    const empChanged = confirmedEmpId.value !== props.currentEmp
    const hasChanged = storeChanged || empChanged

    if (hasChanged && (props.currentStore || props.currentEmp)) {
      const changeType = storeChanged && empChanged ? '业务员和门店' : storeChanged ? '门店' : '业务员'
      showConfirm({
        title: '提示',
        msg: `切换${changeType}将会清空购物车，确认切换吗？`,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        async success(res) {
          if (res.action === 'confirm') {
            try {
              if (CommonUtil.isFunction(props.clearDraft)) {
                await props.clearDraft()
              }
              resolve(true)
              globalLoading.close()
              globalToast.success(`已切换至${currentStoreInfo.value?.label}门店`)
            }
            catch (error) {
              resolve(false)
              globalLoading.close()
              globalToast.error('切换失败')
            }
          }
          else {
            resolve(false)
          }
        },
      })
    }
    else {
      resolve(true)
    }
  }
}

const beforeEmpConfirm: SelectPickerBeforeConfirm = (value, resolve) => {
  if (!value) {
    globalToast.warning('请选择业务员')
    resolve(false)
  }
  else {
    resolve(true)
  }
}

watch(() => props.currentStore, (newVal) => {
  if (!CommonUtil.isEqual(newVal, confirmedStoreId.value)) {
    confirmedStoreId.value = newVal
  }
})

defineExpose({
  openSelect: () => {
    // 先选择业务员
    empSelectRef.value?.open()
  },
})
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <view class="wholesale-edit-header z-3 box-border w-full bg-[var(--greyapplication-bottomlayer)] px-3" :style="customStyle" :class="customClass">
    <wd-select-picker
      ref="empSelectRef"
      v-model="confirmedEmpId"
      :close-on-click-modal="false"
      :columns="empListData"
      :loading="empLoading"
      title="选择业务员"
      filter-placeholder="业务员名称/代码"
      label="业务员"
      type="radio"
      filterable use-default-slot hide-label
      :before-confirm="beforeEmpConfirm"
      @confirm="handleSelectEmp"
      @cancel="handleEmpClose"
    >
      <template #default>
        <view class="h-88rpx flex items-center justify-between text-28rpx text-[var(--textapplication-text-2)]">
          <view class="flex-auto truncate">
            <text class="i-carbon-user mr-1 flex-none" />
            <text class="mr-2 flex-none">
              业务员
            </text>
            <text class="mr-2 text-[var(--textapplication-text-1)]">
              {{ currentEmp?.label || '请选择业务员' }}
            </text>
          </view>
          <text class="i-carbon-chevron-right flex-none text-[var(--textapplication-text-1)]" />
        </view>
      </template>
    </wd-select-picker>

    <wd-select-picker
      ref="storeSelectRef"
      v-model="confirmedStoreId"
      :close-on-click-modal="false"
      :columns="storeListData"
      :loading="storeLoading"
      title="选择门店"
      label="门店"
      type="radio"
      hide-label
      filterable
      filter-placeholder="门店名称/代码"
      use-default-slot
      :before-confirm="beforeConfirm"
      @confirm="handleSelect"
      @cancel="handleStoreClose"
    >
      <template #default>
        <view class="h-88rpx flex items-center justify-between text-28rpx text-[var(--textapplication-text-2)]" @click="handleDirectStoreOpen">
          <view class="flex flex-auto items-center truncate">
            <image src="@/static/icon/ic_store.svg" class="mr-1 h-5 w-5 flex-none" />
            <text class="mr-2 flex-none">
              门店
            </text>
            <text class="mr-2 truncate text-[var(--textapplication-text-1)]">
              {{ currentStoreInfo?.label || '请选择门店' }}
            </text>
          </view>
          <text class="i-carbon-chevron-right flex-none text-[var(--textapplication-text-1)]" />
        </view>
      </template>
    </wd-select-picker>
  </view>
</template>

<style lang="scss" scoped>
.wholesale-edit-header {
  background-color: #fff;

  :deep() {
    // .wd-action-sheet__close {
    //   display: none;
    // }

    .wd-select-picker__header {
      height: 112rpx;
      line-height: 112rpx;
    }
  }

  &-bottom {
    background: var(--wot-sidebar-bg, var(--wot-color-gray-1, #f7f8fa));
  }
}
</style>
