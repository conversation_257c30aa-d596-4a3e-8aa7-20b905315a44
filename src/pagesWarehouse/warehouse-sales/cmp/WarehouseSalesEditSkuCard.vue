<script setup lang="ts">
import { computed } from 'vue'

import type { VehSaleGetDraftGoodsResponseDTO, VehSaleGetDraftQpcResponseDTO } from '@/api/globals'
import type { CartSku } from '@/composables/useDraft'

type QpcDetail = VehSaleGetDraftQpcResponseDTO & {
  qpcQty: number
  qpcQtyMax: number
}

type EditSku = CartSku & {
  qpcDetails: QpcDetail[]
}

const props = defineProps({
  sku: {
    type: Object as PropType<CartSku>,
    required: true,
  },
})

const emits = defineEmits<{
  change: [spec: VehSaleGetDraftQpcResponseDTO, sku: VehSaleGetDraftGoodsResponseDTO]
}>()

function initEditSku(sku: CartSku) {
  // 计算出每个规格数量的和
  const totalQty = sku.qpcDetails.reduce((acc, item) => acc + item.qty, 0)
  const remainQty = (sku.goods.vehSaleWrhQty || 0) - totalQty
  return {
    ...sku,
    qpcDetails: sku.qpcDetails.map((item) => {
      return { ...item, qpcQty: Math.floor(item.qty / item.qpc), qpcQtyMax: Math.max(Math.floor((item.qty + remainQty) / item.qpc), 0) }
    }),
  }
}
/**
 * 商品信息
 */
const skuValue = ref<EditSku>(CommonUtil.deepClone(initEditSku(props.sku)))

/**
 * 跟踪每个规格是否曾经被点击过，用于判断是否显示完整步进器
 */
const specTouchedMap = ref<Map<string, boolean>>(new Map())

watch(() => props.sku, (newVal) => {
  const newSku = initEditSku(newVal)
  if (!CommonUtil.isEqual(skuValue.value, newSku)) {
    skuValue.value = CommonUtil.deepClone(newSku)
  }
}, {
  deep: true,
})

/**
 * 商品图片列表
 */
const skuImgList = computed(() => {
  const imgList = skuValue.value.goods.imageDetails && skuValue.value.goods.imageDetails.length > 0 ? skuValue.value.goods.imageDetails.map(item => item.fileUrl) : []
  return imgList.filter(item => CommonUtil.isDef(item))
})

/**
 * 商品主图
 */
const skuMainImg = computed(() => {
  return skuImgList.value.length > 0 ? skuImgList.value[0] : ''
})

/**
 * 是否已加购（商品数量>0）
 */
const isAddedToCart = computed(() => {
  if (!skuValue.value.qpcDetails || skuValue.value.qpcDetails.length === 0) {
    return false
  }
  return skuValue.value.qpcDetails.some((spec: QpcDetail) => (spec.qpcQty || 0) > 0)
})

// 排序后的规格列表：默认规格置顶，其他按从大到小排序
const sortedQpcDetails = computed(() => {
  if (!skuValue.value.qpcDetails || skuValue.value.qpcDetails.length === 0) {
    return []
  }

  const details = [...skuValue.value.qpcDetails]

  return details.sort((a, b) => {
    // 通过goods.qpcStr判断默认规格（useDraft已经将默认规格移到第一位，这里重新排序）
    const defaultQpcStr = skuValue.value.goods.qpcStr
    const isADefault = a.qpcStr === defaultQpcStr
    const isBDefault = b.qpcStr === defaultQpcStr

    // 默认规格置顶
    if (isADefault)
      return -1
    if (isBDefault)
      return 1

    // 其他规格按qpc从大到小排序
    return (b.qpc || 0) - (a.qpc || 0)
  })
})

/**
 * 生成规格的唯一键
 */
function getSpecKey(spec: QpcDetail, index: number): string {
  // 构建唯一键：商品代码_规格字符串_索引_uuid(如果有)
  const baseKey = `${skuValue.value.goods.code}_${spec.qpcStr}_${index}`

  // 如果有uuid则加上，没有则使用qpc值作为额外标识
  const uniqueId = spec.uuid || `qpc_${spec.qpc}`

  return `${baseKey}_${uniqueId}`
}

/**
 * 判断规格是否为未录入状态
 */
function isSpecUntouched(spec: QpcDetail, index: number) {
  const specKey = getSpecKey(spec, index)
  const hasValue = spec.qpcQty && spec.qpcQty > 0
  const wasTouched = specTouchedMap.value.get(specKey) || false

  // 未录入状态：没有值或值为0且从未被点击过
  return !hasValue && !wasTouched
}

/**
 * 更新规格数据
 */
const updateQpc = CommonUtil.debounce((spec: QpcDetail) => {
  const qpcIndex = skuValue.value.qpcDetails.findIndex((item: QpcDetail) => item.qpcStr === spec.qpcStr)
  skuValue.value.qpcDetails[qpcIndex].qpcQty = spec.qpcQty
  skuValue.value.qpcDetails[qpcIndex].qtyStr = `${spec.qpcQty || 0}`
  skuValue.value.qpcDetails[qpcIndex].qty = Number(Number(spec.qpcQty * spec.qpc).toFixed(4))
  skuValue.value = initEditSku(skuValue.value)
  emits('change', spec, skuValue.value)
}, 300, {
  leading: false,
  trailing: true,
})

/**
 * 处理规格变化
 * @param spec 规格
 * @param param1 数量
 * @param param1.value 数量
 */
function handleSpecChange(spec: QpcDetail, index: number, { value }: { value: number }) {
  // 标记该规格已被点击过
  const specKey = getSpecKey(spec, index)
  specTouchedMap.value.set(specKey, true)

  if (spec.qpcQty !== value) {
    spec.qpcQty = value
    updateQpc(spec)
  }
}
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <view class="warehouse-sales-edit-sku-card mb-3 flex overflow-hidden rounded-lg bg-white">
    <view class="flex-auto">
      <!-- 商品信息 -->
      <view class="flex p-3">
        <!-- 商品图片 -->
        <view class="relative h-172rpx w-172rpx flex-none">
          <SkuImage
            :src="skuMainImg"
            :preview-src="skuImgList"
            width="172rpx"
            height="172rpx"
            mode="widthFix"
            custom-class="mr-3 flex-none"
          />

          <!-- 缺货遮罩 -->
          <view v-if="Number(skuValue.goods.vehSaleWrhQty) <= 0" class="pointer-events-none absolute top-0 h-172rpx w-172rpx flex items-center justify-center rounded-12rpx bg-black/40 text-4 text-white">
            缺货
          </view>

          <!-- 已加购标记 -->
          <view v-if="isAddedToCart" class="pointer-events-none absolute top-0 h-172rpx w-172rpx flex items-center justify-center rounded-12rpx bg-black/40 text-4 text-white">
            已加购
          </view>
        </view>

        <view class="ml-3 flex-1" :class="{ 'opacity-50': Number(skuValue.goods.vehSaleWrhQty) <= 0 }">
          <view class="mb-1">
            <text class="line-clamp-2 break-all text-28rpx text-[#2D2D2D] font-medium">
              {{ skuValue.goods.name }}
            </text>
          </view>
          <view class="mb-1">
            <view class="inline-block break-all rounded bg-[#F5F6F7] px-1 py-0 text-26rpx text-[#5C5C5C]">
              {{ skuValue.goods.gdCode }}
            </view>
          </view>
          <view class="flex items-center justify-between">
            <text class="text-3 text-[#8A8A8A]">
              车余：{{ getQpcQty(skuValue.goods.vehSaleWrhQtyStr!, skuValue.goods.useSignMunit!, skuValue.goods.minMunit!) }}
            </text>
          </view>
        </view>
      </view>

      <!-- 规格选择区域（全部展开显示） -->
      <view v-if="sortedQpcDetails.length && Number(skuValue.goods.vehSaleWrhQty) > 0" class="border-t border-[#EFEFEF] p-3">
        <view
          v-for="(spec, index) in sortedQpcDetails"
          :key="index"
          class="mb-3 last:mb-0"
        >
          <view class="mb-2 flex items-center justify-between">
            <view class="flex items-center">
              <view class="inline-block h-48rpx flex items-center truncate rounded bg-[#F5F6F7] px-1 py-0 text-sm text-[#5C5C5C]">
                {{ spec.qpcStr }}
              </view>

              <view class="ml-2 flex items-end">
                <text class="text-26rpx text-[#F57F00]">
                  ¥
                </text>
                <text class="text-lg text-[#F57F00] font-medium leading-none">
                  {{ spec.price }}
                </text>
                <text class="text-xs">
                  /{{ spec.munit }}
                </text>
              </view>
            </view>

            <wd-input-number
              :model-value="spec.qpcQty"
              allow-null
              :immediate="false"
              :precision="0"
              :min="0"
              :max="spec.qpcQtyMax"
              input-width="100%"
              :custom-class="`input-number-adjust ${isSpecUntouched(spec, index) ? 'input-number-zero' : ''}`"
              @change="(value) => handleSpecChange(spec, index, value)"
            />
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.warehouse-sales-edit-sku-card {
  &:not(:last-child) {
    margin-bottom: 16rpx;
  }
}

// 自定义数量输入步进器样式
:deep(.input-number-adjust) {
  // 按钮
  .wd-input-number__action {
    width: 56rpx;
    height: 56rpx;
  }
  // 输入框
  .wd-input-number__inner{
    width: 100rpx;
    height: 56rpx;
    .wd-input-number__input{
      height: 100%;
      font-size: 30rpx;
      font-weight: 500;
    }
  }
}

// 未录入状态只显示加号
:deep(.input-number-zero) {
  .wd-input-number__action:first-child {
    display: none; // 隐藏减号按钮
  }
  .wd-input-number__inner {
    display: none; // 隐藏输入框
  }
}
</style>
