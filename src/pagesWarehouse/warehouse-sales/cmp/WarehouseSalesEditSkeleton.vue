<!--
 * @Author: claude
 * @Date: 2025-01-04 15:00:00
 * @Description: 仓管端买赔单开单页面骨架屏组件
 * @FilePath: /lsym-cx-mini/src/pagesWarehouse/warehouse-sales/cmp/WarehouseSalesEditSkeleton.vue
-->
<script setup lang="ts">
defineProps({
  customClass: {
    type: String,
    default: '',
  },
  customStyle: {
    type: String,
    default: '',
  },
})
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <view class="warehouse-sales-edit-skeleton w-screen" :class="customClass" :style="customStyle">
    <!-- 主内容区域骨架屏 -->
    <view class="main-skeleton flex bg-white pl-96px">
      <!-- 侧边分类栏骨架屏 -->
      <view class="main-sidebar fixed left-0 w-96px bg-[#F9F9F9]" style="top: 176rpx; height: calc(100vh - 176rpx - 44px - 48rpx)">
        <view class="flex flex-col gap-1 p-1">
          <wd-skeleton
            v-for="i in 6"
            :key="i"
            :row-col="[{ width: '100%', height: '48px' }]"
            animation="flashed"
          />
        </view>
      </view>

      <!-- 主内容区域 -->
      <view class="w-[calc(100vw-96px)] flex flex-auto flex-col">
        <!-- 排序工具栏骨架屏 -->
        <view class="sort-toolbar-skeleton fixed right-0 z-1 w-[calc(100vw-96px)] bg-white pl-3" style="top: 176rpx">
          <view class="flex items-center justify-end py-2">
            <wd-skeleton :row-col="[{ width: '60px', height: '20px' }]" animation="flashed" />
            <view class="mx-2" />
            <wd-skeleton :row-col="[{ width: '60px', height: '20px' }]" animation="flashed" />
          </view>
        </view>

        <!-- 占位间距 -->
        <view class="h-36px" />

        <!-- 商品列表骨架屏 -->
        <view class="p-3">
          <view v-for="i in 4" :key="i" class="mb-3">
            <!-- 商品卡片骨架屏 -->
            <view class="rounded-lg bg-white p-3 shadow-sm">
              <!-- 商品基本信息 -->
              <view class="flex items-start gap-3">
                <wd-skeleton :row-col="[{ width: '100px', height: '100px' }]" animation="flashed" />
                <view class="flex-1">
                  <wd-skeleton
                    :row-col="[
                      { width: '85%', height: '18px' },
                      { width: '65%', height: '14px' },
                      { width: '45%', height: '14px' },
                    ]"
                    animation="flashed"
                  />
                </view>
              </view>

              <!-- 商品规格和数量操作 -->
              <view class="mt-3">
                <view class="flex items-center justify-between">
                  <wd-skeleton :row-col="[{ width: '120px', height: '36px' }]" animation="flashed" />
                  <wd-skeleton :row-col="[{ width: '100px', height: '36px' }]" animation="flashed" />
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部按钮骨架屏 -->
    <view class="fixed bottom-0 left-0 right-0 z-1 bg-white p-3">
      <wd-skeleton :row-col="[{ width: '100%', height: '44px' }]" animation="flashed" />
    </view>
  </view>
</template>

<style lang="scss" scoped>
.warehouse-sales-edit-skeleton {
  background-color: #F9F9F9;
  padding-bottom: calc(44px + 48rpx);
  padding-bottom: calc(44px + 48rpx + const(safe-area-inset-bottom)) !important;
  padding-bottom: calc(44px + 48rpx + env(safe-area-inset-bottom)) !important;
}
</style>
