<script setup lang="ts">
import { reactive, ref } from 'vue'
import { isArray } from 'wot-design-uni/components/common/util'
import PickConfirmCard from './cmp/PickConfirmCard.vue'
import MergeAuditBar from './cmp/MergeAuditBar.vue'
import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
import TimeFilter from '@/components/TimeFilter.vue'
import type { OrderStatus } from '@/utils/bill'
import type { VehSaleUseSignQueryResponseDTO } from '@/api/globals'
import { createGlobalLoadingMiddleware } from '@/api/core/middleware'

const router = useRouter()
const globalToast = useGlobalToast()
const globalLoading = useGlobalLoading()
const { currentWms } = storeToRefs(useWmsStore())
// mescroll相关方法 - 使用hooks
const { mescrollInit, getMescroll, downCallback } = useMescroll(onPageScroll, onReachBottom)

const timeFilterRef = ref()

// 业务员选择
const { empList, empLoading, handleEmpSelect, empId, currentEmp } = useVehSaleEmpSelect(currentWms.value?.gid)

// 当前选中的Tab - 已申请/已审核/已作废
// 1300:已申请
// 100:已审核
// 1310:审核后作废
const currentTab = ref<OrderStatus>('applied')

// 底部审核按钮模式 - common:普通模式，merge-audit:合并审核模式
const mergeAuditMode = ref<'common' | 'merge-audit'>('common')
// 是否显示选中按钮
const showChecked = computed(() => mergeAuditMode.value === 'merge-audit' && currentTab.value === 'applied' && !!empId.value)
// 已选单据列表
const selectedRecordList = ref<{ num: string, version: number }[]>([])

/**
 * 监听下游数据是否存在更新，如果存在则更新列表数据
 */
useWatchRefreshData({
  callback: () => {
    resetMergeAuditMode()
    handleRefresh()
  },
  name: 'pick-confirm-list',
})

watch([empId, currentTab], () => {
  resetMergeAuditMode()
})

function resetMergeAuditMode() {
  mergeAuditMode.value = 'common'
  selectedRecordList.value = []
}

// 查询参数
const queryParams = reactive({
  keyword: '',
  vehSaleEmpGid: undefined as number | undefined,
  wrhGid: undefined as number | undefined,
  stats: [1300] as (number[] | undefined),
  beginDate: '',
  finishDate: '',
  sorts: [
    {
      field: 'lstupdTime',
      asc: false,
    },
  ],
})

// 使用 usePagination 实现分页查询
const {
  // 数据列表
  data: recordList,
  reload: reloadRecords,
  total,
  page,
} = usePagination(
  (page, pageSize) => Apis.vehsaleusesignInterface.queryUsingPOST_5({
    data: {
      ...queryParams,
      page: page - 1, // 后端页码从0开始，usePagination从1开始
      pageSize,
      vehSaleEmpGid: empId.value ? Number(empId.value) : undefined,
      wmsGid: currentWms.value?.gid,
    },
  }),
  {
    immediate: false,
    append: true,
    initialData: [],
    initialPageSize: 10,
    initialPage: 1,
    total: response => response.total,
    data: (response) => {
      return (response.data || []).map((item: VehSaleUseSignQueryResponseDTO) => {
        return {
          ...item,
          checked: selectedRecordList.value.some(selectedItem => selectedItem.num === item.num),
        }
      })
    },
  },
).onSuccess((resp) => {
  getMescroll().endBySize(resp.data.data?.length || 0, total.value)
}).onError((error) => {
  globalToast.error(error.error?.message || '加载领货确认列表失败')
  getMescroll().endErr()
})

// 上拉加载更多 - 结合usePagination使用
async function upCallback(mescroll: any) {
  if (mescroll.num === 1) {
    await reloadRecords()
  }
  else {
    page.value = mescroll.num
  }
}

// 处理路由参数
onLoad((options: any) => {
  // 设置初始Tab状态
  // 当前选中的Tab - 已申请/已审核/已作废
  // 1300:已申请
  // 100:已审核
  // 1310:申请后作废
  // 1310:审核后作废
  if (options.stat) {
    switch (options.stat) {
      case 1300:
        currentTab.value = 'applied'
        queryParams.stats = [1300]
        break
      case 100:
        currentTab.value = 'audited'
        queryParams.stats = [100]
        break
      case 1310:
        currentTab.value = 'canceled'
        queryParams.stats = [110, 1310]
        break

      default:
        currentTab.value = 'applied'
        queryParams.stats = [1300]
        break
    }
  }

  // 如果有时间范围参数，设置查询参数
  if (options.beginDate) {
    queryParams.beginDate = options.beginDate
  }

  if (options.endDate) {
    queryParams.finishDate = options.endDate
  }
})

function handleRefresh() {
  // 初始加载数据
  if (getMescroll()) {
    getMescroll().resetUpScroll()
  }
}

// 切换Tab
function changeTab(event: { index: number, name: OrderStatus }) {
  currentTab.value = event.name
  // 根据当前选中的Tab设置查询参数
  switch (event.name) {
    case 'applied':
      queryParams.stats = [1300]
      break
    case 'audited':
      queryParams.stats = [100]
      break
    case 'canceled':
      queryParams.stats = [110, 1310]
      break
    default:
      queryParams.stats = [1300]
      break
  }
  handleRefresh()
}

/**
 * 选择业务员
 * @param event 结合wd-select-picker组件的confirm事件
 * @param event.value 选中的业务员ID值
 * @param event.selectedItems 选中的业务员项
 * @param event.selectedItems.value 选中的业务员项值
 * @param event.selectedItems.label 选中的业务员项标签
 */
function handleSelectEmp(event: { value: string, selectedItems: { label: string, value: string, code: string, name: string } }) {
  handleEmpSelect(event)
  handleRefresh()
}

/**
 * 筛选时间的回调
 */
function handleTimeFilterConfirm(payload: { range: string | [string, string] }) {
  if (isArray(payload.range)) {
    queryParams.beginDate = payload.range[0]
    queryParams.finishDate = payload.range[1]
  }
  else {
    queryParams.beginDate = payload.range
    queryParams.finishDate = payload.range
  }
  handleRefresh()
}

/**
 * 选中/取消选中单据
 * @param record 单据信息
 */
function handleCheck(record: VehSaleUseSignQueryResponseDTO) {
  const index = selectedRecordList.value.findIndex(item => item.num === record.num)
  if (index === -1) {
    selectedRecordList.value.push({ num: record.num!, version: record.version! })
  }
  else {
    selectedRecordList.value.splice(index, 1)
  }
  recordList.value.forEach((item) => {
    item.checked = selectedRecordList.value.some(selectedItem => selectedItem.num === item.num)
  })
}

/**
 * 合并审核校验
 */
const {
  send: mergeAuditCheck,
} = useRequest(
  (nums: string[]) => Apis.vehsaleusesignInterface.mergeAuditQueryUsingPOST({
    data: nums,
  }),
  {
    immediate: false,
    middleware: createGlobalLoadingMiddleware({
      loadingText: '合并中...',
    }),
  },
).onError((error) => {
  globalLoading.close()
  globalToast.error(error.error.message || '获取领货单详情失败')
}).onSuccess((resp) => {
  globalLoading.close()
  if (resp.data.data && resp.data.data.bills) {
    router.push({
      name: 'pick-confirm-detail',
      params: { bills: JSON.stringify(resp.data.data.bills) },
    })
  }
  else {
    globalToast.warning('没有找到需要合并审核的单据')
  }
})

/**
 * 开启批量审核
 */
function handleEnableMergeAudit() {
  if (!empId.value) {
    globalToast.warning('请选择业务员后开启批量审核')
  }
  else {
    mergeAuditMode.value = 'merge-audit'
  }
}

/**
 * 关闭批量审核
 */
function handleDisableMergeAudit() {
  mergeAuditMode.value = 'common'
}

/**
 * 合并审核
 */
function handleMergeAudit() {
  if (selectedRecordList.value.length === 0) {
    globalToast.warning('请先选择单据')
  }
  else {
    mergeAuditCheck(selectedRecordList.value.map(item => item.num))
  }
}

// 查看领货详情
function viewRecordDetail(record: VehSaleUseSignQueryResponseDTO) {
  if (!record || !record.num) {
    globalToast.error('记录ID不存在')
    return
  }

  // 已申请/未审核的单据，跳转至领货确认详情，否则跳转至领货详情
  if (['applied', 'initial'].includes(getBillStatus(record.stat!))) {
    router.push({
      name: 'pick-confirm-detail',
      params: { id: record.num },
    })
  }
  else {
    router.push({
      name: 'pick-detail',
      params: { id: record.num },
    })
  }
}
</script>

<template>
  <view
    class="pick-confirm-list box-border min-h-screen bg-[#F9F9F9] pt-[calc(52px+42px+88rpx)]"
    :class="[currentTab === 'applied' && empId ? 'pick-confirm-list--show-merge' : '']"
  >
    <!-- 搜索和筛选区域 - 固定在顶部 -->
    <view class="fixed-top-section">
      <SearchBar
        v-model="queryParams.keyword" placeholder="单号/仓位/商品"
        :has-filter="!!queryParams.beginDate || !!queryParams.finishDate" @filter="timeFilterRef?.open()"
        @clear="handleRefresh"
        @search="handleRefresh"
      />

      <!-- 类型选择Tab -->
      <wd-tabs v-model="currentTab" auto-line-width @change="changeTab">
        <wd-tab title="已申请" name="applied" />
        <wd-tab title="已审核" name="audited" />
        <wd-tab title="已作废" name="canceled" />
      </wd-tabs>

      <!-- 业务员筛选 -->
      <view class="h-88rpx flex items-center justify-between px-3">
        <wd-select-picker
          v-model="empId" :columns="empList" :loading="empLoading" title="选择业务员" label="业务员"
          type="radio" hide-label filterable use-default-slot @confirm="handleSelectEmp"
        >
          <template #default>
            <view class="flex items-center rounded bg-white px-3 py-1">
              <text class="max-w-32 truncate text-28rpx text-[#2D2D2D] font-500">
                {{ currentEmp?.label || '全部业务员' }}
              </text>
              <text class="i-carbon-caret-down text-4 text-[var(--textapplication-text-3)]" />
            </view>
          </template>
        </wd-select-picker>
        <template v-if="currentTab === 'applied'">
          <view v-if="mergeAuditMode === 'common'" class="flex items-center" :class="empId ? '' : 'opacity-50'" @click="handleEnableMergeAudit">
            <image src="@/static/icon/ic_batch.svg" class="mr-1.5 h-5 w-5" />
            <text class="text-28rpx text-[var(--frequentapplication-primary-content)] font-500">
              批量审核
            </text>
          </view>
          <view v-else class="flex items-center" @click="handleDisableMergeAudit">
            <image src="@/static/icon/ic_exit.svg" class="mr-1.5 h-5 w-5" />
            <text class="text-28rpx text-[var(--frequentapplication-primary-content)] font-500">
              退出
            </text>
          </view>
        </template>
      </view>
    </view>

    <!-- 领货确认列表 -->
    <mescroll-body
      :down="downOption" :up="upOption" height="100%" safearea @init="mescrollInit" @down="downCallback"
      @up="upCallback"
    >
      <view class="mx-3">
        <PickConfirmCard
          v-for="record in recordList"
          :key="record.num" :show-checked="showChecked" :record="record"
          @click="viewRecordDetail(record)"
          @check="handleCheck(record)"
        />
      </view>
    </mescroll-body>

    <MergeAuditBar v-if="currentTab === 'applied' && empId && mergeAuditMode === 'merge-audit'" :selected-count="selectedRecordList.length" @click-merge-audit="handleMergeAudit" />

    <!-- 筛选弹窗 -->
    <TimeFilter ref="timeFilterRef" @confirm="handleTimeFilterConfirm" />
  </view>
</template>

<style lang="scss" scoped>
.pick-confirm-list {

  :deep(.mescroll-body) {
    min-height: calc(100vh - 52px - 42px - 88rpx) !important;
    min-height: calc(100vh - 52px - 42px - 88rpx - constant(safe-area-inset-bottom)) !important;
    min-height: calc(100vh - 52px - 42px - 88rpx - env(safe-area-inset-bottom)) !important;
  }

  &--show-merge {
    padding-bottom: 140rpx;
    :deep(.mescroll-body) {
      min-height: calc(100vh - 52px - 42px - 88rpx - 140rpx) !important;
      min-height: calc(100vh - 52px - 42px - 88rpx - 140rpx - constant(safe-area-inset-bottom)) !important;
      min-height: calc(100vh - 52px - 42px - 88rpx - 140rpx - env(safe-area-inset-bottom)) !important;
    }
  }
}

/* 固定在顶部的搜索栏和筛选条件 */
.fixed-top-section {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 10;
  background-color: #F9F9F9;
}
</style>

<route lang="json">
{
  "name": "pick-confirm-list",
  "style": {
    "navigationBarTitleText": "领货确认"
  },
  "meta": {
    "permissions": ["vehSaleUseSignView"]
  }
}
</route>
