<!--
 * @Description: 领货确认详情骨架屏组件
-->
<script setup lang="ts">
// 状态卡片骨架屏配置
const statusCardSkeletonConfig = [
  [{ width: '60%', height: '20px' }],
  [{ width: '90%', height: '16px' }],
  [{ width: '90%', height: '16px' }],
  [{ width: '90%', height: '16px' }],
  [{ width: '90%', height: '16px' }],
]

// 商品清单标题骨架屏配置
const titleSkeletonConfig = [
  [{ width: '100%', height: '24px' }],
]

// 搜索区域骨架屏配置
const searchSkeletonConfig = [
  [{ width: '100%', height: '40px' }],
]

// 商品列表骨架屏配置
const productSkeletonConfig = [
  [{ width: '100%', height: '120px', marginBottom: '12px' }],
  [{ width: '100%', height: '120px', marginBottom: '12px' }],
  [{ width: '100%', height: '120px', marginBottom: '12px' }],
]
</script>

<template>
  <!-- 状态卡片骨架屏 -->
  <view class="mx-3 mb-3 mt-[56px] rounded-lg bg-white p-4">
    <wd-skeleton :row-col="statusCardSkeletonConfig" animation="flashed" />
  </view>

  <!-- 商品清单头部骨架屏 -->
  <view class="header mx-3 rounded-lg bg-white p-3">
    <wd-skeleton :row-col="titleSkeletonConfig" animation="flashed" />
  </view>

  <!-- 搜索区域骨架屏 -->
  <view class="search-section mx-3 my-3">
    <wd-skeleton :row-col="searchSkeletonConfig" animation="flashed" />
  </view>

  <!-- 商品列表骨架屏 -->
  <view class="product-list mb-3 px-3">
    <wd-skeleton :row-col="productSkeletonConfig" animation="flashed" />
  </view>
</template>

<style lang="scss" scoped>
/* 骨架屏样式 */
</style>
