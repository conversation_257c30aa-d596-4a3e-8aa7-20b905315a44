<script setup lang="ts">
import { deepClone, isDef, isEqual } from 'wot-design-uni/components/common/util'
import type { VehSaleUseSignDtlQpcResponseDTO, VehSaleUseSignDtlResponseDTO } from '@/api/globals'

interface QpcDetail extends VehSaleUseSignDtlQpcResponseDTO {
  qpcQty: number
  qpcQtyMax: number
}

interface Sku extends VehSaleUseSignDtlResponseDTO {
  qpcDetails?: QpcDetail[]
}

// 定义组件接收的数据
const props = defineProps({
  sku: {
    type: Object as PropType<VehSaleUseSignDtlResponseDTO>,
    default: () => ({}),
  },
})

// 定义事件
const emit = defineEmits(['update:sku'])

function initValue() {
  const originSku = deepClone(props.sku)
  // 计算出每个规格数量的和
  const totalQty = originSku.qpcDetails?.reduce((acc, item) => acc + (item.qty || 0), 0) || 0
  const remainQty = (originSku.vehSaleWrhQty || 0) - totalQty

  return {
    ...originSku,
    qpcDetails: originSku.qpcDetails?.map((item) => {
      return {
        ...item,
        qpcQty: Math.floor((item.applyQty || 0) / (item.qpc || 1)),
        qpcQtyMax: Math.max(Math.floor(((item.qty || 0) + remainQty) / (item.qpc || 1)), 0),
      }
    }),
  }
}

const innerSku = ref<Sku>(initValue())

watch(() => props.sku, () => {
  const newSku = initValue()
  if (!isEqual(innerSku.value, newSku)) {
    innerSku.value = deepClone(newSku)
  }
}, {
  deep: true,
})
// 判断是否修改过数量
const isModified = computed(() => {
  if (!props.sku?.qpcDetails?.length)
    return false

  return innerSku.value.qpcDetails!.some((qpcItem) => {
    return qpcItem.qty !== qpcItem.applyQty
  })
})

/**
 * 商品图片列表
 */
const skuImgList = computed(() => {
  const imgList = props.sku.imageDetails && props.sku.imageDetails.length > 0 ? props.sku.imageDetails.map(item => item.fileUrl) : []
  return imgList.filter(item => isDef(item))
})

/**
 * 商品主图
 */
const skuMainImg = computed(() => {
  return skuImgList.value.length > 0 ? skuImgList.value[0] : ''
})

function initEditSku(sku: Sku): Sku {
  // 计算出每个规格数量的和
  const totalQty = sku.qpcDetails?.reduce((acc, item) => acc + (item.qty || 0), 0) || 0
  const remainQty = (sku.vehSaleWrhQty || 0) - totalQty

  return {
    ...sku,
    qpcDetails: sku.qpcDetails?.map((item) => {
      return {
        ...item,
        qpcQty: Math.floor((item.qty || 0) / (item.qpc || 1)),
        qpcQtyMax: Math.max(Math.floor(((item.qty || 0) + remainQty) / (item.qpc || 1)), 0),
      }
    }),
  }
}

/**
 * 更新规格数据
 */
const updateQpc = CommonUtil.debounce((spec: QpcDetail) => {
  const qpcIndex = innerSku.value.qpcDetails!.findIndex((item: QpcDetail) => item.qpcStr === spec.qpcStr)
  if (qpcIndex !== -1) {
    innerSku.value.qpcDetails![qpcIndex].qpcQty = spec.qpcQty
    innerSku.value.qpcDetails![qpcIndex].qtyStr = `${spec.qpcQty || 0}`
    innerSku.value.qpcDetails![qpcIndex].qty = Number(Number(spec.qpcQty * (spec.qpc || 1)).toFixed(4))

    // 重新计算最大值
    innerSku.value = initEditSku(innerSku.value)

    // 发送更新事件
    emit('update:sku', {
      sku: innerSku.value,
      isModified: isModified.value,
    })
  }
}, 300, {
  leading: false,
  trailing: true,
})

/**
 * 处理规格变化
 * @param spec 规格
 * @param value 数量
 */
function handleSpecChange(spec: QpcDetail, { value }: { value: number }) {
  if (spec.qpcQty !== value) {
    spec.qpcQty = value
    updateQpc(spec)
  }
}
</script>

<template>
  <view class="sku-card overflow-hidden rounded-lg bg-white p-3">
    <!-- 商品信息部分 -->
    <view class="mb-3 flex">
      <!-- 商品图片 -->
      <SkuImage
        :src="skuMainImg"
        :preview-src="skuImgList"
        mode="aspectFill"
        custom-class="mr-3"
      />

      <!-- 商品详情 -->
      <view class="flex flex-1 flex-col">
        <!-- 商品名称 -->
        <text class="line-clamp-2 mb-1 break-all text-28rpx text-[#2D2D2D] font-medium">
          {{ sku.goods?.name || '--' }}
        </text>

        <!-- 商品编码和规格标记 -->
        <view class="mb-1 flex items-center">
          <view class="break-all rounded bg-[#F5F6F7] px-1 py-0.5 text-3 text-[#5C5C5C]">
            {{ sku.goods?.code || '--' }}
          </view>

          <text class="ml-2 text-26rpx text-[#8A8A8A]">
            {{ sku.defUseSignQpcStr || '--' }}
          </text>
        </view>

        <view class="flex items-center justify-between">
          <text class="text-26rpx text-[#8A8A8A]">
            车余：{{ getQpcQty(sku.vehSaleWrhQtyStr!, sku.defUseSignMunit!, sku.minMunit!) }}
          </text>
        </view>
      </view>
    </view>

    <!-- qpcDetails明细行 -->
    <view v-if="sku.qpcDetails && sku.qpcDetails.length" class="qpc-details">
      <!-- 明细行 -->
      <view
        v-for="(qpcItem, index) in innerSku.qpcDetails"
        :key="index"
        class="qpc-details-row grid grid-cols-3 items-center border-b border-[#EFEFEF] py-2 last:border-0"
      >
        <!-- 规格 -->
        <view class="truncate">
          <text class="rounded-1 bg-#F5F6F7 px-1.5 py-1 text-3 text-[#5C5C5C]">
            {{ qpcItem.qpcStr || '--' }}
          </text>
        </view>

        <!-- 申请数量 -->
        <view class="text-center text-28rpx text-[#5C5C5C] font-500">
          {{ Number(qpcItem.applyQty).divide(qpcItem.qpc || 1).scale(4) }}{{ qpcItem.munit || '' }}
        </view>

        <!-- 实际领取数量控制 -->
        <view class="flex items-center justify-end">
          <wd-input-number
            :model-value="qpcItem.qpcQty"
            :min="0"
            allow-null
            :immediate="false"
            :precision="0"
            :max="999999999"
            :input-width="50"
            @change="(value) => handleSpecChange(qpcItem, value)"
          />
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>

</style>
