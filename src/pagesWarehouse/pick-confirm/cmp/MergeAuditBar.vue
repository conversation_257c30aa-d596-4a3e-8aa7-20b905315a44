<script setup lang="ts">
defineProps({
  /**
   * 已选单数
   */
  selectedCount: {
    type: Number,
    default: 0,
  },
  /**
   * 是否固定在底部
   */
  fixed: {
    type: Boolean,
    default: true,
  },
  /**
   * 是否显示安全区域
   */
  safeAreaBottom: {
    type: Boolean,
    default: true,
  },
  /**
   * 自定义样式类
   */
  customClass: {
    type: String,
    default: '',
  },
  /**
   * 自定义样式
   */
  customStyle: {
    type: String,
    default: '',
  },
})

const emit = defineEmits<{
  (e: 'click-merge-audit'): void // 点击合并审核
}>()

function handleMergeAudit() {
  emit('click-merge-audit')
}
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <view :class="`merge-audit-bar ${fixed ? 'merge-audit-bar--fixed' : ''} ${customClass}`" :style="customStyle">
    <view class="merge-audit-bar__content box-border h-140rpx w-full flex items-center justify-between px-3">
      <!-- 操作按钮区域 -->
      <view class="w-full flex items-center justify-between">
        <view class="text-28rpx text-[var(--textapplication-text-2)]">
          已选<text class="text-4 text-[var(--atom-primary-primary-6)]">
            ({{ selectedCount }})
          </text>单
        </view>
        <!-- 合并审核按钮 -->
        <view class="flex items-center">
          <wd-button
            custom-class="w-280rpx!"
            type="primary"
            size="large"
            :disabled="selectedCount === 0"
            :round="false"
            block
            @click="handleMergeAudit"
          >
            {{ selectedCount > 1 ? '合并审核' : '审核' }}
          </wd-button>
        </view>
      </view>
    </view>

    <!-- 安全区域 -->
    <wd-gap v-if="safeAreaBottom" safe-area-bottom height="0" />
  </view>
</template>

<style lang="scss" scoped>
.merge-audit-bar {
  background-color: #fff;
  width: 100%;
  box-shadow: 0px -4px 12px 0px rgba(0, 0, 0, 0.05);

  &--fixed {
    position: fixed;
    z-index: 2;
    bottom: 0;
    left: 0;
    right: 0;
  }

  &__content {
    background-color: #fff;
  }

  :deep(.merge-audit-bar__cancel-btn) {
    background: #FFF5F5 !important;
    color: #F14646 !important;
    border: none !important;
    border-radius: 16rpx !important;
    font-size: 32rpx !important;
    font-weight: 500 !important;

    &.is-disabled {
      background: #F5F5F5 !important;
      color: #C8C8C8 !important;
    }
  }

  :deep(.merge-audit-bar__merge-btn) {
    background: #1C64FD !important;
    color: #fff !important;
    border: none !important;
    border-radius: 16rpx !important;
    font-size: 32rpx !important;
    font-weight: 500 !important;

    &.is-disabled {
      background: #C8C8C8 !important;
      color: #fff !important;
    }
  }
}
</style>
