<script setup lang="ts">
export interface InfoItem {
  label: string
  value: string | number | string[]
}

export interface DataItem {
  label: string
  value: string | number
  unit?: string
}

// 定义组件接收的数据
defineProps({
  /**
   * 基本信息项列表
   */
  infoItems: {
    type: Array as PropType<InfoItem[]>,
    default: () => [],
  },
  /**
   * 数据统计项列表
   */
  statItems: {
    type: Array as PropType<DataItem[]>,
    default: () => [],
  },
})

// 展开/收起状态管理
const expandedItems = ref<Record<number, boolean>>({})

// 切换展开/收起状态
function toggleExpand(index: number) {
  expandedItems.value[index] = !expandedItems.value[index]
}

// 判断是否为数组
function isArray(value: any): value is string[] {
  return Array.isArray(value)
}

// 获取显示的数组项
function getDisplayItems(value: string[], index: number) {
  const isExpanded = expandedItems.value[index]
  if (!isExpanded && value.length > 4) {
    return value.slice(0, 4)
  }
  return value
}

// 将数组分组，每2个一组
function groupItems(items: string[]) {
  const groups = []
  for (let i = 0; i < items.length; i += 2) {
    groups.push(items.slice(i, i + 2))
  }
  return groups
}
</script>

<template>
  <view class="header-info-card">
    <!-- 基本信息卡片 -->
    <view class="px-3 py-3">
      <view class="base-info-card rounded-lg p-3">
        <!-- 动态渲染基本信息项 -->
        <view
          v-for="(item, index) in infoItems"
          :key="index"
          class="flex items-start" :class="[index > 0 ? 'mt-2' : '']"
        >
          <view class="min-w-[70px] flex-shrink-0 text-28rpx text-[var(--textapplication-text-2)]">
            {{ item.label }}
          </view>

          <!-- 非数组值的普通展示 -->
          <view v-if="!isArray(item.value)" class="flex-1 break-all text-right text-28rpx text-[var(--textapplication-text-1)]">
            {{ item.value || '--' }}
          </view>

          <!-- 数组值的特殊展示 -->
          <view v-else class="flex-1">
            <view class="array-value-container">
              <!-- 数组项网格展示 -->
              <view
                v-for="(group, groupIndex) in groupItems(getDisplayItems(item.value, index))"
                :key="groupIndex"
                class="mb-1.5 flex justify-end gap-2"
              >
                <view
                  v-for="(arrayItem, itemIndex) in group"
                  :key="itemIndex"
                  class="array-item break-all text-28rpx text-[var(--atom-primary-primary-6)]"
                >
                  {{ arrayItem }}
                </view>
              </view>

              <!-- 展开/收起按钮 -->
              <view
                v-if="item.value.length > 4"
                class="mt-2 flex justify-end"
              >
                <view
                  class="expand-btn flex cursor-pointer items-center text-24rpx text-[var(--atom-primary-primary-6)]"
                  @click="toggleExpand(index)"
                >
                  <text>{{ expandedItems[index] ? '收起' : '展开' }}</text>
                  <wd-icon
                    :name="expandedItems[index] ? 'arrow-up' : 'arrow-down'"
                    size="12px"
                    class="ml-1"
                  />
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 数据统计卡片 -->
    <view class="px-3">
      <view class="flex justify-between">
        <!-- 动态渲染数据统计项 -->
        <view
          v-for="(item, index) in statItems"
          :key="index"
          class="flex-1 border border-[#E6E6E6] rounded-lg border-solid bg-white p-3 not-last:mr-2"
        >
          <view class="flex flex-col">
            <view class="text-[26rpx] text-[#636D78]">
              {{ item.label }}
            </view>
            <view class="text-[44rpx] text-[#343A40] font-bold">
              <template v-if="item.unit">
                {{ item.unit }}
              </template>{{ item.value || '0' }}
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.header-info-card {
  .base-info-card {
    background: linear-gradient(180deg, var(--atom-primary-primary-1, #F5F8FF) 0%, #FFF 100%), #FFF;
  }

  .array-value-container {
    .array-item {
      min-width: 0;
      flex: 1;
      text-align: right;

      &:first-child:last-child {
        // 单个项目时占满宽度
        flex: none;
        width: 100%;
      }
    }

    .expand-btn {
      transition: all 0.3s ease;

      &:hover {
        opacity: 0.8;
      }

      &:active {
        transform: scale(0.95);
      }
    }
  }
}
</style>
