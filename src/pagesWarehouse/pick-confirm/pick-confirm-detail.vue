<script setup lang="ts">
import DetailSkeleton from './cmp/DetailSkeleton.vue'
import HeaderInfoCard, { type DataItem, type InfoItem } from './cmp/HeaderInfoCard.vue'
import SkuEditCard from './cmp/SkuEditCard.vue'
import DetailTableHeader from '@/business/DetailTableHeader.vue'
import { createGlobalLoadingMiddleware } from '@/api/core/middleware'
import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
import type { BillAuditRequestDTO, BillBaseResponseDTO, BillMargeAuditRequestDTO, QuerySort, VehSaleUseSignDtlResponseDTO } from '@/api/globals'
import type { SortItem } from '@/composables/useDraft'

const { mescrollInit, getMescroll, downCallback } = useMescroll(onPageScroll, onReachBottom)
const { success: showSuccess, error: showError, warning: showWarning } = useGlobalToast()
const globalLoading = useGlobalLoading()
const { confirm: showConfirm } = useGlobalMessage()
const router = useRouter()

const { emitRefreshData: emitRefreshPickConfirmList } = useSendRefreshData('pick-confirm-list') // 刷新列表，本页面提交成功需要刷新列表

const id = ref<string>('')
const inited = ref(false)
// 非合并审核的单头详情
const {
  data: order,
  loading: isLoading,
  send: loadOrderDetail,
} = useRequest(
  (num: string) => Apis.vehsaleusesignInterface.getUsingGET_5({
    params: {
      num,
      fetchDetail: false,
    },
  }),
  {
    immediate: false,
    middleware: createGlobalLoadingMiddleware({
      loadingText: '加载中...',
    }),
  },
).onError((error) => {
  showError(error.error.message || '获取领货单详情失败')
}).onSuccess(() => {
  inited.value = true
})

// 单头详情
const orderDetail = computed(() => {
  return order.value?.data
})

const bills = ref<BillBaseResponseDTO[]>([])
/**
 * 合并审核校验
 */
const {
  send: mergeAuditCheck,
  data: mergeAuditOrder,
  loading: isMergeAuditLoading,
} = useRequest(
  (nums: string[]) => Apis.vehsaleusesignInterface.mergeAuditQueryUsingPOST({
    data: nums,
  }),
  {
    immediate: false,
    middleware: createGlobalLoadingMiddleware({
      loadingText: '加载中...',
    }),
  },
).onError((error) => {
  globalLoading.close()
  showError(error.error.message || '获取合并审核详情失败')
}).onSuccess((resp) => {
  globalLoading.close()
  inited.value = true
  if (!resp.data.data || !resp.data.data.bills) {
    showWarning('没有找到需要合并审核的单据信息')
  }
})
/**
 * 合并审核单头详情
 */
const mergeAuditOrderDetail = computed(() => {
  return mergeAuditOrder.value?.data
})

const isMergeAudit = computed(() => {
  return bills.value.length > 0
})

const loading = computed(() => {
  return isLoading.value || isMergeAuditLoading.value
})

// 获取分类相关数据和方法
const { categoryList: categoryListData, categoryId } = useCategorySelect()

// 表头配置
const tableHeaders = ref([
  { title: '规格', field: 'spec' },
  { title: '报领', field: 'applyQty', sortable: true, asc: 0 as 0 | 1 | -1 },
  { title: '实领', field: 'qty' },
])

// 当前排序条件
const currentSort = ref<SortItem[]>([])

// 存储修改过的商品数据
const modifiedSkus = ref<VehSaleUseSignDtlResponseDTO[]>([])

// 处理分类列表，添加数量显示
const categoryList = computed(() => {
  return categoryListData.value.map((item) => {
    // 计算该分类下有输入数量的商品数
    let count = 0
    if (item.code === '') {
      // 全部分类：统计所有有修改的商品
      count = modifiedSkus.value.filter(sku => (sku.qty || 0) > 0).length
    }
    else {
      // 具体分类：统计该分类下有修改的商品
      count = modifiedSkus.value.filter(sku =>
        sku.category?.code === item.code && (sku.qty || 0) > 0,
      ).length
    }
    return {
      label: item.label,
      value: item.value,
      code: item.code,
      count,
    }
  })
})

// 分页查询商品
const {
  // 数据列表
  data: skuList,
  reload: reloadSku,
  total,
  page,
} = usePagination(
  page => Apis.vehsaleusesignInterface.queryDetailsUsingPOST_3({
    data: {
      num: isMergeAudit.value ? `${mergeAuditOrderDetail.value?.bills?.map(bill => bill.num!).join(',')}` : id.value!,
      page: page - 1, // 后端页码从0开始
      pageSize: 10,
      sorts: getSortParams(), // 添加排序参数
      category: categoryId.value
        ? {
            code: categoryId.value,
          }
        : undefined,
    },
  }),
  {
    immediate: false,
    append: true,
    initialData: [],
    initialPageSize: 10,
    initialPage: 1,
    total: response => response.total,
    data: (response) => {
      const data = response.data || []
      // 与modifiedSkus中的数据进行同步
      if (modifiedSkus.value.length > 0) {
        data.forEach((item) => {
          // 查找是否有修改过的相同商品
          const modifiedItem = modifiedSkus.value.find(
            modified =>
              modified.goods?.gid === item.goods?.gid,
          )
          // 如果找到已修改的商品，则更新其数量
          if (modifiedItem) {
            item.qty = modifiedItem.qty
          }
        })
      }

      return data
    },
  },
).onSuccess((resp) => {
  getMescroll().endBySize(resp.data.data?.length || 0, total.value)
})
  .onError((error) => {
    showError(error.error?.message || '加载领货确认列表失败')
    getMescroll().endErr()
  })

/**
 * 获取排序参数
 */
function getSortParams() {
  const sortParams: QuerySort[] = []

  // 遍历当前排序条件
  currentSort.value.forEach((sort) => {
    sortParams.push({
      field: sort.field,
      asc: sort.asc === 1,
    })
  })

  return sortParams
}

/**
 * 处理商品数量变更
 */
function handleQuantityUpdate({ sku, isModified }: { sku: VehSaleUseSignDtlResponseDTO, isModified: boolean }) {
  // 更新修改状态
  if (isModified) {
    // 如果已修改，检查是否已在修改列表中
    const existIndex = modifiedSkus.value.findIndex(
      item => item.goods?.gid === sku.goods?.gid,
    )

    if (existIndex > -1) {
      // 已存在则更新
      modifiedSkus.value[existIndex] = { ...sku }
    }
    else {
      // 不存在则添加
      modifiedSkus.value.push({ ...sku })
    }
  }
  else {
    // 如果未修改，从修改列表中移除
    modifiedSkus.value = modifiedSkus.value.filter(
      item => !(item.goods?.gid === sku.goods?.gid),
    )
  }
}

// 上拉加载更多 - 结合usePagination使用
async function upCallback(mescroll: any) {
  if (mescroll.num === 1) {
    await reloadSku()
  }
  else {
    page.value = mescroll.num
  }
}

// 审核领货单
const {
  send: handleAuditRequest,
} = useRequest(
  (data: BillAuditRequestDTO) => Apis.vehsaleusesignInterface.auditUsingPOST_3({
    data,
  }),
  {
    immediate: false,
    middleware: createGlobalLoadingMiddleware({
      loadingText: '审核中...',
    }),
  },
).onSuccess(() => {
  globalLoading.close()
  emitRefreshPickConfirmList()
  showSuccess({
    msg: '审核成功',
    duration: 500,
    closed() {
      router.replace({
        name: 'pick-detail',
        params: {
          id: id.value,
        },
      })
    },
  })
}).onError((error) => {
  globalLoading.close()
  showError(error.error?.message || '审核失败')
})

// 合并审核领货单
const {
  send: handleMergeAuditRequest,
} = useRequest(
  (data: BillMargeAuditRequestDTO) => Apis.vehsaleusesignInterface.mergeAuditUsingPOST({
    data,
  }),
  {
    immediate: false,
    middleware: createGlobalLoadingMiddleware({
      loadingText: '审核中...',
    }),
  },
).onSuccess(() => {
  emitRefreshPickConfirmList()
  globalLoading.close()
  showSuccess({
    msg: '审核成功',
    duration: 500,
    closed() {
      router.back()
    },
  })
}).onError((error) => {
  globalLoading.close()
  showError(error.error?.message || '审核失败')
})

// 作废领货单
const {
  send: handleAbortRequest,
} = useRequest(
  (num: string, version) => Apis.vehsaleusesignInterface.abortUsingPOST_4({
    data: {
      num,
      version,
    },
  }),
  {
    immediate: false,
    middleware: createGlobalLoadingMiddleware({
      loadingText: '作废中...',
    }),
  },
).onError((error) => {
  globalLoading.close()
  showError(error.error?.message || '作废失败')
}).onSuccess(() => {
  emitRefreshPickConfirmList()
})

// 批量作废，批量调用handleAbortRequest作废接口实现
async function batchAbortOrder() {
  if ((isMergeAudit.value && !bills.value.length) || (!isMergeAudit.value && !id.value)) {
    showError('缺少单号')
    return
  }
  // 串行调用作废接口，await
  try {
    for (let index = 0; index < bills.value.length; index++) {
      await handleAbortRequest(bills.value[index].num!, bills.value[index].version!)
    }
    globalLoading.close()
    showSuccess({
      msg: '作废成功',
      duration: 500,
      closed() {
        router.back()
      },
    })
  }
  catch (error: any) {
    globalLoading.close()
    showError(error.error?.message || '作废失败')
  }
}

// 审核操作
function auditOrder() {
  if ((isMergeAudit.value && !bills.value.length) || (!isMergeAudit.value && !id.value)) {
    showError('缺少单号')
    return
  }
  showConfirm({
    title: '提示',
    msg: '确认审核该领货单吗？',
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    success: (res) => {
      if (res.action === 'confirm') {
        if (!isMergeAudit.value) {
          const data: BillAuditRequestDTO = {
            num: id.value!,
            version: orderDetail.value?.version || 1,
            details: modifiedSkus.value.map((sku) => {
              return sku.qpcDetails?.map((qpc) => {
                return {
                  gdGid: sku.goods?.gid || 0,
                  price: qpc.price || 0,
                  qpc: qpc.qpc || 0,
                  qpcStr: qpc.qpcStr || '',
                  qty: qpc.qty || 0,
                }
              }) || []
            }).flat(),
          }
          handleAuditRequest(data)
        }
        else {
          const data: BillMargeAuditRequestDTO = {
            bills: mergeAuditOrderDetail.value?.bills,
            details: modifiedSkus.value.map((sku) => {
              return sku.qpcDetails?.map((qpc) => {
                return {
                  gdGid: sku.goods?.gid || 0,
                  price: qpc.price || 0,
                  qpc: qpc.qpc || 0,
                  qpcStr: qpc.qpcStr || '',
                  qty: qpc.qty || 0,
                }
              }) || []
            }).flat(),
          }
          handleMergeAuditRequest(data)
        }
      }
    },

  })
}

// 作废操作
function abortOrder() {
  if ((isMergeAudit.value && !bills.value.length) || (!isMergeAudit.value && !id.value)) {
    showError('缺少单号')
    return
  }

  showConfirm({
    title: '提示',
    msg: '确认作废该领货单吗？',
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    success: async (res) => {
      if (res.action === 'confirm') {
        if (isMergeAudit.value) {
          batchAbortOrder()
        }
        else {
          handleAbortRequest(id.value, orderDetail.value?.version || 1).then(() => {
            globalLoading.close()
            showSuccess({
              msg: '作废成功',
              duration: 500,
              closed() {
                router.replace({
                  name: 'pick-detail',
                  params: {
                    id: id.value,
                  },
                })
              },
            })
          })
        }
      }
    },
  })
}

const infoItems = computed(() => {
  const items: InfoItem[] = []

  if (!isMergeAudit.value) {
    items.push({
      label: '出货仓位',
      value: `${orderDetail.value?.wrh?.name}[${orderDetail.value?.wrh?.code}]`,
    }, {
      label: '业务员',
      value: `${orderDetail.value?.vehSaleEmpName}[${orderDetail.value?.vehSaleEmp?.code}]`,
    }, {
      label: '领货单号',
      value: `${orderDetail.value?.num}`,
    })
  }
  else {
    items.push({
      label: '领货单号',
      value: mergeAuditOrderDetail.value?.bills?.map(bill => bill.num || '') || [],
    })
  }

  return items
})

// 计算修改商品的金额差
const modifiedAmountDiff = computed(() => {
  let totalDiff = 0

  modifiedSkus.value.forEach((modifiedSku) => {
    // 找到原始商品数据进行对比
    const originalSku = skuList.value.find(sku => sku.goods?.gid === modifiedSku.goods?.gid)
    if (originalSku && modifiedSku.qpcDetails && originalSku.qpcDetails) {
      modifiedSku.qpcDetails.forEach((modifiedQpc, index) => {
        const originalQpc = originalSku.qpcDetails?.[index]
        if (originalQpc) {
          // 计算报领与实领的数量差
          const qtyDiff = (originalQpc.qty || 0).minus(modifiedQpc.qty || 0)
          // 计算金额差 = 数量差 * 规格价
          const amountDiff = qtyDiff.multiply(modifiedQpc.price || 0).divide(modifiedQpc.qpc || 1)
          totalDiff = totalDiff.add(amountDiff)
        }
      })
    }
  })

  return totalDiff
})

// 计算实际总金额
const actualTotal = computed(() => {
  const originalTotal = isMergeAudit.value
    ? mergeAuditOrderDetail.value?.total || 0
    : orderDetail.value?.total || 0
  return Math.max(originalTotal.minus(modifiedAmountDiff.value).scale(2), 0)
})

const dataItems = computed(() => {
  const items: DataItem[] = [{
    label: '品项数',
    value: isMergeAudit.value ? mergeAuditOrderDetail.value?.goodsCount || 0 : orderDetail.value?.goodsCount || 0,
  }, {
    label: '总金额',
    value: `¥${actualTotal.value.toFixed(2)}`,
  }]
  return items
})

/**
 * 处理排序变化
 * @param sortItems 排序项列表
 */
function handleSortChange(sortItems: SortItem[]) {
  currentSort.value = sortItems
  // 重新加载数据
  handleRefresh()
}

function handleRefresh() {
  if (getMescroll()) {
    getMescroll().resetUpScroll()
  }
}

// 权限检查
const { checkPermission } = usePermissionChecker()

// 检查权限状态
const hasAuditPermission = computed(() => checkPermission('vehSaleUseSignAudit'))
const hasAbortPermission = computed(() => checkPermission('vehSaleUseSignAbort'))

// 判断是否显示按钮区域
const showButtonArea = computed(() => hasAuditPermission.value || hasAbortPermission.value)

onLoad((options: any) => {
  if (options.id) {
    id.value = options.id as string
    loadOrderDetail(id.value)
  }
  else if (options.bills) {
    bills.value = JSON.parse(options.bills)
    mergeAuditCheck(bills.value.map(bill => bill.num))
  }
})
</script>

<template>
  <view class="pick-confirm-detail box-border min-h-screen w-screen bg-white">
    <DetailSkeleton v-if="loading" />
    <view v-else>
      <!-- 顶部信息 -->
      <HeaderInfoCard :info-items="infoItems" :stat-items="dataItems" />

      <!-- 分类Tabs - 固定在顶部 -->
      <view class="mt-2 w-screen bg-white">
        <wd-tabs v-model="categoryId" custom-class="tabs-custom" @change="handleRefresh">
          <wd-tab v-for="cat in categoryList" :key="cat.code" :title="cat.label" :name="cat.code" :badge-props="cat.count ? { isDot: true } : undefined" />
        </wd-tabs>

        <!-- 表头 - 固定在tabs下方 -->
        <view class="border-t border-[#F5F5F5] px-3 py-2">
          <DetailTableHeader
            :headers="tableHeaders"
            :show-title="false"
            @sort-change="handleSortChange"
          />
        </view>
      </view>

      <!-- 商品列表 - 独立的mescroll -->
      <mescroll-body
        v-if="inited"
        :down="downOption"
        :up="upOption"
        @init="mescrollInit"
        @down="downCallback"
        @up="upCallback"
      >
        <view class="product-list px-2.5">
          <view v-for="(sku, index) in skuList" :key="sku.gdCode || index" class="mb-3">
            <SkuEditCard :sku="sku" @update:sku="handleQuantityUpdate" />
          </view>
        </view>
      </mescroll-body>
      <wd-gap v-if="showButtonArea" height="calc(44px + 48rpx)" safe-area-bottom />
      <wd-gap v-else height="0" safe-area-bottom />

      <!-- 底部按钮 -->
      <view
        v-if="showButtonArea"
        class="fixed bottom-0 left-0 right-0 z-10 box-border w-full bg-white p-3"
        style="--wot-button-medium-height: 44px;"
      >
        <view class="flex items-center justify-between">
          <wd-button
            v-if="hasAbortPermission"
            :custom-class="hasAuditPermission ? 'w-50% mr-2!' : 'flex-1'"
            type="error"
            plain
            block
            :round="false"
            @click="abortOrder()"
          >
            作废
          </wd-button>
          <wd-button
            v-if="hasAuditPermission"
            :custom-class="hasAbortPermission ? 'w-50%' : 'flex-1'"
            block
            type="primary"
            :round="false"
            @click="auditOrder()"
          >
            通过
          </wd-button>
        </view>
        <wd-gap height="0" safe-area-bottom />
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.pick-confirm-detail {
  min-height: 100vh;

  :deep(.mescroll-body) {
    min-height: calc(100vh - 240px - 42px - 48px - 80px) !important;
    min-height: calc(100vh - 240px - 42px - 48px - 80px - constant(safe-area-inset-bottom)) !important;
    min-height: calc(100vh - 240px - 42px - 48px - 80px - env(safe-area-inset-bottom)) !important;
  }
}

// tabs样式
:deep(.tabs-custom){
  // 修复徽标导致文字溢出的问题
  .wd-tabs__nav-item-badge {
    display: flex;
    max-width: 100%;
    min-width: 0;

    .wd-badge {
      display: flex;
      max-width: 100%;
      min-width: 0;

      .wd-tabs__nav-item-text {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        flex: 1;
        min-width: 0;
      }
    }
  }
}
</style>

<route lang="json">
{
  "name": "pick-confirm-detail",
  "style": {
    "navigationBarTitleText": "领货详情"
  },
  "meta": {
    "permissions": ["vehSaleUseSignView"]
  }
}
</route>
