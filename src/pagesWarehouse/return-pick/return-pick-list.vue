<!--
 * @Author: claude
 * @Date: 2025-05-15 16:55:00
 * @Description: 领货返回列表页面
 * @FilePath: /lsym-cx-mini/src/pagesWarehouse/return-pick/return-pick-list.vue
-->
<script setup lang="ts">
import { reactive, ref } from 'vue'
import { isArray } from 'wot-design-uni/components/common/util'
import ReturnPickCard from './cmp/ReturnPickCard.vue'
import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
import TimeFilter from '@/components/TimeFilter.vue'

const { mescrollInit, getMescroll, downCallback } = useMescroll(onPageScroll, onReachBottom)

const { currentWms } = storeToRefs(useWmsStore())
const router = useRouter()
const globalToast = useGlobalToast()
const timeFilterRef = ref<InstanceType<typeof TimeFilter>>()
const userStore = useUserStore()

// 业务员选择（仅仓管可见）
const { empList, empLoading, handleEmpSelect, empId, currentEmp } = useVehSaleEmpSelect(currentWms.value?.gid)

// 监听刷新数据事件
useWatchRefreshData({
  callback: () => {
    handleRefresh()
  },
  name: 'return-pick-list',
})

// 查询参数 - 固定查询已审核状态
const queryParams = reactive({
  keyword: '',
  stats: [100] as (number[] | undefined), // 只查询已审核状态
  beginDate: '',
  finishDate: '',
  sorts: [
    {
      field: 'lstupdTime',
      asc: false,
    },
  ],
})

// 使用 usePagination 实现分页查询
const {
  // 数据列表
  data: recordList,
  reload: reloadRecords,
  total,
  page,
} = usePagination(
  (page, pageSize) => Apis.vehsaleusesignbckInterface.queryUsingPOST_4({
    data: {
      ...queryParams,
      page: page - 1, // 后端页码从0开始，usePagination从1开始
      pageSize,
      wmsGid: currentWms.value?.gid,
      vehSaleEmpGid: userStore.userRole === 'salesman'
        ? userStore.vehSaleEmp?.gid
        : (userStore.userRole === 'warehouse' && empId.value ? Number(empId.value) : undefined),
    },
  }),
  {
    immediate: false,
    append: true,
    initialData: [],
    initialPageSize: 10,
    initialPage: 1,
    total: response => response.total,
    data: response => (response.data || []),
  },
).onSuccess((resp) => {
  getMescroll().endBySize(resp.data.data?.length || 0, total.value)
}).onError((error) => {
  globalToast.error(error.error?.message || '加载领货返回列表失败')
  getMescroll().endErr()
})

// 上拉加载更多 - 结合usePagination使用
async function upCallback(mescroll: any) {
  if (mescroll.num === 1) {
    await reloadRecords()
  }
  else {
    page.value = mescroll.num
  }
}

// 处理路由参数
onLoad((options: any) => {
  // 如果有时间范围参数，设置查询参数
  if (options.beginDate) {
    queryParams.beginDate = options.beginDate
  }

  if (options.endDate) {
    queryParams.finishDate = options.endDate
  }
})

function handleRefresh() {
  // 初始加载数据
  if (getMescroll()) {
    getMescroll().resetUpScroll()
  }
}

/**
 * 选择业务员（仅仓管可见）
 * @param event 结合wd-select-picker组件的confirm事件
 */
function handleSelectEmp(event: { value: string, selectedItems: { label: string, value: string, code: string, name: string } }) {
  handleEmpSelect(event)
  handleRefresh()
}

/**
 * 筛选时间的回调
 */
function handleTimeFilterConfirm(payload: { range: string | [string, string] }) {
  if (isArray(payload.range)) {
    queryParams.beginDate = payload.range[0]
    queryParams.finishDate = payload.range[1]
  }
  else {
    queryParams.beginDate = payload.range
    queryParams.finishDate = payload.range
  }

  handleRefresh()
}

// 查看领货返回详情
function viewRecordDetail(id: string | undefined) {
  // 确保 id 存在
  if (!id) {
    globalToast.error('单号不存在')
    return
  }
  router.push({
    name: 'return-pick-detail',
    params: { id },
  })
}
</script>

<template>
  <view
    class="return-pick-list relative box-border min-h-screen bg-[#F9F9F9] pt-[52px]"
    :class="[userStore.userRole === 'warehouse' ? 'return-pick-list--with-emp-filter pt-[calc(52px+88rpx)]!' : '']"
  >
    <!-- 搜索和筛选区域 - 固定在顶部 -->
    <view class="fixed left-0 top-0 z-10 w-full bg-#F9F9F9">
      <SearchBar
        v-model="queryParams.keyword"
        placeholder="单号/仓位/商品"
        :has-filter="!!queryParams.beginDate || !!queryParams.finishDate"
        @filter="timeFilterRef?.open()"
        @clear="handleRefresh"
        @search="handleRefresh"
      />

      <!-- 业务员筛选（仅仓管可见） -->
      <view v-if="userStore.userRole === 'warehouse'" class="h-88rpx flex items-center justify-between px-3">
        <wd-select-picker
          v-model="empId"
          :columns="empList"
          :loading="empLoading"
          title="选择业务员"
          label="业务员"
          type="radio"
          hide-label
          filterable
          use-default-slot
          @confirm="handleSelectEmp"
        >
          <template #default>
            <view class="flex items-center rounded bg-white px-3 py-1">
              <text class="max-w-32 truncate text-28rpx text-[#2D2D2D] font-500">
                {{ currentEmp?.label || '全部业务员' }}
              </text>
              <text class="i-carbon-caret-down text-4 text-[var(--textapplication-text-3)]" />
            </view>
          </template>
        </wd-select-picker>
      </view>
    </view>

    <!-- 领货返回列表 -->
    <mescroll-body
      :down="downOption" :up="upOption" height="100%" safearea @init="mescrollInit"
      @down="downCallback" @up="upCallback"
    >
      <view class="mx-3 pt-2">
        <ReturnPickCard
          v-for="record in recordList"
          :key="record.num"
          :record="record"
          @click="viewRecordDetail(record.num)"
        />
      </view>
    </mescroll-body>

    <!-- 筛选弹窗 -->
    <TimeFilter ref="timeFilterRef" @confirm="handleTimeFilterConfirm" />
  </view>
</template>

<style lang="scss" scoped>
.return-pick-list {

  :deep(.mescroll-body) {
    min-height: calc(100vh - 52px) !important;
    min-height: calc(100vh - 52px - constant(safe-area-inset-bottom)) !important;
    min-height: calc(100vh - 52px - env(safe-area-inset-bottom)) !important;
  }

  &--with-emp-filter {
    :deep(.mescroll-body) {
      min-height: calc(100vh - 52px - 88rpx) !important;
      min-height: calc(100vh - 52px - 88rpx - constant(safe-area-inset-bottom)) !important;
      min-height: calc(100vh - 52px - 88rpx - env(safe-area-inset-bottom)) !important;
    }
  }
}
</style>

<route lang="json">
{
  "name": "return-pick-list",
  "style": {
    "navigationBarTitleText": "领货返回"
  },
  "meta": {
    "permissions": ["vehSaleUseSignBckView"]
  }
}
</route>
