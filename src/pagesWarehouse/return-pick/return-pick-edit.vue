<!--
 * @Author: claude
 * @Date: 2025-05-15 16:55:00
 * @Description: 领货返回列表页面
 * @FilePath: /lsym-cx-mini/src/pagesWarehouse/return-pick/return-pick-edit.vue
-->
<script setup lang="ts">
import { computed, ref } from 'vue'
import WarehousePickEditSkuCard from '../warehouse-pick/cmp/WarehousePickEditSkuCard.vue'
import ReturnPickEditHeader from './cmp/ReturnPickEditHeader.vue'
import ReturnPickEditSkeleton from './cmp/ReturnPickEditSkeleton.vue'
import DraftSubmitVerify from '@/business/DraftSubmitVerify.vue'
import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
import { useGlobalToast } from '@/composables/useGlobalToast'
import type { GCN, VehSaleEmp, VehSaleGetDraftGoodsResponseDTO, VehSaleGetDraftQpcResponseDTO, VehSaleUseSignSubmitRequestDTO } from '@/api/globals'
import { useCategorySelect } from '@/composables/useCategorySelect'
import { createGlobalLoadingMiddleware } from '@/api/core/middleware'

const router = useRouter()
const globalToast = useGlobalToast()
const globalLoading = useGlobalLoading()

const { getDraftInfo, clearDraft, draftInfo, skuQueryParams, skuList, skuListLoading, skuTotal, reloadSkuList, skuPage, onSkuListSuccess, onSkuListError, draftInfoLoading, submitDraft, seleEmp, verifySubmit, verifySubmitData } = useDraft('vehSaleUseSignBck')
// 设置排序参数
skuQueryParams.sorts = [{
  field: 'name',
  asc: 0,
  name: '名称',
}, {
  field: 'vehSaleWrhQty',
  asc: 0,
  name: '车余',
}, {
  field: 'vehSaleDraftQty',
  asc: 0,
  name: '数量',
}]
// 分类相关
const { categoryList: categoryListData, getCategoryList, categoryLoading } = useCategorySelect(false)

// 处理分类列表，添加数量显示
const categoryList = computed(() => {
  return categoryListData.value.map((item) => {
    const count = draftInfo.value?.data?.categorySkuCount?.[item.code] || 0
    return {
      label: item.label,
      value: item.value,
      code: item.code,
      count: item.label === '全部' ? draftInfo.value?.data?.skuCount : count,
    }
  })
})

const { lockPage, unlockPage } = useLockPage()

const loading = computed(() => {
  return categoryLoading.value || draftInfoLoading.value
})

const draftSubmitVerifyRef = ref<InstanceType<typeof DraftSubmitVerify>>()

// mescroll相关方法 - 使用hooks
const { mescrollInit, getMescroll, downCallback } = useMescroll(onPageScroll, onReachBottom)

const { confirm: showConfirm } = useGlobalMessage()

// 上拉加载更多 - 结合usePagination使用
async function upCallback(mescroll: any) {
  if (mescroll.num === 1) {
    await reloadSkuList()
  }
  else {
    skuPage.value = mescroll.num
  }
}

onSkuListSuccess((resp) => {
  getMescroll().endBySize(resp.data.data?.length || 0, skuTotal.value)
  if (skuTotal.value === 0) {
    getMescroll().showEmpty()
  }
})

onSkuListError((err) => {
  globalToast.error(err.error?.message || '加载领货商品失败')
  getMescroll().endErr()
})

const returnPickEditHeaderRef = ref<InstanceType<typeof ReturnPickEditHeader>>()

// 是否完成初始化
const inited = ref(false)
async function changeWrhOrEmp(emp: GCN, wrh: GCN | null) {
  skuQueryParams.vehSaleEmpGid = emp.gid
  if (wrh) {
    skuQueryParams.wrhGid = wrh.gid
  }

  if (!seleEmp.value) {
    seleEmp.value = {} as VehSaleEmp
  }

  if (skuQueryParams.vehSaleEmpGid) {
    seleEmp.value.gid = skuQueryParams.vehSaleEmpGid
  }

  if (!inited.value) {
    await getDraftInfo()
    await getCategoryList()
    inited.value = true
  }
  else {
    handleRefresh()
  }
}

function handleRefresh() {
  const mescroll = getMescroll()
  if (mescroll && mescroll.resetUpScroll) {
    mescroll.resetUpScroll()
  }
}

// 清除草稿并更新
async function clearAndUpdate() {
  await clearDraft()
  await getDraftInfo()
}

// 改变商品数量
async function handleSpecChange(spec: VehSaleGetDraftQpcResponseDTO, sku: VehSaleGetDraftGoodsResponseDTO) {
  const index = skuList.value.findIndex(item => item.goods.gid === sku.goods.gid)
  const oldSku = CommonUtil.deepClone(skuList.value[index])
  skuList.value[index] = CommonUtil.deepClone(sku)

  seleEmp.value.gid = skuQueryParams.vehSaleEmpGid!

  try {
    const result = await submitDraft({
      gdCode: sku.goods.gdCode!,
      goods: {
        gid: sku.goods.gid,
        name: sku.goods.name,
        code: sku.goods.code,
      },
      munit: spec.munit,
      price: spec.price,
      qpc: spec.qpc,
      qpcStr: spec.qpcStr,
      qty: spec.qty,
      qtyStr: spec.qtyStr,
      singlePrice: spec.singlePrice,
      total: spec.singlePrice.multiply(spec.qty).scale(2),
      uuid: spec.uuid,
      version: sku.version,
      // vehSaleEmp: skuQueryParams.vehSaleEmpGid,
      wrh: {
        gid: skuQueryParams.wrhGid!,
      },
    })
    skuList.value[index].version = result.data?.version || skuList.value[index].version
  }
  catch (error) {
    console.log(error)
    skuList.value[index] = oldSku
  }
}

onSkuListError((err) => {
  globalToast.error(err.error?.message || '加载领货返回商品失败')
  getMescroll().endErr()
})

// 购物车品项数量
const skuCount = computed(() => {
  return draftInfo.value?.data?.skuCount || 0
})

// 购物车总金额
const total = computed(() => {
  return draftInfo.value?.data?.total || 0
})

// 前往购物车页面
function goToCart() {
  // 检查购物车是否为空
  if (skuCount.value === 0) {
    globalToast.info('请添加领货返回商品')
    return
  }
  router.push({ name: 'return-pick-cart', params: {
    wrhGid: `${skuQueryParams.wrhGid}`,
    vehSaleEmpGid: `${skuQueryParams.vehSaleEmpGid}`,
  } })
}

// 监听刷新数据事件
useWatchRefreshData({
  callback: () => {
    getDraftInfo()
    handleRefresh()
  },
  name: 'return-pick-edit',
})

/**
 * 监听跳转事件
 */
useWatchReditct({
  callback: (id) => {
    router.replace({
      name: 'return-pick-detail',
      params: { id },
    })
  },
  name: 'return-pick-edit',
})

/**
 * 重新加载数据
 */
function reload() {
  getDraftInfo()
  handleRefresh()
}

// 在组件挂载时获取状态栏高度和初始化数据
onMounted(async () => {
  // 选择业务员和仓位
  returnPickEditHeaderRef.value?.openSelect()
})

// 提交订单请求
const { send: submit } = useRequest(
  (data: VehSaleUseSignSubmitRequestDTO) => Apis.vehsaleusesignbckInterface.submitUsingPOST_3({
    data,
  }),
  {
    immediate: false,
    middleware: createGlobalLoadingMiddleware({
      loadingText: '提交中...',
    }),
  },
).onSuccess((resp) => {
  globalLoading.close()
  globalToast.success({
    msg: '提交成功',
    duration: 500,
    closed: () => {
      viewRecordDetail(resp.data.data)
    },
  })
}).onError((err) => {
  globalLoading.close()
  globalToast.error(err.error?.message || '提交失败')
})

function viewRecordDetail(id: string | undefined) {
  // 确保 id 存在
  if (!id) {
    globalToast.error('单号不存在')
    return
  }
  router.replace({
    name: 'return-pick-detail',
    params: { id },
  })
}

/**
 * 提交订单
 */
async function handleSubmit() {
  return new Promise<boolean>((resolve) => {
    if (skuCount.value === 0) {
      globalToast.info('请先添加商品')
      resolve(false)
    }
    else {
      showConfirm({
        msg: '确认提交本次领货返回',
        confirmButtonText: '确认',
        cancelButtonText: '再想想',
        success: () => {
          const submitData = {
            vehSaleEmp: {
              gid: skuQueryParams.vehSaleEmpGid!,
            }, // userStore.vehSaleEmp,
            wrh: {
              gid: skuQueryParams.wrhGid!,
            },
            attachDetails: [],
          }
          verifySubmit(submitData).then((result) => {
            if (!result || !result.data || !result.data.length) {
              submit(submitData)
              resolve(true)
            }
            else {
              resolve(false)
              globalLoading.close()
              globalToast.warning('部分商品库存不足，请调整数量!')
              draftSubmitVerifyRef.value?.open({
                beforeConfirm: async () => {
                  const res = await handleSubmit()
                  return res
                },
              })
            }
          }).catch((err) => {
            globalLoading.close()
            globalToast.error(err.error?.message || '校验失败')
            resolve(false)
          })
        },
      },
      )
    }
  })
}
</script>

<template>
  <DraftSubmitVerify ref="draftSubmitVerifyRef" title="请确认商品数量" @opened="lockPage" @closed="unlockPage" @confirm="reload" @cancel="reload">
    <template #body>
      <template v-if="verifySubmitData && verifySubmitData.data">
        <view class="mx-3 mb-3 box-border flex items-center rounded-2 bg-[var(--frequentapplication-orange-background)] px-20rpx py-2 text-26rpx text-[var(--frequentapplication-orange-content)]">
          以下商品库存不足，已自动更新为最大可领货返回数
        </view>
        <WarehousePickEditSkuCard
          v-for="sku in verifySubmitData.data"
          :key="sku.goods.gid"
          type="vehSaleUseSignBck"
          :sku="sku"
          @change="handleSpecChange"
        />
      </template>
    </template>
  </DraftSubmitVerify>
  <view class="return-pick-edit min-h-screen bg-[#F9F9F9] pt-88rpx">
    <!--  业务员 -->
    <view class="absolute top-0">
      <ReturnPickEditHeader ref="returnPickEditHeaderRef" custom-class="fixed z-2" :current-emp="skuQueryParams.vehSaleEmpGid" :clear-and-update="clearAndUpdate" @submit="changeWrhOrEmp" />
    </view>

    <!-- 骨架屏 -->
    <ReturnPickEditSkeleton v-if="loading || !inited || !skuQueryParams.vehSaleEmpGid || !skuQueryParams.wrhGid" />

    <!-- 领货返回商品列表 -->
    <view v-if="skuQueryParams.vehSaleEmpGid && skuQueryParams.wrhGid && !categoryLoading">
      <view class="fixed z-2 w-full">
        <wd-tabs v-model="skuQueryParams.sortCode" custom-class="w-full tabs-custom" @change="handleRefresh">
          <wd-tab
            v-for="cat in categoryList"
            :key="cat.code"
            :title="cat.label"
            :name="cat.code"
            :badge-props="cat.count ? { isDot: true } : undefined"
          />
        </wd-tabs>
        <view class="right-0 box-border w-full" style="top: calc(var(--navbar-total-height + 50px))">
          <EditSort v-model:sorts="skuQueryParams.sorts" @change="handleRefresh" />
        </view>
      </view>

      <view class="mescroll-body-container box-border">
        <wd-gap height="calc(150rpx)" />
        <mescroll-body
          :down="downOption"
          :up="upOption"
          @init="mescrollInit"
          @down="downCallback"
          @up="upCallback"
        >
          <view class="px-2.5">
            <SkuListSkeleton v-if=" skuPage === 1 && skuListLoading" />
            <template v-else>
              <view v-if="skuList.length > 0" class="px-2.5 py-3">
                <WarehousePickEditSkuCard
                  v-for="sku in skuList"
                  :key="sku.goods.gid"
                  type="vehSaleUseSignBck"
                  :sku="sku"
                  @change="handleSpecChange"
                />
              </view>
            </template>
          </view>
        </mescroll-body>
      </view>
    </view>

    <!-- 底部购物车 -->
    <CartBar
      :count="skuCount"
      :amount="total"
      safe-area-bottom fixed
      button-text="提交"
      @click-cart="goToCart"
      @click-button="handleSubmit"
    />
  </view>
</template>

<style lang="scss" scoped>
.return-pick-edit {
  min-height: 100vh;
  box-sizing: border-box;
  background-color: #fff;
  padding-bottom: 120rpx;
  padding-bottom: calc(120rpx + const(safe-area-inset-bottom)) !important;
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom)) !important;

   :deep(.mescroll-body) {
    min-height: calc(100vh - 88rpx - 42px - 36px - 120rpx) !important;
    min-height: calc(100vh - 88rpx - 42px - 36px - 120rpx - constant(safe-area-inset-bottom)) !important;
    min-height: calc(100vh - 88rpx - 42px - 36px - 120rpx - env(safe-area-inset-bottom)) !important;
  }
}

// tabs样式
:deep(.tabs-custom){
  // 修复徽标导致文字溢出的问题
  .wd-tabs__nav-item-badge {
    display: flex;
    max-width: 100%;
    min-width: 0;

    .wd-badge {
      display: flex;
      max-width: 100%;
      min-width: 0;

      .wd-tabs__nav-item-text {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        flex: 1;
        min-width: 0;
      }
    }
  }
}
</style>

<route lang="json">
{
  "name": "return-pick-edit",
  "style": {
    "navigationBarTitleText": "领货返回录入"
  },
  "meta": {
    "permissions": ["vehSaleUseSignBckCreate"]
  }
}
</route>
