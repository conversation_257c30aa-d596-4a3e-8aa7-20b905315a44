<script setup lang="ts">
import ReturnPickDetailSkuCard from './cmp/ReturnPickDetailSkuCard.vue'
import DetailTableHeader from '@/business/DetailTableHeader.vue'
import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
import { useGlobalToast } from '@/composables/useGlobalToast'
import type { SortItem } from '@/composables/useDraft'
import type { OrderStatus } from '@/utils/bill'

const globalToast = useGlobalToast()
// mescroll相关
const { mescrollInit, getMescroll, downCallback } = useMescroll(onPageScroll, onReachBottom)

// 获取路由参数中的订单ID和状态
const orderId = ref<string>('')
const status = ref<OrderStatus>('applied')

onLoad((options: any) => {
  orderId.value = options.id
  status.value = options.status
})

// 合并表头和排序配置
const columns = ref([
  { title: '规格' },
  { title: '退货数量', field: 'qty', sortable: true, asc: 0 as 0 | 1 | -1 },
])

const sorts = ref<SortItem[]>([])

// 使用 usePagination 实现分页查询
const {
  data: skuList,
  total,
  reload: reloadSkuList,
  page,
} = usePagination(
  (page, pageSize) => Apis.vehsaleusesignbckInterface.queryDetailsUsingPOST_2({
    data: {
      num: orderId.value,
      page: page - 1, // 后端页码从0开始，usePagination从1开始
      pageSize,
      sorts: sorts.value.map(sort => ({
        field: sort.field,
        asc: sort.asc === 1,
      })),
    },
  }),
  {
    immediate: false,
    append: true,
    initialData: [],
    initialPage: 1,
    initialPageSize: 10,
    total: response => response.total || (response.data?.length || 0),
    data: response => response.data || [],
  },
).onError((error) => {
  globalToast.error(error.error?.message || '加载领货单商品失败')
  getMescroll().endErr()
}).onSuccess((resp) => {
  getMescroll().endBySize(resp.data.data?.length || 0, total.value)
})

// 上拉加载更多
async function upCallback(mescroll: any) {
  if (mescroll.num === 1) {
    await reloadSkuList()
  }
  else {
    page.value = mescroll.num
  }
}

function handleRefresh() {
  const mescroll = getMescroll()
  if (mescroll) {
    mescroll.resetUpScroll()
  }
}

/**
 * 排序变化
 * @param _sorts 排序列表
 */
function handleSortChange(_sorts: SortItem[]) {
  sorts.value = _sorts
  handleRefresh()
}
</script>

<template>
  <view class="product-list-page relative min-h-100vh min-h-screen w-screen flex flex-col bg-white pt-120rpx">
    <!-- 表头+排序 -->
    <view class="fixed left-0 right-0 top-0 z-10 box-border w-screen bg-white p-3">
      <DetailTableHeader
        v-model:headers="columns"
        :show-title="false"
        @sort-change="handleSortChange"
      />
    </view>
    <!-- 商品列表 -->
    <mescroll-body
      :down="downOption"
      :up="upOption"
      height="100%"
      @init="mescrollInit"
      @down="downCallback" @up="upCallback"
    >
      <view class="px-3 py-2">
        <ReturnPickDetailSkuCard v-for="(sku, index) in skuList" :key="index" :sku="sku" />
      </view>
    </mescroll-body>
  </view>
</template>

<style lang="scss" scoped>
.product-list-page {
  :deep(.mescroll-body) {
    min-height: calc(100vh - 120rpx) !important;
    min-height: calc(100vh - 120rpx - constant(safe-area-inset-bottom)) !important;
    min-height: calc(100vh - 120rpx - env(safe-area-inset-bottom)) !important;
  }
}
</style>

<route lang="json">
{
  "name": "return-pick-detail-sku-list",
  "style": {
    "navigationBarTitleText": "商品清单详情",
    "backgroundColor": "#F9F9F9"
  }
}
</route>
