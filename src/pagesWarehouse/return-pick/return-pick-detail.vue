<script setup lang="ts">
import { computed, ref } from 'vue'
import ReturnPickDetailSkuCard from './cmp/ReturnPickDetailSkuCard.vue'

const router = useRouter()
const { error: showError } = useGlobalToast()
// 单号
const id = ref<string>('')

// 加载单头详情
const {
  data: order,
  loading: orderLoading,
  send: loadOrderDetail,
} = useRequest(
  (num: string) => Apis.vehsaleusesignbckInterface.getUsingGET_4({
    params: {
      num,
      fetchDetail: false,
    },
  }),
  {
    immediate: false,
  },
).onError((error) => {
  showError(error.error.message || '获取领货退单详情失败')
})

// 单头详情
const orderDetail = computed(() => {
  return order.value?.data
})

// 加载商品数据
const {
  data: skuData,
  loading: skuLoading,
  send: loadSkuData,
} = useRequest(
  (num: string) => Apis.vehsaleusesignbckInterface.queryDetailsUsingPOST_2({
    data: {
      num,
      page: 0,
      pageSize: 3,
    },
  }),
  {
    immediate: false,
  },
).onError((error) => {
  showError(error.error.message || '获取领货退单商品失败')
})

// 商品列表
const skuList = computed(() => {
  return skuData.value?.data || []
})

// 是否还有更多商品
const hasMoreSku = computed(() => {
  return skuData.value?.more
})

// 领货单状态
const orderStatus = computed(() => {
  if (!orderDetail.value)
    return 'initial'
  return getBillStatus(orderDetail.value.stat || 0)
})

// 统计数据 - 上部分数据
const topStats = computed(() => {
  if (!orderDetail.value) {
    return [
      { title: '开单金额(元)', value: '0.00' },
    ]
  }
  return [
    { title: '开单金额(元)', value: orderDetail.value.total || 0 },
  ]
})

// 统计数据 - 下部分数据
const bottomStats = computed(() => {
  // 示例：可以根据实际需求添加更多数据项
  return [
    { title: '商品项数(种)', value: orderDetail.value?.goodsCount || 0 },
    { title: '单品数(个)', value: orderDetail.value?.qty || 0 },
  ]
})

// 跳转到商品清单详情
function goToSkuList() {
  router.push({
    name: 'return-pick-detail-sku-list',
    params: {
      id: id.value,
      status: orderStatus.value,
    },
  })
}

onLoad((options: any) => {
  id.value = options.id as string
  loadOrderDetail(id.value)
  loadSkuData(id.value)
})
</script>

<template>
  <view class="receipt-detail-page relative min-h-screen w-screen flex flex-col bg-white">
    <!-- 主要内容 -->
    <view class="flex-1 p-3">
      <!-- 骨架屏 -->
      <DetailSkeleton v-if="orderLoading || skuLoading" />
      <!-- 实际内容 -->
      <block v-else>
        <!-- 状态栏 -->
        <DetailStatusHeader
          :status="orderStatus"
          :order-no="orderDetail?.num || ''"
          custom-class="mb-3"
          :enable-action="false"
        />

        <!-- 统计卡片 -->
        <DetailStatsCard
          :top-items="topStats"
          :bottom-items="bottomStats"
          custom-class="mb-3"
        />

        <!-- 商品清单 -->
        <view class="flex flex-col rounded-lg bg-white py-3">
          <DetailTableHeader
            title="商品清单"
            :headers="['规格', '退货数']"
            :show-more="hasMoreSku"
            @more-click="goToSkuList"
          />

          <!-- 商品列表 -->
          <view class="space-y-3">
            <view v-for="sku in skuList" :key="sku.goods?.gid" class="border-b border-[#F5F5F5] last:border-0">
              <ReturnPickDetailSkuCard :sku="sku" :status="orderStatus" />
            </view>
          </view>
        </view>

        <!-- 订单信息 -->
        <DetailInfoCard
          :items="[{ label: '收货仓位', value: orderDetail?.wrh?.name },
                   { label: '业务员', value: `${orderDetail?.vehSaleEmpName}[${orderDetail?.vehSaleEmp?.code}]` },
                   { label: '操作人', value: orderDetail?.lastModifyOper },
                   { label: '操作时间', value: orderDetail?.lstupdTime },
          ]"
        />
      </block>
    </view>

    <!-- 底部按钮 -->
    <!-- <view
      v-if="showAbortButton "
      class="sticky bottom-0 bottom-0 left-0 left-0 right-0 right-0 z-10 box-border w-full bg-white p-3"
      style="--wot-button-medium-height: 44px;"
    >
      <view class="flex items-center justify-between">
        <wd-button
          custom-class="flex-auto"
          type="error"
          plain
          block
          :round="false"
          @click="abortOrder"
        >
          作废
        </wd-button>
      </view>
      <wd-gap height="0" safe-area-bottom />
    </view> -->
  </view>
</template>

<style lang="scss" scoped>
.receipt-detail-page {
  min-height: 100vh;
}
</style>

<route lang="json">
{
  "name": "return-pick-detail",
  "style": {
    "navigationBarTitleText": "详情",
    "backgroundColor": "#F9F9F9"
  },
  "meta": {
    "permissions": ["vehSaleUseSignBckView"]
  }
}
</route>
