<!--
 * @Description: 领货返回编辑页骨架屏组件
-->
<script setup lang="ts">
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <view class="return-pick-edit-skeleton w-screen bg-[#F9F9F9]">
    <!-- Main content area skeleton -->
    <view class="main-skeleton">
      <!-- Fixed Tabs and Sort Skeleton -->
      <view class="fixed-header fixed z-2 w-full bg-white">
        <!-- Tabs Skeleton -->
        <view class="tabs-skeleton flex items-center justify-around p-2">
          <wd-skeleton v-for="i in 4" :key="i" :row-col="[{ width: '60px', height: '28px' }]" animation="flashed" />
        </view>
        <!-- Sort Skeleton -->
        <view class="sort-skeleton flex items-center justify-end px-3 py-2">
          <wd-skeleton :row-col="[{ width: '60px', height: '20px' }]" animation="flashed" />
          <view class="mx-2" />
          <wd-skeleton :row-col="[{ width: '60px', height: '20px' }]" animation="flashed" />
        </view>
      </view>

      <!-- Placeholder for fixed header -->
      <view style="height: 88px;" />

      <!-- List Skeleton -->
      <view class="list-skeleton p-2.5">
        <view v-for="i in 4" :key="i" class="mb-3 rounded-lg bg-white p-3 shadow-sm">
          <!-- SKU Card Skeleton -->
          <view class="flex items-start gap-3">
            <wd-skeleton :row-col="[{ width: '100px', height: '100px' }]" animation="flashed" />
            <view class="flex-1">
              <wd-skeleton
                :row-col="[
                  { width: '85%', height: '18px' },
                  { 'width': '65%', 'height': '14px', 'margin-top': '8px' },
                  { 'width': '45%', 'height': '14px', 'margin-top': '8px' },
                ]"
                animation="flashed"
              />
            </view>
          </view>
          <view class="mt-3 flex items-center justify-between">
            <wd-skeleton :row-col="[{ width: '120px', height: '36px' }]" animation="flashed" />
            <wd-skeleton :row-col="[{ width: '100px', height: '36px' }]" animation="flashed" />
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.return-pick-edit-skeleton {
  padding-bottom: calc(44px + 48rpx);
  padding-bottom: calc(44px + 48rpx + const(safe-area-inset-bottom)) !important;
  padding-bottom: calc(44px + 48rpx + env(safe-area-inset-bottom)) !important;
}
</style>
