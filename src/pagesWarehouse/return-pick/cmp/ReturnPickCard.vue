<script setup lang="ts">
import type { VehSaleUseSignBckQueryResponseDTO } from '@/api/globals'

// 定义组件属性
const props = defineProps({
  record: {
    type: Object as PropType<VehSaleUseSignBckQueryResponseDTO>,
    required: true,
  },
})

const emit = defineEmits(['click'])

// 点击卡片
function handleClick() {
  emit('click', props.record)
}
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <view class="return-pick-card mb-2 w-[calc(100%-48rpx)] rounded-lg bg-white p-3" @click="handleClick">
    <!-- 单号 -->
    <view class="mb-3 flex items-center justify-between">
      <text class="text-4 text-[#2D2D2D] font-medium">
        {{ record.num || '-' }}
      </text>
    </view>

    <!-- 基本信息 -->
    <view class="mb-1 flex flex-wrap">
      <view class="mb-2 min-w-[50%] flex break-all">
        <text class="flex-none text-3 text-[#8A8A8A]">
          收货仓位：
        </text>
        <text class="text-3 text-[#2D2D2D]">
          {{ record.wrh?.name || '-' }}
        </text>
      </view>

      <view class="mb-2 min-w-[50%] flex break-all">
        <text class="flex-none text-3 text-[#8A8A8A]">
          业务员：
        </text>
        <text class="text-3 text-[#2D2D2D]">
          {{ record.vehSaleEmpName }}[{{ record.vehSaleEmp?.code }}]
        </text>
      </view>

      <view class="mb-2 min-w-[50%] flex break-all">
        <text class="flex-none text-3 text-[#8A8A8A]">
          操作时间：
        </text>
        <text class="text-3 text-[#2D2D2D] font-500">
          {{ formatDate(record.lstupdTime) }}
        </text>
      </view>
    </view>

    <!-- 统计信息 -->
    <view class="flex items-center justify-between rounded bg-[#F5F5F5] px-3 py-1">
      <view class="flex items-center">
        <text class="text-28rpx text-[#8A8A8A]">
          共
        </text>
        <text class="mx-1 text-4 text-[#1A1A1A] font-medium">
          {{ record.goodsCount || 0 }}
        </text>
        <text class="text-28rpx text-[#8A8A8A]">
          种商品
        </text>
      </view>

      <view class="flex items-center">
        <text class="text-28rpx text-[#8A8A8A]">
          共
        </text>
        <view class="mx-1 flex items-center">
          <text class="text-3 text-[#8A8A8A]">
            ¥
          </text>
          <text class="text-4 text-[#1A1A1A] font-medium">
            {{ record.total || 0 }}
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.return-pick-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

  &:not(:last-child) {
    margin-bottom: 16rpx;
  }
}
</style>
