<!--
 * @Author: weish<PERSON>
 * @Date: 2025-05-20 16:44:22
 * @LastEditTime: 2025-06-20 16:18:16
 * @LastEditors: weisheng
 * @Description: 领货返回编辑页头部组件 - 只处理业务员选择
 * @FilePath: /lsym-cx-mini/src/pagesWarehouse/return-pick/cmp/ReturnPickEditHeader.vue
 * 记得注释
-->
<script setup lang="ts">
import type { SelectPickerBeforeConfirm, SelectPickerInstance } from 'wot-design-uni/components/wd-select-picker/types'
import type { GCN } from '@/api/globals'
import { createGlobalLoadingMiddleware } from '@/api/core/middleware'

const props = defineProps({
  // 当前业务员
  currentEmp: {
    type: [Number, String],
    default: '',
  },
  // 自定义类名
  customClass: {
    type: String,
    default: '',
  },
  // 自定义样式
  customStyle: {
    type: String,
    default: '',
  },
  // 清空草稿
  clearAndUpdate: {
    type: Function,
  },
})

const emit = defineEmits<{
  (e: 'submit', emp: GCN, wrh: GCN | null): void
}>()

const { confirm: showConfirm, alert: showAlert } = useGlobalMessage()
const globalLoading = useGlobalLoading()
const globalToast = useGlobalToast()

const { currentWms } = useWmsStore()

const { empList, empLoading, currentEmp, empId } = useVehSaleEmpSelect(currentWms?.gid)
const confirmedEmpId = ref<string | number>(props.currentEmp)
const empSelectRef = ref<SelectPickerInstance>()
const empListData = computed(() => {
  return empList.value.filter(item => item.label !== '全部业务员')
})

// 仓位相关
const currentWrh = ref<GCN | null>(null)

// 获取仓位信息
const {
  data: wrhData,
  loading: _wrhLoading,
  send: loadWrh,
} = useRequest(
  (vehSaleEmpGid: number) =>
    Apis.vehsalewrhinvInterface.getWrhUsingGET({
      params: {
        vehSaleEmpGid,
      },
    }),
  { immediate: false, middleware: createGlobalLoadingMiddleware({
    loadingText: '操作中...',
  }) },
).onError((error) => {
  globalLoading.close()
  globalToast.error(error.error?.message || '获取仓位信息失败')
}).onSuccess(() => {
  globalLoading.close()
  currentWrh.value = wrhData.value?.data || null
})

/**
 * 领货校验
 * 校验通过则跳转到领货页面
 * 校验失败则提示
 */
const { send: createCheck } = useRequest(vehSaleEmpGid => Apis.vehsaleusesignInterface.verifyCreateUsingPOST_3({ params: {
  vehSaleEmpGid,
  middleware: createGlobalLoadingMiddleware({
    loadingText: '校验中...',
  }),
} }), {
  immediate: false,
}).onError(() => {
  globalLoading.close()
})

function handleSelectEmp(event: { value: string, selectedItems: { label: string, value: string } }) {
  confirmedEmpId.value = event.value
  empId.value = event.value
  emit('submit', { gid: Number(confirmedEmpId.value) }, currentWrh.value)
}

const beforeEmpConfirm: SelectPickerBeforeConfirm = async (value, resolve) => {
  if (!value) {
    globalToast.warning('请选择业务员')
    resolve(false)
  }
  else if (value !== confirmedEmpId.value && confirmedEmpId.value) {
    showConfirm({
      title: '提示',
      msg: '切换业务员将会清空购物车，确认切换吗？',
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      async success(res: any) {
        if (res.action === 'confirm') {
          try {
            const result = await loadWrh(Number(value))
            if (!result.data?.gid) {
              const selectedEmp = empListData.value.find(item => item.value === value)
              showAlert({
                title: '提示',
                msg: `所选业务员“${selectedEmp?.label}”车销仓内无货品，无法进行领货返回，请重新选择其他业务员！`,
                confirmButtonText: '确认',
              })
              return resolve(false)
            }
            if (CommonUtil.isFunction(props.clearAndUpdate)) {
              await props.clearAndUpdate()
            }
            await createCheck(Number(value))

            resolve(true)
            globalLoading.close()
            globalToast.success(`已切换至${currentEmp.value?.label}业务员`)
          }
          catch (error: any) {
            resolve(false)
            globalLoading.close()
            globalToast.error(error.message || '切换业务员失败')
          }
        }
        else {
          resolve(false)
        }
      },
    })
  }
  else {
    if (!confirmedEmpId.value) {
      try {
        const result = await loadWrh(Number(value))
        if (!result.data?.gid) {
          const selectedEmp = empListData.value.find(item => item.value === value)
          showAlert({
            title: '提示',
            msg: `所选业务员“${selectedEmp?.label}”车销仓内无货品，无法进行领货返回，请重新选择其他业务员！`,
            confirmButtonText: '确认',
          })
          return resolve(false)
        }
        resolve(true)
      }
      catch (error) {
        resolve(false)
        globalLoading.close()
        globalToast.error('获取仓位信息失败')
      }
    }
    resolve(true)
  }
}

// 处理业务员弹框关闭
function handleEmpClose() {
  // 如果已经有业务员选择，恢复原来的状态
  if (props.currentEmp) {
    confirmedEmpId.value = props.currentEmp
    empId.value = String(props.currentEmp)
  }
  else {
    // 如果没有完整选择，重新弹出业务员选择弹框
    nextTick(() => {
      globalToast.warning('请选择业务员')
      empSelectRef.value?.open()
    })
  }
}

defineExpose({
  openSelect: () => {
    // 选择业务员
    empSelectRef.value?.open()
  },
})
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <view class="pick-edit-header z-3 box-border w-full bg-[var(--greyapplication-bottomlayer)]" :style="customStyle" :class="customClass">
    <view class="px-28rpx">
      <wd-select-picker
        ref="empSelectRef"
        v-model="confirmedEmpId"
        :close-on-click-modal="false"
        :columns="empListData"
        :loading="empLoading"
        title="选择业务员"
        filter-placeholder="业务员名称/代码"
        label="业务员"
        type="radio"
        filterable use-default-slot hide-label
        :before-confirm="beforeEmpConfirm"
        @confirm="handleSelectEmp"
        @cancel="handleEmpClose"
      >
        <template #default>
          <view class="h-88rpx flex items-center justify-between text-28rpx text-[var(--textapplication-text-2)]">
            <view class="w-[calc(100%-60rpx)] flex">
              <view class="w-20 shrink-0">
                <text class="i-carbon-user mr-1" />
                <text class="mr-2">
                  业务员
                </text>
              </view>
              <text class="mr-2 w-[calc(100%-160rpx)] flex-1 truncate text-[var(--textapplication-text-1)]">
                {{ currentEmp?.label || '请选择业务员' }}
              </text>
            </view>
            <text class="i-carbon-chevron-right text-[var(--textapplication-text-1)]" />
          </view>
        </template>
      </wd-select-picker>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.pick-edit-header {
  background-color: #fff;

  :deep() {
    .wd-select-picker__header {
      height: 112rpx;
      line-height: 112rpx;
    }
  }

  :deep() {
    .wd-radio__label {
      width: calc(100% - 60rpx);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      text-align: left;
    }
  }
}
</style>
