/*
 * @Description: 物流中心选择组合式API
 */
import { computed, ref } from 'vue'

export function useWmsSelect() {
  const { data: wmsData, loading: wmsLoading } = useRequest(Apis.mdataInterface.queryWmsUsingPOST({ data: {} }), {
    immediate: true,
  })

  // WMS列表
  const wmsList = computed(() => {
    const items = (wmsData.value?.data || []).map((item: any) => {
      return {
        label: `${item.name}[${item.code}]`,
        value: String(item.gid || ''),
      }
    })

    // 添加全部选项
    return [
      {
        label: '全部WMS',
        value: '',
      },
      ...items,
    ]
  })
  // 当前选中的WMS ID
  const wmsId = ref<string>('')

  // 当前选中的WMS
  const currentWms = computed(() => {
    return wmsList.value.find(item => item.value === wmsId.value)
  })

  /**
   * WMS选择
   * @param event 结合wd-select-picker组件的confirm事件
   * @param event.value 选中的WMS ID值
   * @param event.selectedItems 选中的WMS项
   * @param event.selectedItems.value 选中的WMS项值
   * @param event.selectedItems.label 选中的WMS项标签
   */
  function handleWmsSelect(event: { value: string, selectedItems: { label: string, value: string } }) {
    wmsId.value = event.value
  }

  return {
    wmsList,
    wmsLoading,
    handleWmsSelect,
    wmsId,
    currentWms,
    wmsData,
  }
}
