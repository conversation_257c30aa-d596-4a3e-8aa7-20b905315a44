/*
 * @Author: weish<PERSON>
 * @Date: 2025-05-14 14:32:42
 * @LastEditTime: 2025-06-13 15:51:38
 * @LastEditors: weisheng
 * @Description:
 * @FilePath: /lsym-cx-mini/src/composables/useSortLineSelect.ts
 * 记得注释
 */
/*
 * @Description: 线路选择组合式API
 */
import { computed, ref } from 'vue'

/**
 * 线路选择
 * @param vehSaleEmpGid 车销业务员Gid
 * @returns 线路列表、线路选择、当前选中的线路ID、当前选中的线路
 */
export function useSortLineSelect(vehSaleEmpGid?: number) {
  const { data: sortLineData, loading: sortLineLoading, send: getSortLine } = useRequest(() => Apis.mdataInterface.querySortLineUsingPOST({ data: { vehSaleEmpGid } }), {
    immediate: true,
  })

  // 线路列表
  const sortLineList = computed(() => {
    const lines = (sortLineData.value?.data || []).map((item) => {
      return {
        label: `${item.name}`,
        value: String(item.uuid || ''),
      }
    })

    // 添加全部选项
    return [
      {
        label: '全部线路',
        value: '',
      },
      ...lines,
    ]
  })
  // 当前选中的线路ID
  const sortLineId = ref<string>('')

  // 当前选中的线路
  const currentSortLine = computed(() => {
    return sortLineList.value.find(item => item.value === sortLineId.value)
  })

  /**
   * 线路选择
   * @param event 结合wd-select-picker组件的confirm事件
   * @param event.value 选中的线路ID值
   * @param event.selectedItems 选中的线路项
   * @param event.selectedItems.value 选中的线路项值
   * @param event.selectedItems.label 选中的线路项标签
   */
  function handleSortLineSelect(event: { value: string, selectedItems: { label: string, value: string } }) {
    sortLineId.value = event.value
  }

  return {
    sortLineList,
    sortLineLoading,
    handleSortLineSelect,
    sortLineId,
    currentSortLine,
    getSortLine,
  }
}
