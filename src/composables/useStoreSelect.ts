/*
 * @Author: weish<PERSON>
 * @Date: 2025-05-14 11:25:58
 * @LastEditTime: 2025-06-10 15:42:38
 * @LastEditors: weisheng
 * @Description:
 * @FilePath: /lsym-cx-mini/src/composables/useStoreSelect.ts
 * 记得注释
 */
export function useStoreSelect({ vehSaleEmpGid, wmsGid }: { vehSaleEmpGid?: number, wmsGid?: number } = {}, immediate = true) {
  const saleEmpGid = ref(vehSaleEmpGid)
  const wmsGidValue = ref(wmsGid)
  const { data: storeData, loading: storeLoading, send: queryStore } = useRequest((vehSaleEmpGid?: number, wmsGid?: number) => Apis.mdataInterface.queryStoreUsingPOST({ data: {
    page: -1,
    pageSize: -1,
    vehSaleEmpGid: vehSaleEmpGid || saleEmpGid.value,
    wmsGid: wmsGid || wmsGidValue.value,
  } }), {
    immediate,
  })

  // 门店列表
  const storeList = computed(() => {
    const routes = (storeData.value?.data || []).map((item) => {
      return {
        label: `${item.name}[${item.code}]`,
        value: item.gid,
      }
    })

    // 添加全部路线选项
    return [
      {
        label: '全部门店',
        value: '',
      },
      ...routes,
    ]
  })
  // 当前选中的门店Gid
  const storeGid = ref<string | number>('')

  // 当前选中的门店
  const currentStore = computed(() => {
    return storeList.value.find((item) => {
      return `${item.value}` === `${storeGid.value}`
    }) || {
      label: '全部门店',
      value: '',
    }
  })

  /**
   * 门店选择
   * @param event 结合wd-select-picker组件的confirm事件
   * @param event.value 选中的门店ID值
   * @param event.selectedItems 选中的门店项
   * @param event.selectedItems.value 选中的门店项值
   * @param event.selectedItems.label 选中的门店项标签
   */
  function handleStoreSelect(event: { value: string, selectedItems: { label: string, value: string } }) {
    storeGid.value = event.value
  }

  return {
    storeList,
    storeLoading,
    handleStoreSelect,
    storeGid,
    currentStore,
    queryStore,
    saleEmpGid,
  }
}
