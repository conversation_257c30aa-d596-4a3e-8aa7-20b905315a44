import { ref } from 'vue'

/**
 * 监听刷新数据事件的参数
 */
export interface WatchRefreshOptions {
  callback: () => void // 刷新数据回调
  name: string // 用于标识是哪个数据源需要刷新
}

/**
 * 监听跳转事件的参数
 */
export interface WatchRedirectOptions {
  callback: (id: string) => void // 跳转回调
  name: string // 用于标识是哪个数据源需要跳转
}

/**
 * 监听刷新数据事件
 */
export function useWatchRefreshData({ callback, name }: WatchRefreshOptions) {
  const refreshData = ref(false)

  onMounted(() => {
    uni.$on(`refresh-${name}`, () => {
      refreshData.value = true
    })
  })

  onUnmounted(() => {
    uni.$off(`refresh-${name}`)
  })

  onShow(() => {
    if (refreshData.value) {
      refreshData.value = false
      callback()
    }
  })
}

/**
 * 发送刷新数据事件
 * @param name 用于标识是哪个数据源需要刷新
 * @returns 发送刷新数据事件的函数
 */
export function useSendRefreshData(name: string) {
  function emitRefreshData() {
    uni.$emit(`refresh-${name}`)
  }
  return { emitRefreshData }
}

/**
 * 监听跳转事件
 */
export function useWatchReditct({ callback, name }: WatchRedirectOptions) {
  const redirctId = ref<string>('')

  onMounted(() => {
    uni.$on(`redirct-${name}`, (id: string) => {
      redirctId.value = id
    })
  })

  onUnmounted(() => {
    uni.$off(`redirct-${name}`)
  })

  onShow(() => {
    if (redirctId.value) {
      callback(redirctId.value)
    }
  })
}

/**
 * 发送刷新数据事件
 * @param name 用于标识是哪个数据源需要刷新
 * @returns 发送刷新数据事件的函数
 */
export function useSendRedirect(name: string) {
  function emitRedirect(id: string) {
    uni.$emit(`redirct-${name}`, id)
  }
  return { emitRedirect }
}
