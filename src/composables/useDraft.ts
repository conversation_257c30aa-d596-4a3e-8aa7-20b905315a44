import { useUserStore } from '@/store/useUserStore'
import type { Response_List_VehSaleGetDraftGoodsResponseDTO_, VehSaleCheckedRequestDTO, VehSaleDraftGoodsSubmitDTO, VehSaleDraftResponseDTO, VehSaleEmp, VehSaleGdQpcDTO, VehSaleGetDraftGoodsResponseDTO, VehSaleRemoveDraftRequestDTO, VehSaleUseSignDraftQueryFilter } from '@/api/globals'
import { createGlobalLoadingMiddleware } from '@/api/core/middleware'

/**
 * 单据类型
 * vehSaleUseSign  - 领货
 * vehSaleUseSignBck -领货退
 * vehSale - 车销
 * vehSaleBck - 车销退
 */
export type ClsType = 'vehSaleUseSign' | 'vehSaleUseSignBck' | 'vehSale' | 'vehSaleBck'

export interface SortItem {
  /**
   * 排序方式
   * 1 升序，0 重置状态，-1 降序
   */
  asc: 0 | 1 | -1
  /**
   * 排序字段
   * [required]
   */
  field: string
  /**
   * 排序名称
   */
  name: string
}
/**
 * 购物车商品
 */
export type CartSku = VehSaleGetDraftGoodsResponseDTO & { checked?: boolean }

/**
 * 用于删除接口的商品对象
 */
export type RemovedSku = Omit<VehSaleRemoveDraftRequestDTO, 'cls' | 'vehSaleEmpGid'>

/**
 * 草稿
 * @param cls 单据类型
 * @param vehSaleEmp 业务员（默认使用当前登录的业务员，仓管需要手动传入）
 */
export function useDraft(cls: ClsType) {
  const userStore = useUserStore()
  const globalLoading = useGlobalLoading()
  const globalToast = useGlobalToast()
  // 当前业务员
  const seleEmp = ref<VehSaleEmp>(userStore.vehSaleEmp!)

  /**
   * 商品查询参数
   */
  const skuQueryParams = reactive<(Omit<VehSaleUseSignDraftQueryFilter, 'page' | 'pageSize' | 'sorts'> & { sorts: SortItem[] })>({
    cls,
    vehSaleEmpGid: seleEmp.value?.gid || 0,
    sortCode: '',
    sorts: [
      {
        field: 'busInvQty',
        asc: 0,
        name: '库存',
      },
      {
        field: 'vehSaleDraftQty',
        asc: 0,
        name: '已购',
      },
    ],
  })

  /**
   * 组装商品列表查询参数
   */
  function getSkuQueryParams(): Omit<VehSaleUseSignDraftQueryFilter, 'page' | 'pageSize' > {
    return {
      ...skuQueryParams,
      sortCode: skuQueryParams.sortCode || undefined,
      keyword: skuQueryParams.keyword || undefined,
      storeGid: skuQueryParams.storeGid || undefined,
      sorts: skuQueryParams.sorts.filter(item => item.asc !== 0).map(item => ({
        field: item.field,
        asc: item.asc === 1,
      })),
    }
  }

  // 获取草稿基础信息
  const { data: draftInfo, loading: draftInfoLoading, send: getDraftInfo } = useRequest(
    () => Apis.vehsaledraftInterface.getDraftInfoUsingGET({
      params: {
        cls,
        vehSaleEmpGid: seleEmp.value?.gid || 0,
      },
    }),
    {
      immediate: false,
    },
  ).onError((err) => {
    globalLoading.close()
    globalToast.error(err.error?.message || '获取草稿信息失败')
  })

  /**
   * 更新草稿信息
   * @param _draftInfo 草稿信息
   */
  function updateDraftInfo(_draftInfo?: VehSaleDraftResponseDTO) {
    if (draftInfo.value.data) {
      draftInfo.value.data.total = _draftInfo ? _draftInfo.total : draftInfo.value.data.total
      draftInfo.value.data.skuCount = _draftInfo ? _draftInfo.goodsCount : draftInfo.value.data.skuCount
      draftInfo.value.data.categorySkuCount = _draftInfo ? _draftInfo.categorySkuCount : draftInfo.value.data.categorySkuCount
    }
    else {
      draftInfo.value.data = {
        total: _draftInfo ? _draftInfo.total : 0,
        skuCount: _draftInfo ? _draftInfo.goodsCount : 0,
        categorySkuCount: _draftInfo ? _draftInfo.categorySkuCount : {},
      }
    }
  }

  // 校验结果数据
  const verifySubmitData = ref<Response_List_VehSaleGetDraftGoodsResponseDTO_>({
    code: 0,
  })

  const { loading: verifySubmitLoading, send: verifySubmit } = useRequest(() => Apis.vehsaledraftInterface.verifySubmitUsingPOST({
    params: {
      cls,
      vehSaleEmpGid: seleEmp.value?.gid || 0,
    },
  }), {
    immediate: false,
    middleware: createGlobalLoadingMiddleware({
      loadingText: '校验中...',
    }),
  }).onError((err) => {
    globalLoading.close()
    globalToast.error(err.error?.message || '校验失败')
  }).onSuccess((resp) => {
    const data = CommonUtil.deepClone(resp.data)
    if (data && data.data) {
      data.data = data.data.map((item) => {
        // 将item.qpcDetails中qpcStr与item.goods.qpcStr相等的项，顺序调整一下，放在第一个
        const qpcDetails = item.qpcDetails || []
        const qpcStr = item.goods.qpcStr || ''
        const qpcDetail = qpcDetails.find(item => item.qpcStr === qpcStr)
        if (qpcDetail) {
          qpcDetails.splice(qpcDetails.indexOf(qpcDetail), 1) // 删除qpcDetail
          qpcDetails.unshift(qpcDetail) // 将qpcDetail放在第一个
        }
        return {
          ...item,
          qpcDetails,
        }
      })
    }
    verifySubmitData.value = data
  })

  // 获取商品列表
  const {
    data: skuList,
    loading: skuListLoading,
    page: skuPage,
    reload: reloadSkuList,
    total: skuTotal,
    send: getSkuList,
    onSuccess: onSkuListSuccess,
    onError: onSkuListError,
  } = usePagination(
    (page, pageSize) =>
      Apis.vehsaledraftInterface.queryGoodsUsingPOST({
        data: {
          ...getSkuQueryParams(),
          page: page - 1, // 后端页码从0开始，usePagination从1开始
          pageSize,
        },
      }),
    {
      immediate: false,
      append: true,
      initialData: [],
      initialPageSize: 10,
      initialPage: 1,
      total: response => response.total,
      data: (response) => {
        return (response.data || []).map((item) => {
          // 将item.qpcDetails中qpcStr与item.goods.qpcStr相等的项，顺序调整一下，放在第一个
          const qpcDetails = item.qpcDetails || []
          const qpcStr = item.goods.qpcStr || ''
          const qpcDetail = qpcDetails.find(item => item.qpcStr === qpcStr)
          if (qpcDetail) {
            qpcDetails.splice(qpcDetails.indexOf(qpcDetail), 1) // 删除qpcDetail
            qpcDetails.unshift(qpcDetail) // 将qpcDetail放在第一个
          }
          return {
            ...item,
            qpcDetails,
          }
        })
      },
    },
  )

  // 提交草稿
  const {
    loading: submitLoading,
    send: submitDraft,
    onSuccess: onSubmitSuccess,
    onError: onSubmitError,
  } = useRequest(
    (data: Omit<VehSaleDraftGoodsSubmitDTO, 'cls' | 'vehSaleEmp'>) =>
      Apis.vehsaledraftInterface.submitDraftUsingPOST({
        data: {
          ...data,
          cls,
          vehSaleEmp: seleEmp.value!,
        },
      }),
    {
      middleware: createGlobalLoadingMiddleware({
        loadingText: '操作中...',
        delay: 200,
      }),
      immediate: false,
    },
  ).onError((err) => {
    globalLoading.close()
    globalToast.error(err.error?.message || '更新失败')
  }).onSuccess((resp) => {
    updateDraftInfo(resp.data.data)
  })

  // 删除草稿
  const {
    loading: removeLoading,
    send: removeDraft,
    onSuccess: onRemoveSuccess,
    onError: onRemoveError,
  } = useRequest(
    (data: RemovedSku[]) =>
      Apis.vehsaledraftInterface.removeDraftUsingPOST({
        data: data.map(item => ({
          ...item,
          cls,
          vehSaleEmpGid: seleEmp.value?.gid || 0,
        })),
      }),
    {
      immediate: false,
      middleware: createGlobalLoadingMiddleware({
        loadingText: '删除中...',
      }),
    },
  ).onError((err) => {
    globalLoading.close()
    globalToast.error(err.error?.message || '删除失败')
  }).onSuccess((resp) => {
    globalLoading.close()
    globalToast.success('删除成功')
    updateDraftInfo(resp.data.data)
  })

  // 清空草稿
  const {
    loading: clearLoading,
    send: clearDraft,
    onSuccess: onClearSuccess,
    onError: onClearError,
  } = useRequest(
    () =>
      Apis.vehsaledraftInterface.clearDraftUsingPOST({
        params: {
          cls,
          vehSaleEmpGid: seleEmp.value?.gid || 0,
        },
      }),
    {
      immediate: false,
      middleware: createGlobalLoadingMiddleware({
        loadingText: '操作中...',
      }),
    },
  )

  const cartSkuQueryParams = reactive<(Omit<VehSaleUseSignDraftQueryFilter, 'page' | 'pageSize' | 'sorts'> & { sorts: SortItem[] })>({
    cls,
    vehSaleEmpGid: seleEmp.value?.gid || 0,
    sortCode: '',
    sorts: [
      {
        field: 'busInvQty',
        asc: 0,
        name: '库存',
      },
      {
        field: 'vehSaleDraftQty',
        asc: 0,
        name: '已购',
      },
    ],
  })

  function getCartSkuQueryParams(): Omit<VehSaleUseSignDraftQueryFilter, 'page' | 'pageSize' > {
    return {
      ...cartSkuQueryParams,
      sortCode: cartSkuQueryParams.sortCode || undefined,
      storeGid: skuQueryParams.storeGid || undefined,
      sorts: cartSkuQueryParams.sorts.filter(item => item.asc !== 0).map(item => ({
        field: item.field,
        asc: item.asc === 1,
      })),
    }
  }

  /**
   * 购物车已选商品列表
   */
  const checkedSkuList = ref<number[]>([])

  /**
   * 合并和排序 qpcDetails
   * @param item 商品项
   * @returns 处理后的 qpcDetails
   */
  function processQpcDetails(item: VehSaleGetDraftGoodsResponseDTO) {
    const qpcDetails = [...(item.qpcDetails || [])]
    const goodsQpcDetails = item.goods.qpcDetails || []
    const currentQpcStr = item.goods.qpcStr || ''

    // 合并 goods 中的 qpcDetails，避免重复
    goodsQpcDetails.forEach((goodsQpcDetail: VehSaleGdQpcDTO) => {
      const exists = qpcDetails.some(detail => detail.qpcStr === goodsQpcDetail.qpcStr)
      if (!exists) {
        qpcDetails.push({
          qty: 0,
          price: Number(((item.goods.singlePrice || 0) * (goodsQpcDetail.qpc || 0)).toFixed(4)),
          qpcStr: goodsQpcDetail.qpcStr!,
          singlePrice: item.goods.singlePrice!,
          total: 0,
          munit: goodsQpcDetail.munit!,
          qpc: goodsQpcDetail.qpc!,
          qtyStr: '0',
        })
      }
    })

    // 将匹配的 qpcDetail 移动到第一位
    const targetIndex = qpcDetails.findIndex(detail => detail.qpcStr === currentQpcStr)
    if (targetIndex > 0) {
      const [targetDetail] = qpcDetails.splice(targetIndex, 1)
      qpcDetails.unshift(targetDetail)
    }

    return qpcDetails
  }

  /**
   * 处理购物车商品数据
   * @param item 原始商品数据
   * @returns 处理后的商品数据
   */
  function processCartSkuItem(item: VehSaleGetDraftGoodsResponseDTO): CartSku {
    return {
      ...item,
      qpcDetails: processQpcDetails(item),
      checked: checkedSkuList.value.includes(item.goods.gid!),
    }
  }

  // 获取购物车商品列表
  const {
    data: cartSkuList,
    loading: cartSkuListLoading,
    page: cartSkuPage,
    reload: reloadCartSkuList,
    total: cartSkuTotal,
    send: getCartSkuList,
    onSuccess: onCartSkuListSuccess,
    onError: onCartSkuListError,
  } = usePagination(
    (page, pageSize) =>
      Apis.vehsaledraftInterface.getDraftUsingPOST({
        data: {
          ...getCartSkuQueryParams(),
          page: page - 1,
          pageSize,
          storeGid: cartSkuQueryParams.storeGid,
        },
      }),
    {
      immediate: false,
      append: true,
      initialData: [],
      initialPageSize: 10,
      initialPage: 1,
      total: response => response.total,
      data: response => (response.data || []).map(processCartSkuItem) as CartSku[],
    },
  )

  /**
   * 已选商品数量
   */
  const checkedSkuCount = ref(0)

  /**
   * 购物车是否全选
   */
  const isAllChecked = ref(false)

  // 切换选中商品
  const {
    loading: checkedLoading,
    send: setChecked,
    onSuccess: onCheckedSuccess,
    onError: onCheckedError,
  } = useRequest(
    (data: Omit<VehSaleCheckedRequestDTO, 'cls' | 'vehSaleEmpGid'>) =>
      Apis.vehsaledraftInterface.checkedUsingPOST({
        data: {
          ...data,
          cls,
          vehSaleEmpGid: seleEmp.value?.gid || 0,
        },
      }),
    {
      immediate: false,
      middleware: createGlobalLoadingMiddleware({
        loadingText: '操作中...',
      }),
    },
  ).onSuccess((resp) => {
    checkedSkuList.value = resp.data.data || []
    checkedSkuCount.value = resp.data.data?.length || 0
    if (checkedSkuCount.value === draftInfo.value.data?.skuCount) {
      isAllChecked.value = true
    }
    else {
      isAllChecked.value = false
    }
    cartSkuList.value.forEach((item) => {
      if (checkedSkuList.value.includes(item.goods.gid!)) {
        item.checked = true
      }
      else {
        item.checked = false
      }
    })
  }).onError((err) => {
    globalLoading.close()
    globalToast.error(err.error?.message || '操作失败')
  })

  /**
   * 删除所有已选中状态商品
   */
  const {
    send: removeCheckedSku,
  } = useRequest(
    () =>
      Apis.vehsaledraftInterface.removeCheckedUsingPOST({
        params: {
          cls,
          vehSaleEmpGid: seleEmp.value?.gid || 0,
        },
      }),
    {
      immediate: false,
      middleware: createGlobalLoadingMiddleware({
        loadingText: '操作中...',
      }),
    },
  ).onSuccess((resp) => {
    globalLoading.close()
    globalToast.success('删除成功')
    isAllChecked.value = false
    checkedSkuCount.value = 0
    checkedSkuList.value = []
    // 更新草稿信息
    updateDraftInfo(resp.data.data)
  }).onError((err) => {
    globalLoading.close()
    globalToast.error(err.error?.message || '操作失败')
  })

  /**
   * 更新已选商品列表
   * @param sku 商品
   */
  function updateSkuChecked(sku: CartSku) {
    setChecked({
      gdGid: sku.goods.gid!,
      checked: sku.checked || false,
    })
  }

  /**
   * 全选购物车商品
   * 全选则将当前cartSkuList商品置为已选，并更新checkedSkuList
   * 否则将当前cartSkuList商品置为未选，并更新checkedSkuList
   */
  function selectAllCartSku() {
    if (isAllChecked.value) {
      setChecked({
        checked: false,
      })
    }
    else {
      setChecked({
        checked: true,
      })
    }
  }

  const isLoading = computed(() => draftInfoLoading.value || skuListLoading.value || cartSkuListLoading)

  return {
    // 加载状态
    isLoading,

    // 草稿基础信息
    draftInfoLoading,
    draftInfo,
    getDraftInfo,

    // 商品列表
    skuList,
    reloadSkuList,
    skuPage,
    skuTotal,
    skuQueryParams,
    skuListLoading,
    getSkuList,
    onSkuListSuccess,
    onSkuListError,

    // 提交草稿
    submitLoading,
    submitDraft,
    onSubmitSuccess,
    onSubmitError,

    // 删除草稿
    removeLoading,
    removeDraft,
    onRemoveSuccess,
    onRemoveError,

    // 清空草稿
    clearLoading,
    clearDraft,
    onClearSuccess,
    onClearError,

    // 购物车商品列表
    cartSkuList,
    cartSkuPage,
    cartSkuTotal,
    cartSkuQueryParams,
    cartSkuListLoading,
    getCartSkuList,
    reloadCartSkuList,
    onCartSkuListSuccess,
    onCartSkuListError,
    // 更新已选商品列表
    updateSkuChecked,
    checkedSkuCount,
    selectAllCartSku,
    isAllChecked,
    checkedSkuList,

    // 切换商品选中状态
    checkedLoading,
    setChecked,
    onCheckedSuccess,
    onCheckedError,

    // 删除所有选中状态的商品
    removeCheckedSku,
    // 业务员
    seleEmp,
    // 获取购物车分类
    // getCartCategoryList,
    // categoryList,
    // categoryLoading,

    // 校验提交
    verifySubmitData,
    verifySubmitLoading,
    verifySubmit,
  }
}
