/*
 * @Author: weisheng
 * @Date: 2025-04-18 10:46:28
 * @LastEditTime: 2025-05-17 11:22:32
 * @LastEditors: weisheng
 * @Description:
 * @FilePath: /lsym-cx-mini/src/composables/useGlobalMessage.ts
 * 记得注释
 */
import { deepMerge, isString } from 'wot-design-uni/components/common/util'
import type { MessageOptions, MessageResult } from 'wot-design-uni/components/wd-message-box/types'

export type GlobalMessageOptions = MessageOptions & {
  success?: (res: MessageResult) => void
  fail?: (res: MessageResult) => void
}

interface GlobalMessage {
  messageOptions: GlobalMessageOptions | null
  currentPage: string
}

export const useGlobalMessage = defineStore('global-message', {
  state: (): GlobalMessage => ({
    messageOptions: null,
    currentPage: '',
  }),
  getters: {},
  actions: {
    show(option: GlobalMessageOptions | string) {
      this.currentPage = getCurrentPath()
      this.messageOptions = {
        ...(isString(option) ? { title: option } : option),
        cancelButtonProps: {
          round: false,
        },
        confirmButtonProps: {
          round: false,
        },
      }
    },
    alert(option: GlobalMessageOptions | string) {
      const messageOptions = deepMerge({ type: 'alert' }, isString(option) ? { title: option } : option) as MessageOptions
      messageOptions.showCancelButton = false
      this.show(messageOptions)
    },
    confirm(option: GlobalMessageOptions | string) {
      const messageOptions = deepMerge({ type: 'confirm' }, isString(option) ? { title: option } : option) as MessageOptions
      messageOptions.showCancelButton = true
      this.show(messageOptions)
    },
    prompt(option: GlobalMessageOptions | string) {
      const messageOptions = deepMerge({ type: 'prompt' }, isString(option) ? { title: option } : option) as MessageOptions
      messageOptions.showCancelButton = true
      this.show(messageOptions)
    },
    close() {
      this.messageOptions = null
      this.currentPage = ''
    },
  },
})
