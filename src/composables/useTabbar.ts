/*
 * @Author: weish<PERSON>
 * @Date: 2024-10-29 22:12:54
 * @LastEditTime: 2025-05-15 14:16:59
 * @LastEditors: weisheng
 * @Description:
 * @FilePath: /lsym-cx-mini/src/composables/useTabbar.ts
 * 记得注释
 */
import { computed, ref } from 'vue'
import { useUserStore } from '@/store/useUserStore'
// 将图标导入进来
import home from '@/static/icon/home.svg'
import homeActive from '@/static/icon/home_active.svg'
import sales from '@/static/icon/sales.svg'
import salesActive from '@/static/icon/sales_active.svg'
import profile from '@/static/icon/profile.svg'
import profileActive from '@/static/icon/profile_active.svg'

export interface TabbarItem {
  name: string
  value: number | null
  active: boolean
  title: string
  icon: string
  activeIcon: string
}

const tabbarItems = ref<TabbarItem[]>([
  { name: 'salesman-home', value: null, active: true, title: '库存', icon: home, activeIcon: homeActive },
  { name: 'sales-store-list', value: null, active: false, title: '车销', icon: sales, activeIcon: salesActive },
  { name: 'warehouse-dashboard', value: null, active: false, title: '工作台', icon: home, activeIcon: homeActive },
  { name: 'profile', value: null, active: false, title: '我的', icon: profile, activeIcon: profileActive },
])

export function useTabbar() {
  const userStore = useUserStore()

  // 根据用户类型过滤标签栏项目
  const tabbarList = computed(() => {
    // 如果是仓管，过滤掉首页和车销
    if (userStore.userType === 1) {
      return tabbarItems.value.filter(item =>
        item.name !== 'salesman-home' && item.name !== 'sales-store-list',
      )
    }
    // 如果是业务员，过滤掉工作台
    else {
      return tabbarItems.value.filter(item =>
        item.name !== 'warehouse-dashboard',
      )
    }
  })

  const activeTabbar = computed(() => {
    const item = tabbarItems.value.find(item => item.active)
    return item || tabbarItems.value[0]
  })

  const getTabbarItemValue = (name: string) => {
    const item = tabbarItems.value.find(item => item.name === name)
    return item && item.value ? item.value : null
  }

  const setTabbarItem = (name: string, value: number) => {
    const tabbarItem = tabbarItems.value.find(item => item.name === name)
    if (tabbarItem) {
      tabbarItem.value = value
    }
  }

  const setTabbarItemActive = (name: string) => {
    tabbarItems.value.forEach((item) => {
      if (item.name === name) {
        item.active = true
      }
      else {
        item.active = false
      }
    })
  }

  return {
    tabbarList,
    activeTabbar,
    getTabbarItemValue,
    setTabbarItem,
    setTabbarItemActive,
  }
}
