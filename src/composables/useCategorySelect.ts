/*
 * @Description: 分类选择组合式API
 */
import { computed, ref } from 'vue'

/**
 * 分类选择组合式API
 * @param autoLoad 是否自动加载分类数据
 * @returns 分类列表
 */
export function useCategorySelect(autoLoad = true) {
  const globalLoading = useGlobalLoading()
  const { error: showError } = useGlobalToast()

  const { data: categoryData, loading: categoryLoading, send: getCategoryList } = useRequest(() => Apis.mdataInterface.queryCategoryUsingPOST({ data: {} }), {
    immediate: autoLoad,
  }).onError((err) => {
    globalLoading.close()
    showError(err.error?.message || '加载分类失败')
  })

  // 分类列表
  const categoryList = computed(() => {
    const categorys = (categoryData.value?.data || []).map((item) => {
      return {
        label: item.name,
        code: item.code,
        value: item.code,
      }
    })

    // 添加全部选项
    return [
      {
        label: '全部',
        value: '',
        code: '',
      },
      ...categorys,
    ]
  })
  // 当前选中的分类ID
  const categoryId = ref<string>('')

  // 当前选中的分类
  const currentcategory = computed(() => {
    return categoryList.value.find(item => item.value === categoryId.value)
  })

  /**
   * 分类选择
   * @param event 结合wd-select-picker组件的confirm事件
   * @param event.value 选中的分类ID值
   * @param event.selectedItems 选中的分类项
   * @param event.selectedItems.value 选中的分类项值
   * @param event.selectedItems.label 选中的分类项标签
   */
  function handlecategorySelect(event: { value: string, selectedItems: { label: string, value: string } }) {
    categoryId.value = event.value
  }

  return {
    categoryList,
    categoryLoading,
    handlecategorySelect,
    categoryId,
    currentcategory,
    getCategoryList,
  }
}
