import type { PermissionType } from '@/utils/permissions'

/**
 * 基础权限检查工具函数
 */
export function usePermissionChecker() {
  const userStore = useUserStore()

  /**
   * 检查用户是否有指定权限
   */
  const checkPermission = (permission: PermissionType): boolean => {
    return userStore.hasPermission(permission)
  }

  /**
   * 检查用户是否有多个权限中的任意一个
   */
  const checkAnyPermission = (permissions: PermissionType[]): boolean => {
    return userStore.hasAnyPermission(permissions)
  }

  /**
   * 检查用户是否有所有指定权限
   */
  const checkAllPermissions = (permissions: PermissionType[]): boolean => {
    return userStore.hasAllPermissions(permissions)
  }

  /**
   * 检查页面路由权限（从route meta信息中获取）
   * @param route 路由对象，包含meta信息
   * @returns 是否有权限访问
   */
  const checkRoutePermission = (permissions?: PermissionType[], requireAll?: boolean): boolean => {
    // 如果没有配置权限要求，则允许访问
    if (!permissions || permissions.length === 0) {
      return true
    }

    // 根据requireAll字段决定使用哪种检查方式
    if (requireAll) {
      return checkAllPermissions(permissions)
    }
    else {
      return checkAnyPermission(permissions)
    }
  }

  /**
   * 检查按钮权限
   */
  const checkButtonPermission = (buttonKey: string): boolean => {
    const requiredPermission = BUTTON_PERMISSIONS[buttonKey]

    if (!requiredPermission) {
      return true // 没有配置权限要求则允许显示
    }

    return checkPermission(requiredPermission)
  }

  /**
   * 获取用户激活的权限列表
   */
  const getActivePermissions = (): string[] => {
    return userStore.getActivePermissions()
  }

  /**
   * 权限调试工具
   */
  const debugPermissions = () => {
    const debugInfo = userStore.getPermissionDebugInfo()

    console.group('🔐 权限调试信息')
    console.log('用户角色:', debugInfo.roleCode)
    console.log('用户类型:', debugInfo.userType === 0 ? '业务员' : '仓管')
    console.log('所有权限:', debugInfo.permissions)
    console.log('激活权限:', debugInfo.activePermissions)
    console.groupEnd()

    return debugInfo
  }

  return {
    checkPermission,
    checkAnyPermission,
    checkAllPermissions,
    checkRoutePermission,
    checkButtonPermission,
    getActivePermissions,
    debugPermissions,
  }
}

/**
 * 应用权限检查Composable
 * @param permission 权限或权限列表
 * @returns 权限检查相关方法和状态
 */
export function usePermission(permission?: PermissionType | PermissionType[]) {
  const userStore = useUserStore()
  const {
    checkPermission,
    checkAnyPermission,
    checkAllPermissions,
    checkButtonPermission,
    getActivePermissions,
    debugPermissions,
  } = usePermissionChecker()

  // 当前权限状态（响应式）
  const currentPermission = computed(() => {
    if (!permission)
      return true

    if (Array.isArray(permission)) {
      return checkAnyPermission(permission)
    }
    else {
      return checkPermission(permission)
    }
  })

  // 用户权限信息
  const userPermissions = computed(() => ({
    all: userStore.permissions,
    active: getActivePermissions(),
    roleCode: userStore.userInfo?.roleCode,
    userType: userStore.userType,
  }))

  return {
    // 权限检查方法
    checkPermission,
    checkAnyPermission,
    checkAllPermissions,
    checkButtonPermission,

    // 响应式权限状态
    currentPermission,
    userPermissions,

    // 工具方法
    debug: debugPermissions,
  }
}

/**
 * 按钮权限Composable
 * @param buttonKey 按钮权限key
 * @returns 按钮是否可见
 */
export function useButtonPermission(buttonKey: string) {
  const { checkButtonPermission } = usePermissionChecker()
  const isVisible = computed(() => checkButtonPermission(buttonKey))

  return {
    isVisible,
  }
}

/**
 * 页面权限Composable
 * @param requiredPermissions 页面所需权限
 * @returns 页面权限状态
 */
export function usePagePermission(requiredPermissions: PermissionType[]) {
  const { checkAnyPermission } = usePermissionChecker()

  const hasAccess = computed(() => {
    if (!requiredPermissions || requiredPermissions.length === 0) {
      return true
    }
    return checkAnyPermission(requiredPermissions)
  })

  const accessDenied = computed(() => !hasAccess.value)

  return {
    hasAccess,
    accessDenied,
  }
}

/**
 * 权限守卫Composable - 用于页面级权限检查
 * @param permissions 所需权限
 * @param options 选项
 */
export function usePermissionGuard(
  permissions: PermissionType | PermissionType[],
  options: {
    tipType?: 'toast' | 'message' // 是否显示提示
    messgae?: string // 提示信息
    checkAll?: boolean // 是否要求全部权限
  } = {},
) {
  const { error: showError } = useGlobalToast()
  const { alert: showAlert } = useGlobalMessage()
  const { checkPermission, checkAnyPermission, checkAllPermissions } = usePermissionChecker()
  const { tipType = true, messgae = '您没有权限访问此页面' } = options
  const hasAccess = computed(() => {
    if (CommonUtil.isArray(permissions)) {
      return options.checkAll ? checkAllPermissions(permissions) : checkAnyPermission(permissions)
    }
    else {
      return checkPermission(permissions)
    }
  })

  // 检查权限，无权限时执行相应操作
  const checkAccess = () => {
    if (!hasAccess.value) {
      if (tipType === 'toast') {
        showError({
          msg: messgae,
        })
      }
      else if (tipType === 'message') {
        showAlert({
          msg: messgae,
          zIndex: 10000,
        })
      }
      return false
    }

    return true
  }

  return {
    hasAccess,
    checkAccess,
  }
}
