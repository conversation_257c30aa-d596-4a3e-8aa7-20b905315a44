import dayjs from 'dayjs'

export interface OssInfo {
  OSSAccessKeyId: string
  expire: string
  host: string
  key: string
  policy: string
  signature: string
  success_action_status: string
  type: string
}
export function useOssInfo() {
  const { error: showError } = useGlobalToast()
  // imageDir: 图片目录，用于上传图片，年月日
  const { loading: ossLoading, data: ossInfoData } = useRequest(() => Apis.commonInterface.ossSignatureUsingGET({ params: { imageDir: `${dayjs().format('YYYYMMDD')}` } }), {
    immediate: true,
  }).onError((err) => {
    showError(err.error.message || '获取OSS签名失败，上传附件将会异常！')
  }).onSuccess((res) => {
    console.log(res)
  })

  const ossInfo = computed(() => {
    if (!ossInfoData.value || !ossInfoData.value.data) {
      return null
    }
    return ossInfoData.value.data as OssInfo
  })

  return {
    ossLoading,
    ossInfo,
  }
}
