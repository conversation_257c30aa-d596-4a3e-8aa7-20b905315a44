/**
 * 锁定页面，弹出框存在时锁定页面滚动
 */
export function useLockPage() {
  const pageStyle = ref<string>('')

  const setPageStyle = (uni as any).setPageStyle

  const lockPage = (isTabbar = false) => {
    if (CommonUtil.isFunction(setPageStyle)) {
      setPageStyle({
        style: {
          overflow: 'hidden',
        },
      })
    }
    else {
      pageStyle.value = `height:calc(100vh - ${isTabbar ? '50px' : '0'} - constant(safe-area-inset-bottom));height:calc(100vh - ${isTabbar ? '50px' : '0'} - env(safe-area-inset-bottom));overflow:hidden;position:fixed;`
    }
  }

  const unlockPage = () => {
    if (CommonUtil.isFunction(setPageStyle)) {
      setPageStyle({
        style: {
          overflow: 'visible',
        },
      })
    }
    pageStyle.value = ''
  }

  return {
    lockPage,
    unlockPage,
    pageStyle,
  }
}
