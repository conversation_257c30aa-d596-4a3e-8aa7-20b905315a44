/*
 * @Author: weisheng
 * @Date: 2025-04-18 14:26:00
 * @LastEditTime: 2025-05-26 11:45:15
 * @LastEditors: weisheng
 * @Description:
 * @FilePath: /lsym-cx-mini/src/composables/useTheme.ts
 * 记得注释
 */
import type { ConfigProviderThemeVars } from 'wot-design-uni'

/**
 * 主题管理组合式API
 * @returns 主题相关的状态和方法
 */
export function useTheme() {
  const { statusBarHeight, navBarHeight, navBarTotalHeight } = storeToRefs(useDeviceInfo())

  const themeVars = reactive<ConfigProviderThemeVars>({
    colorTheme: 'var(--atom-primary-primary-6)',
    tabsNavLineBgColor: 'var(--atom-primary-primary-6)',
    sidebarBg: '#F9F9F9',
    sidebarFontSize: '14px',
    tooltipFs: '20rpx',
    tooltipLineHeight: '24rpx',
    inputNumberIconColor: 'var(--atom-primary-primary-6)',
    navbarHeight: CommonUtil.addUnit(navBarHeight.value),
    tagInfoBg: 'var(--greyapplication-secondary)',
  })

  const customThemeVars = computed(() => {
    const vars = {
      '--status-bar-height': `${statusBarHeight.value}px`,
      '--navbar-total-height': `${navBarTotalHeight.value}px`,
    }
    return CommonUtil.objToStyle(vars)
  })

  return {
    themeVars,
    customThemeVars,
  }
}
