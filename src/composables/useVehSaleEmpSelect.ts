/*
 * @Description: 车销业务员选择组合式API
 */
import { computed, ref } from 'vue'
import type { BaseExcludedPageQueryFilter } from '@/api/globals'

export function useVehSaleEmpSelect(wmsGid?: number) {
  const { data: empData, loading: empLoading } = useRequest((data: BaseExcludedPageQueryFilter) => Apis.mdataInterface.queryVehSaleEmpUsingPOST({ data: {
    ...data,
    wmsGid,
  } }), {
    immediate: true,
  })

  // 业务员列表
  const empList = computed(() => {
    const emps = (empData.value?.data || []).map((item: any) => {
      return {
        label: `${item.name}[${item.code}]`,
        value: String(item.gid || ''),
        code: item.code || '',
        name: item.name || '',
      }
    })

    // 添加全部选项
    return [
      {
        label: '全部业务员',
        value: '',
        code: '',
        name: '全部业务员',
      },
      ...emps,
    ]
  })
  // 当前选中的业务员ID
  const empId = ref<string>('')

  // 当前选中的业务员
  const currentEmp = computed(() => {
    return empList.value.find(item => item.value === empId.value)
  })

  /**
   * 业务员选择
   * @param event 结合wd-select-picker组件的confirm事件
   * @param event.value 选中的业务员ID值
   * @param event.selectedItems 选中的业务员项
   * @param event.selectedItems.value 选中的业务员项值
   * @param event.selectedItems.label 选中的业务员项标签
   */
  function handleEmpSelect(event: { value: string, selectedItems: { label: string, value: string, code: string, name: string } }) {
    empId.value = event.value
  }

  return {
    empList,
    empLoading,
    handleEmpSelect,
    empId,
    currentEmp,
  }
}
