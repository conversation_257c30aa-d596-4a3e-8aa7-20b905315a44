import { ref } from 'vue'
import dayjs from 'dayjs'
import { callInterceptor } from 'wot-design-uni/components/common/interceptor'
import { useGlobalToast } from './useGlobalToast'
import type { StoreDTO, StoreSignDTO } from '@/api/globals'
import { createGlobalLoadingMiddleware } from '@/api/core/middleware'

export interface LocationInfo {
  latitude: number
  longitude: number
  speed?: number
  accuracy?: number
  altitude?: number
  verticalAccuracy?: number
  horizontalAccuracy?: number
}

export type CheckInState = 'canCheckIn' | 'canCheckOut' | 'outOfRange'

interface LocationError extends Error {
  code?: string
  message: string
}

// 定义位置上报前的拦截器类型
export type BeforeReportLocation = (msg?: string) => boolean | Promise<boolean>

export default function useCheckIn(currentStore?: StoreDTO) {
  const { setStore } = useSalesStore()
  const userStore = useUserStore()
  const store = ref<StoreDTO>(currentStore || {} as StoreDTO)
  const location = ref<LocationInfo | null>(null)
  const initLocationLoading = ref(false)
  const router = useRouter()
  const { error: showError, success: showSuccess } = useGlobalToast()
  const { confirm: showConfirm } = useGlobalMessage()
  const globalLoading = useGlobalLoading()
  const { emitRefreshData: emitRefreshStoreList } = useSendRefreshData('sales-store-list') // 刷新门店列表，上报经纬度需要更新门店信息

  /**
   * 更新门店信息
   * @param _store 门店信息
   */
  function updateStore(_store: StoreDTO) {
    store.value = _store
  }

  // 门店签到最大距离
  const maxDistance = computed(() => {
    const distance = userStore.getOption('PS4_StoreSignMaxDistance')
    if (CommonUtil.isDef(distance)) {
      return Number(distance)
    }
    else {
      return 200
    }
  })

  // 获取门店签到状态
  // 1是可以签到，-1是可以签退
  const { data: signState, send: getSignState, loading: signStateLoading } = useRequest(() => Apis.signInterface.getSignStateUsingGET({ params: {
    storeGid: store.value?.gid || 0,
    vehSaleEmpGid: userStore.vehSaleEmp?.gid || 0,
  } }), {
    immediate: false,
  }).onError((error) => {
    showError(error.error.message || '获取门店签到状态失败')
  })

  // 上报门店经纬度
  const { send: reportStoreLocation } = useRequest(({ latitude, longitude }: { latitude: number, longitude: number }) => Apis.signInterface.reportLocationUsingPOST({ data: {
    store: store.value!,
    latitude,
    longitude,
  } }), {
    immediate: false,
    middleware: createGlobalLoadingMiddleware({
      loadingText: '上报中...',
    }),
  }).onError((error) => {
    globalLoading.close()
    showError(error.error.message || '上报门店位置失败')
  }).onSuccess((resp) => {
    globalLoading.close()
    showSuccess('已成功记录门店位置')
    const { latitude, longitude } = resp.args[0]
    store.value!.latitude = `${latitude}`
    store.value!.longitude = `${longitude}`
    // 上报经纬度需要更新门店信息
    emitRefreshStoreList()
  })

  // 获取当前位置与门店的距离
  const { data: distance, send: getDistance, loading: distanceLoading } = useRequest(() => Apis.signInterface.getDistanceUsingGET({ params: {
    storeGid: store.value?.gid || 0,
    latitude: location.value?.latitude || 0,
    longitude: location.value?.longitude || 0,
  } }), {
    immediate: false,
  }).onError((error) => {
    globalLoading.close()
    showError(error.error.message || '获取当前位置与门店的距离失败')
  })

  // 签到
  const { data: signResult, send: signIn, loading: signInLoading } = useRequest((data: StoreSignDTO) => Apis.signInterface.signInUsingPOST({ data: {
    ...data,
    store: store.value!,
    latitude: `${location.value?.latitude}`,
    longitude: `${location.value?.longitude}`,
    signInTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    vehSaleEmp: userStore.vehSaleEmp,
  } }), {
    immediate: false,
    middleware: createGlobalLoadingMiddleware({
      loadingText: '签到中...',
      delay: 200,
    }),
  }).onError((error) => {
    globalLoading.close()
    showError(error.error.message || '签到失败')
  }).onSuccess(() => {
    globalLoading.close()
    showSuccess('签到成功')
    initLocation()
  })

  // 获取未签到的门店
  const { data: noSignOutStore, send: getNoSignOutStore, loading: noSignOutStoreLoading } = useRequest(() => Apis.signInterface.getNoSignOutStoreUsingGET({ params: {
    storeGid: store.value?.gid || 0,
    vehSaleEmpGid: userStore.vehSaleEmp?.gid || 0,
  } }), {
    immediate: false,
  }).onError((error) => {
    showError(error.error.message || '获取门店签退状态失败')
  })

  // 门店签到记录
  const { data: signHistoryData, send: getSignHistory, loading: signHistoryLoading } = useRequest(() => Apis.signInterface.getStoreSignByStoreUsingPOST({ params: {
    storeGid: store.value?.gid || 0,
    vehSaleEmpGid: userStore.vehSaleEmp?.gid || 0,
  } }), {
    immediate: false,
  }).onError((error) => {
    showError(error.error.message || '获取门店签到记录失败')
  })

  const signHistory = computed(() => {
    if (signHistoryData.value) {
      return signHistoryData.value.data || []
    }
    return []
  })

  // 签退前校验
  const { send: verifySignOut } = useRequest(() => Apis.signInterface.signOutVerifyUsingPOST({ params: {
    storeGid: store.value?.gid || 0,
    vehSaleEmpGid: userStore.vehSaleEmp?.gid || 0,
  } }), {
    immediate: false,
    middleware: createGlobalLoadingMiddleware({
      loadingText: '校验中...',
    }),
  })

  // 签退
  const { data: signOutResult, send: signOut, loading: signOutLoading } = useRequest((data: StoreSignDTO) => Apis.signInterface.signOutUsingPOST({ data: {
    ...data,
    store: store.value!,
    latitude: `${location.value?.latitude}`,
    longitude: `${location.value?.longitude}`,
    isException: Number(distance?.value?.data) > maxDistance.value,
    signOutTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    vehSaleEmp: userStore.vehSaleEmp,
  } }), {
    immediate: false,
    middleware: createGlobalLoadingMiddleware({
      loadingText: '签退中...',
      delay: 200,
    }),
  }).onError((error) => {
    showError(error.error.message || '签退失败')
  }).onSuccess(() => {
    globalLoading.close()
    showSuccess('签退成功')
    initLocation()
  })

  /**
   * 处理签退校验结果
   * @param data 签退数据
   * @param skipSignOut 是否跳过签退操作（仅校验）
   */
  async function handleSignOutVerify(data: StoreSignDTO, skipSignOut: boolean = false): Promise<void> {
    try {
      const verifyResult = await verifySignOut()

      if (verifyResult.code === 2000 && verifyResult.data) {
        const { valid, message, moduleId } = verifyResult.data

        if (valid) {
          // 校验成功，如果不跳过签退则直接签退
          if (!skipSignOut) {
            await signOut(data)
          }
        }
        else {
          // 校验失败，显示弹框
          const confirmMessage = message || '存在未提交的草稿，请确认！'

          return new Promise((resolve, reject) => {
            showConfirm({
              title: '提示',
              msg: confirmMessage,
              confirmButtonText: '去查看',
              cancelButtonText: '忽略',
              success: async (res) => {
                if (res.action === 'confirm') {
                  // 根据moduleId跳转到对应页面
                  navigateToCartByModuleId(moduleId)
                  reject(new Error('用户选择查看草稿'))
                }
              },
              fail: async (res) => {
                if (res.action === 'cancel') {
                  // 忽略，继续签退
                  if (!skipSignOut) {
                    await signOut(data)
                  }
                  resolve()
                }
              },
            })
          })
        }
      }
      else {
        // 其他错误
        const errorMsg = verifyResult.msg || '签退校验失败'
        showError(errorMsg)
        throw new Error(errorMsg)
      }
    }
    catch (error: any) {
      const errorMsg = error.error?.message || error.message || '签退校验失败'
      showError(errorMsg)
      throw error
    }
  }

  /**
   * 根据moduleId跳转到对应的购物车页面
   * @param moduleId 模块ID
   */
  function navigateToCartByModuleId(moduleId?: string): void {
    if (moduleId === 'vehSale') {
      // 跳转到车销购物车
      setStore(store.value)
      router.push({ name: 'sales-cart' })
    }
    else if (moduleId === 'vehSaleBck') {
      // 跳转到车销退货购物车
      setStore(store.value)
      router.push({ name: 'sales-back-cart' })
    }
    else {
      showError('未知的模块类型')
    }
  }

  const loading = computed(() => {
    return distanceLoading.value || signInLoading.value || signOutLoading.value || signStateLoading.value || noSignOutStoreLoading.value || signHistoryLoading.value || initLocationLoading.value
  })

  // 检查系统定位开关状态
  function checkSystemLocation(): boolean {
    const { locationEnabled, locationAuthorized } = uni.getSystemInfoSync()
    if (locationEnabled === false || locationAuthorized === false) {
      showError('请打开手机GPS并允许微信使用定位！')
      return false
    }
    return true
  }

  // 获取定位权限设置
  function getLocationSetting(): Promise<boolean> {
    return new Promise((resolve) => {
      uni.getSetting({
        success: res => resolve(!!res.authSetting['scope.userLocation']),
        fail: () => resolve(false),
      })
    })
  }

  // 请求定位权限
  function requestLocationAuth(): Promise<boolean> {
    return new Promise((resolve) => {
      uni.authorize({
        scope: 'scope.userLocation',
        success: () => resolve(true),
        fail: () => {
          showConfirm({
            title: '提示',
            closeOnClickModal: false,
            msg: '需要开启定位权限才能签到，是否前往设置？',
            confirmButtonText: '去设置',
            cancelButtonText: '取消',
            success: async (res) => {
              if (res.action === 'confirm') {
                uni.openSetting()
              }
              else {
                showError('用户取消授权')
              }
            },
            fail: () => {
              showError('用户取消授权')
            },
          })
          resolve(false)
        },
      })
    })
  }

  // 获取具体位置信息
  function getCurrentLocation(): Promise<LocationInfo> {
    return new Promise((resolve, reject) => {
      uni.getLocation({
        type: 'gcj02',
        success: res => resolve(res),
        fail: err => reject(new Error(err.errMsg || '获取位置信息失败')),
      })
    })
  }

  // 对外暴露的主要方法
  async function getLocation(): Promise<LocationInfo | null> {
    try {
      // 1. 检查系统定位开关
      if (!checkSystemLocation()) {
        return null
      }

      // 2. 检查定位权限
      const hasPermission = await getLocationSetting()
      if (!hasPermission) {
        // 3. 请求权限
        const granted = await requestLocationAuth()
        if (!granted) {
          return null
        }
      }

      // 4. 获取位置信息
      const locationInfo = await getCurrentLocation()
      location.value = locationInfo
      return locationInfo
    }
    catch (err) {
      const error = err as LocationError
      showError(error.message || '获取位置信息失败')
      return null
    }
  }

  /**
   * 报告门店位置
   */
  function reportLocation(msg?: string): Promise<boolean> {
    if (store.value?.latitude && store.value?.longitude) {
      return Promise.resolve(true)
    }
    return new Promise((resolve) => {
      showConfirm({
        msg: msg || '门店未维护位置信息，确认后会将当前定位设置为门店位置，请在地图页确认门店位置。',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        success: ({ action }) => {
          if (action === 'confirm') {
            uni.chooseLocation({
              success: async (res) => {
                try {
                  await reportStoreLocation({ latitude: res.latitude, longitude: res.longitude })
                  resolve(true)
                }
                catch (error: any) {
                  const reslut = await reportLocation(`${error.error.message || '上报门店位置失败，'}尝试重新上报门店位置信息，确认后会将当前定位设置为门店位置，请在地图页确认门店位置。`)
                  resolve(reslut)
                }
              },
              fail: (error) => {
                const reslut = reportLocation(`${error.errMsg || '上报门店位置失败，'}尝试重新上报门店位置信息，确认后会将当前定位设置为门店位置，请在地图页确认门店位置。`)
                resolve(reslut)
              },
            })
          }
          else {
            router.back()
            resolve(false)
          }
        },
        fail: () => {
          router.back()
          resolve(false)
        },
      })
    })
  }

  /**
   * 初始化位置信息
   */
  async function initLocation() {
    initLocationLoading.value = true
    callInterceptor(() => reportLocation(), {
      done: async () => {
        try {
          const locationInfo = await getLocation()
          if (locationInfo) {
            await Promise.all([
              getNoSignOutStore(),
              getSignState(),
              getSignHistory(),
              getDistance(),
            ])
          }
        }
        finally {
          initLocationLoading.value = false
        }
      },
      canceled: () => {
        initLocationLoading.value = false
      },
      error: () => {
        initLocationLoading.value = false
      },
    })
  }

  /**
   * 获取打卡状态
   */
  const checkInState = computed<CheckInState>(() => {
    if (signState.value) {
      // 1是可以签到，-1是可以签退
      // 打卡状态分三种，可以签到，可以签退，不在打卡范围内
      if (signState.value.data === -1) {
        return 'canCheckOut'
      }
      else if (signState.value.data === 1 && distance && distance.value) {
        if (Number(distance?.value?.data) <= maxDistance.value) {
          return 'canCheckIn'
        }
        return 'outOfRange'
      }
      else {
        return 'outOfRange'
      }
    }
    else {
      return 'outOfRange'
    }
  })

  // 是否可以开单，签到状态是已签到，且签到门店和当前门店是同一个
  const canOpenOrder = computed(() => {
    return signState.value?.data === -1 && !loading.value && !noSignOutStore.value?.data
  })

  return {
    location,
    loading,
    initLocationLoading,
    distance,
    maxDistance,
    signResult,
    signOutResult,
    getDistance,
    signIn,
    signOut,
    getLocation,
    signState,
    getSignState,
    initLocation,
    checkInState,
    noSignOutStore,
    getNoSignOutStore,
    signHistory,
    getSignHistory,
    canOpenOrder,
    // 更新门店信息
    updateStore,
    // 签退校验
    handleSignOutVerify,
  }
}
