/*
 * @Description: 仓位选择组合式API
 */
import { computed, ref } from 'vue'

/**
 * 仓位选择组合式API
 * @param setFirstDefault 是否使用第一个仓位,开启此选项则不会填充全部仓位的选项
 * @param vehSaleEmpGid 业务员ID
 * @returns 仓位列表、仓位选择、当前选中的仓位
 */
export function useWrhSelect(vehSaleEmpGid?: number, wmsGid?: number) {
  // 当前选中的仓位ID
  const wrhId = ref<string | number>('')

  const { data: wrhData, loading: wrhLoading } = useRequest(() => Apis.mdataInterface.queryWrhUsingPOST({ data: { vehSaleEmpGid, wmsGid } }), {
    immediate: true,
  })
  // 仓位列表
  const wrhList = computed(() => {
    const items = (wrhData.value?.data || []).map((item) => {
      return {
        label: `${item.name}[${item.code}]`,
        value: item.gid || '',
        code: item.code || '',
        name: item.name || '',
      }
    })

    return [
      {
        label: '全部仓位',
        value: '',
        code: '',
        name: '全部仓位',
      },
      ...items,
    ]
  })

  // 当前选中的仓位
  const currentWrh = computed(() => {
    return wrhList.value.find(item => item.value === wrhId.value)
  })

  /**
   * 仓位选择
   * @param event 结合wd-select-picker组件的confirm事件
   * @param event.value 选中的仓位ID值
   * @param event.selectedItems 选中的仓位项
   * @param event.selectedItems.value 选中的仓库项值
   * @param event.selectedItems.label 选中的仓库项标签
   */
  function handleWrhSelect(event: { value: string, selectedItems: { label: string, value: string, code: string, name: string } }) {
    wrhId.value = event.value
  }

  return {
    wrhList,
    wrhLoading,
    handleWrhSelect,
    wrhId,
    currentWrh,
  }
}
