import type { App } from 'vue'

// 扩展Vue实例类型，添加$formatDate方法
declare module 'vue'{
  export interface ComponentCustomProperties {
    $formatDate: typeof formatDate
    $fullDate: typeof fullDate
  }
}

/**
 * 日期格式化插件
 * 在全局添加$formatDate方法
 */
export default {
  install: (app: App) => {
    // 添加全局方法
    app.config.globalProperties.$formatDate = formatDate
    app.config.globalProperties.$fullDate = fullDate

    // 添加全局属性，可以在模板中直接使用
    app.provide('formatDate', formatDate)
    app.provide('fullDate', fullDate)
  },
}
