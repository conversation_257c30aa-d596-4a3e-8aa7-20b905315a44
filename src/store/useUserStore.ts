/*
 * @Author: we<PERSON><PERSON>
 * @Date: 2025-04-19 19:30:00
 * @LastEditTime: 2025-07-04 14:28:56
 * @LastEditors: weisheng
 * @Description: 用户信息存储
 * @FilePath: /lsym-cx-mini/src/store/useUserStore.ts
 */
import { defineStore } from 'pinia'
import type { CodeName, Permission, User, VehSaleEmp } from '@/api/globals'
import { clearAuth, getToken, setToken } from '@/utils/auth'
import type { PermissionType } from '@/utils/permissions'

interface UserState {
  // 用户信息
  userInfo: User | null
  // 是否已登录
  isLoggedIn: boolean
  // 用户类型（0: 业务员, 1: 管理员）
  userType: number
  // 登录时间
  loginTime: number | null
}

export const useUserStore = defineStore('user', {
  state: (): UserState => ({
    userInfo: null,
    isLoggedIn: false,
    userType: 0, // 默认为业务员
    loginTime: null,
  }),

  getters: {
    // 获取用户权限列表
    permissions(): Permission[] {
      return this.userInfo?.permissions || []
    },

    // 获取用户线路列表
    sortLines(): CodeName[] {
      return this.userInfo?.sortLines || []
    },

    // 获取用户token
    token(): string | undefined {
      return this.userInfo?.token
    },

    // 获取车销业务员信息
    vehSaleEmp(): VehSaleEmp | undefined {
      return this.userInfo?.vehSaleEmp
    },

    // 获取用户名称
    userName(): string {
      // 仓管是员工name，业务员是业务员name
      return this.userRole === 'warehouse' ? this.userInfo?.faEmp?.name || '' : this.vehSaleEmp?.name || ''
    },

    // 获取用户编码
    userCode(): string {
      // 仓管是员工code，业务员是业务员code
      return this.userRole === 'warehouse' ? this.userInfo?.faEmp?.code || '' : this.vehSaleEmp?.code || ''
    },

    // 判断是否为业务员
    isSalesman(): boolean {
      return this.userType === 0
    },

    // 判断是否为管理员
    isAdmin(): boolean {
      return this.userType === 1
    },

    // 获取用户角色标识，用于路由跳转判断
    userRole(): string {
      // 根据roleCode返回角色标识
      if (this.userInfo?.roleCode === '02') {
        return 'warehouse' // 仓管
      }
      else if (this.userInfo?.roleCode === '01') {
        return 'salesman' // 业务员
      }
      return '' // 未知角色
    },

    /**
     * 获取用户选项
     * @returns 选项值
     */
    getOption() {
      /**
       * PS4_StoreSignMaxDistance 门店签到最大距离
       * PS4_CopyVehSaleUseSignBillDays 车销领货单可复制的单据天数， 默认10，表示10天内的单据
       * PS4_VehSaleUseSignArvDiffDay 超过n天未处理的差异单提示天数
       * PS4_EnableCreateVehSale 是否允许创建车销单，0-不允许，1-允许
       */
      return (key: 'PS4_StoreSignMaxDistance' | 'PS4_CopyVehSaleUseSignBillDays' | 'PS4_VehSaleUseSignArvDiffDay' | 'PS4_EnableCreateVehSale') => {
        return this.userInfo?.options?.find(option => option.optionCaption === key)?.optionValue || null
      }
    },
  },

  actions: {
    /**
     * 设置用户信息
     * @param user 用户信息
     */
    setUser(user: User) {
      this.userInfo = user
      this.isLoggedIn = true

      // 根据roleCode设置userType：01-车销业务员，02-仓管
      // 默认为业务员(0)
      this.userType = user.roleCode === '02' ? 1 : 0

      this.loginTime = Date.now()

      // 保存token到本地存储
      if (user.token) {
        setToken(user.token)
      }
    },

    /**
     * 清除用户信息
     */
    clearUser() {
      this.userInfo = null
      this.isLoggedIn = false
      this.userType = 0
      this.loginTime = null

      // 清除本地存储的认证信息
      clearAuth()
    },

    /**
     * 获取当前token
     * @returns token字符串或null
     */
    getToken(): string | null {
      // 优先从store中获取
      if (this.userInfo?.token) {
        return this.userInfo.token
      }

      // 其次从本地存储获取
      return getToken()
    },

    /**
     * 获取用户角色代码
     * @returns 角色代码
     */
    getRoleCode(): string {
      return this.userInfo?.roleCode || ''
    },

    /**
     * 设置用户类型
     * @param userType 用户类型（0: 业务员, 1: 管理员）
     */
    setUserType(userType: number) {
      this.userType = userType
    },

    /**
     * 检查用户是否有指定权限
     * @param module 模块名称
     * @returns 是否有权限
     */
    hasPermission(module: PermissionType): boolean {
      if (!this.userInfo?.permissions)
        return false

      return this.userInfo.permissions.some(
        permission => permission.module === module && permission.state === 1,
      )
    },

    /**
     * 检查用户是否有多个权限中的任意一个
     * @param modules 权限模块列表
     * @returns 是否有权限
     */
    hasAnyPermission(modules: PermissionType[]): boolean {
      if (!modules || modules.length === 0)
        return true
      return modules.some(module => this.hasPermission(module))
    },

    /**
     * 检查用户是否有所有指定权限
     * @param modules 权限模块列表
     * @returns 是否有权限
     */
    hasAllPermissions(modules: PermissionType[]): boolean {
      if (!modules || modules.length === 0)
        return true
      return modules.every(module => this.hasPermission(module))
    },
    /**
     * 获取用户权限列表（仅包含已激活的权限）
     * @returns 权限模块列表
     */
    getActivePermissions(): string[] {
      if (!this.userInfo?.permissions)
        return []

      return this.userInfo.permissions
        .filter(permission => permission.state === 1)
        .map(permission => permission.module || '')
        .filter(module => module !== '')
    },

    /**
     * 权限调试信息
     * @returns 权限调试信息
     */
    getPermissionDebugInfo() {
      return {
        permissions: this.permissions,
        activePermissions: this.getActivePermissions(),
        roleCode: this.userInfo?.roleCode,
        userType: this.userType,
      }
    },
  },
})
