import { defineStore } from 'pinia'

export const useSearchHistoryStore = defineStore('searchHistory', {
  state: () => ({
    // 历史搜索记录，最多20条
    history: [] as string[],
  }),

  getters: {
    // 获取历史记录列表
    getHistory: state => state.history,

    // 获取历史记录数量
    getHistoryCount: state => state.history.length,
  },

  actions: {
    // 初始化，从本地存储读取
    init() {
      try {
        const stored = uni.getStorageSync('search-history')
        if (stored && Array.isArray(stored)) {
          this.history = stored.slice(0, 20) // 确保不超过20条
        }
      }
      catch (error) {
        console.error('Failed to load search history:', error)
      }
    },

    // 保存到本地存储
    save() {
      try {
        uni.setStorageSync('search-history', this.history)
      }
      catch (error) {
        console.error('Failed to save search history:', error)
      }
    },

    // 添加搜索记录
    addSearchKeyword(keyword: string) {
      if (!keyword || !keyword.trim())
        return

      const trimmedKeyword = keyword.trim()

      // 如果已经存在，先移除旧的
      const index = this.history.indexOf(trimmedKeyword)
      if (index > -1) {
        this.history.splice(index, 1)
      }

      // 添加到开头
      this.history.unshift(trimmedKeyword)

      // 限制最多20条
      if (this.history.length > 20) {
        this.history = this.history.slice(0, 20)
      }

      // 保存到本地存储
      this.save()
    },

    // 删除指定搜索记录
    removeSearchKeyword(keyword: string) {
      const index = this.history.indexOf(keyword)
      if (index > -1) {
        this.history.splice(index, 1)
        this.save()
      }
    },

    // 清空所有历史记录
    clearHistory() {
      this.history = []
      this.save()
    },
  },
})
