/*
 * @Author: weish<PERSON>
 * @Date: 2025-06-13 18:20:55
 * @LastEditTime: 2025-06-27 18:07:20
 * @LastEditors: weisheng
 * @Description:
 * @FilePath: /lsym-cx-mini/src/store/usePrinterStore.ts
 * 记得注释
 */
import { defineStore } from 'pinia'
import type { BluetoothDevice } from '@/utils/bluetooth/BluetoothConnection'

export const usePrinterStore = defineStore('printer', {
  state: () => ({
    // 当前选中的打印机
    selectedPrinter: null as BluetoothDevice | null,
    // 可用的打印机列表
    availablePrinters: [] as BluetoothDevice[],
    // 当前选中的打印模板名称
    selectPrintTemplateName: '' as string,
  }),

  getters: {
    // 获取当前选中的打印机
    getCurrentPrinter: state => state.selectedPrinter,

    // 获取当前选中打印机的名称
    getCurrentPrinterName: state => state.selectedPrinter?.name || '',

    // 获取当前选中打印机的ID
    getCurrentPrinterId: state => state.selectedPrinter?.deviceId || '',

    // 获取打印模板名称
    getCurrentPrintTemplateName: state => state.selectPrintTemplateName || '',

    // 获取可用打印机列表
    getAvailablePrinters: state => state.availablePrinters,

    // 检查是否已选择打印机
    hasSelectedPrinter: state => !!state.selectedPrinter,
  },

  actions: {
    // 设置选中的打印机
    setSelectedPrinter(printer: BluetoothDevice) {
      this.selectedPrinter = { ...printer }
      // 更新可用列表中的选中状态
      this.availablePrinters.forEach((p) => {
        p.checked = p.deviceId === printer.deviceId
      })

      // 如果设备不在可用列表中，添加进去
      const existingIndex = this.availablePrinters.findIndex(p => p.deviceId === printer.deviceId)
      if (existingIndex === -1) {
        this.availablePrinters.push({ ...printer, checked: true })
      }
    },

    // 清除选中的打印机
    clearSelectedPrinter() {
      this.selectedPrinter = null
      // 清除所有设备的选中状态
      this.availablePrinters.forEach((p) => {
        p.checked = false
      })
    },

    // 设置选中的打印模板
    setSelectPrintTemplateName(templateName: string) {
      this.selectPrintTemplateName = templateName
    },

    // 清除选中的打印模板
    clearSelectPrintTemplateName() {
      this.selectPrintTemplateName = ''
    },

    // 更新可用打印机列表
    setAvailablePrinters(printers: BluetoothDevice[]) {
      this.availablePrinters = printers.map(p => ({
        ...p,
        checked: this.selectedPrinter ? p.deviceId === this.selectedPrinter.deviceId : false,
      }))
    },

    // 添加设备到可用列表
    addAvailablePrinter(printer: BluetoothDevice) {
      const existingIndex = this.availablePrinters.findIndex(p => p.deviceId === printer.deviceId)
      if (existingIndex === -1) {
        this.availablePrinters.push({
          ...printer,
          checked: this.selectedPrinter ? printer.deviceId === this.selectedPrinter.deviceId : false,
        })
      }
    },

    // 清空可用打印机列表
    clearAvailablePrinters() {
      this.availablePrinters = []
    },
  },
})
