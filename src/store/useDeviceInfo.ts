/*
 * @Author: weish<PERSON>
 * @Date: 2025-04-19 16:30:00
 * @LastEditTime: 2025-05-23 00:18:07
 * @LastEditors: weisheng
 * @Description: 设备信息存储，包括安全区域和胶囊按钮位置
 * @FilePath: /lsym-cx-mini/src/store/useDeviceInfo.ts
 * 记得注释
 */
import { defineStore } from 'pinia'

interface SafeArea {
  left: number
  right: number
  top: number
  bottom: number
  width: number
  height: number
}

interface CapsuleInfo {
  width: number
  height: number
  left: number
  right: number
  top: number
  bottom: number
}

interface DeviceInfo {
  // 安全区域信息
  safeArea: SafeArea
  // 胶囊按钮信息
  capsule: CapsuleInfo
  // 状态栏高度
  statusBarHeight: number
  // 屏幕宽度
  screenWidth: number
  // 屏幕高度
  screenHeight: number
  // 窗口宽度
  windowWidth: number
  // 窗口高度
  windowHeight: number
  // 是否已初始化
  initialized: boolean
}

export const useDeviceInfo = defineStore('device-info', {
  state: (): DeviceInfo => ({
    safeArea: {
      left: 0,
      right: 0,
      top: 0,
      bottom: 0,
      width: 0,
      height: 0,
    },
    capsule: {
      width: 0,
      height: 0,
      left: 0,
      right: 0,
      top: 0,
      bottom: 0,
    },
    statusBarHeight: 0,
    screenWidth: 0,
    screenHeight: 0,
    windowWidth: 0,
    windowHeight: 0,
    initialized: false,
  }),

  getters: {
    // 获取底部安全区域高度
    safeAreaBottomHeight(): number {
      return this.screenHeight - this.safeArea.bottom
    },

    // 获取顶部安全区域高度（包含状态栏）
    safeAreaTopHeight(): number {
      return this.safeArea.top
    },

    // 获取导航栏高度（不包含状态栏）
    navBarHeight(): number {
      return this.capsule.height + (this.capsule.top - this.statusBarHeight) * 2
    },

    // 获取导航栏整体高度（包含状态栏）
    navBarTotalHeight(): number {
      return this.navBarHeight + this.statusBarHeight
    },
  },

  actions: {
    /**
     * 初始化设备信息
     */
    init() {
      try {
        // 获取系统信息
        const systemInfo = uni.getSystemInfoSync()
        // 更新安全区域信息
        if (systemInfo.safeArea) {
          this.safeArea = {
            left: systemInfo.safeArea.left,
            right: systemInfo.screenWidth - systemInfo.safeArea.right,
            top: systemInfo.safeArea.top,
            bottom: systemInfo.safeArea.bottom,
            width: systemInfo.safeArea.width,
            height: systemInfo.safeArea.height,
          }
        }

        // 更新状态栏高度
        this.statusBarHeight = systemInfo.statusBarHeight || 0

        // 更新屏幕尺寸
        this.screenWidth = systemInfo.screenWidth
        this.screenHeight = systemInfo.screenHeight
        this.windowWidth = systemInfo.windowWidth
        this.windowHeight = systemInfo.windowHeight

        // 获取胶囊按钮信息
        // #ifdef MP-WEIXIN || MP-QQ || MP-TOUTIAO
        const capsuleInfo = uni.getMenuButtonBoundingClientRect()
        if (capsuleInfo) {
          this.capsule = {
            width: capsuleInfo.width,
            height: capsuleInfo.height,
            left: capsuleInfo.left,
            right: systemInfo.screenWidth - capsuleInfo.right,
            top: capsuleInfo.top,
            bottom: capsuleInfo.bottom,
          }
        }
        // #endif

        this.initialized = true
      }
      catch (error) {
        console.error('获取设备信息失败', error)
      }
    },

    /**
     * 更新设备信息
     */
    update() {
      this.init()
    },
  },
})
