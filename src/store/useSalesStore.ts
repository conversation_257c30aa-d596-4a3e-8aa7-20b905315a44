/*
 * @Author: weish<PERSON>
 * @Date: 2025-05-15 16:00:00
 * @LastEditTime: 2025-05-27 19:31:27
 * @LastEditors: weisheng
 * @Description: 物流中心信息存储
 * @FilePath: /lsym-cx-mini/src/store/useSalesStore.ts
 */
import { defineStore } from 'pinia'
import type { StoreDTO } from '@/api/globals'

interface SalesStore {
  // 当前门店数据
  store: StoreDTO | null
}

export const useSalesStore = defineStore('sales-store', {
  state: (): SalesStore => ({
    store: null,
  }),

  actions: {
    /**
     * 设置当前门店数据
     * @param store 门店数据
     */
    setStore(store: StoreDTO) {
      this.store = store
    },

    /**
     * 清除当前门店数据
     */
    clearStore() {
      this.store = null
    },
  },
})
