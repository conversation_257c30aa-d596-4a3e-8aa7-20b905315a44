/*
 * @Author: we<PERSON><PERSON>
 * @Date: 2025-05-15 16:00:00
 * @LastEditTime: 2025-05-15 16:36:13
 * @LastEditors: weisheng
 * @Description: 物流中心信息存储
 * @FilePath: /lsym-cx-mini/src/store/useWmsStore.ts
 */
import { defineStore } from 'pinia'
import type { GCN } from '@/api/globals'

interface WmsState {
  // 当前选中的物流中心
  currentWms: GCN | null
}

export const useWmsStore = defineStore('wms', {
  state: (): WmsState => ({
    currentWms: null,
  }),

  getters: {
    // 判断是否已选择物流中心
    isWmsSelected(): boolean {
      return this.currentWms !== null
    },
  },

  actions: {
    /**
     * 设置当前物流中心
     * @param wms 物流中心信息
     */
    setCurrentWms(wms: GCN) {
      this.currentWms = wms
    },

    /**
     * 清除当前物流中心
     */
    clearCurrentWms() {
      this.currentWms = null
    },
  },
})
