<script setup lang="ts">
defineProps({
  customClass: {
    type: String,
    default: '',
  },
  customStyle: {
    type: String,
    default: '',
  },
})
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <view class="sales-list-skeleton" :class="customClass" :style="customStyle">
    <view v-for="i in 6" :key="i" class="skeleton-card mb-2 w-[calc(100%-48rpx)] rounded-lg bg-white p-3">
      <!-- 单号行 -->
      <view class="mb-3 flex items-center justify-between">
        <view class="h-4 w-40 animate-pulse rounded bg-[#F5F6F7]" />
        <view class="h-6 w-12 animate-pulse rounded bg-[#F5F6F7]" />
      </view>

      <!-- 基本信息行 -->
      <view class="mb-1 flex flex-wrap">
        <!-- 门店信息 -->
        <view class="mb-2 min-w-[50%] flex items-center break-all">
          <view class="h-3 w-8 animate-pulse rounded bg-[#F5F6F7]" />
          <view class="ml-1 h-3 w-24 animate-pulse rounded bg-[#F5F6F7]" />
        </view>

        <!-- 业务员 -->
        <view class="mb-2 min-w-[50%] flex items-center break-all">
          <view class="h-3 w-10 animate-pulse rounded bg-[#F5F6F7]" />
          <view class="ml-1 h-3 w-16 animate-pulse rounded bg-[#F5F6F7]" />
        </view>

        <!-- 时间 -->
        <view class="mb-2 min-w-[50%] flex items-center break-all">
          <view class="h-3 w-8 animate-pulse rounded bg-[#F5F6F7]" />
          <view class="ml-1 h-3 w-28 animate-pulse rounded bg-[#F5F6F7]" />
        </view>
      </view>

      <!-- 统计信息 -->
      <view class="flex items-center justify-between rounded bg-[#F5F5F5] px-3 py-1">
        <view class="flex items-center">
          <view class="h-3 w-6 animate-pulse rounded bg-[#F5F6F7]" />
          <view class="mx-1 h-4 w-4 animate-pulse rounded bg-[#F5F6F7]" />
          <view class="h-3 w-8 animate-pulse rounded bg-[#F5F6F7]" />
        </view>
        <view class="flex items-center">
          <view class="h-3 w-6 animate-pulse rounded bg-[#F5F6F7]" />
          <view class="mx-1 h-4 w-16 animate-pulse rounded bg-[#F5F6F7]" />
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped>
.sales-list-skeleton {
  padding: 12px 0;
}

.skeleton-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.animate-pulse {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}
</style>
