<!--
 * @Description: 领货单详情页骨架屏组件
-->
<script setup lang="ts">
// 组件不需要任何props或状态
</script>

<template>
  <view class="pick-detail-skeleton">
    <!-- 状态栏骨架屏 -->
    <view class="mb-2">
      <wd-skeleton :row-col="[{ width: '30%', height: '24px' }, { width: '40%', height: '16px' }]" animation="flashed" />
    </view>

    <!-- 统计卡片骨架屏 -->
    <view class="mb-2">
      <wd-skeleton :row-col="[{ width: '100%', height: '100px' }]" animation="flashed" />
    </view>

    <!-- 商品清单骨架屏 -->
    <view class="mb-2">
      <wd-skeleton :row-col="[{ width: '100%', height: '20px' }, { width: '100%', height: '100px' }]" animation="flashed" />
    </view>

    <!-- 基本信息骨架屏 -->
    <view class="mb-2">
      <wd-skeleton
        :row-col="[
          { width: '30%', height: '16px' }, { width: '60%', height: '16px' },
          { width: '30%', height: '16px' }, { width: '60%', height: '16px' },
          { width: '30%', height: '16px' }, { width: '60%', height: '16px' },
          { width: '30%', height: '16px' }, { width: '60%', height: '16px' },
        ]"
        animation="flashed"
      />
    </view>

    <!-- 备注信息骨架屏 -->
    <view class="mb-2">
      <wd-skeleton
        :row-col="[
          { width: '30%', height: '16px' },
          { width: '100%', height: '40px' },
        ]"
        animation="flashed"
      />
    </view>
  </view>
</template>

<style lang="scss" scoped>
.pick-detail-skeleton {
  width: 100%;
}
</style>
