<!--
 * @Author: weish<PERSON>
 * @Date: 2025-05-19 10:25:55
 * @LastEditTime: 2025-06-11 11:50:41
 * @LastEditors: weisheng
 * @Description: 通用表头组件 - 支持互斥排序
 * @FilePath: /lsym-cx-mini/src/business/DetailTableHeader.vue
 * 记得注释
 *
 * 使用示例：
 * 1. 字符串数组（向后兼容）：
 *    <DetailTableHeader :headers="['商品名称', '数量', '金额']" />
 *
 * 2. 对象数组（支持排序）：
 *    <DetailTableHeader
 *      :headers="[
 *        { title: '商品名称', field: 'name', sortable: false },
 *        { title: '数量', field: 'qty', sortable: true, asc: 0 },
 *        { title: '金额', field: 'amount', sortable: true, asc: 0 }
 *      ]"
 *      @sort-change="handleSortChange"
 *    />
 *
 * 排序状态：asc: 0 (无排序), 1 (升序), -1 (降序)
 * 注意：支持互斥排序，同时只能有一个排序条件激活
-->
<script setup lang="ts">
import type { CSSProperties, PropType } from 'vue'
import { computed, ref, watch } from 'vue'
import type { SortItem } from '@/composables/useDraft'

interface HeaderItem {
  title: string
  field?: string
  sortable?: boolean
  asc?: 0 | 1 | -1
}

const props = defineProps({
  // 表头 - 支持字符串数组和对象数组
  headers: {
    type: Array as PropType<(string | HeaderItem)[]>,
    required: true,
  },
  // 标题
  title: {
    type: String,
    default: '',
  },
  // 是否显示查看全部按钮
  showMore: {
    type: Boolean,
    default: false,
  },
  // 是否显示标题栏
  showTitle: {
    type: Boolean,
    default: true,
  },
})

const emits = defineEmits<{
  (e: 'more-click'): void
  (e: 'update:headers', value: HeaderItem[]): void
  (e: 'sort-change', value: SortItem[]): void
}>()

// 总列数
const total = computed(() => {
  return props.headers.length
})

// 计算每列的宽度
const columnStyle = computed(() => {
  const style: CSSProperties = {
    width: `${Math.floor((100 / total.value) * 100) / 100}%`,
  }
  return `${CommonUtil.objToStyle(style)}`
})

// 将字符串数组转换为对象数组
const normalizedHeaders = computed(() => {
  return props.headers.map((header) => {
    if (typeof header === 'string') {
      return {
        title: header,
        field: undefined,
        sortable: false,
        asc: 0 as 0 | 1 | -1,
      }
    }
    return {
      title: header.title,
      field: header.field,
      sortable: header.sortable || false,
      asc: (header.asc || 0) as 0 | 1 | -1,
    }
  })
})

const headerList = ref<HeaderItem[]>(normalizedHeaders.value.map(header => ({
  ...header,
  asc: header.asc ? -header.asc as 0 | 1 | -1 : 0,
})))

// 深度比较函数
function isEqual(a: any, b: any): boolean {
  return JSON.stringify(a) === JSON.stringify(b)
}

watch(normalizedHeaders, (newVal) => {
  // 避免不必要的更新，只有在真正发生变化时才更新
  const oldVal = headerList.value.map(header => ({
    ...header,
    asc: header.asc ? -header.asc as 0 | 1 | -1 : 0,
  }))
  if (!isEqual(oldVal, newVal)) {
    headerList.value = newVal.map(header => ({
      ...header,
      asc: header.asc ? -header.asc as 0 | 1 | -1 : 0,
    }))
  }
}, { deep: true, flush: 'post' })

// 使用防抖来避免频繁更新
let updateTimer: ReturnType<typeof setTimeout> | null = null

watch(headerList, (newVal, oldVal) => {
  // 清除之前的定时器
  if (updateTimer) {
    clearTimeout(updateTimer)
  }

  // 检查是否真的发生了变化
  if (isEqual(newVal, oldVal)) {
    return
  }

  // 使用防抖延迟执行
  updateTimer = setTimeout(() => {
    emits('update:headers', newVal.map(header => ({
      ...header,
      asc: header.asc ? -header.asc as 0 | 1 | -1 : 0,
    })))

    // 发出排序变化事件
    const sortItems: SortItem[] = newVal
      .filter(header => header.sortable && header.field && header.asc !== 0)
      .map(header => ({
        field: header.field!,
        name: header.title,
        asc: header.asc ? -header.asc as 0 | 1 | -1 : 0,
      }))

    emits('sort-change', sortItems)
  }, 0)
}, { deep: true, flush: 'post' })

// 处理排序按钮变化
function handleSortChange(index: number, asc: 0 | 1 | -1) {
  const newHeaders = [...headerList.value]

  // 如果当前排序不是重置操作，则重置其他所有排序条件
  if (asc !== 0) {
    newHeaders.forEach((header, idx) => {
      if (idx !== index && header.sortable) {
        header.asc = 0
      }
    })
  }

  // 设置当前排序条件
  newHeaders[index] = { ...newHeaders[index], asc }
  headerList.value = newHeaders
}

function onMoreClick() {
  emits('more-click')
}
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <view class="flex flex-col" style="--wot-sort-button-height:72rpx;">
    <!-- 标题栏 -->
    <view v-if="showTitle" class="mb-2 flex items-center justify-between">
      <view class="flex items-center">
        <!-- 蓝色装饰条 -->
        <view class="mr-2 h-[28rpx] w-[8rpx] flex-shrink-0 rounded-[2rpx] bg-[var(--frequentapplication-primary-content,#1C64FD)]" />
        <text class="text-4 font-medium">
          {{ title }}
        </text>
      </view>
      <view v-if="showMore" class="flex items-center" @click="onMoreClick">
        <text class="text-28rpx text-[var(--frequentapplication-primary-content)]">
          查看全部
        </text>
        <wd-icon name="arrow-right" size="14px" custom-class="text-[var(--frequentapplication-primary-content)]" />
      </view>
    </view>

    <!-- 表头 -->
    <view
      class="box-border w-full flex items-center justify-between rounded-lg bg-[#F5F5F5] px-3"
    >
      <template
        v-for="(header, index) in headerList"
        :key="header.field || index"
      >
        <!-- 可排序的表头 -->
        <wd-sort-button
          v-if="header.sortable && header.field"
          :model-value="header.asc"
          :title="header.title"
          :custom-class="`text-26rpx! text-[var(--textapplication-text-2)]! ${[
            index === 0 ? 'text-left' : '',
            index === headerList.length - 1 ? 'text-right' : '',
            index !== 0 && index !== headerList.length - 1 ? 'text-center' : '',
          ].join(' ')}`"
          :custom-style="columnStyle"
          allow-reset
          :line="false"
          @update:model-value="(asc) => handleSortChange(index, asc)"
        />
        <!-- 不可排序的表头 -->
        <text
          v-else
          class="text-26rpx text-[var(--textapplication-text-2)] line-height-[var(--wot-sort-button-height)]"
          :class="[
            index === 0 ? 'text-left' : '',
            index === headerList.length - 1 ? 'text-right' : '',
            index !== 0 && index !== headerList.length - 1 ? 'text-center' : '',
          ]"
          :style="columnStyle"
        >
          {{ header.title }}
        </text>
      </template>
    </view>
  </view>
</template>

<style lang="scss" scoped>
</style>
