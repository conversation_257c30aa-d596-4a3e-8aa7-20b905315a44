<!--
 * @Author: weish<PERSON>
 * @Date: 2025-05-21 16:16:11
 * @LastEditTime: 2025-05-28 14:42:39
 * @LastEditors: weisheng
 * @Description:
 * @FilePath: /lsym-cx-mini/src/business/SkuListSkeleton.vue
 * 记得注释
-->
<script setup lang="ts">
defineProps({
  customClass: {
    type: String,
    default: '',
  },
  customStyle: {
    type: String,
    default: '',
  },
})
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <view class="sku-list-skeleton" :class="customClass" :style="customStyle">
    <view v-for="i in 6" :key="i" class="skeleton-card mb-3 flex rounded-lg bg-white p-3">
      <view class="skeleton-img mr-3 h-20 w-20 animate-pulse rounded bg-[#F5F6F7]" />
      <view class="flex flex-1 flex-col justify-between">
        <view class="mb-2 h-4 w-2/3 animate-pulse rounded bg-[#F5F6F7]" />
        <view class="mb-2 h-3 w-1/3 animate-pulse rounded bg-[#F5F6F7]" />
        <view class="mb-2 h-3 w-1/2 animate-pulse rounded bg-[#F5F6F7]" />
        <view class="mt-2 flex items-center justify-between">
          <view class="h-4 w-16 animate-pulse rounded bg-[#F5F6F7]" />
          <view class="h-6 w-20 animate-pulse rounded bg-[#F5F6F7]" />
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped>
.sku-list-skeleton {
  padding: 12px 0;
}
.skeleton-card {
  min-height: 88px;
}
.animate-pulse {
  animation: pulse 1.5s infinite;
}
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}
</style>
