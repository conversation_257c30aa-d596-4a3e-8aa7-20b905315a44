<!--
 * @Description: 订单状态头部组件
 * @FilePath: /lsym-cx-mini/src/business/DetailStatusHeader.vue
-->
<script setup lang="ts">
import icStatusAudited from '@/static/icon/ic_status_audited.svg' // 已审核 已完成
import icStatusApplied from '@/static/icon/ic_status_applied.svg' // 已申请
import icStatusCancelled from '@/static/icon/ic_status_cancelled.svg' // 已作废
import icStatusPending from '@/static/icon/ic_status_pending.svg' // 待处理
import type { OrderStatus } from '@/utils/bill'

const props = defineProps({
  // 订单状态
  status: {
    type: String as PropType<OrderStatus>,
    default: 'applied',
  },
  // 订单号
  orderNo: {
    type: String,
    default: '',
  },
  // 操作按钮文字
  actionText: {
    type: String,
    default: '再来一单',
  },
  // 是否显示操作按钮
  enableAction: {
    type: Boolean,
    default: false,
  },
  // 自定义类名
  customClass: {
    type: String,
    default: '',
  },
  // 自定义样式
  customStyle: {
    type: String,
    default: '',
  },
})

// 定义点击操作按钮事件
const emit = defineEmits(['action'])

/**
 * 订单状态文本
 */
const statusTextMap: Record<OrderStatus, string> = {
  initial: '未审核',
  audited: '已审核',
  applied: '已申请',
  canceled: '已作废',
  pending: '待处理',
  completed: '已完成',
  unknown: '未知状态',
}

/**
 * 订单状态图标
 */
const statusIconMap: Record<OrderStatus, string> = {
  initial: '',
  unknown: '',
  audited: icStatusAudited,
  applied: icStatusApplied,
  canceled: icStatusCancelled,
  pending: icStatusPending,
  completed: icStatusAudited,
}

/**
 * 订单状态文本
 */
const statusText = computed(() => {
  return statusTextMap[props.status]
})

/**
 * 订单状态图标
 */
const statusIcon = computed(() => {
  return statusIconMap[props.status]
})

// 操作按钮点击事件
function handleAction() {
  emit('action')
}
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <view class="flex items-center justify-between" :class="customClass" :style="customStyle">
    <view class="flex flex-col">
      <view class="mb-2 flex items-center">
        <view class="mr-1.5 flex items-center justify-center rounded-full">
          <image :src="statusIcon" class="h-auto w-6" mode="widthFix" />
        </view>
        <text class="text-36rpx text-[#333333] font-semibold">
          {{ statusText }}
        </text>
      </view>
      <text class="pl-7.5 text-28rpx text-[#666666]">
        {{ orderNo }}
      </text>
    </view>
    <view v-if="enableAction" class="flex items-center justify-center rounded bg-[var(--frequentapplication-prmari-background)] px-4 py-1 text-28rpx text-[var(--frequentapplication-primary-content)] font-500" @click="handleAction">
      {{ actionText }}
    </view>
  </view>
</template>

<style lang="scss" scoped>
// 确保图标颜色正确显示
image {
  display: block;
}
</style>
