<!--
 * @Author: weish<PERSON>
 * @Date: 2025-06-03 19:33:17
 * @LastEditTime: 2025-06-19 15:30:34
 * @LastEditors: weisheng
 * @Description: 关联单据组件 - 支持跳转和复制两种模式
 * @FilePath: /lsym-cx-mini/src/business/DetailRelatedBills.vue
 * 记得注释
-->
<script setup lang="ts">
interface Bill {
  /**
   * 生成单据类型
   */
  genCls?: string
  /**
   * 生成单据单号
   */
  genNum?: string
  /**
   * 生成单据时间
   */
  genTime?: string
}

interface Props {
  relatedBills: Bill[]
  customClass?: string
  mode?: 'jump' | 'copy' // 新增模式属性：jump-跳转模式, copy-复制模式
}

interface Emits {
  (e: 'billClick', bill: Bill): void
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'jump', // 默认为跳转模式
})
const emit = defineEmits<Emits>()

const { success: showSuccess, error: showError } = useGlobalToast()

/**
 * 处理单据点击事件
 */
function handleBillClick(bill: Bill) {
  if (props.mode === 'copy') {
    // 复制模式 - 复制单号到剪切板
    copyToClipboard(bill)
  }
  else {
    // 跳转模式 - 触发跳转事件
    emit('billClick', bill)
  }
}

/**
 * 复制单号到剪切板
 */
function copyToClipboard(bill: Bill) {
  if (!bill.genNum) {
    return
  }

  uni.setClipboardData({
    data: bill.genNum,
    success() {
      uni.hideToast()
      showSuccess({
        msg: '单号已复制到剪切板',
      })
    },
    fail(error) {
      showError({
        msg: error.errMsg,
      })
    },
  })
}
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <view v-if="relatedBills && relatedBills.length > 0" class="related-bills rounded-lg bg-white py-3" :class="customClass">
    <!-- 标题 -->
    <view class="mb-3 flex items-center">
      <view class="mr-2 h-[28rpx] w-[8rpx] flex-shrink-0 rounded-[2rpx] bg-[var(--frequentapplication-primary-content,#1C64FD)]" />
      <text class="text-3.75 text-[#2C3036] font-500">
        关联单据
      </text>
      <text v-if="mode === 'copy'" class="ml-2 text-3 text-[#8a8a8a]">
        (点击复制单号)
      </text>
    </view>

    <!-- 单据列表 -->
    <view>
      <view
        v-for="(bill, index) in relatedBills"
        :key="index"
        class="flex items-center justify-between bg-white py-3"
        @click="handleBillClick(bill)"
      >
        <view class="flex items-center">
          <!-- 单据图标 -->
          <image
            class="mr-1 h-5 w-5"
            src="@/static/icon/ic_danju_fill.svg"
            mode="widthFix"
          />

          <!-- 单据信息 -->
          <view class="flex flex-col">
            <text class="text-3.5 text-[#5C5C5C]">
              {{ bill.genCls }}
            </text>
          </view>
        </view>

        <view class="flex items-center">
          <!-- 单据号 -->
          <text class="mr-3 text-3.5 text-[var(--textapplication-text-1)] font-500">
            {{ bill.genNum }}
          </text>

          <!-- 图标 - 根据模式显示不同图标 -->
          <text
            v-if="mode === 'copy'"
            class="i-carbon-copy text-4 text-[var(--textapplication-text-1)]"
          />
          <text
            v-else
            class="i-carbon-chevron-right text-4 text-[var(--textapplication-text-1)]"
          />
        </view>
      </view>
    </view>
  </view>
</template>
