# DetailRelatedBills 关联单据组件

## 功能概述

关联单据组件用于在详情页面中展示相关联的单据信息，支持两种模式：跳转模式和复制模式。

## 主要特性

- ✅ **跳转模式**: 点击关联单据可跳转到对应的详情页面（默认模式）
- ✅ **复制模式**: 点击关联单据可复制单号到剪切板
- ✅ **图标区分**: 不同模式显示不同的图标（箭头/复制图标）
- ✅ **模式提示**: 复制模式下显示"(点击复制单号)"提示文字

## 使用方法

### 1. 跳转模式（默认）

```vue
<script setup>
function handleBillClick(bill) {
  // 处理跳转逻辑
  router.push({
    name: 'target-detail',
    params: { id: bill.genNum, mode: 'copy' },
  })
}
</script>

<template>
  <DetailRelatedBills
    :related-bills="orderDetail?.genBillDetails || []"
    @bill-click="handleBillClick"
  />
</template>
```

### 2. 复制模式

```vue
<script setup>
// 复制模式下不需要处理@bill-click事件
// 组件内部会自动处理复制逻辑
</script>

<template>
  <DetailRelatedBills
    :related-bills="relatedBills"
    mode="copy"
  />
</template>
```

### 3. 动态模式切换

```vue
<script setup>
const relatedBillsMode = ref<'jump' | 'copy'>('jump')

onLoad((options) => {
  // 根据路由参数决定模式
  if (options.mode === 'copy') {
    relatedBillsMode.value = 'copy'
  }
})
</script>

<template>
  <DetailRelatedBills
    :related-bills="relatedBills"
    :mode="relatedBillsMode"
    @bill-click="handleBillClick"
  />
</template>
```

## API 接口

### 组件属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `relatedBills` | `Bill[]` | `[]` | 关联单据列表 |
| `customClass` | `string` | `''` | 自定义样式类名 |
| `mode` | `'jump' \| 'copy'` | `'jump'` | 显示模式：jump-跳转模式，copy-复制模式 |

### 组件事件

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| `bill-click` | 单据点击事件（仅跳转模式下触发） | `bill: Bill` |

### 数据类型

```typescript
interface Bill {
  genCls?: string // 生成单据类型
  genNum?: string // 生成单据单号
  genTime?: string // 生成单据时间
}
```

## 使用场景

### 1. 详情页面关联单据展示
- **跳转模式**: 用户可以点击关联单据跳转到对应详情页面
- **复制模式**: 用户可以快速复制单号，适用于需要在外部系统查询的场景

### 2. 跨页面模式传递
当从关联单据跳转到新页面时，建议传递`mode=copy`参数，避免用户在关联单据间无限跳转。

```javascript
// 在handleBillClick中传递copy模式
router.push({
  name: 'target-detail',
  params: { id: bill.genNum, mode: 'copy' }, // 下个页面将使用复制模式
})
```

## 交互体验

### 跳转模式
- 图标：`i-carbon-chevron-right`（右箭头）
- 点击行为：触发`bill-click`事件
- 适用场景：正常的单据详情查看流程

### 复制模式
- 图标：`i-carbon-copy`（复制图标）
- 点击行为：复制单号到剪切板并显示成功提示
- 适用场景：避免跳转循环，提供快速复制功能
- 提示文字："(点击复制单号)"

## 注意事项

- 复制功能使用`uni.setClipboardData`实现，需要小程序授权
- 复制成功后会显示Toast提示："单号已复制到剪切板"
- 建议在跳转到关联单据页面时传递`mode=copy`参数，避免用户在关联单据间无限跳转
- 组件会自动处理空单号的情况，不会进行无效复制

## 相关页面

使用该组件的页面已全部支持模式参数：
- `back-detail.vue` - 回货单详情
- `back-diff-detail.vue` - 回货差异单详情
- `compensation-detail.vue` - 买赔单详情
