<script setup lang="ts">
import type { CSSProperties } from 'vue'

import { callInterceptor } from 'wot-design-uni/components/common/interceptor'

export type BeforeModeChange = (mode: 'common' | 'operation') => boolean | Promise<boolean>

const props = defineProps({
  /**
   * 模式
   * common: 普通模式
   * operation: 操作模式
   */
  mode: {
    type: String as PropType<'common' | 'operation'>,
    default: 'common',
  },
  /**
   * 左侧文本
   */
  leftText: {
    type: String,
    default: '',
  },
  /**
   * 是否固定
   */
  fixed: {
    type: Boolean,
    default: true,
  },
  /**
   * 是否显示左侧箭头
   */
  leftArrow: {
    type: Boolean,
    default: true,
  },
  /** 标题 */
  title: {
    type: String,
    default: '购物车',
  },
  /** 是否显示返回按钮 */
  showBack: {
    type: Boolean,
    default: true,
  },
  /** 自定义类名 */
  customClass: {
    type: String,
    default: '',
  },
  /** 自定义样式 */
  customStyle: {
    type: String,
    default: '',
  },
  /** 切换模式前的拦截器 */
  beforeModeChange: {
    type: Function as PropType<BeforeModeChange>,
    default: undefined,
  },
})

const emit = defineEmits<{
  (e: 'update:mode', mode: 'common' | 'operation'): void
}>()

const router = useRouter()
const { capsule } = storeToRefs(useDeviceInfo())
const userStore = useUserStore()

const navbarStyle = computed(() => {
  const style: CSSProperties = {
    paddingRight: `${capsule.value.right + capsule.value.width}px`,
  }
  return `--navbar-right-padding: ${capsule.value.right + capsule.value.width}px;${CommonUtil.objToStyle(style)}${props.customStyle}`
})

function handleChangeMode() {
  const newMode = props.mode === 'common' ? 'operation' : 'common'

  callInterceptor(() => props.beforeModeChange?.(newMode), {
    done: () => {
      emit('update:mode', newMode)
    },
  })
}

function handleBack() {
  // 默认返回上一页
  if (getCurrentPages && getCurrentPages().length > 1) {
    uni.navigateBack()
  }
  else {
    const userRole = userStore.userRole
    // 仓管角色
    if (userRole === 'warehouse') {
      router.replaceAll({
        name: 'warehouse-dashboard',
      })
    }
    else {
      // 业务员角色
      router.replaceAll({
        name: 'salesman-home',
      })
    }
  }
}
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <view class="cart-nav-bar">
    <wd-navbar :custom-style="navbarStyle" :left-text="leftText" :bordered="false" :fixed="fixed" :title="title" safe-area-inset-top :left-arrow="leftArrow" placeholder @click-left="handleBack">
      <template #title>
        <slot v-if="$slots.title" name="title" />
        <view v-else class="w-full flex items-center justify-end">
          <!-- <text class="i-carbon-search text-4 text-black" /> -->
          <text class="ml-4 text-4 text-black" @click="handleChangeMode">
            {{ mode === 'common' ? '管理' : '退出管理' }}
          </text>
        </view>
      </template>
    </wd-navbar>
  </view>
</template>

<style lang="scss" scoped>
.cart-nav-bar {
  :deep() {
    .wd-navbar__content{
      padding-right: var(--navbar-right-padding);
      display: flex;
      box-sizing: border-box;
    }
    .wd-navbar__left{
      position: relative !important;
      flex: 0 0 auto;
      z-index: 1;
    }

    .wd-navbar__title{
      max-width: unset !important;
      margin: 0 !important;
      flex: 1 1 auto;
      box-sizing: border-box;
      padding: 0 24rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;

      view {
        display: flex;
        width: 100%;
      }
    }
  }

}
</style>
