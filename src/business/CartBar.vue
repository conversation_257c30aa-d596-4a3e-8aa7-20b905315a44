<!--
 * @Author: we<PERSON><PERSON>
 * @Date: 2025-05-20 17:36:02
 * @LastEditTime: 2025-06-17 14:59:45
 * @LastEditors: weisheng
 * @Description:
 * @FilePath: /lsym-cx-mini/src/business/CartBar.vue
 * 记得注释
-->
<script setup lang="ts">
defineProps({
  // 是否显示打印按钮
  usePrint: {
    type: Boolean,
    default: false,
  },
  /**
   * 是否展示购物车图标
   */
  showCart: {
    type: Boolean,
    default: true,
  },
  // 购物车数量
  count: {
    type: Number,
    default: 0,
  },
  // 总金额
  amount: {
    type: Number,
    default: 0,
  },
  // 按钮文本
  buttonText: {
    type: String,
    default: '查看已购',
  },
  // 是否固定
  fixed: {
    type: Boolean,
    default: false,
  },
  // 是否显示安全区域
  safeAreaBottom: {
    type: Boolean,
    default: false,
  },
  // 自定义样式类
  customClass: {
    type: String,
    default: '',
  },
  // 自定义样式
  customStyle: {
    type: String,
    default: '',
  },
  // 模式
  mode: {
    /**
     * 模式
     * common: 普通模式
     * operation: 操作模式
     */
    type: String as PropType<'common' | 'operation'>,
    default: 'common',
  },
  // operation模式下已选数量
  selectedCount: {
    type: Number,
    default: 0,
  },
  // operation模式下是否全选
  allSelected: {
    type: Boolean,
    default: false,
  },
})
const emit = defineEmits<{
  (e: 'click-cart'): void // 点击购物车
  (e: 'click-button'): void // 点击按钮
  (e: 'click-select-all'): void // 点击全选
  (e: 'click-delete'): void // 点击删除
  (e: 'click-print'): void // 点击打印
}>()
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <view :class="`cart-bar ${fixed ? 'cart-bar--fixed' : ''} ${customClass}`" :style="customStyle">
    <view v-if="mode === 'common'" class="relative box-border box-border h-120rpx w-full flex items-center overflow-hidden px-3">
      <!-- 购物车图标和数量 -->
      <view class="h-full w-[calc(100%-280rpx)] flex flex-auto items-end overflow-hidden">
        <view v-if="showCart" class="flex-none pr-4" @click="emit('click-cart')">
          <wd-badge :model-value="count" :max="999">
            <image
              src="@/static/icon/ic_cart.svg"
              mode="scaleToFill"
              class="h-80rpx w-80rpx"
            />
          </wd-badge>
        </view>
        <!-- 总价 -->
        <view class="box-border flex flex-auto flex-col justify-between overflow-hidden py-2">
          <text class="text-3 text-[var(--textapplication-text-2)]">
            总价:
          </text>
          <view class="flex items-end line-height-40rpx">
            <text class="mr-1 text-3 text-[var(--textapplication-text-3)]">
              ¥
            </text>
            <text class="truncate text-5 text-[var(--textapplication-text-1)]">
              {{ (amount.scale(2)).toFixed(2) }}
            </text>
          </view>
        </view>
      </view>
      <!-- 操作按钮 -->
      <wd-button
        v-if="usePrint"
        :custom-class="`h-88rpx!  flex-none ${usePrint ? 'mr-3! w-220rpx!' : 'w-280rpx!'}`"
        custom-style="flex:0 0 auto;"
        type="primary"
        size="large"
        :round="false"
        plain
        @click="emit('click-print')"
      >
        打印
      </wd-button>
      <wd-button
        :custom-class="`h-88rpx!  flex-none ${usePrint ? 'w-220rpx!' : 'w-280rpx!'}`"
        custom-style="flex:0 0 auto;"
        type="primary"
        size="large"
        :round="false"
        @click="emit('click-button')"
      >
        {{ buttonText }}
      </wd-button>
    </view>
    <!-- operation模式 -->
    <view v-else-if="mode === 'operation'" class="cart-bar-operation box-border h-120rpx w-full flex items-center justify-between px-3">
      <view class="flex items-center" @click="emit('click-select-all')">
        <wd-checkbox :model-value="allSelected" shape="circle" size="24rpx" class="mr-2" />
        <text class="text-4 text-[var(--textapplication-text-2)]">
          全选（已选{{ selectedCount }}）
        </text>
      </view>
      <wd-button
        custom-class="h-88rpx! w-280rpx! flex-none"
        custom-style="flex:0 0 auto;"
        type="error"
        size="large"
        :round="false"
        @click="emit('click-delete')"
      >
        删除
      </wd-button>
    </view>
    <wd-gap v-if="safeAreaBottom" safe-area-bottom height="0" />
  </view>
</template>

<style lang="scss" scoped>
.cart-bar {
  background-color: #fff;
  width: 100%;
  &--fixed {
    position: fixed;
    z-index: 2;
    bottom: 0;
    left: 0;
    right: 0;
  }

  :deep(){
    .wd-badge__content{
      transform: translateY(-50%) translateX(30%) !important;
    }
  }
}

.cart-bar-delete-btn {
  background: #F14646 !important;
  color: #fff !important;
  border-radius: 16rpx !important;
  font-size: 32rpx !important;
  font-weight: 500 !important;
}
</style>
