<!--
 * @Author: weisheng
 * @Date: 2025-05-19 10:42:50
 * @LastEditTime: 2025-06-10 18:44:35
 * @LastEditors: weisheng
 * @Description:
 * @FilePath: /lsym-cx-mini/src/business/DetailInfoCard.vue
 * 记得注释
-->
<!--
 * @Description: 单据信息卡片组件
-->
<script setup lang="ts">
interface InfoItem {
  label: string
  value: string | number | null | undefined
}

defineProps({
  // 左侧标签最小宽度
  labelMinWidth: {
    type: String,
    default: '160rpx',
  },
  // 信息项配置
  items: {
    type: Array as PropType<InfoItem[]>,
    default: () => [
    ],
  },
})
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <view class="border border-white rounded-lg from-[#F5F8FF] to-white bg-gradient-to-b p-3">
    <!-- 普通文本类型 -->
    <view v-for="(item, index) in items" :key="index" class="item flex items-start justify-between line-height-44rpx">
      <text class="flex-none text-28rpx text-[var(--textapplication-text-2)]" :style="{ minWidth: labelMinWidth }">
        {{ item.label }}
      </text>
      <text class="flex-auto break-all text-right text-28rpx text-[var(--textapplication-text-1)]">
        {{ item.value }}
      </text>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.item{
  &:not(:last-child){
    margin-bottom: 16rpx;
  }
}
</style>
