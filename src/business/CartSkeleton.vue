<!--
 * @Description: 购物车页面骨架屏组件
-->
<script setup lang="ts">
// 组件不需要props或状态
</script>

<template>
  <view class="cart-skeleton">
    <!-- Tabs骨架 -->
    <view class="tabs-skeleton flex items-center gap-3 border-b border-[#eee] bg-white px-4">
      <wd-skeleton v-for="i in 5" :key="i" :row-col="[{ width: '120rpx', height: '56rpx' }]" animation="flashed" />
    </view>
    <!-- 商品卡片骨架区 -->
    <view class="p-3">
      <view v-for="i in 3" :key="i" class="mb-3">
        <view class="flex items-start gap-3 rounded-lg bg-white p-3">
          <wd-skeleton :row-col="[{ width: '80px', height: '80px' }]" animation="flashed" />
          <view class="flex-1">
            <wd-skeleton :row-col="[{ width: '60%', height: '20px' }, { width: '40%', height: '16px' }, { width: '30%', height: '16px' }]" animation="flashed" />
            <view class="mt-2">
              <wd-skeleton :row-col="[{ width: '100%', height: '24px' }]" animation="flashed" />
            </view>
          </view>
        </view>
      </view>
    </view>
    <!-- 底部操作栏骨架 -->
    <view class="cartbar-skeleton fixed bottom-0 left-0 z-10 w-full flex items-center justify-between border-t border-[#eee] bg-white px-4 py-3">
      <wd-skeleton :row-col="[{ width: '60px', height: '28px' }]" animation="flashed" />
      <wd-skeleton :row-col="[{ width: '100px', height: '36px' }]" animation="flashed" />
    </view>
  </view>
</template>

<style lang="scss" scoped>
.cart-skeleton {
  min-height: 100vh;
  background: #F9F9F9;
  .navbar-skeleton {
    height: 56px;
  }
  .tabs-skeleton {
    height: 42px;
  }
  .cartbar-skeleton {
    box-shadow: 0 -2px 8px 0 rgba(0,0,0,0.03);
  }
}
</style>
