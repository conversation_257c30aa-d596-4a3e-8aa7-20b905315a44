<!--
 * @Author: weish<PERSON>
 * @Date: 2025-05-27 17:40:28
 * @LastEditTime: 2025-07-14 11:27:21
 * @LastEditors: weisheng
 * @Description: 上传图片弹窗组件 - 支持多配置项
 * @FilePath: /lsym-cx-mini/src/business/UploadImgPop.vue
 * 记得注释
-->
<script setup lang="ts">
import type { UploadBuildFormData, UploadFile, UploadSuccessEvent } from 'wot-design-uni/components/wd-upload/types'
import { debounce } from 'wot-design-uni/components/common/util'
import type { OssInfo } from '@/composables/UseOssInfo'
import type { BillSubmitAttachDtlRequestDTO } from '@/api/globals'

export interface UploadConfig {
  title?: string // 弹窗主标题
  subTitle?: string // 小标题
  label?: string // 上传区域标题
  description?: string // 描述文字
  fileList?: UploadFile[] // 文件列表
  ossInfo?: OssInfo // OSS信息
  confirm?: (imgList: BillSubmitAttachDtlRequestDTO[]) => void // 确认回调
}

defineProps({
  /**
   * 弹窗主标题
   */
  title: {
    type: String,
    default: '上传图片信息',
  },
  /**
   * 小标题
   */
  subTitle: {
    type: String,
    default: '',
  },
  /**
   * 上传区域标题
   */
  label: {
    type: String,
    default: '上传门店照片',
  },
  /**
   * 描述文字
   */
  description: {
    type: String,
    default: '',
  },
})

const emit = defineEmits<{
  (e: 'opened'): void
  (e: 'closed'): void
  (e: 'submit', value: BillSubmitAttachDtlRequestDTO[]): void
}>()

const state = reactive({
  show: false,
  config: {} as UploadConfig,
})

/**
 * 打开弹窗
 * @param config 配置项
 */
function open(config: UploadConfig) {
  state.show = true
  state.config = {
    ...config,
  }
}

/* *
 * 构建 formData
 * @param {Object} { file, formData, resolve }
 * @return {Object} formData
 * */
const buildFormData: UploadBuildFormData = ({ file, formData, resolve }) => {
  if (!state.config.ossInfo) {
    return
  }
  const { OSSAccessKeyId, policy, signature, success_action_status, key } = state.config.ossInfo
  const imageName = file.url.substring(file.url.lastIndexOf('/') + 1) // 从图片路径中截取图片名称

  formData = {
    ...formData,
    key: `${key}/${imageName}`,
    OSSAccessKeyId,
    policy,
    signature,
    success_action_status,
  }
  resolve(formData) // 组装成功后返回 formData，必须返回
}

function handleSuccess(value: UploadSuccessEvent) {
  const { fileList } = value

  state.config.fileList = fileList
}

const debouncedSubmit = debounce(handleSubmit, 200, {
  leading: true,
  trailing: false,
})

/**
 * 提交
 */
function handleSubmit() {
  const { host, key } = state.config.ossInfo!

  const imgList: BillSubmitAttachDtlRequestDTO[] = (state.config.fileList || []).map((file) => {
    const imageName = file.url.substring(file.url.lastIndexOf('/') + 1) // 从图片路径中截取图片名称
    return {
      fileId: imageName,
      fileName: imageName,
      fileUrl: `${host}/${key}/${imageName}`,
    }
  })
  emit('submit', imgList)

  if (state.config.confirm) {
    state.config.confirm(imgList)
  }
  state.show = false
}

defineExpose({
  open,
})
</script>

<template>
  <wd-action-sheet
    v-model="state.show"
    :title="state.config.title || '上传图片信息'"
    @opened="emit('opened')"
    @closed="emit('closed')"
  >
    <view class="box-border px-3">
      <view class="min-h-50vh">
        <!-- 小标题区域 -->
        <view v-if="state.config.subTitle" class="mb-3 flex items-center">
          <view class="h-28rpx w-1 rounded-2rpx bg-[var(--frequentapplication-primary-content)]" />
          <text class="ml-3 text-30rpx text-[#2C3036] font-500">
            {{ state.config.subTitle }}
          </text>
        </view>

        <!-- 上传区域标题和描述 -->
        <view class="mb-4">
          <view v-if="state.config.label" class="mb-2 text-28rpx text-[#606266]">
            {{ state.config.label }}
          </view>
          <view v-if="state.config.description" class="text-24rpx text-[#B2B2B2]">
            {{ state.config.description }}
          </view>
        </view>

        <!-- 上传组件 -->
        <view>
          <wd-upload
            v-if="state.config.ossInfo"
            :file-list="state.config.fileList || []"
            :action="state.config.ossInfo.host"
            :build-form-data="buildFormData"
            :limit="10"
            multiple
            @success="handleSuccess"
          />
        </view>
      </view>

      <!-- 底部提交按钮 -->
      <view class="box-border h-136rpx w-full flex items-center px-3 shadow-[0px_-4px_12px_0px_rgba(0,0,0,0.05)]">
        <wd-button type="primary" block size="large" custom-class="w-full" @click="debouncedSubmit">
          提交
        </wd-button>
      </view>
    </view>
  </wd-action-sheet>
</template>

<style scoped lang="scss">

</style>
