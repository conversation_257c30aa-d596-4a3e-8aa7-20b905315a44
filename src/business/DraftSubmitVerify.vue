<script setup lang="ts">
import { callInterceptor } from 'wot-design-uni/components/common/interceptor'

defineProps({
  title: {
    type: String,
    default: '以下商品库存不足已更新领货数',
  },
})

const emit = defineEmits(['opened', 'closed', 'confirm', 'cancel'])

type BeforeConfirm = () => boolean | Promise<boolean>

const state = reactive({
  show: false, // 是否打开
  beforeConfirm: null as BeforeConfirm | null, // 确认前回调
})

function open({ beforeConfirm }: { beforeConfirm: BeforeConfirm }) {
  state.beforeConfirm = beforeConfirm
  state.show = true
}

function close() {
  state.show = false
}

function handleCancel() {
  close()
  emit('cancel')
}

/**
 * 确认
 */
function handleConfirm() {
  callInterceptor(() => state.beforeConfirm?.(), {
    done: () => {
      close()
      emit('confirm')
    },
  })
}

defineExpose({
  open,
  close,
})
</script>

<template>
  <wd-action-sheet v-model="state.show" :title="title" @opened="emit('opened')" @closed="emit('closed')" @close="handleCancel">
    <scroll-view class="h-60vh" scroll-y>
      <slot name="body" />
    </scroll-view>
    <view class="box-border h-136rpx flex items-center justify-between bg-white px-3">
      <wd-button type="primary" plain :round="false" size="large" custom-class="flex-1 w-[1/2] mr-3!" @click="handleCancel">
        返回
      </wd-button>
      <wd-button type="primary" :round="false" size="large" custom-class="w-[1/2] flex-1" @click="handleConfirm">
        继续提交
      </wd-button>
    </view>
  </wd-action-sheet>
</template>

<style scoped lang="scss">

</style>
