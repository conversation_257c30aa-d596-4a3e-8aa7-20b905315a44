<!--
 * @Description: 统计卡片组件
 * @FilePath: /lsym-cx-mini/src/business/DetailStatsCard.vue
-->
<script setup lang="ts">
interface StatItem {
  title: string
  value: string | number
}

const props = defineProps({
  /**
   * 上部分数据
   */
  topItems: {
    type: Array as () => StatItem[],
    default: () => [],
  },
  /**
   * 下部分数据
   */
  bottomItems: {
    type: Array as () => StatItem[],
    default: () => [],
  },
  /**
   * 自定义类名
   */
  customClass: {
    type: String,
    default: '',
  },
  /**
   * 自定义样式
   */
  customStyle: {
    type: String,
    default: '',
  },
})

// 处理上部分显示的数据项
const topItemsToShow = computed(() => {
  // 限制最多显示2个项
  return props.topItems.slice(0, 2)
})

// 处理下部分显示的数据项
const bottomItemsToShow = computed(() => {
  return props.bottomItems
})
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <view class="stats-card relative overflow-hidden rounded-lg from-[#5088FF] to-[#1C64FD] bg-gradient-to-r" :class="customClass" :style="customStyle">
    <!-- 蓝色背景区域（仅上半部分） -->
    <image
      src="@/static/icon/ic_detail_bg1.svg"
      mode="widthFix"
      class="absolute right-0 top-34rpx z-1 h-auto w-208rpx"
    />
    <image
      src="@/static/icon/ic_detail_bg2.svg"
      mode="widthFix"
      class="absolute right-104rpx top-96rpx z-1 h-auto w-7 transform-translate-x-[50%]"
    />

    <!-- 内容区域 -->
    <view class="relative z-10">
      <!-- 上部分（蓝色背景） -->
      <view class="p-3">
        <view class="flex justify-between">
          <view
            v-for="(item, index) in topItemsToShow"
            :key="index"
            class="max-w-1/2 flex-1 break-all"
            :class="{ 'mr-3': index === 0 && topItemsToShow.length > 1 }"
          >
            <text class="text-3 text-white opacity-80">
              {{ item.title }}
            </text>
            <text class="mt-1 block text-6 text-white font-bold">
              {{ item.value }}
            </text>
          </view>
        </view>
      </view>

      <!-- 下部分（白色背景） -->
      <view
        class="rounded-t-2 bg-[linear-gradient(134deg,#F1F6FF_0%,#F3F7FF_27.33%,#F8FAFF_47.5%,#F4F8FF_65.76%,#F1F5FF_97.6%)] px-4 py-3"
      >
        <view class="flex flex-wrap">
          <view
            v-for="(item, index) in bottomItemsToShow"
            :key="index"
            class="mb-2 w-1/2"
          >
            <text class="block text-3 text-[#999999]">
              {{ item.title }}
            </text>
            <text class="mt-1 block text-6 text-[#333333] font-bold">
              {{ item.value }}
            </text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.stats-card {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}
</style>
