<!--
 * @Description: 回货前校验弹窗组件
-->
<script setup lang="ts">
export interface ReturnVerifyData {
  storeGid?: number
  nums?: string[]
}

interface Props {
  modelValue: boolean
  verifyData: ReturnVerifyData
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'goToStore'): void
  (e: 'goToOrder'): void
  (e: 'close'): void
  (e: 'closed'): void
}

defineProps<Props>()
const emit = defineEmits<Emits>()

// 关闭弹窗
function handleClose() {
  emit('update:modelValue', false)
  emit('close')
}

function handleClosed() {
  emit('closed')
}

// 去签退
function handleGoToStore() {
  emit('goToStore')
}

// 去审核
function handleGoToOrder() {
  emit('goToOrder')
}
</script>

<template>
  <wd-popup
    :model-value="modelValue"
    position="center"
    custom-class="bg-transparent!"
    :closable="false"
    :lock-scroll="true"
    :close-on-click-modal="false"
    :z-index="1000"
    @update:model-value="emit('update:modelValue', $event)"
    @after-leave="handleClosed"
  >
    <view class="mx-auto box-border w-600rpx">
      <!-- 弹窗内容 -->
      <view class="rounded-2 bg-white px-4 py-5">
        <!-- 标题 -->
        <view class="mb-5 text-center text-36rpx text-[var(--textapplication-text-1)] font-medium leading-6">
          请先处理如下数据再发起回货
        </view>

        <!-- 警告项列表 -->
        <view class="mb-5">
          <!-- 未签退门店 -->
          <view v-if="verifyData.storeGid" class="mb-2 flex flex-col rounded-1 bg-[var(--greyapplication-bottomlayer)] px-3 py-2" @click="handleGoToStore">
            <view class="mb-1.5 text-4 text-[var(--textapplication-text-1)] leading-5">
              存在未签退的门店，请完成签退
            </view>
            <view class="flex items-center justify-end">
              <text class="mr-4px text-28rpx text-[var(--frequentapplication-primary-content)]">
                去签退
              </text>
              <view class="i-carbon-chevron-right text-28rpx text-[var(--frequentapplication-primary-content)]" />
            </view>
          </view>

          <!-- 进行中的领货单 -->
          <view v-if="verifyData.nums && verifyData.nums.length > 0" class="flex flex-col rounded-1 bg-[var(--greyapplication-bottomlayer)] px-3 py-2" @click="handleGoToOrder">
            <view class="mb-1.5 text-4 text-[var(--textapplication-text-1)] leading-5">
              存在进行中的领货单，请先作废或联系仓管审核
            </view>
            <view class="flex items-center justify-end">
              <text class="mr-4px text-28rpx text-[var(--frequentapplication-primary-content)]">
                去查看
              </text>
              <view class="i-carbon-chevron-right text-28rpx text-[var(--frequentapplication-primary-content)]" />
            </view>
          </view>
        </view>

        <!-- 关闭按钮 -->
        <view class="mt-8px">
          <wd-button
            size="large"

            plain block
            custom-style="border: 1px solid #E5E5E5; color: #666666; height: 44px; border-radius: 8px; font-size: 16px; font-weight: 400;"
            @click="handleClose"
          >
            关闭
          </wd-button>
        </view>
      </view>
    </view>
  </wd-popup>
</template>

<style lang="scss" scoped>
// 移除所有自定义样式，使用 UnoCSS
</style>
