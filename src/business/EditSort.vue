<!--
 * @Author: we<PERSON><PERSON>
 * @Date: 2025-05-21 11:19:19
 * @LastEditTime: 2025-06-10 14:56:56
 * @LastEditors: weisheng
 * @Description: 排序组件 - 支持互斥排序
 * @FilePath: /lsym-cx-mini/src/business/EditSort.vue
 * 记得注释
-->
<script setup lang="ts">
import type { SortItem } from '@/composables/useDraft'

const props = defineProps({
  /**
   * 排序列表
   */
  sorts: {
    type: Array as PropType<SortItem[]>,
    default: () => [],
  },
  customClass: {
    type: String,
    default: '',
  },
  customStyle: {
    type: String,
    default: '',
  },
})

const emits = defineEmits<{
  (e: 'update:sorts', value: SortItem[]): void
  (e: 'change', value: SortItem[]): void
}>()

const sortList = ref<SortItem[]>(props.sorts.map((sort) => {
  return {
    field: sort.field,
    asc: sort.asc ? -sort.asc as 0 | 1 | -1 : 0,
    name: sort.name,
  }
}))

// 处理排序按钮变化 - 实现互斥排序逻辑
function handleSortChange(index: number, asc: 0 | 1 | -1) {
  const newSortList = [...sortList.value]

  // 如果当前排序不是重置操作，则重置其他所有排序条件
  if (asc !== 0) {
    newSortList.forEach((sort, idx) => {
      if (idx !== index) {
        sort.asc = 0
      }
    })
  }

  // 设置当前排序条件
  newSortList[index] = { ...newSortList[index], asc }
  sortList.value = newSortList
}

watch(sortList, (newVal) => {
  emits('update:sorts', newVal.map((sort) => {
    return {
      field: sort.field,
      asc: sort.asc ? -sort.asc as 0 | 1 | -1 : 0,
      name: sort.name,
    }
  }))
  emits('change', newVal)
}, { deep: true })
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <view class="box-border flex items-center justify-end rounded-lg bg-white px-3" :class="customClass" :style="`--wot-sort-button-height:36px;${customStyle}`">
    <slot name="left" />
    <wd-sort-button
      v-for="(sort, index) in sortList"
      :key="sort.field"
      :model-value="sort.asc"
      custom-class="text-26rpx! text-[var(--textapplication-text-2)]! ml-2"
      :title="sort.name" :line="false"
      allow-reset
      @update:model-value="(asc) => handleSortChange(index, asc)"
    />
  </view>
</template>

<style lang="scss" scoped>

</style>
