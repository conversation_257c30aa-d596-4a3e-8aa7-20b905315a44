/// <reference types='./globals.d.ts' />
/* tslint:disable */
/* eslint-disable */
/**
 * 车销服务服务API文档 - version 1.0
 *
 *
 *
 * OpenAPI version: 3.0.0
 *
 *
 * NOTE: This file is auto generated by the alova's vscode plugin.
 *
 * https://alova.js.org/devtools/vscode
 *
 * **Do not edit the file manually.**
 */
export default {
  'commonInterface.ossSignatureUsingGET': ['GET', '/vbs-service/rest/common/image/oss/signature'],
  'mainInterface.getDataUsingPOST': ['POST', '/vbs-service/rest/main/get'],
  'mdataInterface.queryCategoryUsingPOST': ['POST', '/vbs-service/rest/mdata/category/query'],
  'mdataInterface.querySortLineUsingPOST': ['POST', '/vbs-service/rest/mdata/sortline/query'],
  'mdataInterface.getStoreUsingGET': ['GET', '/vbs-service/rest/mdata/store/get'],
  'mdataInterface.queryStoreUsingPOST': ['POST', '/vbs-service/rest/mdata/store/query'],
  'mdataInterface.queryVehSaleEmpUsingPOST': ['POST', '/vbs-service/rest/mdata/vehsaleemp/query'],
  'mdataInterface.queryWmsUsingPOST': ['POST', '/vbs-service/rest/mdata/wms/query'],
  'mdataInterface.queryWrhUsingPOST': ['POST', '/vbs-service/rest/mdata/wrh/query'],
  'printInterface.getPrintFileUsingPOST': ['POST', '/vbs-service/rest/print/get/printfile'],
  'printInterface.printUsingPOST': ['POST', '/vbs-service/rest/print/get/printstr'],
  'printInterface.getTemplatesUsingGET': ['GET', '/vbs-service/rest/print/template/gets'],
  'signInterface.getDistanceUsingGET': ['GET', '/vbs-service/rest/sign/distance'],
  'signInterface.getNoSignOutStoreUsingGET': ['GET', '/vbs-service/rest/sign/get/nosignoutstore'],
  'signInterface.getSignStateUsingGET': ['GET', '/vbs-service/rest/sign/get/state'],
  'signInterface.getStoreSignByStoreUsingPOST': ['POST', '/vbs-service/rest/sign/getbystore'],
  'signInterface.signInUsingPOST': ['POST', '/vbs-service/rest/sign/in'],
  'signInterface.signOutUsingPOST': ['POST', '/vbs-service/rest/sign/out'],
  'signInterface.signOutVerifyUsingPOST': ['POST', '/vbs-service/rest/sign/out/verify'],
  'signInterface.queryStoreSignByStoreUsingPOST': ['POST', '/vbs-service/rest/sign/query'],
  'signInterface.reportLocationUsingPOST': ['POST', '/vbs-service/rest/sign/report/location'],
  'loginInterface.loginUsingPOST': ['POST', '/vbs-service/rest/user/login'],
  'vehsaleInterface.abortUsingPOST_1': ['POST', '/vbs-service/rest/vehsale/abort'],
  'vehsaleInterface.getUsingGET_1': ['GET', '/vbs-service/rest/vehsale/get'],
  'vehsaleInterface.queryUsingPOST_1': ['POST', '/vbs-service/rest/vehsale/query'],
  'vehsaleInterface.queryDetailsUsingPOST_1': ['POST', '/vbs-service/rest/vehsale/query/details'],
  'vehsaleInterface.submitUsingPOST_1': ['POST', '/vbs-service/rest/vehsale/submit'],
  'vehsaleInterface.verifyCreateUsingPOST_1': ['POST', '/vbs-service/rest/vehsale/verify/create'],
  'vehsalebckInterface.abortUsingPOST': ['POST', '/vbs-service/rest/vehsalebck/abort'],
  'vehsalebckInterface.getUsingGET': ['GET', '/vbs-service/rest/vehsalebck/get'],
  'vehsalebckInterface.queryUsingPOST': ['POST', '/vbs-service/rest/vehsalebck/query'],
  'vehsalebckInterface.queryDetailsUsingPOST': ['POST', '/vbs-service/rest/vehsalebck/query/details'],
  'vehsalebckInterface.submitUsingPOST': ['POST', '/vbs-service/rest/vehsalebck/submit'],
  'vehsalebckInterface.verifyCreateUsingPOST': ['POST', '/vbs-service/rest/vehsalebck/verify/create'],
  'vehsaledraftInterface.getCategoryUsingGET': ['GET', '/vbs-service/rest/vehsaledraft/draft/category/get'],
  'vehsaledraftInterface.checkedUsingPOST': ['POST', '/vbs-service/rest/vehsaledraft/draft/checked'],
  'vehsaledraftInterface.clearDraftUsingPOST': ['POST', '/vbs-service/rest/vehsaledraft/draft/clear'],
  'vehsaledraftInterface.getDraftInfoUsingGET': ['GET', '/vbs-service/rest/vehsaledraft/draft/info/get'],
  'vehsaledraftInterface.removeDraftUsingPOST': ['POST', '/vbs-service/rest/vehsaledraft/draft/remove'],
  'vehsaledraftInterface.removeCheckedUsingPOST': ['POST', '/vbs-service/rest/vehsaledraft/draft/remove/checked'],
  'vehsaledraftInterface.getDraftUsingPOST': ['POST', '/vbs-service/rest/vehsaledraft/get'],
  'vehsaledraftInterface.queryGoodsUsingPOST': ['POST', '/vbs-service/rest/vehsaledraft/goods/query'],
  'vehsaledraftInterface.submitDraftUsingPOST': ['POST', '/vbs-service/rest/vehsaledraft/submit'],
  'vehsaledraftInterface.verifySubmitUsingPOST': ['POST', '/vbs-service/rest/vehsaledraft/verifysubmit'],
  'vehsaleusesignInterface.abortUsingPOST_4': ['POST', '/vbs-service/rest/vehsaleusesign/abort'],
  'vehsaleusesignInterface.auditUsingPOST_3': ['POST', '/vbs-service/rest/vehsaleusesign/audit'],
  'vehsaleusesignInterface.copyUsingPOST': ['POST', '/vbs-service/rest/vehsaleusesign/copy'],
  'vehsaleusesignInterface.getUsingGET_5': ['GET', '/vbs-service/rest/vehsaleusesign/get'],
  'vehsaleusesignInterface.mergeAuditUsingPOST': ['POST', '/vbs-service/rest/vehsaleusesign/merge/audit'],
  'vehsaleusesignInterface.mergeAuditQueryUsingPOST': ['POST', '/vbs-service/rest/vehsaleusesign/merge/audit/query'],
  'vehsaleusesignInterface.queryUsingPOST_5': ['POST', '/vbs-service/rest/vehsaleusesign/query'],
  'vehsaleusesignInterface.queryDetailsUsingPOST_3': ['POST', '/vbs-service/rest/vehsaleusesign/query/details'],
  'vehsaleusesignInterface.submitUsingPOST_4': ['POST', '/vbs-service/rest/vehsaleusesign/submit'],
  'vehsaleusesignInterface.verifyCreateUsingPOST_3': ['POST', '/vbs-service/rest/vehsaleusesign/verify/create'],
  'vehsaleusesignarvInterface.abortUsingPOST_2': ['POST', '/vbs-service/rest/vehsaleusesignarv/abort'],
  'vehsaleusesignarvInterface.getArvGoodsUsingGET': ['GET', '/vbs-service/rest/vehsaleusesignarv/arvgoods/get'],
  'vehsaleusesignarvInterface.hasArvGoodsUsingGET': ['GET', '/vbs-service/rest/vehsaleusesignarv/arvgoods/has'],
  'vehsaleusesignarvInterface.auditUsingPOST': ['POST', '/vbs-service/rest/vehsaleusesignarv/audit'],
  'vehsaleusesignarvInterface.autoArvUsingPOST': ['POST', '/vbs-service/rest/vehsaleusesignarv/autoarv'],
  'vehsaleusesignarvInterface.getUsingGET_2': ['GET', '/vbs-service/rest/vehsaleusesignarv/get'],
  'vehsaleusesignarvInterface.getDetailsUsingPOST': ['POST', '/vbs-service/rest/vehsaleusesignarv/get/details'],
  'vehsaleusesignarvInterface.queryUsingPOST_2': ['POST', '/vbs-service/rest/vehsaleusesignarv/query'],
  'vehsaleusesignarvInterface.submitUsingPOST_2': ['POST', '/vbs-service/rest/vehsaleusesignarv/submit'],
  'vehsaleusesignarvInterface.verifyUsingPOST': ['POST', '/vbs-service/rest/vehsaleusesignarv/verify'],
  'vehsaleusesignarvdiffInterface.auditUsingPOST_1': ['POST', '/vbs-service/rest/vehsaleusesignarvdiff/audit'],
  'vehsaleusesignarvdiffInterface.hasDiffDataUsingPOST': ['POST', '/vbs-service/rest/vehsaleusesignarvdiff/bill/has'],
  'vehsaleusesignarvdiffInterface.getUsingGET_3': ['GET', '/vbs-service/rest/vehsaleusesignarvdiff/get'],
  'vehsaleusesignarvdiffInterface.getDetailsUsingPOST_1': [
    'POST',
    '/vbs-service/rest/vehsaleusesignarvdiff/get/details'
  ],
  'vehsaleusesignarvdiffInterface.queryUsingPOST_3': ['POST', '/vbs-service/rest/vehsaleusesignarvdiff/query'],
  'vehsaleusesignarvdiffInterface.submitPayUsingPOST': ['POST', '/vbs-service/rest/vehsaleusesignarvdiff/submitpay'],
  'vehsaleusesignarvdiffInterface.submitSaleUsingPOST': ['POST', '/vbs-service/rest/vehsaleusesignarvdiff/submitsale'],
  'vehsaleusesignarvdiffInterface.queryVehSaleGoodsUsingPOST': [
    'POST',
    '/vbs-service/rest/vehsaleusesignarvdiff/vehsale/goods/query'
  ],
  'vehsaleusesignbckInterface.abortUsingPOST_3': ['POST', '/vbs-service/rest/vehsaleusesignbck/abort'],
  'vehsaleusesignbckInterface.auditUsingPOST_2': ['POST', '/vbs-service/rest/vehsaleusesignbck/audit'],
  'vehsaleusesignbckInterface.getUsingGET_4': ['GET', '/vbs-service/rest/vehsaleusesignbck/get'],
  'vehsaleusesignbckInterface.queryUsingPOST_4': ['POST', '/vbs-service/rest/vehsaleusesignbck/query'],
  'vehsaleusesignbckInterface.queryDetailsUsingPOST_2': ['POST', '/vbs-service/rest/vehsaleusesignbck/query/details'],
  'vehsaleusesignbckInterface.submitUsingPOST_3': ['POST', '/vbs-service/rest/vehsaleusesignbck/submit'],
  'vehsaleusesignbckInterface.verifyCreateUsingPOST_2': ['POST', '/vbs-service/rest/vehsaleusesignbck/verify/create'],
  'vehsalewrhinvInterface.getTemplatesUsingPOST': ['POST', '/vbs-service/rest/vehsalewrhinv/get'],
  'vehsalewrhinvInterface.getWrhUsingGET': ['GET', '/vbs-service/rest/vehsalewrhinv/get/wrh'],
  'vehsalewrhinvInterface.queryGoodsUsingPOST_1': ['POST', '/vbs-service/rest/vehsalewrhinv/goods/query'],
  'wholesaleInterface.getUsingGET_6': ['GET', '/vbs-service/rest/wholesale/get'],
  'wholesaleInterface.getDetailsUsingPOST_2': ['POST', '/vbs-service/rest/wholesale/get/details'],
  'wholesaleInterface.queryUsingPOST_6': ['POST', '/vbs-service/rest/wholesale/query']
};
