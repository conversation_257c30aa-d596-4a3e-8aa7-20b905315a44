/*
 * @Author: weisheng
 * @Date: 2025-04-10 18:02:00
 * @LastEditTime: 2025-05-30 12:47:04
 * @LastEditors: weisheng
 * @Description: Alova instance configuration
 * @FilePath: /lsym-cx-mini/src/api/core/instance.ts
 */
import { createAlova } from 'alova'
import vueHook from 'alova/vue'
import AdapterUniapp from '@alova/adapter-uniapp'
import mockAdapter from '../mock/mockAdapter'
import { handleAlovaError, handleAlovaResponse } from './handlers'
import { useUserStore } from '@/store/useUserStore'

export const alovaInstance = createAlova({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://**************:7891/',
  ...AdapterUniapp({
    mockRequest: mockAdapter,
  }),
  statesHook: vueHook,
  beforeRequest: (method) => {
    // 从用户存储中获取token
    const userStore = useUserStore()
    const token = userStore.getToken()
    const roleCode = userStore.getRoleCode()

    // 添加token到请求头
    if (token) {
      method.config.headers.token = token
    }

    if (roleCode) {
      method.config.headers.roleCode = roleCode
    }

    // Add content type for POST/PUT/PATCH requests
    if (['POST', 'PUT', 'PATCH'].includes(method.type)) {
      method.config.headers['Content-Type'] = 'application/json'
    }

    // Add timestamp to prevent caching for GET requests
    if (method.type === 'GET') {
      method.config.params = {
        ...method.config.params,
        _t: Date.now(),
      }
    }

    // Log request in development
    if (import.meta.env.MODE === 'development') {
      console.log(`[Alova Request] ${method.type} ${method.url}`, method.data || method.config.params)
      console.log(`[API Base URL] ${import.meta.env.VITE_API_BASE_URL}`)
      console.log(`[Environment] ${import.meta.env.VITE_ENV_NAME}`)
    }
  },

  // Response handlers
  responded: {
    // Success handler
    onSuccess: handleAlovaResponse,

    // Error handler
    onError: handleAlovaError,

    // Complete handler - runs after success or error
    onComplete: async () => {
      // Any cleanup or logging can be done here
    },
  },

  // We'll use the middleware in the hooks
  // middleware is not directly supported in createAlova options

  // Default request timeout (10 seconds)
  timeout: 60000,
  // 设置为null即可全局关闭全部请求缓存
  cacheFor: null,
})

export default alovaInstance
