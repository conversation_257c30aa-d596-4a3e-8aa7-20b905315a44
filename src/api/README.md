# Alova API Layer for Uni-App

This directory contains the optimized API layer for the uni-app project using alova and @alova/wormhole for API generation.

## Directory Structure

```
src/api/
├── core/                  # Core API functionality
│   ├── instance.ts        # Alova instance configuration
│   └── handlers.ts        # Response and error handlers
├── modules/               # API modules by domain
│   ├── user.ts            # User-related API endpoints
│   ├── university.ts      # University-related API endpoints
│   └── feedback.ts        # Feedback-related API endpoints
├── mock/                  # Mock data for development
│   ├── mockAdapter.ts     # Mock adapter configuration
│   └── modules/           # Mock data by domain
│       ├── user.ts        # User mock data
│       ├── university.ts  # University mock data
│       └── feedback.ts    # Feedback mock data
├── globals.d.ts           # Generated type definitions
├── apiDefinitions.ts      # Generated API endpoint definitions
├── createApis.ts          # Generated API creation helper
└── index.ts               # Main API exports
```

## Usage Examples

### Basic Usage

```typescript
import { userApi } from '@/api'

// In a Vue component setup function
export default defineComponent({
  setup() {
    // Login
    const login = async (code: string) => {
      try {
        // Convert code to session
        const session = await userApi.code2Session(code).send()

        // Login with openid
        const userInfo = await userApi.login(session.openid).send()

        // Do something with user info
        console.log('User logged in:', userInfo)
        return userInfo
      }
      catch (error) {
        console.error('Login failed:', error)
        throw error
      }
    }

    return { login }
  }
})
```

### Using with Vue Composition API

```typescript
import { useRequest } from 'alova'
import { userApi } from '@/api'

// In a Vue component setup function
export default defineComponent({
  setup() {
    // Get user info with loading state
    const {
      loading: userLoading,
      data: userData,
      error: userError,
      send: fetchUserInfo
    } = useRequest(userApi.getInfo(), {
      immediate: true, // Fetch immediately when component is mounted
      initialData: {}, // Initial data before request completes
    })

    // Update user nickname
    const updateNickname = async (nickname: string) => {
      try {
        await userApi.updateNickname(nickname).send()
        // Refresh user info after update
        fetchUserInfo()
      }
      catch (error) {
        console.error('Failed to update nickname:', error)
      }
    }

    return {
      userLoading,
      userData,
      userError,
      updateNickname
    }
  }
})
```

### Pagination Example

```typescript
import { usePagination } from 'alova'
import { universityApi } from '@/api'

// In a Vue component setup function
export default defineComponent({
  setup() {
    // Create pagination request
    const {
      // Data list
      data: universities,
      // Loading status
      loading,
      // Current page
      page,
      // Page size
      pageSize,
      // Total items
      total,
      // Load next page
      loadNext,
      // Load previous page
      loadPrev,
      // Reload current page
      reload,
      // Is there a next page
      isLastPage,
    } = usePagination(
      (page, pageSize) =>
        universityApi.queryByUni({
          page,
          pageSize,
          // Other filter parameters
          propIn: ['985', '211'],
          provinceCodeEquals: 'BJ'
        }),
      {
        initialPage: 1,
        initialPageSize: 10,
        initialData: [],
        // Transform response to extract data and total
        transform: response => ({
          data: response.data || [],
          total: response.total || 0,
        }),
      }
    )

    return {
      universities,
      loading,
      page,
      pageSize,
      total,
      loadNext,
      loadPrev,
      reload,
      isLastPage,
    }
  }
})
```

### Form Submission Example

```typescript
import { useRequest } from 'alova'
import { feedbackApi } from '@/api'

// In a Vue component setup function
export default defineComponent({
  setup() {
    // Form data
    const formData = ref({
      type: 'ADVISE',
      content: '',
      contactInfo: '',
      images: []
    })

    // Create submission request (not executed immediately)
    const {
      loading: submitting,
      send: submitFeedback,
      error: submitError,
      onSuccess,
      onError,
      onComplete
    } = useRequest(
      () => feedbackApi.create(formData.value),
      { immediate: false }
    )

    // Handle success
    onSuccess(() => {
      uni.showToast({
        title: 'Feedback submitted successfully',
        icon: 'success'
      })

      // Reset form
      formData.value = {
        type: 'ADVISE',
        content: '',
        contactInfo: '',
        images: []
      }
    })

    // Handle error
    onError((error) => {
      console.error('Failed to submit feedback:', error)
    })

    // Handle form submission
    const handleSubmit = () => {
      // Validate form
      if (!formData.value.content) {
        uni.showToast({
          title: 'Please enter feedback content',
          icon: 'none'
        })
        return
      }

      // Submit form
      submitFeedback()
    }

    return {
      formData,
      submitting,
      submitError,
      handleSubmit
    }
  }
})
```

## Best Practices

1. **Organize by Domain**: Group API endpoints by domain/module for better organization.

2. **Use TypeScript**: Leverage TypeScript for better type safety and autocompletion.

3. **Handle Errors Properly**: Always handle errors in your components.

4. **Use Composition API Hooks**: Leverage alova's hooks like `useRequest` and `usePagination`.

5. **Cache Appropriately**: Configure caching based on the nature of the data.

6. **Mock Data for Development**: Use mock data during development for faster iteration.

7. **Invalidate Cache When Needed**: Invalidate cache after mutations to keep data fresh.

8. **Transform Responses**: Use transform functions to format data for your components.

9. **Separate Concerns**: Keep API logic separate from UI logic.

10. **Document Your API**: Add comments and documentation for better maintainability.

## Using @alova/wormhole

This project uses [@alova/wormhole](https://alova.js.org/tutorial/getting-started/extension-integration) for API generation from OpenAPI/Swagger specifications.

### Generated APIs

The generated APIs can be accessed through the global `Apis` object:

```typescript
import Apis from '@/api'

// Example: Get university levels
const { loading, data, error } = useRequest(() =>
  Apis.general.levelsUsingGET({})
)

// Example: Submit feedback with data
const { loading, send } = useRequest(
  feedback => Apis.general.createUsingPOST({
    data: feedback
  }),
  { immediate: false }
)
```

### VSCode Extension

For the best development experience, install the [Alova VSCode Extension](vscode:extension/Alova.alova-vscode-extension) which provides:

1. API documentation in the editor
2. Quick API lookup with `a->` trigger
3. Auto-completion for API parameters
4. Automatic API updates

### Configuration

The API generation is configured in `alova.config.ts` in the project root. This configuration specifies:

- The OpenAPI/Swagger source
- Output directory
- API version and format
- Auto-update settings

### Example

See `src/pages/example/alova-wormhole-example.vue` for a complete example of using alova with the generated APIs.
