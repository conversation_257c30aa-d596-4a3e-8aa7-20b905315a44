/*
 * @Author: weisheng
 * @Date: 2025-04-17 16:21:36
 * @LastEditTime: 2025-06-16 14:31:39
 * @LastEditors: weisheng
 * @Description:
 * @FilePath: /lsym-cx-mini/src/api/mock/mockAdapter.ts
 * 记得注释
 */
import { uniappMockResponse, uniappRequestAdapter } from '@alova/adapter-uniapp'
import { createAlovaMockAdapter } from '@alova/mock'
// API模块的mock数据
import printInterfaceMocks from './modules/printInterface'
import vehsalebckInterfaceMocks from './modules/vehsalebckInterface'
// 车销领货
import vehsaleusesignarvInterfaceMocks from './modules/vehsaleusesignarvInterface'
import vehsaleusesignarvdiffInterfaceMocks from './modules/vehsaleusesignarvdiffInterface'
import vehsaleusesignbckInterfaceMocks from './modules/vehsaleusesignbckInterface'
import wholesaleInterfaceMocks from './modules/wholesaleInterface'
import commonMocks from './modules/common'

// Combine all mock definitions
const allMocks = [
  // API接口模块mock
  // loginInterfaceMocks,
  // mainInterfaceMocks,
  // mdataInterfaceMocks,
  // printInterfaceMocks,
  // vehsaleInterfaceMocks,
  // vehsalebckInterfaceMocks,
  // vehsaledraftInterfaceMocks,
  // vehsaleusesignInterfaceMocks,
  // vehsaleusesignarvInterfaceMocks,
  // vehsaleusesignarvdiffInterfaceMocks,
  // vehsaleusesignbckInterfaceMocks,
  // vehsalewrhinvInterfaceMocks,
  // wholesaleInterfaceMocks,

  // 通用mock处理（放在最后，作为兜底处理）
  commonMocks,
]

// Create mock adapter
const mockAdapter = createAlovaMockAdapter(allMocks, {
  // Use uniapp request adapter for non-mocked requests
  httpAdapter: uniappRequestAdapter,

  // Use uniapp mock response adapter
  onMockResponse: uniappMockResponse,

  // Enable/disable mock based on configuration
  enable: true,

  // Add delay to simulate network latency (200-600ms)
  delay: Math.random() * 400 + 200,

})

export default mockAdapter
