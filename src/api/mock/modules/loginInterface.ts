/*
 * @Author: we<PERSON><PERSON>
 * @Date: 2025-05-14 16:16:24
 * @LastEditTime: 2025-05-15 13:00:00
 * @LastEditors: weisheng
 * @Description:
 * @FilePath: /lsym-cx-mini/src/api/mock/modules/loginInterface.ts
 * 记得注释
 */
import { defineMock } from '@alova/mock'
import type { LoginCredentials, Response_User_ } from '../../globals.d'
import { generateMockData } from '../utils'

export default defineMock({
  // 用户登录
  '[POST]/vbs-service/rest/user/login': (_params: { data: LoginCredentials }): Response_User_ => {
    // 确定用户角色
    let roleCode = '01' // 默认为业务员(01)

    // 指定账号规则，如果账号包含特定字符则固定角色
    if (_params.data.loginCode.toLowerCase().includes('admin')) {
      roleCode = '02' // 仓管角色
    }
    else if (_params.data.loginCode.toLowerCase().includes('sales')) {
      roleCode = '01' // 业务员角色
    }
    else {
      // 随机决定角色，50%概率是仓管，50%概率是业务员
      roleCode = Math.random() > 0.5 ? '02' : '01'
    }

    console.log(`[Mock] 登录用户角色: ${roleCode === '01' ? '业务员' : '仓管'}`)

    return {
      code: 2000,
      data: generateMockData.user(roleCode),
      msg: '登录成功',
    }
  },
})
