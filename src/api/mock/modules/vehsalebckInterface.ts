import { defineMock } from '@alova/mock'
import type {
  BaseResponse_List_VehSaleBckDtlResponseDTO_,
  BaseResponse_List_VehSaleBckQueryResponseDTO_,
  Response_VehSaleBckResponseDTO_,
  Response_Void_,
  Response_boolean_,
  Response_string_,
  VehSaleBckDtlResponseDTO,
  VehSaleBckQueryFilter,
  VehSaleBckQueryResponseDTO,
  VehSaleBckResponseDTO,
  VehSaleGoodsDTO,
} from '../../globals.d'
import { generateMockData } from '../utils'

export default defineMock({
  // 作废单据
  '[POST]/vbs-service/rest/vehsalebck/abort': (_params: any): Response_Void_ => {
    return {
      code: 2000,
      msg: '作废成功',
    }
  },

  // 审核单据
  '[POST]/vbs-service/rest/vehsalebck/audit': (_params: any): Response_Void_ => {
    return {
      code: 2000,
      msg: '审核成功',
    }
  },

  // 获取单据详情
  '[GET]/vbs-service/rest/vehsalebck/get': (_params: any): Response_VehSaleBckResponseDTO_ => {
    const details: VehSaleBckDtlResponseDTO[] = generateMockData.array((index: number) => {
      const goods = generateMockData.goods(index)
      return {
        line: index + 1,
        gdCode: goods.code,
        goods: goods as VehSaleGoodsDTO,
        munit: goods.munit,
        price: goods.price,
        qpc: goods.qpc,
        qpcStr: goods.qpcStr,
        qty: generateMockData.number(5, 50),
        qtyStr: `${generateMockData.number(1, 5)}`,
        note: '',
        total: generateMockData.number(100, 1000),
      }
    }, 5)

    const vehSaleBck: VehSaleBckResponseDTO = {
      num: generateMockData.code('VSB'),
      ocrDate: generateMockData.date(),
      receiver: {
        gid: generateMockData.id(),
        code: generateMockData.code('STORE'),
        name: generateMockData.name('门店'),
      },
      vehSaleEmp: {
        gid: generateMockData.id(),
        code: generateMockData.code('EMP'),
        name: generateMockData.name('业务员'),
      },
      vehSaleEmpName: generateMockData.name('业务员'),
      wrh: {
        gid: generateMockData.id(),
        code: generateMockData.code('WRH'),
        name: generateMockData.name('仓库'),
      },
      carNumber: '粤A12345',
      stat: 1, // 已提交
      goodsCount: details.length,
      total: 888,
      filDate: generateMockData.datetime(),
      filler: generateMockData.name('创建人'),
      lstupdTime: generateMockData.datetime(),
      lastModifyOper: generateMockData.name('修改人'),
      note: '',
      details,
      attachDetails: [],
    }

    return {
      code: 2000,
      data: vehSaleBck,
      msg: '查询成功',
    }
  },

  // 查询单据列表
  '[POST]/vbs-service/rest/vehsalebck/query': (params: VehSaleBckQueryFilter): BaseResponse_List_VehSaleBckQueryResponseDTO_ => {
    // 获取查询参数
    const { page = 0, pageSize = 10, keyword = '' } = params
    // 获取日期范围 (如果存在)
    const beginDate = params.beginDate
    const finishDate = params.finishDate

    // 生成50条数据
    const allVehSaleBckList: VehSaleBckQueryResponseDTO[] = generateMockData.array((index: number) => ({
      num: generateMockData.code('VSB'),
      stat: index % 3, // 0: 草稿, 1: 已提交, 2: 已审核
      goodsCount: generateMockData.number(1, 10),
      total: generateMockData.number(100, 1000),
      lstupdTime: generateMockData.datetime(-generateMockData.number(0, 30)), // 随机生成0-30天内的时间
      store: {
        gid: generateMockData.id(),
        code: generateMockData.code('STORE'),
        name: generateMockData.name('门店'),
        address: '广州市天河区天河路123号',
        latitude: '23.12345',
        longitude: 113.12345,
      },
      vehSaleEmp: {
        gid: generateMockData.id(),
        code: generateMockData.code('EMP'),
        name: generateMockData.name('业务员'),
      },
    }), 50)

    // 根据关键字和时间范围过滤
    let filteredList = allVehSaleBckList
    if (keyword) {
      filteredList = filteredList.filter(item =>
        item.num?.includes(keyword)
        || item.vehSaleEmp?.name?.includes(keyword)
        || item.store?.name?.includes(keyword),
      )
    }

    // 日期筛选
    if (beginDate) {
      const beginDateObj = new Date(beginDate)
      filteredList = filteredList.filter(item =>
        new Date(item.lstupdTime || '').getTime() >= beginDateObj.getTime(),
      )
    }
    if (finishDate) {
      const finishDateObj = new Date(finishDate)
      filteredList = filteredList.filter(item =>
        new Date(item.lstupdTime || '').getTime() <= finishDateObj.getTime(),
      )
    }

    // 分页处理
    const startIndex = page * pageSize
    const endIndex = startIndex + pageSize
    const paginatedList = filteredList.slice(startIndex, endIndex)

    return {
      code: 2000,
      data: paginatedList,
      total: filteredList.length,
      more: endIndex < filteredList.length,
      msg: '查询成功',
    }
  },

  // 提交单据
  '[POST]/vbs-service/rest/vehsalebck/submit': (_params: any): Response_string_ => {
    return {
      code: 2000,
      data: generateMockData.code('VSB'),
      msg: '提交成功',
    }
  },

  // 新的创建前验证接口
  '[POST]/vbs-service/rest/vehsalebck/verify/create': (_params: any): Response_boolean_ => {
    return {
      code: 2000,
      data: true,
      msg: '验证通过',
    }
  },

  // 获取单据明细详情 - 新接口
  '[POST]/vbs-service/rest/vehsalebck/query/details': (_params: any): BaseResponse_List_VehSaleBckDtlResponseDTO_ => {
    const details: VehSaleBckDtlResponseDTO[] = generateMockData.array((index: number) => {
      const goods = generateMockData.goods(index)
      return {
        line: index + 1,
        gdCode: goods.code,
        goods: goods as VehSaleGoodsDTO,
        munit: goods.munit,
        price: goods.price,
        qpc: goods.qpc,
        qpcStr: goods.qpcStr,
        qty: generateMockData.number(5, 50),
        qtyStr: `${generateMockData.number(1, 5)}`,
        note: '',
        total: generateMockData.number(100, 1000),
      }
    }, 5)

    return {
      code: 2000,
      data: details,
      msg: '查询成功',
      total: details.length,
      more: false,
    }
  },
})
