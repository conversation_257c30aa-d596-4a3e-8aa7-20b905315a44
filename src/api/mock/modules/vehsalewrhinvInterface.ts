/*
 * @Author: weish<PERSON>
 * @Date: 2025-04-25 17:20:00
 * @LastEditTime: 2025-05-16 10:36:44
 * @LastEditors: weisheng
 * @Description: 车销仓库存相关接口的模拟数据
 * @FilePath: /lsym-cx-mini/src/api/mock/modules/vehsalewrhinvInterface.ts
 */
import { defineMock } from '@alova/mock'
import type {
  VehSaleGoodsDTO,
} from '../../globals.d'
import { generateMockData } from '../utils'
import skuDefaultImage from '@/static/icon/ic_sku_default.svg'

// 生成库存商品列表数据的辅助函数
function generateGoodsList(count = 10): VehSaleGoodsDTO[] {
  return generateMockData.array((index: number) => {
    return {
      gid: generateMockData.id(),
      code: generateMockData.code('GOODS'),
      name: generateMockData.name('商品'),
      gdCode: generateMockData.code('BAR'),
      munit: '个',
      price: generateMockData.number(1, 1000) / 100,
      qpc: generateMockData.number(1, 10),
      qpcStr: `${generateMockData.number(1, 10)}个/箱`,
      singlePrice: generateMockData.number(1, 100) / 100,
      vehSaleWrhQty: generateMockData.number(10, 1000),
      busInvQty: generateMockData.number(10, 1000),
      advUseSignQty: generateMockData.number(1, 10),
      category: {
        code: `CATE_${index % 5}`,
        name: `分类${index % 5}`,
      },
      gdImages: [
        {
          fileId: `img_${index}`,
          fileName: `商品图片${index}.jpg`,
          fileType: 'img',
          fileUrl: skuDefaultImage,
          line: 0,
        },
      ],
      qpcDetails: [
        {
          munit: '个',
          qpc: generateMockData.number(1, 10),
          qpcStr: `${generateMockData.number(1, 10)}个/箱`,
        },
      ],
    }
  }, count)
}

export default defineMock({
  // 1. 获取车销仓库存 - POST /vbs-service/rest/vehsalewrhinv/get
  '[POST]/vbs-service/rest/vehsalewrhinv/get': (_params: any): any => {
    return {
      code: 2000,
      data: {
        // 可用库存金额
        total: generateMockData.number(10000, 100000) / 100,
        // 可用品项数
        skuCount: generateMockData.number(10, 100),
        // 可用库存数
        qty: generateMockData.number(100, 1000),
        // 不可用库存金额
        unusableTotal: generateMockData.number(1000, 10000) / 100,
        // 不可用库存数
        unusableQty: generateMockData.number(10, 100),
        // 上次领货日期
        lastVehSaleUseSignDate: generateMockData.date(-1),
        // 上次回货日期
        lastVehSaleUseSignArvDate: generateMockData.date(-2),
        // 上次领货金额
        vehSaleUseSignTotal: generateMockData.number(5000, 20000) / 100,
        // 上次回货金额
        vehSaleUseSignArvTotal: generateMockData.number(3000, 10000) / 100,
      },
      msg: '查询成功',
    }
  },

  // 2. 查询车销仓商品 - GET /vbs-service/rest/vehsalewrhinv/goods/query
  '[POST]/vbs-service/rest/vehsalewrhinv/goods/query': (_params: any): any => {
    const goodsList = generateGoodsList()

    return {
      code: 2000,
      data: goodsList,
      total: goodsList.length,
      more: false,
      msg: '查询成功',
    }
  },
})
