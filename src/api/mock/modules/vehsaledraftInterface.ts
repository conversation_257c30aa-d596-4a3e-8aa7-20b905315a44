/*
 * @Author: weish<PERSON>
 * @Date: 2025-04-22 10:00:00
 * @LastEditTime: 2025-05-21 19:22:30
 * @LastEditors: weisheng
 * @Description: 车销草稿相关接口的模拟数据
 * @FilePath: /lsym-cx-mini/src/api/mock/modules/vehsaledraftInterface.ts
 */
import { defineMock } from '@alova/mock'
import type {
  BaseResponse_List_VehSaleGetDraftGoodsResponseDTO_,
  Response_VehSaleDraftResponseDTO_,
  VehSaleDraftGoodsSubmitDTO,
  VehSaleGetDraftGoodsResponseDTO,
  VehSaleRemoveDraftRequestDTO,
  VehSaleUseSignDraftQueryFilter,
} from '../../globals.d'
import { generateMockData } from '../utils'

// 生成草稿列表数据的辅助函数
function generateDraftList(count = 5): VehSaleGetDraftGoodsResponseDTO[] {
  return generateMockData.array((index: number) => {
    const goods = generateMockData.goods(index)
    // 生成1-3个qpcDetails
    const qpcDetailCount = generateMockData.number(1, 4)
    const qpcDetails = generateMockData.array(() => ({
      applyQty: generateMockData.number(10, 100),
      applyQtyStr: `${generateMockData.number(1, 10)}箱`,
      munit: goods.munit || '个',
      price: goods.price || 0,
      qpc: goods.qpc || 1,
      qpcStr: goods.qpcStr || '1个/箱',
      qty: generateMockData.number(5, 50),
      qtyStr: `${generateMockData.number(1, 5)}箱`,
      total: (goods.price || 0) * generateMockData.number(10, 100),
      singlePrice: (goods.price || 0) / (goods.qpc || 1),
      version: 1,
    }), qpcDetailCount)
    return {
      goods,
      qpcDetails,
      version: 1,
    }
  }, count)
}

export default defineMock({
  // 1. 删除草稿 - POST /vbs-service/rest/vehsaledraft/draft/remove
  '[POST]/vbs-service/rest/vehsaledraft/draft/remove': (_params: VehSaleRemoveDraftRequestDTO[]): Response_VehSaleDraftResponseDTO_ => {
    return {
      code: 2000,
      data: {
        goodsCount: 0,
        total: 0,
      },
      msg: '删除成功',
    }
  },

  // 2. 获取草稿 - POST /vbs-service/rest/vehsaledraft/get
  '[POST]/vbs-service/rest/vehsaledraft/get': (_params: VehSaleUseSignDraftQueryFilter): BaseResponse_List_VehSaleGetDraftGoodsResponseDTO_ => {
    const draftList = generateDraftList()

    return {
      code: 2000,
      data: draftList,
      total: draftList.length,
      more: false,
      msg: '查询成功',
    }
  },

  // 3. 获取商品 - POST /vbs-service/rest/vehsaledraft/goods/query
  '[POST]/vbs-service/rest/vehsaledraft/goods/query': (_params: VehSaleUseSignDraftQueryFilter): BaseResponse_List_VehSaleGetDraftGoodsResponseDTO_ => {
    // 支持真实分页
    const page = _params.page ?? 0
    const pageSize = _params.pageSize ?? 10
    const total = 53 // 假设总条数
    const allList = generateDraftList(total)
    const start = page * pageSize
    const end = start + pageSize
    const data = allList.slice(start, end)
    const more = end < total
    return {
      code: 2000,
      data,
      total,
      more,
      msg: '查询成功',
    }
  },

  // 4. 提交草稿 - POST /vbs-service/rest/vehsaledraft/submit
  '[POST]/vbs-service/rest/vehsaledraft/submit': (_params: VehSaleDraftGoodsSubmitDTO): Response_VehSaleDraftResponseDTO_ => {
    // 30% 概率失败
    if (Math.random() < 0.3) {
      return {
        code: 5000,
        data: {
          goodsCount: 0,
          total: 0,
        },
        msg: '提交失败，请重试',
      }
    }
    return {
      code: 2000,
      data: {
        goodsCount: 1,
        total: _params.total, // 直接使用参数中的 total 字段
      },
      msg: '提交成功',
    }
  },

  // 5. 清空草稿 - POST /vbs-service/rest/vehsaledraft/draft/clear
  '[POST]/vbs-service/rest/vehsaledraft/draft/clear': (_params: any) => {
    return {
      code: 2000,
      msg: '草稿已清空',
    }
  },

  // 6. 获取草稿基础信息 - POST /vbs-service/rest/vehsaledraft/draft/info/get
  '[POST]/vbs-service/rest/vehsaledraft/draft/info/get': (_params: any) => {
    // 生成分类品项数
    const categorySkuCount: Record<string, number> = {}
    for (let i = 0; i < 3; i++) {
      categorySkuCount[`CATE_${i}`] = generateMockData.number(1, 10)
    }
    return {
      code: 2000,
      data: {
        categorySkuCount,
        skuCount: generateMockData.number(5, 30),
        store: {
          gid: generateMockData.id(),
          code: generateMockData.code('STORE'),
          name: generateMockData.name('门店'),
        },
        total: generateMockData.number(100, 1000),
        wms: {
          gid: generateMockData.id(),
          code: generateMockData.code('WMS'),
          name: generateMockData.name('物流中心'),
        },
        wrh: {
          gid: generateMockData.id(),
          code: generateMockData.code('WRH'),
          name: generateMockData.name('仓库'),
        },
      },
      msg: '获取草稿基础信息成功',
    }
  },
})
