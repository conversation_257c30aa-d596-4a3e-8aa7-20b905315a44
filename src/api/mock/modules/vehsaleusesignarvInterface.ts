/*
 * @Author: weish<PERSON>
 * @Date: 2023-05-20 10:00:00
 * @LastEditTime: 2025-05-22 10:11:13
 * @LastEditors: weisheng
 * @Description: 车销使用签收到货相关接口的mock数据
 * @FilePath: /lsym-cx-mini/src/api/mock/modules/vehsaleusesignarvInterface.ts
 */
import { defineMock } from '@alova/mock'
import type {
  BaseResponse_List_VehSaleUseSignArvQueryResponseDTO_,
  BaseResponse_List_VehSaleUseSignDtlResponseDTO_,
  Response_List_VehSaleGoodsDTO_,
  Response_VehSaleUseSignArvResponseDTO_,
  Response_Void_,
  Response_boolean_,
  Response_string_,
  VehSaleGoodsDTO,
  VehSaleUseSignArvQueryFilter,
  VehSaleUseSignArvQueryResponseDTO,
  VehSaleUseSignArvResponseDTO,
  VehSaleUseSignDtlResponseDTO,
} from '../../globals.d'
import { generateMockData } from '../utils'

export default defineMock({
  // 车销使用签收到货查询
  '[POST]/vbs-service/rest/vehsaleusesignarv/query': (params: VehSaleUseSignArvQueryFilter): BaseResponse_List_VehSaleUseSignArvQueryResponseDTO_ => {
    // 获取查询参数
    const { page = 0, pageSize = 10, keyword = '' } = params
    // 获取日期范围 (如果存在)
    const beginDate = params.beginDate
    const finishDate = params.finishDate
    const stat = params.stat

    // 生成50条数据
    const allSignList: VehSaleUseSignArvQueryResponseDTO[] = generateMockData.array((index: number) => ({
      num: generateMockData.code('SIGNARV'),
      stat: index % 3 === 0 ? 100 : index % 3 === 1 ? 1300 : 1310, // 对应back-list页面中的状态: 100-已申请, 1300-已审核, 1310-已作废
      goodsCount: generateMockData.number(1, 10),
      total: generateMockData.number(100, 1000),
      lstupdTime: generateMockData.datetime(-generateMockData.number(0, 30)), // 随机生成0-30天内的时间
      vehSaleEmp: {
        gid: generateMockData.id(),
        code: generateMockData.code('EMP'),
        name: generateMockData.name('业务员'),
      },
      wrh: {
        gid: generateMockData.id(),
        code: generateMockData.code('WRH'),
        name: generateMockData.name('仓库'),
      },
      receiver: {
        gid: generateMockData.id(),
        code: generateMockData.code('RCV'),
        name: generateMockData.name('接收方'),
      },
    }), 50)

    // 根据关键字和时间范围过滤
    let filteredList = allSignList
    if (keyword) {
      filteredList = filteredList.filter(item =>
        item.num?.includes(keyword)
        || item.vehSaleEmp?.name?.includes(keyword)
        || item.wrh?.name?.includes(keyword),
      )
    }

    // 日期筛选
    if (beginDate) {
      const beginDateObj = new Date(beginDate)
      filteredList = filteredList.filter(item =>
        new Date(item.lstupdTime || '').getTime() >= beginDateObj.getTime(),
      )
    }
    if (finishDate) {
      const finishDateObj = new Date(finishDate)
      filteredList = filteredList.filter(item =>
        new Date(item.lstupdTime || '').getTime() <= finishDateObj.getTime(),
      )
    }

    // 状态筛选
    if (stat !== undefined) {
      filteredList = filteredList.filter(item => item.stat === stat)
    }

    // 分页处理
    const startIndex = page * pageSize
    const endIndex = startIndex + pageSize
    const paginatedList = filteredList.slice(startIndex, endIndex)

    return {
      code: 2000,
      data: paginatedList,
      total: filteredList.length,
      more: endIndex < filteredList.length,
      msg: '查询成功',
    }
  },

  // 车销使用签收到货获取
  '[GET]/vbs-service/rest/vehsaleusesignarv/get': (_params: any): Response_VehSaleUseSignArvResponseDTO_ => {
    const details: VehSaleUseSignDtlResponseDTO[] = generateMockData.array((index: number) => {
      const goods = generateMockData.goods(index)
      return {
        line: index + 1,
        gdCode: goods.code,
        goods: goods as VehSaleGoodsDTO,
        note: '',
        qpcDetails: [{
          applyQty: generateMockData.number(10, 100),
          applyQtyStr: `${generateMockData.number(1, 10)}箱`,
          munit: goods.munit || '个',
          price: goods.price || 0,
          qpc: goods.qpc || 1,
          qpcStr: goods.qpcStr || '1个/箱',
          qty: generateMockData.number(5, 50),
          qtyStr: `${generateMockData.number(1, 5)}箱`,
          total: generateMockData.number(100, 1000),
        }],
      }
    }, 5)

    const sign: VehSaleUseSignArvResponseDTO = {
      num: generateMockData.code('SIGNARV'),
      ocrDate: generateMockData.date(),
      vehSaleEmp: {
        gid: generateMockData.id(),
        code: generateMockData.code('EMP'),
        name: generateMockData.name('业务员'),
      },
      vehSaleEmpName: generateMockData.name('业务员'),
      wrh: {
        gid: generateMockData.id(),
        code: generateMockData.code('WRH'),
        name: generateMockData.name('仓库'),
      },
      receiver: {
        gid: generateMockData.id(),
        code: generateMockData.code('RCV'),
        name: generateMockData.name('接收方'),
      },
      carNumber: '粤A12345',
      stat: 100, // 已申请
      goodsCount: details.length,
      total: 999,
      filDate: generateMockData.datetime(),
      filler: generateMockData.name('创建人'),
      lstupdTime: generateMockData.datetime(),
      lastModifyOper: generateMockData.name('修改人'),
      note: '',
      details,
      attachDetails: [],
    }

    return {
      code: 2000,
      data: sign,
      msg: '查询成功',
    }
  },

  // 车销使用签收到货提交
  '[POST]/vbs-service/rest/vehsaleusesignarv/submit': (_params: any): Response_string_ => {
    return {
      code: 2000,
      data: generateMockData.code('SIGNARV'),
      msg: '提交成功',
    }
  },

  // 车销使用签收到货审核
  '[POST]/vbs-service/rest/vehsaleusesignarv/audit': (_params: any): Response_Void_ => {
    return {
      code: 2000,
      msg: '审核成功',
    }
  },

  // 车销使用签收到货作废
  '[POST]/vbs-service/rest/vehsaleusesignarv/abort': (_params: any): Response_Void_ => {
    return {
      code: 2000,
      msg: '作废成功',
    }
  },

  // 获取到货商品
  '[GET]/vbs-service/rest/vehsaleusesignarv/arvgoods/get': (_params: any): Response_List_VehSaleGoodsDTO_ => {
    const goodsList = generateMockData.array((index: number) => {
      const goods = generateMockData.goods(index)
      return goods as VehSaleGoodsDTO
    }, 5)

    return {
      code: 2000,
      data: goodsList,
      msg: '查询成功',
    }
  },

  // 是否有到货商品
  '[GET]/vbs-service/rest/vehsaleusesignarv/arvgoods/has': (_params: any): Response_boolean_ => {
    return {
      code: 2000,
      data: Math.random() < 0.5,
      msg: '查询成功',
    }
  },

  // 自动回货
  '[POST]/vbs-service/rest/vehsaleusesignarv/autoarv': (_params: any): Response_string_ => {
    return {
      code: 2000,
      data: generateMockData.code('SIGNARV'),
      msg: '自动回货成功',
    }
  },

  // 获取单据明细详情
  '[POST]/vbs-service/rest/vehsaleusesignarv/get/details': (_params: any): BaseResponse_List_VehSaleUseSignDtlResponseDTO_ => {
    const details: VehSaleUseSignDtlResponseDTO[] = generateMockData.array((index: number) => {
      const goods = generateMockData.goods(index)
      return {
        line: index + 1,
        gdCode: goods.code,
        goods: goods as VehSaleGoodsDTO,
        note: '',
        qpcDetails: [{
          applyQty: generateMockData.number(10, 100),
          applyQtyStr: `${generateMockData.number(1, 10)}箱`,
          munit: goods.munit || '个',
          price: goods.price || 0,
          qpc: goods.qpc || 1,
          qpcStr: goods.qpcStr || '1个/箱',
          qty: generateMockData.number(5, 50),
          qtyStr: `${generateMockData.number(1, 5)}箱`,
          total: generateMockData.number(100, 1000),
        }],
      }
    }, 5)

    return {
      code: 2000,
      data: details,
      msg: '查询成功',
      total: details.length,
      more: false,
    }
  },
})
