/*
 * @Author: weish<PERSON>
 * @Date: 2023-05-20 10:00:00
 * @LastEditTime: 2025-05-21 16:33:20
 * @LastEditors: weisheng
 * @Description: 主数据相关接口的mock数据
 * @FilePath: /lsym-cx-mini/src/api/mock/modules/mdataInterface.ts
 */
import { defineMock } from '@alova/mock'
import type {
  CategoryDTO,
  CategoryQueryFilter,
  GCN,
  GoodsQueryFilter,
  SortLineResponseDTO,
  StoreDTO,
  VehSaleGoodsDTO,
} from '../../globals.d'
import { generateMockData } from '../utils'
import skuDefaultImage from '@/static/icon/ic_sku_default.svg'

export default defineMock({
  // 线路查询
  '[POST]/vbs-service/rest/mdata/sortline/query': (_params: any): any => {
    const sortLines: SortLineResponseDTO[] = generateMockData.array((_index: number) => ({
      archCode: generateMockData.code('ARCH'),
      archName: generateMockData.name('线路分类体系'),
      archUuid: `uuid-${generateMockData.id()}`,
      code: generateMockData.code('LINE'),
      name: generateMockData.name('线路'),
      uuid: `uuid-${generateMockData.id()}`,
    }), 10)

    return {
      code: 2000,
      data: sortLines,
      total: sortLines.length,
      more: false,
      msg: '查询成功',
    }
  },

  // 车销业务员查询
  '[POST]/vbs-service/rest/mdata/vehsaleemp/query': (_params: any): any => {
    const empList: GCN[] = generateMockData.array((_index: number) => ({
      gid: generateMockData.id(),
      code: generateMockData.code('EMP'),
      name: generateMockData.name('车销业务员'),
    }), 10)

    return {
      code: 2000,
      data: empList,
      total: empList.length,
      more: false,
      msg: '查询成功',
    }
  },
  // 商品分类查询
  '[POST]/vbs-service/rest/mdata/category/query': (_params: CategoryQueryFilter): any => {
    const categories: CategoryDTO[] = generateMockData.array((index: number) => ({
      code: generateMockData.code('CATE'),
      name: generateMockData.name('分类'),
      children: index % 3 !== 2
        ? generateMockData.array((_childIndex: number) => ({
            code: generateMockData.code('CATE'),
            name: generateMockData.name('子分类'),
            children: [],
          }), 2)
        : [],
    }), 20)

    return {
      code: 2000,
      data: categories,
      total: categories.length,
      more: false,
      msg: '查询成功',
    }
  },

  // 商品查询
  '[POST]/vbs-service/rest/mdata/goods/query': (_params: GoodsQueryFilter): any => {
    const goodsList: VehSaleGoodsDTO[] = generateMockData.array((_index: number) => ({
      gid: generateMockData.id(),
      code: generateMockData.code('GOODS'),
      name: generateMockData.name('商品'),
      munit: '个',
      price: generateMockData.number(1, 1000) / 100,
      qpc: generateMockData.number(1, 10),
      qpcStr: `${generateMockData.number(1, 10)}个/箱`,
      singlePrice: generateMockData.number(1, 100) / 100,
      category: {
        code: generateMockData.code('CATE'),
        name: generateMockData.name('分类'),
      },
      gdCode: generateMockData.code('BAR'),
      gdImages: [skuDefaultImage],
      busInvQty: generateMockData.number(100, 1000),
      vehSaleWrhQty: generateMockData.number(10, 100),
      advUseSignQty: generateMockData.number(1, 10),
    }), 20)

    return {
      code: 2000,
      data: goodsList,
      total: goodsList.length,
      more: false,
      msg: '查询成功',
    }
  },

  // 仓库查询
  '[POST]/vbs-service/rest/mdata/wrh/query': (_params: any): any => {
    const wrhList: GCN[] = generateMockData.array((_index: number) => ({
      gid: generateMockData.id(),
      code: generateMockData.code('WRH'),
      name: generateMockData.name('仓库'),
    }), 5)

    return {
      code: 2000,
      data: wrhList,
      total: wrhList.length,
      more: false,
      msg: '查询成功',
    }
  },

  // 物流中心查询
  '[POST]/vbs-service/rest/mdata/wms/query': (_params: any): any => {
    const wmsList: GCN[] = generateMockData.array((_index: number) => ({
      gid: generateMockData.id(),
      code: generateMockData.code('WMS'),
      name: generateMockData.name('物流中心'),
    }), 30)

    return {
      code: 2000,
      data: wmsList,
      total: wmsList.length,
      more: false,
      msg: '查询成功',
    }
  },

  // 门店查询
  '[POST]/vbs-service/rest/mdata/store/query': (params: any): any => {
    // 获取查询参数
    const { page = 0, pageSize = 10, keyword = '' } = params

    // 生成门店数据
    const allStores: StoreDTO[] = generateMockData.array((_index: number) => ({
      gid: generateMockData.id(),
      code: generateMockData.code('STORE'),
      name: generateMockData.name('门店'),
      address: `${generateMockData.name('城市')}${generateMockData.name('区')}${generateMockData.name('街道')}${generateMockData.number(1, 100)}号`,
      lastVehSaleTime: generateMockData.datetime(-generateMockData.number(1, 30)), // 随机生成1-30天前的时间
      lastVehSaleTotal: generateMockData.number(100, 10000) / 100, // 随机生成100-10000之间的金额，保留两位小数
      latitude: `${30 + generateMockData.number(0, 10) / 10}`,
      longitude: `${114 + generateMockData.number(0, 10) / 10}`, // 经度应该是数字类型
    }), 50)

    // 根据关键字过滤
    let filteredStores = allStores
    if (keyword) {
      filteredStores = allStores.filter(store =>
        store.code?.includes(keyword)
        || store.name?.includes(keyword),
      )
    }

    // 分页处理
    const startIndex = page * pageSize
    const endIndex = startIndex + pageSize
    const paginatedStores = filteredStores.slice(startIndex, endIndex)

    return {
      code: 2000,
      data: paginatedStores,
      total: filteredStores.length,
      more: endIndex < filteredStores.length,
      msg: '查询成功',
    }
  },
})
