/*
 * @Author: weisheng
 * @Date: 2023-05-20 10:00:00
 * @LastEditTime: 2025-05-17 17:01:42
 * @LastEditors: weisheng
 * @Description: 车销使用签收相关接口的mock数据
 * @FilePath: /lsym-cx-mini/src/api/mock/modules/vehsaleusesignInterface.ts
 */
import { defineMock } from '@alova/mock'
import type {
  BaseResponse_List_VehSaleGetDraftGoodsResponseDTO_,
  BaseResponse_List_VehSaleUseSignDtlResponseDTO_,
  BaseResponse_List_VehSaleUseSignQueryResponseDTO_,
  Response_VehSaleDraftResponseDTO_,
  Response_VehSaleUseSignResponseDTO_,
  Response_Void_,
  Response_string_,
  VehSaleDraftGoodsSubmitDTO,
  VehSaleGetDraftGoodsResponseDTO,
  VehSaleGoodsDTO,
  VehSaleRemoveDraftRequestDTO,
  VehSaleUseSignDtlQpcResponseDTO,
  VehSaleUseSignDtlResponseDTO,
  VehSaleUseSignQueryFilter,
  VehSaleUseSignQueryResponseDTO,
  VehSaleUseSignResponseDTO,
} from '../../globals.d'
import { generateMockData } from '../utils'
import skuDefaultImage from '@/static/icon/ic_sku_default.svg'

export default defineMock({
  // 车销使用签收草稿查询
  '[GET]/vbs-service/rest/vehsaleusesign/draft/get': (): BaseResponse_List_VehSaleGetDraftGoodsResponseDTO_ => {
    const draftList: VehSaleGetDraftGoodsResponseDTO[] = generateMockData.array((index: number) => {
      const goods = generateMockData.goods(index)
      return {
        goods: goods as VehSaleGoodsDTO,
        qpcDetails: [{
          applyQty: generateMockData.number(10, 100),
          applyQtyStr: `${generateMockData.number(1, 10)}箱`,
          munit: goods.munit || '个',
          price: goods.price || 0,
          qpc: goods.qpc || 1,
          qpcStr: goods.qpcStr || '1个/箱',
          qty: generateMockData.number(5, 50),
          qtyStr: `${generateMockData.number(1, 5)}箱`,
          total: (goods.price || 0) * generateMockData.number(10, 100),
          singlePrice: (goods.price || 0) / (goods.qpc || 1),
          version: 1,
        }],
        version: 1,
      }
    }, 5)

    return {
      code: 2000,
      data: draftList,
      total: draftList.length,
      more: false,
      msg: '查询成功',
    }
  },

  // 车销使用签收草稿删除
  '[POST]/vbs-service/rest/vehsaleusesign/draft/remove': (_params: VehSaleRemoveDraftRequestDTO[]): Response_VehSaleDraftResponseDTO_ => {
    return {
      code: 2000,
      data: {
        goodsCount: 0,
        total: 0,
      },
      msg: '删除成功',
    }
  },

  // 车销使用签收草稿提交
  '[POST]/vbs-service/rest/vehsaleusesign/draft/submit': (_params: VehSaleDraftGoodsSubmitDTO): Response_string_ => {
    return {
      code: 2000,
      data: generateMockData.code('SIGN'),
      msg: '提交成功',
    }
  },

  // 车销使用签收查询
  '[POST]/vbs-service/rest/vehsaleusesign/query': (params: VehSaleUseSignQueryFilter): BaseResponse_List_VehSaleUseSignQueryResponseDTO_ => {
    // 获取查询参数
    const { page = 0, pageSize = 10, keyword = '' } = params
    // 获取日期范围 (如果存在)
    const beginDate = params.beginDate
    const finishDate = params.finishDate

    // 生成50条数据
    const allSignList: VehSaleUseSignQueryResponseDTO[] = generateMockData.array((index: number) => ({
      num: generateMockData.code('SIGN'),
      stat: index % 3, // 0: 草稿, 1: 已提交, 2: 已审核
      goodsCount: generateMockData.number(1, 10),
      total: generateMockData.number(100, 1000),
      lstupdTime: generateMockData.datetime(-generateMockData.number(0, 30)), // 随机生成0-30天内的时间
      vehSaleEmp: {
        gid: generateMockData.id(),
        code: generateMockData.code('EMP'),
        name: generateMockData.name('业务员'),
      },
      wrh: {
        gid: generateMockData.id(),
        code: generateMockData.code('WRH'),
        name: generateMockData.name('仓库'),
      },
    }), 50)

    // 根据关键字和时间范围过滤
    let filteredSignList = allSignList
    if (keyword) {
      filteredSignList = filteredSignList.filter(sign =>
        sign.num?.includes(keyword)
        || sign.vehSaleEmp?.name?.includes(keyword)
        || sign.wrh?.name?.includes(keyword),
      )
    }

    // 日期筛选
    if (beginDate) {
      const beginDateObj = new Date(beginDate)
      filteredSignList = filteredSignList.filter(sign =>
        new Date(sign.lstupdTime || '').getTime() >= beginDateObj.getTime(),
      )
    }
    if (finishDate) {
      const finishDateObj = new Date(finishDate)
      filteredSignList = filteredSignList.filter(sign =>
        new Date(sign.lstupdTime || '').getTime() <= finishDateObj.getTime(),
      )
    }

    // 分页处理
    const startIndex = page * pageSize
    const endIndex = startIndex + pageSize
    const paginatedSignList = filteredSignList.slice(startIndex, endIndex)

    return {
      code: 2000,
      data: paginatedSignList,
      total: filteredSignList.length,
      more: endIndex < filteredSignList.length,
      msg: '查询成功',
    }
  },

  // 车销使用签收获取
  '[GET]/vbs-service/rest/vehsaleusesign/get': (_params: any): Response_VehSaleUseSignResponseDTO_ => {
    const details: VehSaleUseSignDtlResponseDTO[] = generateMockData.array((index: number) => {
      const goods = generateMockData.goods(index)
      return {
        line: index + 1,
        gdCode: goods.code,
        goods: goods as VehSaleGoodsDTO,
        note: '',
        qpcDetails: [{
          applyQty: generateMockData.number(10, 100),
          applyQtyStr: `${generateMockData.number(1, 10)}箱`,
          munit: goods.munit || '个',
          price: goods.price || 0,
          qpc: goods.qpc || 1,
          qpcStr: goods.qpcStr || '1个/箱',
          qty: generateMockData.number(5, 50),
          qtyStr: `${generateMockData.number(1, 5)}箱`,
          total: generateMockData.number(100, 1000),
        }],
      }
    }, 5)

    const sign: VehSaleUseSignResponseDTO = {
      num: generateMockData.code('SIGN'),
      ocrDate: generateMockData.date(),
      vehSaleEmp: {
        gid: generateMockData.id(),
        code: generateMockData.code('EMP'),
        name: generateMockData.name('业务员'),
      },
      vehSaleEmpName: generateMockData.name('业务员'),
      wrh: {
        gid: generateMockData.id(),
        code: generateMockData.code('WRH'),
        name: generateMockData.name('仓库'),
      },
      carNumber: '粤A12345',
      stat: generateMockData.stat(),
      goodsCount: details.length,
      total: 999, // 计算总金额
      filDate: generateMockData.datetime(),
      filler: generateMockData.name('创建人'),
      lstupdTime: generateMockData.datetime(),
      lastModifyOper: generateMockData.name('修改人'),
      note: '',
      details,
      attachDetails: [],
    }

    return {
      code: 2000,
      data: sign,
      msg: '查询成功',
    }
  },

  // 车销使用签收提交
  '[POST]/vbs-service/rest/vehsaleusesign/submit': (_params: any): Response_string_ => {
    return {
      code: 2000,
      data: generateMockData.code('SIGN'),
      msg: '提交成功',
    }
  },

  // 车销使用签收审核
  '[POST]/vbs-service/rest/vehsaleusesign/audit': (_params: any): Response_Void_ => {
    return {
      code: 2000,
      msg: '审核成功',
    }
  },

  // 车销使用签收作废
  '[POST]/vbs-service/rest/vehsaleusesign/abort': (_params: any): Response_Void_ => {
    return {
      code: 2000,
      msg: '作废成功',
    }
  },

  // 车销使用签收复制
  '[POST]/vbs-service/rest/vehsaleusesign/copy': (_params: any): Response_string_ => {
    return {
      code: 2000,
      data: generateMockData.code('SIGN'),
      msg: '复制成功',
    }
  },

  // 车销使用签收验证创建
  '[POST]/vbs-service/rest/vehsaleusesign/verify/create': (_params: any): Response_Void_ => {
    return {
      code: 2000,
      msg: '验证通过',
    }
  },

  // 获取单据明细详情
  '[POST]/vbs-service/rest/vehsaleusesign/query/details': (_params: any): BaseResponse_List_VehSaleUseSignDtlResponseDTO_ => {
    const details: VehSaleUseSignDtlResponseDTO[] = generateMockData.array((index: number) => {
      const goods = generateMockData.goods(index)
      const totalQty = generateMockData.number(10, 100)

      // 随机生成1-3个规格子项
      const qpcCount = generateMockData.number(1, 3)

      // 生成随机比例，用于分配数量
      const ratios: number[] = []
      for (let i = 0; i < qpcCount; i++) {
        ratios.push(Math.random())
      }

      // 计算总比例，用于归一化
      const totalRatio = ratios.reduce((sum, ratio) => sum + ratio, 0)

      // 计算每个子项的具体数量
      const qpcDetails: VehSaleUseSignDtlQpcResponseDTO[] = []
      let allocatedQty = 0

      for (let i = 0; i < qpcCount; i++) {
        // 计算分配比例
        const normalizedRatio = ratios[i] / totalRatio

        // 对于最后一个子项，直接分配剩余数量以避免舍入误差
        let itemQty = 0
        if (i === qpcCount - 1) {
          itemQty = totalQty - allocatedQty
        }
        else {
          // 四舍五入到整数
          itemQty = Math.round(totalQty * normalizedRatio)
          allocatedQty += itemQty
        }

        qpcDetails.push({
          applyQty: itemQty,
          applyQtyStr: `${Math.ceil(itemQty / (i + 1))}箱`,
          munit: `${goods.munit || '个'}${i + 1}`,
          price: (goods.price || 0) * (i + 1),
          qpc: (goods.qpc || 1) * (i + 1),
          qpcStr: `${i + 1}${goods.munit || '个'}/箱`,
          qty: itemQty, // 确保qty和applyQty相等
          qtyStr: `${Math.ceil(itemQty / (i + 1))}箱`,
          total: itemQty * (goods.price || 0) * (i + 1),
        })
      }

      return {
        line: index + 1,
        gdCode: goods.code,
        goods: goods as VehSaleGoodsDTO,
        imageDetails: [{
          fileUrl: skuDefaultImage,
          fileType: 'img',
        }, { fileUrl: skuDefaultImage, fileType: 'img' }],
        note: '',
        qty: totalQty,
        applyQty: totalQty, // 确保商品级别的qty和applyQty相等
        qpcDetails,
      }
    }, 5)

    return {
      code: 2000,
      data: details,
      msg: '查询成功',
      total: details.length,
      more: false,
    }
  },
})
