import { defineMock } from '@alova/mock'
import type {
  BaseResponse_List_VehSaleUseSignBckDtlResponseDTO_,
  BaseResponse_List_VehSaleUseSignBckQueryResponseDTO_,
  Response_Void_,
  Response_string_,
  VehSaleGoodsDTO,
  VehSaleUseSignBckDtlResponseDTO,
  VehSaleUseSignBckQueryFilter,
  VehSaleUseSignBckQueryResponseDTO,
} from '../../globals.d'
import { generateMockData } from '../utils'

export default defineMock({
  // 作废单据
  '[POST]/vbs-service/rest/vehsaleusesignbck/abort': (_params: any): Response_Void_ => {
    return {
      code: 2000,
      msg: '作废成功',
    }
  },

  // 审核单据
  '[POST]/vbs-service/rest/vehsaleusesignbck/audit': (_params: any): Response_Void_ => {
    return {
      code: 2000,
      msg: '审核成功',
    }
  },

  // 获取单据详情
  '[GET]/vbs-service/rest/vehsaleusesignbck/get': (request): {
    code: number
    msg?: string
    data?: any
  } => {
    const num = request.params?.num || generateMockData.code('VSSB')

    return {
      code: 2000,
      data: {
        num,
        stat: 100,
        goodsCount: 5,
        total: 2500,
        lstupdTime: generateMockData.datetime(),
        filDate: generateMockData.datetime(),
        filler: '张三',
        lastModifyOper: '李四',
        note: '这是备注信息',
        ocrDate: generateMockData.date(),
        vehSaleEmp: {
          gid: generateMockData.id(),
          code: generateMockData.code('EMP'),
          name: generateMockData.name('业务员'),
        },
        vehSaleEmpName: generateMockData.name('业务员'),
        wrh: {
          gid: generateMockData.id(),
          code: generateMockData.code('WRH'),
          name: generateMockData.name('仓库'),
        },
        receiver: {
          gid: generateMockData.id(),
          code: generateMockData.code('RCV'),
          name: generateMockData.name('接收人'),
        },
        details: Array.from({ length: 5 }).map((_, index) => ({
          line: index + 1,
          gdCode: generateMockData.code('GD'),
          note: `商品${index + 1}备注`,
          goods: {
            gid: generateMockData.id(),
            code: generateMockData.code('GD'),
            name: generateMockData.name('商品'),
            gdCode: generateMockData.code('GD'),
            qpc: 10,
            qpcStr: '10*10',
            munit: '箱',
            price: generateMockData.number(10, 100),
            singlePrice: generateMockData.number(1, 10),
            category: {
              code: generateMockData.code('CT'),
              name: generateMockData.name('分类'),
            },
            advUseSignQty: generateMockData.number(5, 50),
            busInvQty: generateMockData.number(100, 1000),
            vehSaleWrhQty: generateMockData.number(10, 100),
            qpcDetails: [
              {
                qpc: 10,
                qpcStr: '10*10',
                munit: '箱',
              },
              {
                qpc: 1,
                qpcStr: '1*1',
                munit: '个',
              },
            ],
          },
          qpcDetails: [
            {
              qpc: 10,
              qpcStr: '10*10',
              munit: '箱',
              price: generateMockData.number(100, 1000),
              applyQty: generateMockData.number(1, 10),
              applyQtyStr: `${generateMockData.number(1, 10)}箱`,
              qty: generateMockData.number(1, 10),
              qtyStr: `${generateMockData.number(1, 10)}箱`,
              total: generateMockData.number(100, 10000),
            },
          ],
        })),
        attachDetails: Array.from({ length: 2 }).map((_, index) => ({
          line: index + 1,
          fileId: generateMockData.id().toString(),
          fileName: `附件${index + 1}.jpg`,
          fileUrl: 'https://example.com/attachment.jpg',
          note: `附件${index + 1}备注`,
        })),
      },
      msg: '获取成功',
    }
  },

  // 查询单据列表
  '[POST]/vbs-service/rest/vehsaleusesignbck/query': (params: VehSaleUseSignBckQueryFilter): BaseResponse_List_VehSaleUseSignBckQueryResponseDTO_ => {
    // 生成50条mock数据
    const mockData: VehSaleUseSignBckQueryResponseDTO[] = generateMockData.array((index: number) => ({
      num: generateMockData.code('VSSB'),
      stat: [100, 110, 1300, 1310][index % 4], // 状态循环：草稿、已申请、已审核、已作废
      goodsCount: generateMockData.number(1, 20),
      total: generateMockData.number(100, 10000),
      lstupdTime: generateMockData.datetime(),
      vehSaleEmp: {
        gid: generateMockData.id(),
        code: generateMockData.code('EMP'),
        name: generateMockData.name('业务员'),
      },
      wrh: {
        gid: generateMockData.id(),
        code: generateMockData.code('WRH'),
        name: generateMockData.name('仓库'),
      },
      receiver: {
        gid: generateMockData.id(),
        code: generateMockData.code('RCV'),
        name: generateMockData.name('接收人'),
      },
    }), 50)

    // 根据传入的参数筛选数据
    let filteredData = [...mockData]

    // 如果指定了状态，则进行筛选
    if (params.stat !== undefined) {
      filteredData = filteredData.filter(item => item.stat === params.stat)
    }

    // 如果指定了业务员，则进行筛选
    if (params.vehSaleEmpGid !== undefined) {
      filteredData = filteredData.filter(item => item.vehSaleEmp?.gid === params.vehSaleEmpGid)
    }

    // 如果指定了仓库，则进行筛选
    if (params.wmsGid !== undefined) {
      filteredData = filteredData.filter(item => item.wrh?.gid === params.wmsGid)
    }

    // 如果有关键字搜索，则进行筛选
    if (params.keyword) {
      const keyword = params.keyword.toLowerCase()
      filteredData = filteredData.filter(item =>
        item.num?.toLowerCase().includes(keyword)
        || item.vehSaleEmp?.name?.toLowerCase().includes(keyword)
        || item.wrh?.name?.toLowerCase().includes(keyword),
      )
    }

    // 分页处理
    const pageSize = params.pageSize || 10
    const pageIndex = params.page || 0
    const start = pageIndex * pageSize
    const end = start + pageSize
    const pagedData = filteredData.slice(start, end)

    return {
      code: 2000,
      data: pagedData,
      total: filteredData.length,
      more: end < filteredData.length,
      msg: '查询成功',
    }
  },

  // 提交单据
  '[POST]/vbs-service/rest/vehsaleusesignbck/submit': (_params: any): Response_string_ => {
    return {
      code: 2000,
      data: generateMockData.code('VSSB'),
      msg: '提交成功',
    }
  },

  // 验证创建
  '[POST]/vbs-service/rest/vehsaleusesignbck/verify/create': (_params: any): Response_Void_ => {
    return {
      code: 2000,
      msg: '验证创建通过',
    }
  },

  // 获取单据明细详情
  '[POST]/vbs-service/rest/vehsaleusesignbck/query/details': (_params: any): BaseResponse_List_VehSaleUseSignBckDtlResponseDTO_ => {
    const details: VehSaleUseSignBckDtlResponseDTO[] = generateMockData.array((index: number) => {
      const goods = generateMockData.goods(index)
      return {
        line: index + 1,
        gdCode: goods.code,
        goods: goods as VehSaleGoodsDTO,
        munit: goods.munit,
        price: goods.price,
        qpc: goods.qpc,
        qpcStr: goods.qpcStr,
        qty: generateMockData.number(5, 50),
        qtyStr: `${generateMockData.number(1, 5)}箱`,
        note: '',
        tax: generateMockData.number(1, 10),
        taxRate: 0.13,
        taxType: 'includePrice',
        total: generateMockData.number(100, 1000),
      }
    }, 5)

    return {
      code: 2000,
      data: details,
      msg: '查询成功',
      total: details.length,
      more: false,
    }
  },
})
