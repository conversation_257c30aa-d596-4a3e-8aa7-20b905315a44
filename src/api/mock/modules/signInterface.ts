/*
 * @Author: claude
 * @Date: 2025-05-15 15:00:00
 * @LastEditTime: 2025-05-15 15:00:00
 * @LastEditors: claude
 * @Description: 打卡相关接口mock数据
 * @FilePath: /lsym-cx-mini/src/api/mock/modules/signInterface.ts
 */
import { defineMock } from '@alova/mock'
import { generateMockData } from '../utils'

export default defineMock({
  // 获取打卡距离
  '[GET]/vbs-service/rest/sign/distance': (_params: any): any => {
    return {
      code: 2000,
      data: {
        distance: generateMockData.number(10, 500),
        maxDistance: 500, // 最大允许打卡距离（米）
      },
      msg: '获取打卡距离成功',
    }
  },

  // 签到
  '[POST]/vbs-service/rest/sign/signin': (params: any): any => {
    const { latitude, longitude, address, storeId } = params

    if (!latitude || !longitude || !address) {
      return {
        code: 4000,
        msg: '位置信息不完整',
      }
    }

    return {
      code: 2000,
      data: {
        id: generateMockData.id(),
        signTime: new Date().toISOString(),
        latitude,
        longitude,
        address,
        storeId,
        storeName: storeId ? `门店${storeId}` : null,
        signType: 'in',
      },
      msg: '签到成功',
    }
  },

  // 签退
  '[POST]/vbs-service/rest/sign/signout': (params: any): any => {
    const { latitude, longitude, address, storeId } = params

    if (!latitude || !longitude || !address) {
      return {
        code: 4000,
        msg: '位置信息不完整',
      }
    }

    return {
      code: 2000,
      data: {
        id: generateMockData.id(),
        signTime: new Date().toISOString(),
        latitude,
        longitude,
        address,
        storeId,
        storeName: storeId ? `门店${storeId}` : null,
        signType: 'out',
      },
      msg: '签退成功',
    }
  },

  // 查询线路列表
  '[POST]/vbs-service/rest/sign/sortline/query': (params: any): any => {
    const { page = 0, pageSize = 10 } = params

    const total = 20
    const startIndex = page * pageSize
    const endIndex = Math.min(startIndex + pageSize, total)
    const count = endIndex - startIndex

    const lines = generateMockData.array((_index: number) => ({
      id: generateMockData.id(),
      code: generateMockData.code('LINE'),
      name: generateMockData.name('线路'),
      storeCount: generateMockData.number(5, 20),
      lastVisitTime: generateMockData.datetime(-generateMockData.number(0, 30)), // 最近0-30天内的时间
    }), count)

    return {
      code: 2000,
      data: lines,
      total,
      more: endIndex < total,
      msg: '查询成功',
    }
  },

  // 验证门店
  '[GET]/vbs-service/rest/sign/verify/store': (params: any): any => {
    const { storeId, latitude, longitude } = params

    if (!storeId) {
      return {
        code: 4000,
        msg: '门店ID不能为空',
      }
    }

    if (!latitude || !longitude) {
      return {
        code: 4000,
        msg: '位置信息不完整',
      }
    }

    // 模拟80%的情况下验证通过
    const isValid = Math.random() > 0.2

    if (isValid) {
      return {
        code: 2000,
        data: {
          valid: true,
          distance: generateMockData.number(1, 400), // 1-400米
          maxDistance: 500, // 最大允许距离
          store: {
            id: storeId,
            name: `门店${storeId}`,
            address: `${generateMockData.name('城市')}${generateMockData.name('区')}${generateMockData.name('街道')}${generateMockData.number(1, 100)}号`,
            latitude: (Number.parseFloat(latitude) + (Math.random() * 0.01)).toString(),
            longitude: (Number.parseFloat(longitude) + (Math.random() * 0.01)).toString(),
          },
        },
        msg: '门店验证成功',
      }
    }
    else {
      return {
        code: 4001,
        data: {
          valid: false,
          distance: generateMockData.number(501, 1000), // 超出范围
          maxDistance: 500,
          store: {
            id: storeId,
            name: `门店${storeId}`,
            address: `${generateMockData.name('城市')}${generateMockData.name('区')}${generateMockData.name('街道')}${generateMockData.number(1, 100)}号`,
            latitude: (Number.parseFloat(latitude) + (Math.random() * 0.05)).toString(),
            longitude: (Number.parseFloat(longitude) + (Math.random() * 0.05)).toString(),
          },
        },
        msg: '位置距离门店太远，无法打卡',
      }
    }
  },

  // 签退前校验
  '[POST]/vbs-service/rest/sign/out/verify': (params: any): any => {
    const { storeGid, vehSaleEmpGid } = params

    if (!storeGid || !vehSaleEmpGid) {
      return {
        code: 4000,
        msg: '参数不完整',
      }
    }

    // 模拟不同的校验结果
    const random = Math.random()

    if (random < 0.7) {
      // 70%的情况校验成功，无草稿
      return {
        code: 2000,
        data: {
          valid: true,
          message: null,
          moduleId: null,
        },
        msg: '校验成功',
      }
    }
    else if (random < 0.85) {
      // 15%的情况存在车销草稿
      return {
        code: 2000,
        data: {
          valid: false,
          message: '存在未提交的车销单，请确认！',
          moduleId: 'vehSale',
        },
        msg: '存在未提交的车销单，请确认！',
      }
    }
    else {
      // 15%的情况存在车销退货草稿
      return {
        code: 2000,
        data: {
          valid: false,
          message: '存在未提交的车销退货单，请确认！',
          moduleId: 'vehSaleBck',
        },
        msg: '存在未提交的车销退货单，请确认！',
      }
    }
  },
})
