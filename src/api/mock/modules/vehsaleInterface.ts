import { defineMock } from '@alova/mock'
import type {
  BaseResponse_List_VehSaleDtlResponseDTO_,
  BaseResponse_List_VehSaleGoodsDTO_,
  BaseResponse_List_VehSaleQueryResponseDTO_,
  GoodsQueryFilter,
  Response_VehSaleResponseDTO_,
  Response_Void_,
  Response_boolean_,
  Response_string_,
  VehSaleDtlResponseDTO,
  VehSaleGoodsDTO,
  VehSaleQueryFilter,
  VehSaleQueryResponseDTO,
  VehSaleResponseDTO,
} from '../../globals.d'
import { generateMockData } from '../utils'
import skuDefaultImage from '@/static/icon/ic_sku_default.svg'

export default defineMock({
  // 作废单据
  '[POST]/vbs-service/rest/vehsale/abort': (_params: any): Response_Void_ => {
    return {
      code: 2000,
      msg: '作废成功',
    }
  },

  // 审核单据
  '[POST]/vbs-service/rest/vehsale/audit': (_params: any): Response_Void_ => {
    return {
      code: 2000,
      msg: '审核成功',
    }
  },

  // 获取单据详情
  '[GET]/vbs-service/rest/vehsale/get': (_params: any): Response_VehSaleResponseDTO_ => {
    const details: VehSaleDtlResponseDTO[] = generateMockData.array((index: number) => {
      const goods = generateMockData.goods(index)
      return {
        line: index + 1,
        gdCode: goods.code,
        goods: goods as VehSaleGoodsDTO,
        munit: goods.munit,
        price: goods.price,
        qpc: goods.qpc,
        qpcStr: goods.qpcStr,
        applyQty: generateMockData.number(10, 100),
        applyQtyStr: `${generateMockData.number(1, 10)}箱`,
        qty: generateMockData.number(5, 50),
        qtyStr: `${generateMockData.number(1, 5)}`,
        note: '',
        total: generateMockData.number(100, 1000),
      }
    }, 5)

    const vehSale: VehSaleResponseDTO = {
      num: generateMockData.code('VS'),
      ocrDate: generateMockData.date(),
      vehSaleEmp: {
        gid: generateMockData.id(),
        code: generateMockData.code('EMP'),
        name: generateMockData.name('业务员'),
      },
      vehSaleEmpName: generateMockData.name('业务员'),
      wrh: {
        gid: generateMockData.id(),
        code: generateMockData.code('WRH'),
        name: generateMockData.name('仓库'),
      },
      carNumber: '粤A12345',
      stat: 1, // 已提交
      goodsCount: details.length,
      total: 999,
      filDate: generateMockData.datetime(),
      filler: generateMockData.name('创建人'),
      lstupdTime: generateMockData.datetime(),
      lastModifyOper: generateMockData.name('修改人'),
      note: '',
      details,
      attachDetails: [],
    }

    return {
      code: 2000,
      data: vehSale,
      msg: '查询成功',
    }
  },

  // 查询单据列表
  '[POST]/vbs-service/rest/vehsale/query': (params: VehSaleQueryFilter): BaseResponse_List_VehSaleQueryResponseDTO_ => {
    // 获取查询参数
    const { page = 0, pageSize = 10, keyword = '' } = params
    // 获取日期范围 (如果存在)
    const beginDate = params.beginDate
    const finishDate = params.finishDate

    // 生成50条数据
    const allVehSaleList: VehSaleQueryResponseDTO[] = generateMockData.array((index: number) => ({
      num: generateMockData.code('VS'),
      stat: index % 3, // 0: 草稿, 1: 已提交, 2: 已审核
      goodsCount: generateMockData.number(1, 10),
      total: generateMockData.number(100, 1000),
      lstupdTime: generateMockData.datetime(-generateMockData.number(0, 30)), // 随机生成0-30天内的时间
      vehSaleEmp: {
        gid: generateMockData.id(),
        code: generateMockData.code('EMP'),
        name: generateMockData.name('业务员'),
      },
    }), 50)

    // 根据关键字和时间范围过滤
    let filteredList = allVehSaleList
    if (keyword) {
      filteredList = filteredList.filter(item =>
        item.num?.includes(keyword)
        || item.vehSaleEmp?.name?.includes(keyword),
      )
    }

    // 日期筛选
    if (beginDate) {
      const beginDateObj = new Date(beginDate)
      filteredList = filteredList.filter(item =>
        new Date(item.lstupdTime || '').getTime() >= beginDateObj.getTime(),
      )
    }
    if (finishDate) {
      const finishDateObj = new Date(finishDate)
      filteredList = filteredList.filter(item =>
        new Date(item.lstupdTime || '').getTime() <= finishDateObj.getTime(),
      )
    }

    // 分页处理
    const startIndex = page * pageSize
    const endIndex = startIndex + pageSize
    const paginatedList = filteredList.slice(startIndex, endIndex)

    return {
      code: 2000,
      data: paginatedList,
      total: filteredList.length,
      more: endIndex < filteredList.length,
      msg: '查询成功',
    }
  },

  // 提交单据
  '[POST]/vbs-service/rest/vehsale/submit': (_params: any): Response_string_ => {
    return {
      code: 2000,
      data: generateMockData.code('VS'),
      msg: '提交成功',
    }
  },

  // 新的创建前验证接口
  '[POST]/vbs-service/rest/vehsale/verify/create': (_params: any): Response_boolean_ => {
    return {
      code: 2000,
      data: true,
      msg: '验证通过',
    }
  },

  // 获取单据明细详情 - 新接口
  '[POST]/vbs-service/rest/vehsale/query/details': (_params: any): BaseResponse_List_VehSaleDtlResponseDTO_ => {
    const details: VehSaleDtlResponseDTO[] = generateMockData.array((index: number) => {
      const goods = generateMockData.goods(index)
      return {
        line: index + 1,
        gdCode: goods.code,
        goods: goods as VehSaleGoodsDTO,
        munit: goods.munit,
        price: goods.price,
        qpc: goods.qpc,
        qpcStr: goods.qpcStr,
        applyQty: generateMockData.number(10, 100),
        applyQtyStr: `${generateMockData.number(1, 10)}箱`,
        qty: generateMockData.number(5, 50),
        qtyStr: `${generateMockData.number(1, 5)}`,
        note: '',
        total: generateMockData.number(100, 1000),
      }
    }, 5)

    return {
      code: 2000,
      data: details,
      msg: '查询成功',
      total: details.length,
      more: false,
    }
  },

  // 查询商品列表
  '[POST]/vbs-service/rest/vehsale/goods/query': (params: GoodsQueryFilter): BaseResponse_List_VehSaleGoodsDTO_ => {
    // 获取查询参数
    const { page = 0, pageSize = 10, keyword = '' } = params

    // 生成商品数据
    const allGoodsList: VehSaleGoodsDTO[] = generateMockData.array((index: number) =>
      generateMockData.goods(index) as VehSaleGoodsDTO, 30)

    // 根据关键字过滤
    let filteredList: VehSaleGoodsDTO[] = allGoodsList.map((item) => {
      return { ...item, imageDetails: [{
        fileUrl: skuDefaultImage,
        fileType: 'img',
      }, { fileUrl: skuDefaultImage, fileType: 'img' }] }
    })
    if (keyword) {
      filteredList = filteredList.filter(item =>
        item.code?.includes(keyword)
        || item.name?.includes(keyword)
        || item.qpcStr?.includes(keyword),
      )
    }

    // 分页处理
    const startIndex = page * pageSize
    const endIndex = startIndex + pageSize
    const paginatedList = filteredList.slice(startIndex, endIndex)

    return {
      code: 2000,
      data: paginatedList,
      total: filteredList.length,
      more: endIndex < filteredList.length,
      msg: '查询成功',
    }
  },
})
