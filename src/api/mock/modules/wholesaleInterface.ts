import { defineMock } from '@alova/mock'
import type {
  BaseResponse_List_WholeSaleDtlResponseDTO_,
  BaseResponse_List_WholeSaleQueryResponseDTO_,
  Response_WholeSaleResponseDTO_,
  VehSaleGoodsDTO,
  WholeSaleDtlResponseDTO,
  WholeSaleQueryFilter,
  WholeSaleQueryResponseDTO,
  WholeSaleResponseDTO,
} from '../../globals.d'
import { generateMockData } from '../utils'

export default defineMock({
  // 获取单据详情
  '[GET]/vbs-service/rest/wholesale/get': (_params: any): Response_WholeSaleResponseDTO_ => {
    const details: WholeSaleDtlResponseDTO[] = generateMockData.array((index: number) => {
      const goods = generateMockData.goods(index)
      return {
        line: index + 1,
        gdCode: goods.code,
        goods: goods as VehSaleGoodsDTO,
        munit: goods.munit,
        price: goods.price,
        qpc: goods.qpc,
        qpcStr: goods.qpcStr,
        qty: generateMockData.number(5, 50),
        qtyStr: `${generateMockData.number(1, 5)}箱`,
        note: '',
        tax: generateMockData.number(1, 10),
        taxRate: 0.13,
        taxType: 'includePrice',
        total: generateMockData.number(100, 1000),
      }
    }, 5)

    const wholesale: WholeSaleResponseDTO = {
      num: generateMockData.code('WS'),
      ocrDate: generateMockData.date(),

      wrh: {
        gid: generateMockData.id(),
        code: generateMockData.code('WRH'),
        name: generateMockData.name('仓库'),
      },
      stat: 1, // 已提交
      goodsCount: details.length,
      total: details.reduce((sum, item) => sum + (item.total || 0), 0),
      filDate: generateMockData.datetime(),
      filler: generateMockData.name('创建人'),
      lstupdTime: generateMockData.datetime(),
      lastModifyOper: generateMockData.name('修改人'),
      note: '',
      details,
      attachDetails: [],
    }

    return {
      code: 2000,
      data: wholesale,
      msg: '查询成功',
    }
  },

  // 查询单据列表
  '[POST]/vbs-service/rest/wholesale/query': (params: WholeSaleQueryFilter): BaseResponse_List_WholeSaleQueryResponseDTO_ => {
    // 获取查询参数
    const { page = 0, pageSize = 10, keyword = '', beginDate, finishDate, wrhGid } = params

    // 生成50条数据
    const allWholesaleList: WholeSaleQueryResponseDTO[] = generateMockData.array((index: number) => ({
      num: generateMockData.code('WS'),
      stat: index % 3, // 0: 草稿, 1: 已提交, 2: 已审核
      goodsCount: generateMockData.number(1, 10),
      total: generateMockData.number(100, 1000),
      lstupdTime: generateMockData.datetime(-generateMockData.number(0, 30)), // 随机生成0-30天内的时间
      wrh: {
        gid: generateMockData.id(),
        code: generateMockData.code('WRH'),
        name: generateMockData.name('仓库'),
      },
    }), 50)

    // 根据关键字过滤
    let filteredList = allWholesaleList
    if (keyword) {
      filteredList = allWholesaleList.filter(item =>
        item.wrh?.code?.includes(keyword)
        || item.wrh?.name?.includes(keyword),
      )
    }

    // 根据仓库ID过滤
    if (wrhGid) {
      filteredList = filteredList.filter(item => item.wrh?.gid === wrhGid)
    }

    // 根据日期过滤
    if (beginDate) {
      const beginTime = new Date(beginDate).getTime()
      filteredList = filteredList.filter(item =>
        item.lstupdTime && new Date(item.lstupdTime).getTime() >= beginTime,
      )
    }
    if (finishDate) {
      const endTime = new Date(finishDate).getTime()
      filteredList = filteredList.filter(item =>
        item.lstupdTime && new Date(item.lstupdTime).getTime() <= endTime,
      )
    }

    // 分页处理
    const startIndex = page * pageSize
    const endIndex = startIndex + pageSize
    const paginatedList = filteredList.slice(startIndex, endIndex)

    return {
      code: 2000,
      data: paginatedList,
      total: filteredList.length,
      more: endIndex < filteredList.length,
      msg: '查询成功',
    }
  },

  // 获取单据明细详情
  '[POST]/vbs-service/rest/wholesale/get/details': (_params: any): BaseResponse_List_WholeSaleDtlResponseDTO_ => {
    const details: WholeSaleDtlResponseDTO[] = generateMockData.array((index: number) => {
      const goods = generateMockData.goods(index)
      return {
        line: index + 1,
        gdCode: goods.code,
        goods: goods as VehSaleGoodsDTO,
        munit: goods.munit,
        price: goods.price,
        qpc: goods.qpc,
        qpcStr: goods.qpcStr,
        qty: generateMockData.number(5, 50),
        qtyStr: `${generateMockData.number(1, 5)}箱`,
        note: '',
        tax: generateMockData.number(1, 10),
        taxRate: 0.13,
        taxType: 'includePrice',
        total: generateMockData.number(100, 1000),
      }
    }, 5)

    return {
      code: 2000,
      data: details,
      msg: '查询成功',
      total: details.length,
      more: false,
    }
  },
})
