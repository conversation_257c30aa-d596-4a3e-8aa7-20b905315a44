/*
 * @Author: we<PERSON><PERSON>
 * @Date: 2025-04-21 12:35:00
 * @LastEditTime: 2025-05-14 20:09:47
 * @LastEditors: weisheng
 * @Description: 主页相关接口的mock数据
 * @FilePath: /lsym-cx-mini/src/api/mock/modules/mainInterface.ts
 */
import { defineMock } from '@alova/mock'
import type {
  MainResponseDTO,
} from '../../globals.d'
import { generateMockData } from '../utils'

export default defineMock({
  // 获取主页信息 - 改为POST请求
  '[POST]/vbs-service/rest/main/get': (_params: any): any => {
    const mainData: MainResponseDTO = {
      // 车销单据数
      vehSaleCount: generateMockData.number(10, 50),
      // 车销金额
      vehSaleTotal: generateMockData.number(5000, 20000),
      // 车销退金额
      vehSaleBckTotal: generateMockData.number(500, 2000),
      // 买赔单单数（买赔单据数）
      wholeSaleTotal: generateMockData.number(5, 20),
    }

    return {
      code: 2000,
      data: mainData,
      msg: '获取主页信息成功',
    }
  },

  // 获取统计数据
  '[POST]/vbs-service/rest/main/data': (params: any): any => {
    // 获取时间范围参数
    const { beginDate, finishDate } = params

    // 根据时间范围生成不同的数据
    let multiplier = 1

    // 如果是周数据，数值翻5倍
    if (beginDate !== finishDate) {
      const start = new Date(beginDate)
      const end = new Date(finishDate)
      const days = Math.round((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24))

      if (days >= 7 && days < 28) {
        multiplier = 5 // 周数据
      }
      else if (days >= 28) {
        multiplier = 20 // 月数据
      }
    }

    const mainData: MainResponseDTO = {
      // 车销单据数
      vehSaleCount: generateMockData.number(10, 50) * multiplier,
      // 车销金额
      vehSaleTotal: generateMockData.number(5000, 20000) * multiplier,
      // 车销退金额
      vehSaleBckTotal: generateMockData.number(500, 2000) * multiplier,
      // 买赔单单数（买赔单据数）
      wholeSaleTotal: generateMockData.number(5, 20) * multiplier,
    }

    return {
      code: 2000,
      data: mainData,
      msg: '获取统计数据成功',
    }
  },

  // 获取数据模板
  '[POST]/vbs-service/rest/main/template/getdata': (_params: any): any => {
    return {
      code: 2000,
      data: {
        templates: [
          {
            id: generateMockData.id(),
            name: '模板1',
            type: 'type1',
            content: '模板内容1',
          },
          {
            id: generateMockData.id(),
            name: '模板2',
            type: 'type2',
            content: '模板内容2',
          },
        ],
      },
      msg: '获取模板数据成功',
    }
  },
})
