import { defineMock } from '@alova/mock'
import type {
  BaseResponse_List_VehSaleUseSignArvDiffDtlResponseDTO_,
  BaseResponse_List_VehSaleUseSignArvDiffQueryResponseDTO_,
  Response_VehSaleUseSignArvDiffResponseDTO_,
  Response_Void_,
  Response_boolean_,
  Response_string_,
  VehSaleGoodsDTO,
  VehSaleUseSignArvDiffDtlResponseDTO,
  VehSaleUseSignArvDiffQueryFilter,
  VehSaleUseSignArvDiffQueryResponseDTO,
  VehSaleUseSignArvDiffResponseDTO,
} from '../../globals.d'
import { generateMockData } from '../utils'

export default defineMock({
  // 作废单据
  '[POST]/vbs-service/rest/vehsaleusesignarvdiff/abort': (_params: any): Response_Void_ => {
    return {
      code: 2000,
      msg: '作废成功',
    }
  },

  // 审核单据
  '[POST]/vbs-service/rest/vehsaleusesignarvdiff/audit': (_params: any): Response_Void_ => {
    return {
      code: 2000,
      msg: '审核成功',
    }
  },

  // 获取单据详情
  '[GET]/vbs-service/rest/vehsaleusesignarvdiff/get': (_params: any): Response_VehSaleUseSignArvDiffResponseDTO_ => {
    const details: VehSaleUseSignArvDiffDtlResponseDTO[] = generateMockData.array((index: number) => {
      const goods = generateMockData.goods(index)
      return {
        line: index + 1,
        gdCode: goods.code,
        goods: goods as VehSaleGoodsDTO,
        munit: goods.munit,
        price: goods.price,
        qpc: goods.qpc,
        qpcStr: goods.qpcStr,
        signQty: generateMockData.number(10, 100),
        signQtyStr: `${generateMockData.number(1, 10)}箱`,
        arvQty: generateMockData.number(5, 50),
        arvQtyStr: `${generateMockData.number(1, 5)}箱`,
        diffQty: generateMockData.number(1, 10),
        diffQtyStr: `${generateMockData.number(1, 2)}箱`,
        note: '',
        tax: generateMockData.number(1, 10),
        taxRate: 0.13,
        taxType: 'includePrice',
        total: generateMockData.number(100, 1000),
      }
    }, 5)

    const arvDiff: VehSaleUseSignArvDiffResponseDTO = {
      num: generateMockData.code('VSAD'),
      ocrDate: generateMockData.date(),
      vehSaleEmp: {
        gid: generateMockData.id(),
        code: generateMockData.code('EMP'),
        name: generateMockData.name('业务员'),
      },
      vehSaleEmpName: generateMockData.name('业务员'),
      wrh: {
        gid: generateMockData.id(),
        code: generateMockData.code('WRH'),
        name: generateMockData.name('仓库'),
      },

      carNumber: '粤A12345',
      stat: 1, // 已提交
      goodsCount: details.length,
      total: details.reduce((sum, item) => sum + (item.total || 0), 0),
      filDate: generateMockData.datetime(),
      filler: generateMockData.name('创建人'),
      lstupdTime: generateMockData.datetime(),
      lastModifyOper: generateMockData.name('修改人'),
      note: '',
      details,
    }

    return {
      code: 2000,
      data: arvDiff,
      msg: '查询成功',
    }
  },

  // 查询单据列表
  '[POST]/vbs-service/rest/vehsaleusesignarvdiff/query': (params: VehSaleUseSignArvDiffQueryFilter): BaseResponse_List_VehSaleUseSignArvDiffQueryResponseDTO_ => {
    // 获取查询参数
    const { page = 0, pageSize = 10, keyword = '', stat } = params

    // 生成50条数据
    const allArvDiffList: VehSaleUseSignArvDiffQueryResponseDTO[] = generateMockData.array((index: number) => ({
      num: generateMockData.code('VSAD'),
      stat: index % 2 === 0 ? 100 : 110, // 100: 待处理, 110: 已完成
      goodsCount: generateMockData.number(1, 10),
      total: generateMockData.number(100, 1000),
      lstupdTime: generateMockData.datetime(-generateMockData.number(0, 30)),
      vehSaleEmp: {
        gid: generateMockData.id(),
        code: generateMockData.code('EMP'),
        name: generateMockData.name('业务员'),
      },
      vehSaleEmpName: generateMockData.name('业务员'),
      vehSaleUseSignArvNum: generateMockData.code('ARIV'),
    }), 50)

    // 根据关键字过滤
    let filteredList = allArvDiffList
    if (keyword) {
      filteredList = filteredList.filter(item =>
        item.num?.includes(keyword)
        || item.vehSaleEmpName?.includes(keyword)
        || item.vehSaleUseSignArvNum?.includes(keyword),
      )
    }

    // 状态筛选
    if (stat !== undefined) {
      filteredList = filteredList.filter(item => item.stat === stat)
    }

    // 分页处理
    const startIndex = page * pageSize
    const endIndex = startIndex + pageSize
    const paginatedList = filteredList.slice(startIndex, endIndex)

    return {
      code: 2000,
      data: paginatedList,
      total: filteredList.length,
      more: endIndex < filteredList.length,
      msg: '查询成功',
    }
  },

  // 提交单据
  '[POST]/vbs-service/rest/vehsaleusesignarvdiff/submit': (_params: any): Response_string_ => {
    return {
      code: 2000,
      data: generateMockData.code('VSAD'),
      msg: '提交成功',
    }
  },

  // 是否有单据
  '[POST]/vbs-service/rest/vehsaleusesignarvdiff/bill/has': (_params: any): Response_boolean_ => {
    return {
      code: 2000,
      data: true,
      msg: '查询成功',
    }
  },

  // 获取单据明细详情
  '[POST]/vbs-service/rest/vehsaleusesignarvdiff/get/details': (_params: any): BaseResponse_List_VehSaleUseSignArvDiffDtlResponseDTO_ => {
    const details: VehSaleUseSignArvDiffDtlResponseDTO[] = generateMockData.array((index: number) => {
      const goods = generateMockData.goods(index)
      return {
        line: index + 1,
        gdCode: goods.code,
        goods: goods as VehSaleGoodsDTO,
        munit: goods.munit,
        price: goods.price,
        qpc: goods.qpc,
        qpcStr: goods.qpcStr,
        signQty: generateMockData.number(10, 100),
        signQtyStr: `${generateMockData.number(1, 10)}箱`,
        arvQty: generateMockData.number(5, 50),
        arvQtyStr: `${generateMockData.number(1, 5)}箱`,
        diffQty: generateMockData.number(1, 10),
        diffQtyStr: `${generateMockData.number(1, 2)}箱`,
        note: '',
        tax: generateMockData.number(1, 10),
        taxRate: 0.13,
        taxType: 'includePrice',
        total: generateMockData.number(100, 1000),
      }
    }, 5)

    return {
      code: 2000,
      data: details,
      msg: '查询成功',
      total: details.length,
      more: false,
    }
  },
})
