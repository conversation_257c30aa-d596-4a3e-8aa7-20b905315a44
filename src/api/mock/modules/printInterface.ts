/*
 * @Author: weish<PERSON>
 * @Date: 2025-04-21 12:30:00
 * @LastEditTime: 2025-06-16 11:39:40
 * @LastEditors: weisheng
 * @Description: 打印相关接口的mock数据
 * @FilePath: /lsym-cx-mini/src/api/mock/modules/printInterface.ts
 */
import { defineMock } from '@alova/mock'
import type {
  PrintRequestDTO,
  PrintTemplateResponseDTO,
  Response_Array_byte_,
  Response_List_PrintTemplateResponseDTO_,
  Response_string_,
} from '../../globals.d'
import { generateMockData } from '../utils'

// 生成随机商品数据
function generateRandomProducts(count: number) {
  const products = []
  let total = 0
  for (let i = 1; i <= count; i++) {
    const price = Math.floor(Math.random() * 1000) + 1
    const quantity = Math.floor(Math.random() * 5) + 1
    const subtotal = price * quantity
    total += subtotal
    products.push({
      name: `商品${i}`,
      price,
      quantity,
      subtotal,
    })
  }
  return { products, total }
}

export default defineMock({
  // 获取打印文件
  '[POST]/vbs-service/rest/print/get/printfile': (_params: PrintRequestDTO): Response_Array_byte_ => {
    return {
      code: 2000,
      data: 'data:application/pdf;base64,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',
      msg: '获取打印文件成功',
    }
  },

  // 获取打印文件地址
  '[POST]/vbs-service/rest/print/get/printurl': (_params: PrintRequestDTO): Response_string_ => {
    return {
      code: 2000,
      data: 'https://example.com/print/files/sample.pdf',
      msg: '获取打印文件地址成功',
    }
  },

  // 获取打印模板
  '[GET]/vbs-service/rest/print/template/gets': (_params: any): Response_List_PrintTemplateResponseDTO_ => {
    const templates: PrintTemplateResponseDTO[] = generateMockData.array((index: number) => ({
      fileName: `template_${index + 1}.jasper`,
      moduleId: _params.moduleId || 'vehsale',
      templateName: `模板${index + 1}`,
      pagePercent: '100%',
      printMode: index % 2 === 0 ? 'singlePrint' : 'mergPrint',
      uploadTime: generateMockData.date(-index * 10),
    }), 5)

    return {
      code: 2000,
      data: templates,
      msg: '获取打印模板成功',
    }
  },

  // 打印接口
  '[POST]/vbs-service/rest/print/get/printstr': (_params: PrintRequestDTO): Response_string_ => {
    // const { products, total } = generateRandomProducts(50)
    // const now = new Date()
    const printText = `欢迎光临zs门店zs01分店!\r\n-----------车销-------------\r\n  货  号  单  价  数  量  金  额\r\n--------------------------------\r\n合计                           件\r\n应付                           元\r\n优惠                           元\r\n积分                            \r\n总积分                          \r\n会员                          \r\n--------------------------------\r\nss01修改一下很长的名          元\r\nss03                          元\r\nss06                          元\r\n找零                          元\r\n交易时间:                       \r\n送货电话:                       \r\n谢谢惠顾!\r\n`.trim()

    return {
      code: 2000,
      data: printText,
      msg: '打印成功',
    }
  },
})
