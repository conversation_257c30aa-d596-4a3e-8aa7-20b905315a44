/*
 * @Author: weish<PERSON>
 * @Date: 2025-04-18 15:30:00
 * @LastEditTime: 2025-04-18 13:51:39
 * @LastEditors: weisheng
 * @Description: 自定义 alova hooks，集成中间件
 * @FilePath: /lsym-cx-mini/src/api/hooks/useAlovaRequest.ts
 */
import type { Method } from 'alova'
import { useRequest } from 'alova/client'
import {
  createDelayLoadingMiddleware,
} from '../core/middleware'

// 创建中间件实例
const delayLoadingMiddleware = createDelayLoadingMiddleware(300)

// 不再需要自定义中间件上下文类型，因为我们使用了 alova 官方的中间件方式

/**
 * 增强版 useRequest hook，集成中间件
 * @param method Alova 方法或返回 Alova 方法的函数
 * @param options 请求配置选项
 * @returns 增强的 useRequest 结果
 */
export function useEnhancedRequest(
  method: Method | (() => Method),
  options?: Record<string, any>,
) {
  // 合并选项，添加中间件
  const enhancedOptions = {
    ...options,
    // 使用 alova 官方的中间件方式
    // 使用延迟加载中间件
    middleware: delayLoadingMiddleware,
    // 使用 alova 内置的重试功能
    retryCount: 2,
    retryDelay: 1000,
    // 只重试网络错误和超时错误
    retryCondition: (error: any) => error.name === 'NetworkError' || error.name === 'TimeoutError',
  }

  // 使用原始的 useRequest hook，并传入增强的选项
  return useRequest(method, enhancedOptions)
}

/**
 * 增强版搜索 hook，集成防抖中间件
 * @param searchFn 基于搜索查询返回方法的函数
 * @param options 请求选项
 * @returns 带有防抖搜索功能的增强版 useRequest 结果
 */
export function useEnhancedSearch(
  searchFn: (query: string) => Method,
  options?: Record<string, any> & { debounceTime?: number },
) {
  // 提取防抖时间
  const { debounceTime = 500, ...requestOptions } = options || {}

  // 创建搜索状态
  let currentQuery = ''

  // 使用原始的 useRequest hook，并添加防抖和延迟加载
  const result = useRequest(
    // 返回当前查询对应的方法
    () => searchFn(currentQuery),
    {
      ...requestOptions,
      immediate: false, // 初始不发送请求
      // 使用 alova 内置的防抖功能
      debounce: debounceTime,
      // 使用延迟加载中间件
      middleware: delayLoadingMiddleware,
    },
  )

  // 创建搜索函数
  const search = (query: string) => {
    // 更新当前查询
    currentQuery = query
    // 发送请求
    return result.send()
  }

  // 返回原始结果并增加搜索函数
  return {
    ...result,
    search,
  }
}

export default {
  useEnhancedRequest,
  useEnhancedSearch,
}
