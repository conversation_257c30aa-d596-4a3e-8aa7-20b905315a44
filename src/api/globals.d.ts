/* tslint:disable */
/* eslint-disable */
/**
 * 车销服务服务API文档 - version 1.0
 *
 *
 *
 * OpenAPI version: 3.0.0
 *
 *
 * NOTE: This file is auto generated by the alova's vscode plugin.
 *
 * https://alova.js.org/devtools/vscode
 *
 * **Do not edit the file manually.**
 */
import type { Alova, AlovaMethodCreateConfig, AlovaGenerics, Method } from 'alova';
import type { $$userConfigMap, alovaInstance } from '.';
import type apiDefinitions from './apiDefinitions';

type CollapsedAlova = typeof alovaInstance;
type UserMethodConfigMap = typeof $$userConfigMap;

type Alova2MethodConfig<Responded> =
  CollapsedAlova extends Alova<
    AlovaGenerics<
      any,
      any,
      infer RequestConfig,
      infer Response,
      infer ResponseHeader,
      infer L1Cache,
      infer L2Cache,
      infer SE
    >
  >
    ? Omit<
        AlovaMethodCreateConfig<
          AlovaGenerics<Responded, any, RequestConfig, Response, ResponseHeader, L1Cache, L2Cache, SE>,
          any,
          Responded
        >,
        'params'
      >
    : never;

// Extract the return type of transform function that define in $$userConfigMap, if it not exists, use the default type.
type ExtractUserDefinedTransformed<
  DefinitionKey extends keyof typeof apiDefinitions,
  Default
> = DefinitionKey extends keyof UserMethodConfigMap
  ? UserMethodConfigMap[DefinitionKey]['transform'] extends (...args: any[]) => any
    ? Awaited<ReturnType<UserMethodConfigMap[DefinitionKey]['transform']>>
    : Default
  : Default;
type Alova2Method<
  Responded,
  DefinitionKey extends keyof typeof apiDefinitions,
  CurrentConfig extends Alova2MethodConfig<any>
> =
  CollapsedAlova extends Alova<
    AlovaGenerics<
      any,
      any,
      infer RequestConfig,
      infer Response,
      infer ResponseHeader,
      infer L1Cache,
      infer L2Cache,
      infer SE
    >
  >
    ? Method<
        AlovaGenerics<
          CurrentConfig extends undefined
            ? ExtractUserDefinedTransformed<DefinitionKey, Responded>
            : CurrentConfig['transform'] extends (...args: any[]) => any
              ? Awaited<ReturnType<CurrentConfig['transform']>>
              : ExtractUserDefinedTransformed<DefinitionKey, Responded>,
          any,
          RequestConfig,
          Response,
          ResponseHeader,
          L1Cache,
          L2Cache,
          SE
        >
      >
    : never;

export type Response_Map_string_object_ = {
  /**
   * 响应码。2000:成功，其他(5000+):业务异常
   * [required]
   */
  code: number;
  /**
   * 返回数据
   */
  data?: object;
  /**
   * 提示信息，一般在异常时返回
   */
  msg?: string;
};
export type MainResponseDTO = {
  /**
   * 车销退金额
   */
  vehSaleBckTotal?: number;
  /**
   * 车销单据数
   */
  vehSaleCount?: number;
  /**
   * 车销金额
   */
  vehSaleTotal?: number;
  /**
   * 批发单数（买赔单据数）
   */
  wholeSaleTotal?: number;
};
export type Response_MainResponseDTO_ = {
  /**
   * 响应码。2000:成功，其他(5000+):业务异常
   * [required]
   */
  code: number;
  /**
   * MainResponseDTO
   * ---
   * 我的响应信息
   */
  data?: MainResponseDTO;
  /**
   * 提示信息，一般在异常时返回
   */
  msg?: string;
};
export type MainQueryFilter = {
  /**
   * 开始日期
   */
  beginDate?: string;
  /**
   * 结束日期
   */
  finishDate?: string;
  /**
   * 车销业务员
   */
  vehSaleEmpGid?: number;
};
export type CategoryDTO = {
  /**
   * 代码
   * [required]
   */
  code: string;
  /**
   * 名称
   * [required]
   */
  name: string;
};
export type Response_List_CategoryDTO_ = {
  /**
   * 响应码。2000:成功，其他(5000+):业务异常
   * [required]
   */
  code: number;
  /**
   * 返回数据
   */
  data?: CategoryDTO[];
  /**
   * 提示信息，一般在异常时返回
   */
  msg?: string;
};
export type CategoryQueryFilter = {
  /**
   * 关键字
   */
  keyword?: string;
  /**
   * 业务员Gid
   */
  vehSaleEmpGid?: number;
  /**
   * 物流中心
   */
  wmsGid?: number;
};
export type SortLineResponseDTO = {
  /**
   * 线路分类体系代码
   */
  archCode?: string;
  /**
   * 线路分类体系名称
   */
  archName?: string;
  /**
   * 线路分类体系Uuid
   */
  archUuid?: string;
  code?: string;
  name?: string;
  uuid?: string;
};
export type Response_List_SortLineResponseDTO_ = {
  /**
   * 响应码。2000:成功，其他(5000+):业务异常
   * [required]
   */
  code: number;
  /**
   * 返回数据
   */
  data?: SortLineResponseDTO[];
  /**
   * 提示信息，一般在异常时返回
   */
  msg?: string;
};
export type BaseExcludedPageQueryFilter = {
  /**
   * 关键字
   */
  keyword?: string;
  /**
   * 业务员Gid
   */
  vehSaleEmpGid?: number;
  /**
   * 物流中心
   */
  wmsGid?: number;
};
export type StoreDTO = {
  /**
   * 地址
   */
  address?: string;
  /**
   * 代码
   */
  code?: string;
  /**
   * 联系人
   */
  contactor?: string;
  /**
   * 实体GID
   * [required]
   */
  gid: number;
  /**
   * 上次车销单时间
   */
  lastVehSaleTime?: string;
  /**
   * 上次车销单金额
   */
  lastVehSaleTotal?: number;
  /**
   * 纬度
   */
  latitude?: string;
  /**
   * 经度
   */
  longitude?: string;
  /**
   * 名称
   */
  name?: string;
  /**
   * 手机号
   */
  phone?: string;
  /**
   * 线路
   */
  sortLines?: SortLineResponseDTO[];
};
export type Response_StoreDTO_ = {
  /**
   * 响应码。2000:成功，其他(5000+):业务异常
   * [required]
   */
  code: number;
  /**
   * StoreDTO
   * ---
   * 门店
   */
  data?: StoreDTO;
  /**
   * 提示信息，一般在异常时返回
   */
  msg?: string;
};
export type BaseResponse_List_StoreDTO_ = {
  /**
   * 响应码。2000:接口调用成功，其他(5000+):正常业务异常
   */
  code?: number;
  /**
   * 响应实体
   */
  data?: StoreDTO[];
  /**
   * 输入字段提示。一般为表单提交时，后端对表单进行整体校验，返回给前端相应字段的校验结果。
   */
  fields?: Record<string, string>;
  /**
   * 是否还有更多记录，一般只在列表查询时返回。
   */
  more?: boolean;
  /**
   * 提示信息，一般在异常时返回
   */
  msg?: string;
  /**
   * 请求响应的记录数，一般只在列表查询时返回。
   */
  total?: number;
};
export type QuerySort = {
  /**
   * 排序方式，默认倒叙
   */
  asc?: boolean;
  /**
   * 排序字段
   * [required]
   */
  field: string;
};
export type StoreQueryFilter = {
  /**
   * 要求返回的部份信息
   */
  fetchParts?: string[];
  /**
   * 关键字
   */
  keyword?: string;
  /**
   * 页码，从0开始，默认为0
   * [required]
   */
  page: number;
  /**
   * 每页条数，默认为50
   * [required]
   */
  pageSize: number;
  /**
   * 线路UUID
   */
  sortLineUuid?: string;
  /**
   * 排序条件
   */
  sorts?: QuerySort[];
  /**
   * 门店Gid
   */
  storeGid?: number;
  /**
   * 业务员Gid
   */
  vehSaleEmpGid?: number;
  /**
   * 物流中心
   */
  wmsGid?: number;
};
export type GCN = {
  /**
   * 代码
   */
  code?: string;
  /**
   * 实体GID
   * [required]
   */
  gid: number;
  /**
   * 名称
   */
  name?: string;
};
export type Response_List_GCN_ = {
  /**
   * 响应码。2000:成功，其他(5000+):业务异常
   * [required]
   */
  code: number;
  /**
   * 返回数据
   */
  data?: GCN[];
  /**
   * 提示信息，一般在异常时返回
   */
  msg?: string;
};
export type WarehouseQueryFilter = {
  /**
   * 关键字
   */
  keyword?: string;
  /**
   * 业务员Gid
   */
  vehSaleEmpGid?: number;
  /**
   * 物流中心
   */
  wmsGid?: number;
};
export type PrintResponseDTO = {
  /**
   * 打印文件流
   * [required]
   */
  printFileBytes: string;
  /**
   * 打印文件访问路径
   * [required]
   */
  printFileUrl: string;
};
export type Response_PrintResponseDTO_ = {
  /**
   * 响应码。2000:成功，其他(5000+):业务异常
   * [required]
   */
  code: number;
  /**
   * PrintResponseDTO
   * ---
   */
  data?: PrintResponseDTO;
  /**
   * 提示信息，一般在异常时返回
   */
  msg?: string;
};
export type PrintRequestDTO = {
  /**
   * 单据类型
   */
  cls?: string;
  /**
   * 打印模板文件名
   * [required]
   */
  fileName: string;
  /**
   * 模块ID
   * [required]
   */
  moduleId: string;
  /**
   * 单号
   */
  num?: string;
  /**
   * 车销业务员Gid
   * [required]
   */
  vehSaleEmpGid: number;
};
export type Response_string_ = {
  /**
   * 响应码。2000:成功，其他(5000+):业务异常
   * [required]
   */
  code: number;
  /**
   * 返回数据
   */
  data?: string;
  /**
   * 提示信息，一般在异常时返回
   */
  msg?: string;
};
export type PrintTemplateResponseDTO = {
  /**
   * 文件名
   */
  fileName?: string;
  /**
   * 模块ID
   */
  moduleId?: string;
  /**
   * 页面缩放比例 ：默认为整宽不变形
   */
  pagePercent?: string;
  /**
   * 打印模式, 整单打印-singlePrint（def），合并打印-mergPrint
   */
  printMode?: string;
  /**
   * 模板名
   */
  templateName?: string;
  /**
   * 上传日期
   */
  uploadTime?: string;
};
export type Response_List_PrintTemplateResponseDTO_ = {
  /**
   * 响应码。2000:成功，其他(5000+):业务异常
   * [required]
   */
  code: number;
  /**
   * 返回数据
   */
  data?: PrintTemplateResponseDTO[];
  /**
   * 提示信息，一般在异常时返回
   */
  msg?: string;
};
export type Response_bigdecimal_ = {
  /**
   * 响应码。2000:成功，其他(5000+):业务异常
   * [required]
   */
  code: number;
  /**
   * 返回数据
   */
  data?: number;
  /**
   * 提示信息，一般在异常时返回
   */
  msg?: string;
};
export type Response_int_ = {
  /**
   * 响应码。2000:成功，其他(5000+):业务异常
   * [required]
   */
  code: number;
  /**
   * 返回数据
   */
  data?: number;
  /**
   * 提示信息，一般在异常时返回
   */
  msg?: string;
};
export type AttachmentDTO = {
  /**
   * 文件Id
   */
  fileId?: string;
  /**
   * 文件名称
   */
  fileName?: string;
  /**
   * 文件地址
   */
  fileUrl?: string;
  /**
   * 行号
   */
  line?: number;
  /**
   * 签到类型
   */
  signType?: number;
};
export type StoreSignDTO = {
  /**
   * 附件
   */
  attachDetails?: AttachmentDTO[];
  /**
   * 是否异常
   */
  isException?: boolean;
  /**
   * 纬度
   */
  latitude?: string;
  /**
   * 经度
   */
  longitude?: string;
  /**
   * 手机号
   */
  phone?: string;
  /**
   * 签到时间
   */
  signInTime?: string;
  /**
   * 签到时长
   */
  signMinute?: number;
  /**
   * 签退时间
   */
  signOutTime?: string;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  store?: GCN;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  vehSaleEmp?: GCN;
};
export type Response_List_StoreSignDTO_ = {
  /**
   * 响应码。2000:成功，其他(5000+):业务异常
   * [required]
   */
  code: number;
  /**
   * 返回数据
   */
  data?: StoreSignDTO[];
  /**
   * 提示信息，一般在异常时返回
   */
  msg?: string;
};
export type Response_Void_ = {
  /**
   * 响应码。2000:成功，其他(5000+):业务异常
   * [required]
   */
  code: number;
  /**
   * 提示信息，一般在异常时返回
   */
  msg?: string;
};
export type SignOutVerifyDTO = {
  /**
   * 消息
   */
  message?: string;
  /**
   * 模块Id
   */
  moduleId?: string;
  /**
   * 是否校验通过
   */
  valid?: boolean;
};
export type Response_SignOutVerifyDTO_ = {
  /**
   * 响应码。2000:成功，其他(5000+):业务异常
   * [required]
   */
  code: number;
  /**
   * SignOutVerifyDTO
   * ---
   * 签退校验
   */
  data?: SignOutVerifyDTO;
  /**
   * 提示信息，一般在异常时返回
   */
  msg?: string;
};
export type BaseResponse_List_StoreSignDTO_ = {
  /**
   * 响应码。2000:接口调用成功，其他(5000+):正常业务异常
   */
  code?: number;
  /**
   * 响应实体
   */
  data?: StoreSignDTO[];
  /**
   * 输入字段提示。一般为表单提交时，后端对表单进行整体校验，返回给前端相应字段的校验结果。
   */
  fields?: Record<string, string>;
  /**
   * 是否还有更多记录，一般只在列表查询时返回。
   */
  more?: boolean;
  /**
   * 提示信息，一般在异常时返回
   */
  msg?: string;
  /**
   * 请求响应的记录数，一般只在列表查询时返回。
   */
  total?: number;
};
export type StoreReportRequestDTO = {
  /**
   * 纬度
   */
  latitude?: number;
  /**
   * 经度
   */
  longitude?: number;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  store?: GCN;
};
export type FaEmp = {
  /**
   * 代码
   */
  code?: string;
  /**
   * 实体GID
   * [required]
   */
  gid: number;
  /**
   * 是否启用
   */
  isEnable?: number;
  /**
   * 名称
   */
  name?: string;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  org?: GCN;
  /**
   * 手机号
   */
  phone?: string;
};
export type Option = {
  /**
   * 模块号
   */
  moduleNo?: number;
  /**
   * 选项名
   */
  optionCaption?: string;
  /**
   * 选项值
   */
  optionValue?: string;
  /**
   * 组织
   */
  orgGid?: number;
};
export type Permission = {
  /**
   * 模块名
   */
  module?: string;
  /**
   * 模块描述
   */
  moduleName?: string;
  /**
   * 角色ID
   */
  roleId?: string;
  /**
   * 权限状态
   */
  state?: number;
};
export type SortLine = {
  /**
   * 线路分类体系代码
   */
  archCode?: string;
  /**
   * 线路分类体系名称
   */
  archName?: string;
  /**
   * 线路分类体系Uuid
   */
  archUuid?: string;
  code?: string;
  name?: string;
  uuid?: string;
};
export type VehSaleEmp = {
  /**
   * 车牌号
   */
  carNumber?: string;
  /**
   * 代码
   */
  code?: string;
  /**
   * FaEmp
   * ---
   * 员工
   */
  faEmp?: FaEmp;
  /**
   * 实体GID
   * [required]
   */
  gid: number;
  /**
   * 是否启用
   */
  isEnable?: number;
  /**
   * 名称
   */
  name?: string;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  org?: GCN;
  /**
   * 手机号
   */
  phone?: string;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  vehSaleWrh?: GCN;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  wms?: GCN;
};
export type User = {
  /**
   * FaEmp
   * ---
   * 员工
   */
  faEmp?: FaEmp;
  /**
   * 选项列表
   */
  options?: Option[];
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  org?: GCN;
  /**
   * 权限列表
   */
  permissions?: Permission[];
  /**
   * 角色代码.  01-车销业务员，02-仓管
   */
  roleCode?: string;
  /**
   * 线路
   */
  sortLines?: SortLine[];
  /**
   * 秘钥
   */
  token?: string;
  /**
   * VehSaleEmp
   * ---
   * 车销业务员
   */
  vehSaleEmp?: VehSaleEmp;
};
export type Response_User_ = {
  /**
   * 响应码。2000:成功，其他(5000+):业务异常
   * [required]
   */
  code: number;
  /**
   * User
   * ---
   * 车销业务员
   */
  data?: User;
  /**
   * 提示信息，一般在异常时返回
   */
  msg?: string;
};
export type LoginCredentials = {
  /**
   * 登录账号
   * [required]
   */
  loginCode: string;
  /**
   * 登录密码
   * [required]
   */
  password: string;
};
export type BillBaseRequestDTO = {
  /**
   * 单号
   * [required]
   */
  num: string;
  /**
   * 版本号
   * [required]
   */
  version: number;
};
export type BillAttachDtlResponseDTO = {
  /**
   * 附件标识
   */
  fileId?: string;
  /**
   * 附件名称
   */
  fileName?: string;
  /**
   * 附件路径
   */
  fileUrl?: string;
  /**
   * 行号
   */
  line?: number;
  /**
   * 备注
   */
  note?: string;
};
export type CodeName = {
  /**
   * 代码
   */
  code?: string;
  /**
   * 名称
   */
  name?: string;
};
export type _ = {
  /**
   * 文件ID
   */
  fileId?: string;
  /**
   * 文件名
   */
  fileName?: string;
  /**
   * 文件类型
   */
  fileType?: 'img' | 'video';
  /**
   * 外部URL
   */
  fileUrl?: string;
  /**
   * 行号
   */
  line?: number;
};
export type VehSaleDtlQpcResponseDTO = {
  /**
   * 申请数量
   */
  applyQty?: number;
  /**
   * 申请包装数量
   */
  applyQtyStr?: string;
  /**
   * 单位
   */
  munit?: string;
  /**
   * 规格价
   */
  price?: number;
  /**
   * 规格
   */
  qpc?: number;
  /**
   * 规格描述
   */
  qpcStr?: string;
  /**
   * 确认数量
   */
  qty?: number;
  /**
   * 确认包装数量
   */
  qtyStr?: string;
  /**
   * 含税金额
   */
  total?: number;
};
export type VehSaleDtlResponseDTO = {
  /**
   * 申请数量
   */
  applyQty?: number;
  /**
   * CodeName
   * ---
   * 代码名称对象
   */
  category?: CodeName;
  /**
   * 商品条码
   */
  gdCode?: string;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  goods?: GCN;
  /**
   * 图片
   */
  imageDetails?: _[];
  /**
   * 备注
   */
  note?: string;
  /**
   * 规格信息
   */
  qpcDetails?: VehSaleDtlQpcResponseDTO[];
  /**
   * 确认数量
   */
  qty?: number;
};
export type VehSaleResponseDTO = {
  /**
   * 附件明细
   */
  attachDetails?: BillAttachDtlResponseDTO[];
  /**
   * 车牌号
   */
  carNumber?: string;
  /**
   * 商品明细
   */
  details?: VehSaleDtlResponseDTO[];
  /**
   * 创建时间
   */
  filDate?: string;
  /**
   * 创建人
   */
  filler?: string;
  /**
   * 商品种类
   */
  goodsCount?: number;
  /**
   * 最后修改人
   */
  lastModifyOper?: string;
  /**
   * 最后修改时间
   */
  lstupdTime?: string;
  /**
   * 备注
   */
  note?: string;
  /**
   * 单号
   */
  num?: string;
  /**
   * 发生日期
   */
  ocrDate?: string;
  /**
   * 总数量
   */
  qty?: number;
  /**
   * 线路
   */
  sortLines?: SortLineResponseDTO[];
  /**
   * 单据状态
   */
  stat?: number;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  store?: GCN;
  /**
   * 收货门店地址
   */
  storeAddress?: string;
  /**
   * 总金额
   */
  total?: number;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  vehSaleEmp?: GCN;
  /**
   * 车销业务员姓名
   */
  vehSaleEmpName?: string;
  /**
   * 版本号
   */
  version?: number;
};
export type Response_VehSaleResponseDTO_ = {
  /**
   * 响应码。2000:成功，其他(5000+):业务异常
   * [required]
   */
  code: number;
  /**
   * VehSaleResponseDTO
   * ---
   * 获取车销单
   */
  data?: VehSaleResponseDTO;
  /**
   * 提示信息，一般在异常时返回
   */
  msg?: string;
};
export type VehSaleQueryResponseDTO = {
  /**
   * 商品种类
   */
  goodsCount?: number;
  /**
   * 最后修改时间
   */
  lstupdTime?: string;
  /**
   * 单号
   */
  num?: string;
  /**
   * 总数量
   */
  qty?: number;
  /**
   * 单据状态
   */
  stat?: number;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  store?: GCN;
  /**
   * 收货门店地址
   */
  storeAddress?: string;
  /**
   * 总金额
   */
  total?: number;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  vehSaleEmp?: GCN;
  /**
   * 业务员姓名
   */
  vehSaleEmpName?: string;
  /**
   * 版本号
   */
  version?: number;
};
export type BaseResponse_List_VehSaleQueryResponseDTO_ = {
  /**
   * 响应码。2000:接口调用成功，其他(5000+):正常业务异常
   */
  code?: number;
  /**
   * 响应实体
   */
  data?: VehSaleQueryResponseDTO[];
  /**
   * 输入字段提示。一般为表单提交时，后端对表单进行整体校验，返回给前端相应字段的校验结果。
   */
  fields?: Record<string, string>;
  /**
   * 是否还有更多记录，一般只在列表查询时返回。
   */
  more?: boolean;
  /**
   * 提示信息，一般在异常时返回
   */
  msg?: string;
  /**
   * 请求响应的记录数，一般只在列表查询时返回。
   */
  total?: number;
};
export type VehSaleQueryFilter = {
  /**
   * 开始日期
   */
  beginDate?: string;
  /**
   * 要求返回的部份信息
   */
  fetchParts?: string[];
  /**
   * 结束日期
   */
  finishDate?: string;
  /**
   * 关键字
   */
  keyword?: string;
  /**
   * 单号
   */
  num?: string;
  /**
   * 页码，从0开始，默认为0
   * [required]
   */
  page: number;
  /**
   * 每页条数，默认为50
   * [required]
   */
  pageSize: number;
  /**
   * 排序条件
   */
  sorts?: QuerySort[];
  /**
   * 状态
   */
  stat?: 100 | 1000 | 1010 | 110 | 300 | 310;
  /**
   * 状态列表
   */
  stats?: number[];
  /**
   * 门店Gid
   */
  storeGid?: number;
  /**
   * 业务员Gid
   */
  vehSaleEmpGid?: number;
  /**
   * 物流中心
   */
  wmsGid?: number;
};
export type BaseResponse_List_VehSaleDtlResponseDTO_ = {
  /**
   * 响应码。2000:接口调用成功，其他(5000+):正常业务异常
   */
  code?: number;
  /**
   * 响应实体
   */
  data?: VehSaleDtlResponseDTO[];
  /**
   * 输入字段提示。一般为表单提交时，后端对表单进行整体校验，返回给前端相应字段的校验结果。
   */
  fields?: Record<string, string>;
  /**
   * 是否还有更多记录，一般只在列表查询时返回。
   */
  more?: boolean;
  /**
   * 提示信息，一般在异常时返回
   */
  msg?: string;
  /**
   * 请求响应的记录数，一般只在列表查询时返回。
   */
  total?: number;
};
export type Category = {
  /**
   * 代码
   * [required]
   */
  code: string;
  /**
   * 名称
   */
  name?: string;
};
export type BillDtlQueryFilter = {
  /**
   * Category
   * ---
   * 分类
   */
  category?: Category;
  /**
   * 要求返回的部份信息
   */
  fetchParts?: string[];
  /**
   * 单号
   * [required]
   */
  num: string;
  /**
   * 页码，从0开始，默认为0
   * [required]
   */
  page: number;
  /**
   * 每页条数，默认为50
   * [required]
   */
  pageSize: number;
  /**
   * 排序条件
   */
  sorts?: QuerySort[];
};
export type BillSubmitAttachDtlRequestDTO = {
  /**
   * 附件标识
   */
  fileId?: string;
  /**
   * 附件名称
   */
  fileName?: string;
  /**
   * 附件路径
   */
  fileUrl?: string;
};
export type VehSaleSubmitRequestDTO = {
  /**
   * 单据附件提交明细
   */
  attachDetails?: BillSubmitAttachDtlRequestDTO[];
  /**
   * 备注
   */
  note?: string;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  store?: GCN;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  vehSaleEmp?: GCN;
};
export type Response_boolean_ = {
  /**
   * 响应码。2000:成功，其他(5000+):业务异常
   * [required]
   */
  code: number;
  /**
   * 返回数据
   */
  data?: boolean;
  /**
   * 提示信息，一般在异常时返回
   */
  msg?: string;
};
export type VehSaleBckDtlQpcResponseDTO = {
  /**
   * 申请数量
   */
  applyQty?: number;
  /**
   * 申请包装数量
   */
  applyQtyStr?: string;
  /**
   * 单位
   */
  munit?: string;
  /**
   * 规格价
   */
  price?: number;
  /**
   * 规格
   */
  qpc?: number;
  /**
   * 规格描述
   */
  qpcStr?: string;
  /**
   * 确认数量
   */
  qty?: number;
  /**
   * 确认包装数量
   */
  qtyStr?: string;
  /**
   * 含税金额
   */
  total?: number;
};
export type VehSaleBckDtlResponseDTO = {
  /**
   * 申请数量
   */
  applyQty?: number;
  /**
   * CodeName
   * ---
   * 代码名称对象
   */
  category?: CodeName;
  /**
   * 商品条码
   */
  gdCode?: string;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  goods?: GCN;
  /**
   * 图片
   */
  imageDetails?: _[];
  /**
   * 备注
   */
  note?: string;
  /**
   * 规格信息
   */
  qpcDetails?: VehSaleBckDtlQpcResponseDTO[];
  /**
   * 确认数量
   */
  qty?: number;
};
export type VehSaleBckResponseDTO = {
  /**
   * 附件明细
   */
  attachDetails?: BillAttachDtlResponseDTO[];
  /**
   * 车牌号
   */
  carNumber?: string;
  /**
   * 商品明细
   */
  details?: VehSaleBckDtlResponseDTO[];
  /**
   * 创建时间
   */
  filDate?: string;
  /**
   * 创建人
   */
  filler?: string;
  /**
   * 商品种类
   */
  goodsCount?: number;
  /**
   * 最后修改人
   */
  lastModifyOper?: string;
  /**
   * 最后修改时间
   */
  lstupdTime?: string;
  /**
   * 备注
   */
  note?: string;
  /**
   * 单号
   */
  num?: string;
  /**
   * 发生日期
   */
  ocrDate?: string;
  /**
   * 总数量
   */
  qty?: number;
  /**
   * 线路
   */
  sortLines?: SortLineResponseDTO[];
  /**
   * 单据状态
   */
  stat?: number;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  store?: GCN;
  /**
   * 退货门店地址
   */
  storeAddress?: string;
  /**
   * 总金额
   */
  total?: number;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  vehSaleEmp?: GCN;
  /**
   * 车销业务员姓名
   */
  vehSaleEmpName?: string;
  /**
   * 版本号
   */
  version?: number;
};
export type Response_VehSaleBckResponseDTO_ = {
  /**
   * 响应码。2000:成功，其他(5000+):业务异常
   * [required]
   */
  code: number;
  /**
   * VehSaleBckResponseDTO
   * ---
   * 获取车销退货单
   */
  data?: VehSaleBckResponseDTO;
  /**
   * 提示信息，一般在异常时返回
   */
  msg?: string;
};
export type VehSaleBckQueryResponseDTO = {
  /**
   * 商品种类
   */
  goodsCount?: number;
  /**
   * 最后修改时间
   */
  lstupdTime?: string;
  /**
   * 单号
   */
  num?: string;
  /**
   * 总数量
   */
  qty?: number;
  /**
   * 单据状态
   */
  stat?: number;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  store?: GCN;
  /**
   * 退货门店地址
   */
  storeAddress?: string;
  /**
   * 总金额
   */
  total?: number;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  vehSaleEmp?: GCN;
  /**
   * 业务员姓名
   */
  vehSaleEmpName?: string;
  /**
   * 版本号
   */
  version?: number;
};
export type BaseResponse_List_VehSaleBckQueryResponseDTO_ = {
  /**
   * 响应码。2000:接口调用成功，其他(5000+):正常业务异常
   */
  code?: number;
  /**
   * 响应实体
   */
  data?: VehSaleBckQueryResponseDTO[];
  /**
   * 输入字段提示。一般为表单提交时，后端对表单进行整体校验，返回给前端相应字段的校验结果。
   */
  fields?: Record<string, string>;
  /**
   * 是否还有更多记录，一般只在列表查询时返回。
   */
  more?: boolean;
  /**
   * 提示信息，一般在异常时返回
   */
  msg?: string;
  /**
   * 请求响应的记录数，一般只在列表查询时返回。
   */
  total?: number;
};
export type VehSaleBckQueryFilter = {
  /**
   * 开始日期
   */
  beginDate?: string;
  /**
   * 要求返回的部份信息
   */
  fetchParts?: string[];
  /**
   * 结束日期
   */
  finishDate?: string;
  /**
   * 关键字
   */
  keyword?: string;
  /**
   * 单号
   */
  num?: string;
  /**
   * 页码，从0开始，默认为0
   * [required]
   */
  page: number;
  /**
   * 每页条数，默认为50
   * [required]
   */
  pageSize: number;
  /**
   * 排序条件
   */
  sorts?: QuerySort[];
  /**
   * 状态
   */
  stat?: 100 | 110 | 300 | 310 | 700 | 710;
  /**
   * 状态列表
   */
  stats?: number[];
  /**
   * 门店Gid
   */
  storeGid?: number;
  /**
   * 业务员Gid
   */
  vehSaleEmpGid?: number;
  /**
   * 物流中心
   */
  wmsGid?: number;
};
export type BaseResponse_List_VehSaleBckDtlResponseDTO_ = {
  /**
   * 响应码。2000:接口调用成功，其他(5000+):正常业务异常
   */
  code?: number;
  /**
   * 响应实体
   */
  data?: VehSaleBckDtlResponseDTO[];
  /**
   * 输入字段提示。一般为表单提交时，后端对表单进行整体校验，返回给前端相应字段的校验结果。
   */
  fields?: Record<string, string>;
  /**
   * 是否还有更多记录，一般只在列表查询时返回。
   */
  more?: boolean;
  /**
   * 提示信息，一般在异常时返回
   */
  msg?: string;
  /**
   * 请求响应的记录数，一般只在列表查询时返回。
   */
  total?: number;
};
export type Response_List_int_ = {
  /**
   * 响应码。2000:成功，其他(5000+):业务异常
   * [required]
   */
  code: number;
  /**
   * 返回数据
   */
  data?: number[];
  /**
   * 提示信息，一般在异常时返回
   */
  msg?: string;
};
export type VehSaleCheckedRequestDTO = {
  /**
   * 是否选中
   * [required]
   */
  checked: boolean;
  /**
   * 单据类型
   * [required]
   */
  cls: string;
  /**
   * 商品Gid
   */
  gdGid?: number;
  /**
   * 业务员GID
   * [required]
   */
  vehSaleEmpGid: number;
};
export type VehSaleDraftInfoDTO = {
  /**
   * 分类的品相数
   */
  categorySkuCount?: Record<string, number>;
  /**
   * 品相数
   */
  skuCount?: number;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  store?: GCN;
  /**
   * 商品总额
   */
  total?: number;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  wms?: GCN;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  wrh?: GCN;
};
export type Response_VehSaleDraftInfoDTO_ = {
  /**
   * 响应码。2000:成功，其他(5000+):业务异常
   * [required]
   */
  code: number;
  /**
   * VehSaleDraftInfoDTO
   * ---
   * 草稿的基础信息
   */
  data?: VehSaleDraftInfoDTO;
  /**
   * 提示信息，一般在异常时返回
   */
  msg?: string;
};
export type VehSaleDraftResponseDTO = {
  /**
   * 分类的品相数
   */
  categorySkuCount?: Record<string, number>;
  /**
   * 品项数
   */
  goodsCount?: number;
  /**
   * 总金额
   */
  total?: number;
  /**
   * 版本号
   */
  version?: number;
};
export type Response_VehSaleDraftResponseDTO_ = {
  /**
   * 响应码。2000:成功，其他(5000+):业务异常
   * [required]
   */
  code: number;
  /**
   * VehSaleDraftResponseDTO
   * ---
   * 草稿返回值
   */
  data?: VehSaleDraftResponseDTO;
  /**
   * 提示信息，一般在异常时返回
   */
  msg?: string;
};
export type VehSaleRemoveDraftRequestDTO = {
  /**
   * 单据类型
   */
  cls?: string;
  /**
   * 商品Gid
   * [required]
   */
  gdGid: number;
  /**
   * 车销业务员Gid
   * [required]
   */
  vehSaleEmpGid: number;
  /**
   * 版本号
   * [required]
   */
  version: number;
};
export type VehSaleGdQpcDTO = {
  /**
   * 计量单位
   */
  munit?: string;
  /**
   * 规格
   */
  qpc?: number;
  /**
   * 规格说明
   */
  qpcStr?: string;
};
export type VehSaleGoodsDTO = {
  /**
   * 建议领货数量
   */
  advUseSignQty?: number;
  /**
   * 建议领货包装数量
   */
  advUseSignQtyStr?: string;
  /**
   * 业务库存数
   */
  busInvQty?: number;
  /**
   * 业务库存包装数
   */
  busInvQtyStr?: string;
  /**
   * CodeName
   * ---
   * 代码名称对象
   */
  category?: CodeName;
  /**
   * 代码
   */
  code?: string;
  /**
   * 商品条码
   */
  gdCode?: string;
  /**
   * 实体GID
   * [required]
   */
  gid: number;
  /**
   * 商品图片
   */
  imageDetails?: _[];
  /**
   * 最小单位
   */
  minMunit?: string;
  /**
   * 单位
   */
  munit?: string;
  /**
   * 名称
   */
  name?: string;
  /**
   * 规格价
   */
  price?: number;
  /**
   * 规格
   */
  qpc?: number;
  /**
   * 规格列表
   */
  qpcDetails?: VehSaleGdQpcDTO[];
  /**
   * 规格说明
   */
  qpcStr?: string;
  /**
   * 单品价
   */
  singlePrice?: number;
  /**
   * 领货单位
   */
  useSignMunit?: string;
  /**
   * 车销仓库存数量
   */
  vehSaleWrhQty?: number;
  /**
   * 车销仓库存包装数量
   */
  vehSaleWrhQtyStr?: string;
};
export type VehSaleGetDraftQpcResponseDTO = {
  /**
   * 单位
   * [required]
   */
  munit: string;
  /**
   * 规格价
   * [required]
   */
  price: number;
  /**
   * 规格
   * [required]
   */
  qpc: number;
  /**
   * 规格说明
   * [required]
   */
  qpcStr: string;
  /**
   * 数量
   * [required]
   */
  qty: number;
  /**
   * 包装数量
   * [required]
   */
  qtyStr: string;
  /**
   * 单品价
   * [required]
   */
  singlePrice: number;
  /**
   * 金额
   * [required]
   */
  total: number;
  /**
   * uuid
   */
  uuid?: string;
};
export type VehSaleGetDraftGoodsResponseDTO = {
  /**
   * 单据类型
   */
  cls?: string;
  /**
   * VehSaleGoodsDTO
   * ---
   * 商品
   * [required]
   */
  goods: VehSaleGoodsDTO;
  /**
   * 规格信息
   * [required]
   */
  qpcDetails: VehSaleGetDraftQpcResponseDTO[];
  /**
   * 唯一标识
   */
  uuid?: string;
  /**
   * 版本号
   * [required]
   */
  version: number;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  wms?: GCN;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  wrh?: GCN;
};
export type BaseResponse_List_VehSaleGetDraftGoodsResponseDTO_ = {
  /**
   * 响应码。2000:接口调用成功，其他(5000+):正常业务异常
   */
  code?: number;
  /**
   * 响应实体
   */
  data?: VehSaleGetDraftGoodsResponseDTO[];
  /**
   * 输入字段提示。一般为表单提交时，后端对表单进行整体校验，返回给前端相应字段的校验结果。
   */
  fields?: Record<string, string>;
  /**
   * 是否还有更多记录，一般只在列表查询时返回。
   */
  more?: boolean;
  /**
   * 提示信息，一般在异常时返回
   */
  msg?: string;
  /**
   * 请求响应的记录数，一般只在列表查询时返回。
   */
  total?: number;
};
export type VehSaleUseSignDraftQueryFilter = {
  /**
   * 单据类型
   * [required]
   */
  cls: string;
  /**
   * 要求返回的部份信息
   */
  fetchParts?: string[];
  /**
   * 关键字
   */
  keyword?: string;
  /**
   * 页码，从0开始，默认为0
   * [required]
   */
  page: number;
  /**
   * 每页条数，默认为50
   * [required]
   */
  pageSize: number;
  /**
   * 类别代码
   */
  sortCode?: string;
  /**
   * 排序条件
   */
  sorts?: QuerySort[];
  /**
   * 门店Gid
   */
  storeGid?: number;
  /**
   * 业务员Gid
   */
  vehSaleEmpGid?: number;
  /**
   * 物流中心
   */
  wmsGid?: number;
  /**
   * 仓位Gid
   */
  wrhGid?: number;
};
export type VehSaleDraftGoodsSubmitDTO = {
  /**
   * 单据类型
   * [required]
   */
  cls: string;
  /**
   * 商品条码
   * [required]
   */
  gdCode: string;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   * [required]
   */
  goods: GCN;
  /**
   * 单位
   * [required]
   */
  munit: string;
  /**
   * 规格价
   * [required]
   */
  price: number;
  /**
   * 规格
   * [required]
   */
  qpc: number;
  /**
   * 规格说明
   * [required]
   */
  qpcStr: string;
  /**
   * 数量
   * [required]
   */
  qty: number;
  /**
   * 包装数量
   * [required]
   */
  qtyStr: string;
  /**
   * 单价
   * [required]
   */
  singlePrice: number;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  store?: GCN;
  /**
   * 金额
   * [required]
   */
  total: number;
  /**
   * uuid
   */
  uuid?: string;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   * [required]
   */
  vehSaleEmp: GCN;
  /**
   * 版本号
   * [required]
   */
  version: number;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  wms?: GCN;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  wrh?: GCN;
};
export type Response_List_VehSaleGetDraftGoodsResponseDTO_ = {
  /**
   * 响应码。2000:成功，其他(5000+):业务异常
   * [required]
   */
  code: number;
  /**
   * 返回数据
   */
  data?: VehSaleGetDraftGoodsResponseDTO[];
  /**
   * 提示信息，一般在异常时返回
   */
  msg?: string;
};
export type BillAuditDtlRequestDTO = {
  /**
   * 商品GId
   * [required]
   */
  gdGid: number;
  /**
   * 规格价
   */
  price?: number;
  /**
   * 规格
   * [required]
   */
  qpc: number;
  /**
   * 规格描述
   * [required]
   */
  qpcStr: string;
  /**
   * 单品数量
   */
  qty?: number;
};
export type BillAuditRequestDTO = {
  /**
   * 单据审核请求参数明细
   */
  details?: BillAuditDtlRequestDTO[];
  /**
   * 单号
   * [required]
   */
  num: string;
  /**
   * 版本号
   * [required]
   */
  version: number;
};
export type VehSaleUseSignDtlQpcResponseDTO = {
  /**
   * 申请数量
   */
  applyQty?: number;
  /**
   * 申请包装数量
   */
  applyQtyStr?: string;
  /**
   * 单位
   */
  munit?: string;
  /**
   * 规格价
   */
  price?: number;
  /**
   * 规格
   */
  qpc?: number;
  /**
   * 规格描述
   */
  qpcStr?: string;
  /**
   * 确认数量
   */
  qty?: number;
  /**
   * 确认包装数量
   */
  qtyStr?: string;
  /**
   * 含税金额
   */
  total?: number;
};
export type VehSaleUseSignDtlResponseDTO = {
  /**
   * 申请数量
   */
  applyQty?: number;
  /**
   * CodeName
   * ---
   * 代码名称对象
   */
  category?: CodeName;
  /**
   * 默认领货单位
   */
  defUseSignMunit?: string;
  /**
   * 默认领货规格
   */
  defUseSignQpc?: number;
  /**
   * 默认领货包装规格
   */
  defUseSignQpcStr?: string;
  /**
   * 商品条码
   */
  gdCode?: string;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  goods?: GCN;
  /**
   * 图片
   */
  imageDetails?: _[];
  /**
   * 最小单位
   */
  minMunit?: string;
  /**
   * 备注
   */
  note?: string;
  /**
   * 规格明细
   */
  qpcDetails?: VehSaleUseSignDtlQpcResponseDTO[];
  /**
   * 确认数量
   */
  qty?: number;
  /**
   * 车销仓库存数量
   */
  vehSaleWrhQty?: number;
  /**
   * 车销仓库存包装数量
   */
  vehSaleWrhQtyStr?: string;
};
export type VehSaleUseSignResponseDTO = {
  /**
   * 附件明细
   */
  attachDetails?: BillAttachDtlResponseDTO[];
  /**
   * 车牌号
   */
  carNumber?: string;
  /**
   * 商品明细
   */
  details?: VehSaleUseSignDtlResponseDTO[];
  /**
   * 创建时间
   */
  filDate?: string;
  /**
   * 创建人
   */
  filler?: string;
  /**
   * 商品种类
   */
  goodsCount?: number;
  /**
   * 最后修改人
   */
  lastModifyOper?: string;
  /**
   * 最后修改时间
   */
  lstupdTime?: string;
  /**
   * 备注
   */
  note?: string;
  /**
   * 单号
   */
  num?: string;
  /**
   * 发生日期
   */
  ocrDate?: string;
  /**
   * 总数量
   */
  qty?: number;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  sender?: GCN;
  /**
   * 单据状态
   */
  stat?: number;
  /**
   * 含税总金额
   */
  total?: number;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  vehSaleEmp?: GCN;
  /**
   * 业务员姓名
   */
  vehSaleEmpName?: string;
  /**
   * 版本号
   */
  version?: number;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  wrh?: GCN;
};
export type Response_VehSaleUseSignResponseDTO_ = {
  /**
   * 响应码。2000:成功，其他(5000+):业务异常
   * [required]
   */
  code: number;
  /**
   * VehSaleUseSignResponseDTO
   * ---
   * 获取车销领货单
   */
  data?: VehSaleUseSignResponseDTO;
  /**
   * 提示信息，一般在异常时返回
   */
  msg?: string;
};
export type BillMargeAuditRequestDTO = {
  /**
   * 单号列表
   */
  bills?: BillBaseRequestDTO[];
  /**
   * 单据审核请求参数明细
   */
  details?: BillAuditDtlRequestDTO[];
};
export type BillBaseResponseDTO = {
  /**
   * 单号
   * [required]
   */
  num: string;
  /**
   * 版本号
   * [required]
   */
  version: number;
};
export type VehSaleUseSignMergeResponseDTO = {
  /**
   * 单据列表
   */
  bills?: BillBaseResponseDTO[];
  /**
   * 商品种类
   */
  goodsCount?: number;
  /**
   * 总数量
   */
  qty?: number;
  /**
   * 含税总金额
   */
  total?: number;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  vehSaleEmp?: GCN;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  wrh?: GCN;
};
export type Response_VehSaleUseSignMergeResponseDTO_ = {
  /**
   * 响应码。2000:成功，其他(5000+):业务异常
   * [required]
   */
  code: number;
  /**
   * VehSaleUseSignMergeResponseDTO
   * ---
   */
  data?: VehSaleUseSignMergeResponseDTO;
  /**
   * 提示信息，一般在异常时返回
   */
  msg?: string;
};
export type VehSaleUseSignQueryResponseDTO = {
  /**
   * 商品种类
   */
  goodsCount?: number;
  /**
   * 最后修改时间
   */
  lstupdTime?: string;
  /**
   * 单号
   */
  num?: string;
  /**
   * 总数量
   */
  qty?: number;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  sender?: GCN;
  /**
   * 单据状态
   */
  stat?: number;
  /**
   * 含税总金额
   */
  total?: number;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  vehSaleEmp?: GCN;
  /**
   * 业务员姓名
   */
  vehSaleEmpName?: string;
  /**
   * 版本号
   */
  version?: number;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  wrh?: GCN;
};
export type BaseResponse_List_VehSaleUseSignQueryResponseDTO_ = {
  /**
   * 响应码。2000:接口调用成功，其他(5000+):正常业务异常
   */
  code?: number;
  /**
   * 响应实体
   */
  data?: VehSaleUseSignQueryResponseDTO[];
  /**
   * 输入字段提示。一般为表单提交时，后端对表单进行整体校验，返回给前端相应字段的校验结果。
   */
  fields?: Record<string, string>;
  /**
   * 是否还有更多记录，一般只在列表查询时返回。
   */
  more?: boolean;
  /**
   * 提示信息，一般在异常时返回
   */
  msg?: string;
  /**
   * 请求响应的记录数，一般只在列表查询时返回。
   */
  total?: number;
};
export type VehSaleUseSignQueryFilter = {
  /**
   * 开始日期
   */
  beginDate?: string;
  /**
   * 要求返回的部份信息
   */
  fetchParts?: string[];
  /**
   * 结束日期
   */
  finishDate?: string;
  /**
   * 关键字
   */
  keyword?: string;
  /**
   * 单号
   */
  num?: string;
  /**
   * 页码，从0开始，默认为0
   * [required]
   */
  page: number;
  /**
   * 每页条数，默认为50
   * [required]
   */
  pageSize: number;
  /**
   * 排序条件
   */
  sorts?: QuerySort[];
  /**
   * 状态
   */
  stat?: 100 | 110 | 1300 | 1310;
  /**
   * 状态列表
   */
  stats?: number[];
  /**
   * 业务员Gid
   */
  vehSaleEmpGid?: number;
  /**
   * 物流中心
   */
  wmsGid?: number;
  /**
   * 发货仓位Gid
   */
  wrhGid?: number;
};
export type BaseResponse_List_VehSaleUseSignDtlResponseDTO_ = {
  /**
   * 响应码。2000:接口调用成功，其他(5000+):正常业务异常
   */
  code?: number;
  /**
   * 响应实体
   */
  data?: VehSaleUseSignDtlResponseDTO[];
  /**
   * 输入字段提示。一般为表单提交时，后端对表单进行整体校验，返回给前端相应字段的校验结果。
   */
  fields?: Record<string, string>;
  /**
   * 是否还有更多记录，一般只在列表查询时返回。
   */
  more?: boolean;
  /**
   * 提示信息，一般在异常时返回
   */
  msg?: string;
  /**
   * 请求响应的记录数，一般只在列表查询时返回。
   */
  total?: number;
};
export type VehSaleUseSignSubmitRequestDTO = {
  /**
   * 单据附件提交明细
   */
  attachDetails?: BillSubmitAttachDtlRequestDTO[];
  /**
   * 备注
   */
  note?: string;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  vehSaleEmp?: GCN;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  wrh?: GCN;
};
export type Response_List_VehSaleGoodsDTO_ = {
  /**
   * 响应码。2000:成功，其他(5000+):业务异常
   * [required]
   */
  code: number;
  /**
   * 返回数据
   */
  data?: VehSaleGoodsDTO[];
  /**
   * 提示信息，一般在异常时返回
   */
  msg?: string;
};
export type VehSaleUseSignArvAuditDtlRequestDTO = {
  /**
   * 商品GId
   * [required]
   */
  gdGid: number;
  /**
   * 规格价
   */
  price?: number;
  /**
   * 规格
   * [required]
   */
  qpc: number;
  /**
   * 规格描述
   * [required]
   */
  qpcStr: string;
  /**
   * 单品数量
   * [required]
   */
  qty: number;
  /**
   * 兑奖物数量
   * [required]
   */
  tgpQty: number;
};
export type VehSaleUseSignArvAuditRequestDTO = {
  /**
   * 单据审核请求参数明细
   */
  details?: VehSaleUseSignArvAuditDtlRequestDTO[];
  /**
   * 单号
   * [required]
   */
  num: string;
  /**
   * 版本号
   * [required]
   */
  version: number;
};
export type VehSaleUseSignArvDtlResponseDTO = {
  /**
   * CodeName
   * ---
   * 代码名称对象
   */
  category?: CodeName;
  /**
   * 商品条码
   */
  gdCode?: string;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  goods?: GCN;
  /**
   * 图片
   */
  imageDetails?: _[];
  /**
   * 行号
   */
  line?: number;
  /**
   * 最小单位
   */
  minMunit?: string;
  /**
   * 单位
   */
  munit?: string;
  /**
   * 备注
   */
  note?: string;
  /**
   * 规格价
   */
  price?: number;
  /**
   * 规格
   */
  qpc?: number;
  /**
   * 规格描述
   */
  qpcStr?: string;
  /**
   * 确认数量
   */
  qty?: number;
  /**
   * 确认包装数量
   */
  qtyStr?: string;
  /**
   * 应回数量
   */
  shouldQty?: number;
  /**
   * 应回包装数量
   */
  shouldQtyStr?: string;
  /**
   * 兑奖物数量
   */
  tgpQty?: number;
  /**
   * 兑奖物包装数量
   */
  tgpQtyStr?: string;
  /**
   * 含税金额
   */
  total?: number;
};
export type VehSaleUseSignArvGenBillDTO = {
  /**
   * 生成单据类型
   */
  genCls?: string;
  /**
   * 生成单据单号
   */
  genNum?: string;
  /**
   * 生成单据时间
   */
  genTime?: string;
};
export type VehSaleUseSignArvResponseDTO = {
  /**
   * 附件明细
   */
  attachDetails?: BillAttachDtlResponseDTO[];
  /**
   * 车牌号
   */
  carNumber?: string;
  /**
   * 商品明细
   */
  details?: VehSaleUseSignArvDtlResponseDTO[];
  /**
   * 差异项数
   */
  diffCount?: number;
  /**
   * 差异总金额
   */
  diffTotal?: number;
  /**
   * 创建时间
   */
  filDate?: string;
  /**
   * 创建人
   */
  filler?: string;
  /**
   * 生成单据明细
   */
  genBillDetails?: VehSaleUseSignArvGenBillDTO[];
  /**
   * 商品种类
   */
  goodsCount?: number;
  /**
   * 最后修改人
   */
  lastModifyOper?: string;
  /**
   * 最后修改时间
   */
  lstupdTime?: string;
  /**
   * 备注
   */
  note?: string;
  /**
   * 单号
   */
  num?: string;
  /**
   * 发生日期
   */
  ocrDate?: string;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  receiver?: GCN;
  /**
   * 单据状态
   */
  stat?: number;
  /**
   * 回货总金额
   */
  total?: number;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  vehSaleEmp?: GCN;
  /**
   * 车销业务员姓名
   */
  vehSaleEmpName?: string;
  /**
   * 版本号
   */
  version?: number;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  wrh?: GCN;
};
export type Response_VehSaleUseSignArvResponseDTO_ = {
  /**
   * 响应码。2000:成功，其他(5000+):业务异常
   * [required]
   */
  code: number;
  /**
   * VehSaleUseSignArvResponseDTO
   * ---
   * 获取车销回货单
   */
  data?: VehSaleUseSignArvResponseDTO;
  /**
   * 提示信息，一般在异常时返回
   */
  msg?: string;
};
export type BaseResponse_List_VehSaleUseSignArvDtlResponseDTO_ = {
  /**
   * 响应码。2000:接口调用成功，其他(5000+):正常业务异常
   */
  code?: number;
  /**
   * 响应实体
   */
  data?: VehSaleUseSignArvDtlResponseDTO[];
  /**
   * 输入字段提示。一般为表单提交时，后端对表单进行整体校验，返回给前端相应字段的校验结果。
   */
  fields?: Record<string, string>;
  /**
   * 是否还有更多记录，一般只在列表查询时返回。
   */
  more?: boolean;
  /**
   * 提示信息，一般在异常时返回
   */
  msg?: string;
  /**
   * 请求响应的记录数，一般只在列表查询时返回。
   */
  total?: number;
};
export type VehSaleUseSignArvQueryResponseDTO = {
  /**
   * 商品种类
   */
  goodsCount?: number;
  /**
   * 最后修改时间
   */
  lstupdTime?: string;
  /**
   * 单号
   */
  num?: string;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  receiver?: GCN;
  /**
   * 单据状态
   */
  stat?: number;
  /**
   * 总金额
   */
  total?: number;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  vehSaleEmp?: GCN;
  vehSaleEmpName?: string;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  wrh?: GCN;
};
export type BaseResponse_List_VehSaleUseSignArvQueryResponseDTO_ = {
  /**
   * 响应码。2000:接口调用成功，其他(5000+):正常业务异常
   */
  code?: number;
  /**
   * 响应实体
   */
  data?: VehSaleUseSignArvQueryResponseDTO[];
  /**
   * 输入字段提示。一般为表单提交时，后端对表单进行整体校验，返回给前端相应字段的校验结果。
   */
  fields?: Record<string, string>;
  /**
   * 是否还有更多记录，一般只在列表查询时返回。
   */
  more?: boolean;
  /**
   * 提示信息，一般在异常时返回
   */
  msg?: string;
  /**
   * 请求响应的记录数，一般只在列表查询时返回。
   */
  total?: number;
};
export type VehSaleUseSignArvQueryFilter = {
  /**
   * 开始日期
   */
  beginDate?: string;
  /**
   * 要求返回的部份信息
   */
  fetchParts?: string[];
  /**
   * 结束日期
   */
  finishDate?: string;
  /**
   * 关键字
   */
  keyword?: string;
  /**
   * 单号
   */
  num?: string;
  /**
   * 页码，从0开始，默认为0
   * [required]
   */
  page: number;
  /**
   * 每页条数，默认为50
   * [required]
   */
  pageSize: number;
  /**
   * 排序条件
   */
  sorts?: QuerySort[];
  /**
   * 状态
   */
  stat?: 100 | 1300 | 1310;
  /**
   * 状态列表
   */
  stats?: number[];
  /**
   * 业务员Gid
   */
  vehSaleEmpGid?: number;
  /**
   * 物流中心
   */
  wmsGid?: number;
};
export type VehSaleUseSignArvDtlSubmitRequestDTO = {
  /**
   * 车销商品
   * [required]
   */
  gdGid: number;
  /**
   * 兑奖物数量
   * [required]
   */
  tgpQty: number;
};
export type VehSaleUseSignArvSubmitRequestDTO = {
  /**
   * 单据附件提交明细
   */
  attachDetails?: BillSubmitAttachDtlRequestDTO[];
  /**
   * 商品明细
   */
  details?: VehSaleUseSignArvDtlSubmitRequestDTO[];
  /**
   * 备注
   */
  note?: string;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  vehSaleEmp?: GCN;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  wrh?: GCN;
};
export type VehSaleUseSignArvVerifyResponseDTO = {
  /**
   * 单据编号
   */
  nums?: string[];
  /**
   * 门店GID
   */
  storeGid?: number;
};
export type Response_VehSaleUseSignArvVerifyResponseDTO_ = {
  /**
   * 响应码。2000:成功，其他(5000+):业务异常
   * [required]
   */
  code: number;
  /**
   * VehSaleUseSignArvVerifyResponseDTO
   * ---
   * 车销回货单校验结果
   */
  data?: VehSaleUseSignArvVerifyResponseDTO;
  /**
   * 提示信息，一般在异常时返回
   */
  msg?: string;
};
export type VehSaleUseSignArvDiffHasFilter = {
  /**
   * 开始日期
   */
  beginDate?: string;
  /**
   * 结束日期
   */
  finishDate?: string;
  /**
   * 物流中心
   */
  wms?: number;
};
export type VehSaleUseSignArvDiffDtlResponseDTO = {
  /**
   * CodeName
   * ---
   * 代码名称对象
   */
  category?: CodeName;
  /**
   * 商品条码
   */
  gdCode?: string;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  goods?: GCN;
  /**
   * 图片
   */
  imageDetails?: _[];
  /**
   * 行号
   */
  line?: number;
  /**
   * 最小单位
   */
  minMunit?: string;
  /**
   * 单位
   */
  munit?: string;
  /**
   * 备注
   */
  note?: string;
  /**
   * 规格价
   */
  price?: number;
  /**
   * 已处理差异数量
   */
  procQty?: number;
  /**
   * 已处理差异包装数量
   */
  procQtyStr?: string;
  /**
   * 规格
   */
  qpc?: number;
  /**
   * 规格描述
   */
  qpcStr?: string;
  /**
   * 差异数量
   */
  qty?: number;
  /**
   * 差异包装数量
   */
  qtyStr?: string;
  /**
   * 含税金额
   */
  total?: number;
};
export type VehSaleUseSignArvDiffGenBillResponseDTO = {
  /**
   * 生成单据类型
   */
  genCls?: string;
  /**
   * 生成单据单号
   */
  genNum?: string;
  /**
   * 生成单据时间
   */
  genTime?: string;
};
export type VehSaleUseSignArvDiffResponseDTO = {
  /**
   * 车牌号
   */
  carNumber?: string;
  /**
   * 商品明细
   */
  details?: VehSaleUseSignArvDiffDtlResponseDTO[];
  /**
   * 创建时间
   */
  filDate?: string;
  /**
   * 创建人
   */
  filler?: string;
  /**
   * 生成单据明细
   */
  genDetails?: VehSaleUseSignArvDiffGenBillResponseDTO[];
  /**
   * 商品种类
   */
  goodsCount?: number;
  /**
   * 最后修改人
   */
  lastModifyOper?: string;
  /**
   * 最后修改时间
   */
  lstupdTime?: string;
  /**
   * 备注
   */
  note?: string;
  /**
   * 单号
   */
  num?: string;
  /**
   * 发生日期
   */
  ocrDate?: string;
  /**
   * 已处理差异数
   */
  procQty?: number;
  /**
   * 已处理差异金额
   */
  procTotal?: number;
  /**
   * 差异数量
   */
  qty?: number;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  receiver?: GCN;
  /**
   * 单据状态
   */
  stat?: number;
  /**
   * 总金额
   */
  total?: number;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  vehSaleEmp?: GCN;
  /**
   * 车销业务员姓名
   */
  vehSaleEmpName?: string;
  /**
   * 车销回货单单号
   */
  vehSaleUseSignArvNum?: string;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  wrh?: GCN;
};
export type Response_VehSaleUseSignArvDiffResponseDTO_ = {
  /**
   * 响应码。2000:成功，其他(5000+):业务异常
   * [required]
   */
  code: number;
  /**
   * VehSaleUseSignArvDiffResponseDTO
   * ---
   * 获取车销回货差异单
   */
  data?: VehSaleUseSignArvDiffResponseDTO;
  /**
   * 提示信息，一般在异常时返回
   */
  msg?: string;
};
export type BaseResponse_List_VehSaleUseSignArvDiffDtlResponseDTO_ = {
  /**
   * 响应码。2000:接口调用成功，其他(5000+):正常业务异常
   */
  code?: number;
  /**
   * 响应实体
   */
  data?: VehSaleUseSignArvDiffDtlResponseDTO[];
  /**
   * 输入字段提示。一般为表单提交时，后端对表单进行整体校验，返回给前端相应字段的校验结果。
   */
  fields?: Record<string, string>;
  /**
   * 是否还有更多记录，一般只在列表查询时返回。
   */
  more?: boolean;
  /**
   * 提示信息，一般在异常时返回
   */
  msg?: string;
  /**
   * 请求响应的记录数，一般只在列表查询时返回。
   */
  total?: number;
};
export type VehSaleUseSignArvDiffQueryResponseDTO = {
  /**
   * 商品种类
   */
  goodsCount?: number;
  /**
   * 最后修改时间
   */
  lstupdTime?: string;
  /**
   * 单号
   */
  num?: string;
  /**
   * 单据状态
   */
  stat?: number;
  /**
   * 总金额
   */
  total?: number;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  vehSaleEmp?: GCN;
  vehSaleEmpName?: string;
  /**
   * 车销回货单单号
   */
  vehSaleUseSignArvNum?: string;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  wrh?: GCN;
};
export type BaseResponse_List_VehSaleUseSignArvDiffQueryResponseDTO_ = {
  /**
   * 响应码。2000:接口调用成功，其他(5000+):正常业务异常
   */
  code?: number;
  /**
   * 响应实体
   */
  data?: VehSaleUseSignArvDiffQueryResponseDTO[];
  /**
   * 输入字段提示。一般为表单提交时，后端对表单进行整体校验，返回给前端相应字段的校验结果。
   */
  fields?: Record<string, string>;
  /**
   * 是否还有更多记录，一般只在列表查询时返回。
   */
  more?: boolean;
  /**
   * 提示信息，一般在异常时返回
   */
  msg?: string;
  /**
   * 请求响应的记录数，一般只在列表查询时返回。
   */
  total?: number;
};
export type VehSaleUseSignArvDiffQueryFilter = {
  /**
   * 开始日期
   */
  beginDate?: string;
  /**
   * 要求返回的部份信息
   */
  fetchParts?: string[];
  /**
   * 结束日期
   */
  finishDate?: string;
  /**
   * 关键字
   */
  keyword?: string;
  /**
   * 单号
   */
  num?: string;
  /**
   * 页码，从0开始，默认为0
   * [required]
   */
  page: number;
  /**
   * 每页条数，默认为50
   * [required]
   */
  pageSize: number;
  /**
   * 排序条件
   */
  sorts?: QuerySort[];
  /**
   * 状态
   */
  stat?: 100 | 300;
  /**
   * 状态列表
   */
  stats?: number[];
  /**
   * 车销业务员Gid
   */
  vehSaleEmpGid?: number;
  /**
   * 物流中心
   */
  wmsGid?: number;
  /**
   * 仓位Gid
   */
  wrhGid?: number;
};
export type BillGenBySrcGdDtlRequestDTO = {
  /**
   * 车销商品
   * [required]
   */
  gdGid: number;
  /**
   * 数量
   */
  qty?: number;
};
export type BillGenBySrcRequestDTO = {
  /**
   * 单据附件提交明细
   */
  attachDetails?: BillSubmitAttachDtlRequestDTO[];
  /**
   * 单据商品提交明细
   */
  goodsDetails?: BillGenBySrcGdDtlRequestDTO[];
  /**
   * 差异单号
   * [required]
   */
  num: string;
  /**
   * 车销业务员
   */
  vehSaleEmpGid?: number;
};
export type VehSaleDiffSaleRequestDTO = {
  /**
   * 单据附件提交明细
   */
  attachDetails?: BillSubmitAttachDtlRequestDTO[];
  /**
   * 单据商品提交明细
   */
  goodsDetails?: BillGenBySrcGdDtlRequestDTO[];
  /**
   * 差异单号
   * [required]
   */
  num: string;
  /**
   * 收货门店
   */
  storeGid?: number;
  /**
   * 车销业务员
   */
  vehSaleEmpGid?: number;
};
export type Response_List_VehSaleUseSignArvDiffDtlResponseDTO_ = {
  /**
   * 响应码。2000:成功，其他(5000+):业务异常
   * [required]
   */
  code: number;
  /**
   * 返回数据
   */
  data?: VehSaleUseSignArvDiffDtlResponseDTO[];
  /**
   * 提示信息，一般在异常时返回
   */
  msg?: string;
};
export type VehSaleUseSignArvDiffVehSaleGoodsQueryDTO = {
  /**
   * 单据商品提交明细
   */
  goodsDetails?: BillGenBySrcGdDtlRequestDTO[];
  /**
   * 单号
   * [required]
   */
  num: string;
  /**
   * 门店GID
   * [required]
   */
  storeGid: number;
};
export type _2 = {
  /**
   * 申请数量
   */
  applyQty?: number;
  /**
   * 申请包装数量
   */
  applyQtyStr?: string;
  /**
   * 单位
   */
  munit?: string;
  /**
   * 规格价
   */
  price?: number;
  /**
   * 规格
   */
  qpc?: number;
  /**
   * 规格描述
   */
  qpcStr?: string;
  /**
   * 确认数量
   */
  qty?: number;
  /**
   * 确认包装数量
   */
  qtyStr?: string;
  /**
   * 含税金额
   */
  total?: number;
};
export type VehSaleUseSignBckDtlResponseDTO = {
  /**
   * 申请数量
   */
  applyQty?: number;
  /**
   * CodeName
   * ---
   * 代码名称对象
   */
  category?: CodeName;
  /**
   * 商品条码
   */
  gdCode?: string;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  goods?: GCN;
  /**
   * 图片
   */
  imageDetails?: _[];
  /**
   * 备注
   */
  note?: string;
  /**
   * 规格数量信息
   */
  qpcDetails?: _2[];
  /**
   * 确认数量
   */
  qty?: number;
};
export type VehSaleUseSignBckResponseDTO = {
  /**
   * 附件明细
   */
  attachDetails?: BillAttachDtlResponseDTO[];
  /**
   * 车牌号
   */
  carNumber?: string;
  /**
   * 商品明细
   */
  details?: VehSaleUseSignBckDtlResponseDTO[];
  /**
   * 创建时间
   */
  filDate?: string;
  /**
   * 创建人
   */
  filler?: string;
  /**
   * 商品种类
   */
  goodsCount?: number;
  /**
   * 最后修改人
   */
  lastModifyOper?: string;
  /**
   * 最后修改时间
   */
  lstupdTime?: string;
  /**
   * 备注
   */
  note?: string;
  /**
   * 单号
   */
  num?: string;
  /**
   * 发生日期
   */
  ocrDate?: string;
  /**
   * 总数量
   */
  qty?: number;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  receiver?: GCN;
  /**
   * 单据状态
   */
  stat?: number;
  /**
   * 含税总金额
   */
  total?: number;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  vehSaleEmp?: GCN;
  /**
   * 业务员姓名
   */
  vehSaleEmpName?: string;
  /**
   * 版本号
   */
  version?: number;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  wrh?: GCN;
};
export type Response_VehSaleUseSignBckResponseDTO_ = {
  /**
   * 响应码。2000:成功，其他(5000+):业务异常
   * [required]
   */
  code: number;
  /**
   * VehSaleUseSignBckResponseDTO
   * ---
   * 获取车销领货退货单
   */
  data?: VehSaleUseSignBckResponseDTO;
  /**
   * 提示信息，一般在异常时返回
   */
  msg?: string;
};
export type VehSaleUseSignBckQueryResponseDTO = {
  /**
   * 商品种类
   */
  goodsCount?: number;
  /**
   * 最后修改时间
   */
  lstupdTime?: string;
  /**
   * 单号
   */
  num?: string;
  /**
   * 总数量
   */
  qty?: number;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  receiver?: GCN;
  /**
   * 单据状态
   */
  stat?: number;
  /**
   * 含税总金额
   */
  total?: number;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  vehSaleEmp?: GCN;
  /**
   * 业务员姓名
   */
  vehSaleEmpName?: string;
  /**
   * 版本号
   */
  version?: number;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  wrh?: GCN;
};
export type BaseResponse_List_VehSaleUseSignBckQueryResponseDTO_ = {
  /**
   * 响应码。2000:接口调用成功，其他(5000+):正常业务异常
   */
  code?: number;
  /**
   * 响应实体
   */
  data?: VehSaleUseSignBckQueryResponseDTO[];
  /**
   * 输入字段提示。一般为表单提交时，后端对表单进行整体校验，返回给前端相应字段的校验结果。
   */
  fields?: Record<string, string>;
  /**
   * 是否还有更多记录，一般只在列表查询时返回。
   */
  more?: boolean;
  /**
   * 提示信息，一般在异常时返回
   */
  msg?: string;
  /**
   * 请求响应的记录数，一般只在列表查询时返回。
   */
  total?: number;
};
export type VehSaleUseSignBckQueryFilter = {
  /**
   * 开始日期
   */
  beginDate?: string;
  /**
   * 要求返回的部份信息
   */
  fetchParts?: string[];
  /**
   * 结束日期
   */
  finishDate?: string;
  /**
   * 关键字
   */
  keyword?: string;
  /**
   * 单号
   */
  num?: string;
  /**
   * 页码，从0开始，默认为0
   * [required]
   */
  page: number;
  /**
   * 每页条数，默认为50
   * [required]
   */
  pageSize: number;
  /**
   * 排序条件
   */
  sorts?: QuerySort[];
  /**
   * 状态
   */
  stat?: 100 | 110 | 1300 | 1310;
  /**
   * 状态列表
   */
  stats?: number[];
  /**
   * 业务员Gid
   */
  vehSaleEmpGid?: number;
  /**
   * 物流中心
   */
  wmsGid?: number;
  /**
   * 收货仓位Gid
   */
  wrhGid?: number;
};
export type BaseResponse_List_VehSaleUseSignBckDtlResponseDTO_ = {
  /**
   * 响应码。2000:接口调用成功，其他(5000+):正常业务异常
   */
  code?: number;
  /**
   * 响应实体
   */
  data?: VehSaleUseSignBckDtlResponseDTO[];
  /**
   * 输入字段提示。一般为表单提交时，后端对表单进行整体校验，返回给前端相应字段的校验结果。
   */
  fields?: Record<string, string>;
  /**
   * 是否还有更多记录，一般只在列表查询时返回。
   */
  more?: boolean;
  /**
   * 提示信息，一般在异常时返回
   */
  msg?: string;
  /**
   * 请求响应的记录数，一般只在列表查询时返回。
   */
  total?: number;
};
export type VehSaleWrhInvResponseDTO = {
  /**
   * 上次回货日期
   */
  lastVehSaleUseSignArvDate?: string;
  /**
   * 上次领货日期
   */
  lastVehSaleUseSignDate?: string;
  /**
   * 可用库存数
   */
  qty?: number;
  /**
   * 可用品项数
   */
  skuCount?: number;
  /**
   * 可用库存金额
   */
  total?: number;
  /**
   * 不可用库存数
   */
  unusableQty?: number;
  /**
   * 不可用库存金额
   */
  unusableTotal?: number;
  /**
   * 上次回货金额
   */
  vehSaleUseSignArvTotal?: number;
  /**
   * 上次领货金额
   */
  vehSaleUseSignTotal?: number;
};
export type Response_VehSaleWrhInvResponseDTO_ = {
  /**
   * 响应码。2000:成功，其他(5000+):业务异常
   * [required]
   */
  code: number;
  /**
   * VehSaleWrhInvResponseDTO
   * ---
   * 车销库存
   */
  data?: VehSaleWrhInvResponseDTO;
  /**
   * 提示信息，一般在异常时返回
   */
  msg?: string;
};
export type Response_GCN_ = {
  /**
   * 响应码。2000:成功，其他(5000+):业务异常
   * [required]
   */
  code: number;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  data?: GCN;
  /**
   * 提示信息，一般在异常时返回
   */
  msg?: string;
};
export type BaseResponse_List_VehSaleGoodsDTO_ = {
  /**
   * 响应码。2000:接口调用成功，其他(5000+):正常业务异常
   */
  code?: number;
  /**
   * 响应实体
   */
  data?: VehSaleGoodsDTO[];
  /**
   * 输入字段提示。一般为表单提交时，后端对表单进行整体校验，返回给前端相应字段的校验结果。
   */
  fields?: Record<string, string>;
  /**
   * 是否还有更多记录，一般只在列表查询时返回。
   */
  more?: boolean;
  /**
   * 提示信息，一般在异常时返回
   */
  msg?: string;
  /**
   * 请求响应的记录数，一般只在列表查询时返回。
   */
  total?: number;
};
export type GoodsQueryFilter = {
  /**
   * 要求返回的部份信息
   */
  fetchParts?: string[];
  /**
   * 关键字
   */
  keyword?: string;
  /**
   * 页码，从0开始，默认为0
   * [required]
   */
  page: number;
  /**
   * 每页条数，默认为50
   * [required]
   */
  pageSize: number;
  /**
   * 类别
   */
  sortCode?: string;
  /**
   * 排序条件
   */
  sorts?: QuerySort[];
  /**
   * 业务员Gid
   */
  vehSaleEmpGid?: number;
  /**
   * 物流中心
   */
  wmsGid?: number;
};
export type WholeSaleDtlResponseDTO = {
  /**
   * CodeName
   * ---
   * 代码名称对象
   */
  category?: CodeName;
  /**
   * 商品条码
   */
  gdCode?: string;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  goods?: GCN;
  /**
   * 图片
   */
  imageDetails?: _[];
  /**
   * 行号
   */
  line?: number;
  /**
   * 最小单位
   */
  minMunit?: string;
  /**
   * 单位
   */
  munit?: string;
  /**
   * 备注
   */
  note?: string;
  /**
   * 规格价
   */
  price?: number;
  /**
   * 规格
   */
  qpc?: number;
  /**
   * 规格描述
   */
  qpcStr?: string;
  /**
   * 数量
   */
  qty?: number;
  /**
   * 包装数量
   */
  qtyStr?: string;
  /**
   * 含税金额
   */
  total?: number;
};
export type WholeSaleResponseDTO = {
  /**
   * 附件明细
   */
  attachDetails?: BillAttachDtlResponseDTO[];
  /**
   * 商品明细
   */
  details?: WholeSaleDtlResponseDTO[];
  /**
   * 创建时间
   */
  filDate?: string;
  /**
   * 创建人
   */
  filler?: string;
  /**
   * 商品种类
   */
  goodsCount?: number;
  /**
   * 最后修改人
   */
  lastModifyOper?: string;
  /**
   * 最后修改时间
   */
  lstupdTime?: string;
  /**
   * 备注
   */
  note?: string;
  /**
   * 单号
   */
  num?: string;
  /**
   * 发生日期
   */
  ocrDate?: string;
  /**
   * 来源类型
   */
  srcCls?: string;
  /**
   * 来源单号
   */
  srcNum?: string;
  /**
   * 单据状态
   */
  stat?: number;
  /**
   * 总金额
   */
  total?: number;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  vehSaleEmp?: GCN;
  vehSaleEmpName?: string;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  wrh?: GCN;
};
export type Response_WholeSaleResponseDTO_ = {
  /**
   * 响应码。2000:成功，其他(5000+):业务异常
   * [required]
   */
  code: number;
  /**
   * WholeSaleResponseDTO
   * ---
   * 获取批发单
   */
  data?: WholeSaleResponseDTO;
  /**
   * 提示信息，一般在异常时返回
   */
  msg?: string;
};
export type BaseResponse_List_WholeSaleDtlResponseDTO_ = {
  /**
   * 响应码。2000:接口调用成功，其他(5000+):正常业务异常
   */
  code?: number;
  /**
   * 响应实体
   */
  data?: WholeSaleDtlResponseDTO[];
  /**
   * 输入字段提示。一般为表单提交时，后端对表单进行整体校验，返回给前端相应字段的校验结果。
   */
  fields?: Record<string, string>;
  /**
   * 是否还有更多记录，一般只在列表查询时返回。
   */
  more?: boolean;
  /**
   * 提示信息，一般在异常时返回
   */
  msg?: string;
  /**
   * 请求响应的记录数，一般只在列表查询时返回。
   */
  total?: number;
};
export type WholeSaleDetailsFilter = {
  /**
   * 要求返回的部份信息
   */
  fetchParts?: string[];
  /**
   * 单号
   * [required]
   */
  num: string;
  /**
   * 页码，从0开始，默认为0
   * [required]
   */
  page: number;
  /**
   * 每页条数，默认为50
   * [required]
   */
  pageSize: number;
  /**
   * 排序条件
   */
  sorts?: QuerySort[];
};
export type WholeSaleQueryResponseDTO = {
  /**
   * 商品种类
   */
  goodsCount?: number;
  /**
   * 最后修改时间
   */
  lstupdTime?: string;
  /**
   * 单号
   */
  num?: string;
  /**
   * 单据状态
   */
  stat?: number;
  /**
   * 总金额
   */
  total?: number;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  vehSaleEmp?: GCN;
  vehSaleEmpName?: string;
  /**
   * GCN
   * ---
   * 包含Gid的基础对象
   */
  wrh?: GCN;
};
export type BaseResponse_List_WholeSaleQueryResponseDTO_ = {
  /**
   * 响应码。2000:接口调用成功，其他(5000+):正常业务异常
   */
  code?: number;
  /**
   * 响应实体
   */
  data?: WholeSaleQueryResponseDTO[];
  /**
   * 输入字段提示。一般为表单提交时，后端对表单进行整体校验，返回给前端相应字段的校验结果。
   */
  fields?: Record<string, string>;
  /**
   * 是否还有更多记录，一般只在列表查询时返回。
   */
  more?: boolean;
  /**
   * 提示信息，一般在异常时返回
   */
  msg?: string;
  /**
   * 请求响应的记录数，一般只在列表查询时返回。
   */
  total?: number;
};
export type WholeSaleQueryFilter = {
  /**
   * 开始日期
   */
  beginDate?: string;
  /**
   * 要求返回的部份信息
   */
  fetchParts?: string[];
  /**
   * 结束日期
   */
  finishDate?: string;
  /**
   * 关键字
   */
  keyword?: string;
  /**
   * 单号
   */
  num?: string;
  /**
   * 页码，从0开始，默认为0
   * [required]
   */
  page: number;
  /**
   * 每页条数，默认为50
   * [required]
   */
  pageSize: number;
  /**
   * 排序条件
   */
  sorts?: QuerySort[];
  /**
   * 单据状态 可选值：1300、100、1310、110
   */
  stat?: number;
  /**
   * 状态列表
   */
  stats?: number[];
  /**
   * 业务员Gid
   */
  vehSaleEmpGid?: number;
  /**
   * 物流中心
   */
  wmsGid?: number;
  /**
   * 仓位Gid
   */
  wrhGid?: number;
};
declare global {
  interface Apis {
    commonInterface: {
      /**
       * ---
       *
       * [GET] 获取OSS临时账户信息
       *
       * **path:** /vbs-service/rest/common/image/oss/signature
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   // 图片存放目录
       *   // [required]
       *   imageDir: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // 返回数据
       *   data?: object
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      ossSignatureUsingGET<
        Config extends Alova2MethodConfig<Response_Map_string_object_> & {
          params: {
            /**
             * 图片存放目录
             * [required]
             */
            imageDir: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<Response_Map_string_object_, 'commonInterface.ossSignatureUsingGET', Config>;
    };
    mainInterface: {
      /**
       * ---
       *
       * [POST] 获取数据统计
       *
       * **path:** /vbs-service/rest/main/get
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 开始日期
       *   beginDate?: string
       *   // 结束日期
       *   finishDate?: string
       *   // 车销业务员
       *   vehSaleEmpGid?: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // [title] MainResponseDTO
       *   // 我的响应信息
       *   data?: {
       *     // 车销退金额
       *     vehSaleBckTotal?: number
       *     // 车销单据数
       *     vehSaleCount?: number
       *     // 车销金额
       *     vehSaleTotal?: number
       *     // 批发单数（买赔单据数）
       *     wholeSaleTotal?: number
       *   }
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      getDataUsingPOST<
        Config extends Alova2MethodConfig<Response_MainResponseDTO_> & {
          data: MainQueryFilter;
        }
      >(
        config: Config
      ): Alova2Method<Response_MainResponseDTO_, 'mainInterface.getDataUsingPOST', Config>;
    };
    mdataInterface: {
      /**
       * ---
       *
       * [POST] 获取类别
       *
       * **path:** /vbs-service/rest/mdata/category/query
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 关键字
       *   keyword?: string
       *   // 业务员Gid
       *   vehSaleEmpGid?: number
       *   // 物流中心
       *   wmsGid?: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // 返回数据
       *   data?: Array<{
       *     // 代码
       *     // [required]
       *     code: string
       *     // 名称
       *     // [required]
       *     name: string
       *   }>
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      queryCategoryUsingPOST<
        Config extends Alova2MethodConfig<Response_List_CategoryDTO_> & {
          data: CategoryQueryFilter;
        }
      >(
        config: Config
      ): Alova2Method<Response_List_CategoryDTO_, 'mdataInterface.queryCategoryUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 获取线路
       *
       * **path:** /vbs-service/rest/mdata/sortline/query
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 关键字
       *   keyword?: string
       *   // 业务员Gid
       *   vehSaleEmpGid?: number
       *   // 物流中心
       *   wmsGid?: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // 返回数据
       *   data?: Array<{
       *     // 线路分类体系代码
       *     archCode?: string
       *     // 线路分类体系名称
       *     archName?: string
       *     // 线路分类体系Uuid
       *     archUuid?: string
       *     code?: string
       *     name?: string
       *     uuid?: string
       *   }>
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      querySortLineUsingPOST<
        Config extends Alova2MethodConfig<Response_List_SortLineResponseDTO_> & {
          data: BaseExcludedPageQueryFilter;
        }
      >(
        config: Config
      ): Alova2Method<Response_List_SortLineResponseDTO_, 'mdataInterface.querySortLineUsingPOST', Config>;
      /**
       * ---
       *
       * [GET] 获取门店
       *
       * **path:** /vbs-service/rest/mdata/store/get
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   // 门店GID
       *   // [required]
       *   storeGid: number
       *   // 车销业务员GID
       *   // [required]
       *   vehSaleEmpGid: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // [title] StoreDTO
       *   // 门店
       *   data?: {
       *     // 地址
       *     address?: string
       *     // 代码
       *     code?: string
       *     // 联系人
       *     contactor?: string
       *     // 实体GID
       *     // [required]
       *     gid: number
       *     // 上次车销单时间
       *     lastVehSaleTime?: string
       *     // 上次车销单金额
       *     lastVehSaleTotal?: number
       *     // 纬度
       *     latitude?: string
       *     // 经度
       *     longitude?: string
       *     // 名称
       *     name?: string
       *     // 手机号
       *     phone?: string
       *     // 线路
       *     sortLines?: Array<{
       *       // 线路分类体系代码
       *       archCode?: string
       *       // 线路分类体系名称
       *       archName?: string
       *       // 线路分类体系Uuid
       *       archUuid?: string
       *       code?: string
       *       name?: string
       *       uuid?: string
       *     }>
       *   }
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      getStoreUsingGET<
        Config extends Alova2MethodConfig<Response_StoreDTO_> & {
          params: {
            /**
             * 门店GID
             * [required]
             */
            storeGid: number;
            /**
             * 车销业务员GID
             * [required]
             */
            vehSaleEmpGid: number;
          };
        }
      >(
        config: Config
      ): Alova2Method<Response_StoreDTO_, 'mdataInterface.getStoreUsingGET', Config>;
      /**
       * ---
       *
       * [POST] 查询门店
       *
       * **path:** /vbs-service/rest/mdata/store/query
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 要求返回的部份信息
       *   fetchParts?: string[]
       *   // 关键字
       *   keyword?: string
       *   // 页码，从0开始，默认为0
       *   // [required]
       *   page: number
       *   // 每页条数，默认为50
       *   // [required]
       *   pageSize: number
       *   // 线路UUID
       *   sortLineUuid?: string
       *   // 排序条件
       *   sorts?: Array<{
       *     // 排序方式，默认倒叙
       *     asc?: boolean
       *     // 排序字段
       *     // [required]
       *     field: string
       *   }>
       *   // 门店Gid
       *   storeGid?: number
       *   // 业务员Gid
       *   vehSaleEmpGid?: number
       *   // 物流中心
       *   wmsGid?: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:接口调用成功，其他(5000+):正常业务异常
       *   code?: number
       *   // 响应实体
       *   data?: Array<{
       *     // 地址
       *     address?: string
       *     // 代码
       *     code?: string
       *     // 联系人
       *     contactor?: string
       *     // 实体GID
       *     // [required]
       *     gid: number
       *     // 上次车销单时间
       *     lastVehSaleTime?: string
       *     // 上次车销单金额
       *     lastVehSaleTotal?: number
       *     // 纬度
       *     latitude?: string
       *     // 经度
       *     longitude?: string
       *     // 名称
       *     name?: string
       *     // 手机号
       *     phone?: string
       *     // 线路
       *     sortLines?: Array<{
       *       // 线路分类体系代码
       *       archCode?: string
       *       // 线路分类体系名称
       *       archName?: string
       *       // 线路分类体系Uuid
       *       archUuid?: string
       *       code?: string
       *       name?: string
       *       uuid?: string
       *     }>
       *   }>
       *   // 输入字段提示。一般为表单提交时，后端对表单进行整体校验，返回给前端相应字段的校验结果。
       *   fields?: Record<string, string>
       *   // 是否还有更多记录，一般只在列表查询时返回。
       *   more?: boolean
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       *   // 请求响应的记录数，一般只在列表查询时返回。
       *   total?: number
       * }
       * ```
       */
      queryStoreUsingPOST<
        Config extends Alova2MethodConfig<BaseResponse_List_StoreDTO_> & {
          data: StoreQueryFilter;
        }
      >(
        config: Config
      ): Alova2Method<BaseResponse_List_StoreDTO_, 'mdataInterface.queryStoreUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 获取车销业务员
       *
       * **path:** /vbs-service/rest/mdata/vehsaleemp/query
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 关键字
       *   keyword?: string
       *   // 业务员Gid
       *   vehSaleEmpGid?: number
       *   // 物流中心
       *   wmsGid?: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // 返回数据
       *   data?: Array<{
       *     // 代码
       *     code?: string
       *     // 实体GID
       *     // [required]
       *     gid: number
       *     // 名称
       *     name?: string
       *   }>
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      queryVehSaleEmpUsingPOST<
        Config extends Alova2MethodConfig<Response_List_GCN_> & {
          data: BaseExcludedPageQueryFilter;
        }
      >(
        config: Config
      ): Alova2Method<Response_List_GCN_, 'mdataInterface.queryVehSaleEmpUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 获取物流中心
       *
       * **path:** /vbs-service/rest/mdata/wms/query
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 关键字
       *   keyword?: string
       *   // 业务员Gid
       *   vehSaleEmpGid?: number
       *   // 物流中心
       *   wmsGid?: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // 返回数据
       *   data?: Array<{
       *     // 代码
       *     code?: string
       *     // 实体GID
       *     // [required]
       *     gid: number
       *     // 名称
       *     name?: string
       *   }>
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      queryWmsUsingPOST<
        Config extends Alova2MethodConfig<Response_List_GCN_> & {
          data: BaseExcludedPageQueryFilter;
        }
      >(
        config: Config
      ): Alova2Method<Response_List_GCN_, 'mdataInterface.queryWmsUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 获取仓位
       *
       * **path:** /vbs-service/rest/mdata/wrh/query
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 关键字
       *   keyword?: string
       *   // 业务员Gid
       *   vehSaleEmpGid?: number
       *   // 物流中心
       *   wmsGid?: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // 返回数据
       *   data?: Array<{
       *     // 代码
       *     code?: string
       *     // 实体GID
       *     // [required]
       *     gid: number
       *     // 名称
       *     name?: string
       *   }>
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      queryWrhUsingPOST<
        Config extends Alova2MethodConfig<Response_List_GCN_> & {
          data: WarehouseQueryFilter;
        }
      >(
        config: Config
      ): Alova2Method<Response_List_GCN_, 'mdataInterface.queryWrhUsingPOST', Config>;
    };
    printInterface: {
      /**
       * ---
       *
       * [POST] 获取打印文件
       *
       * **path:** /vbs-service/rest/print/get/printfile
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 单据类型
       *   cls?: string
       *   // 打印模板文件名
       *   // [required]
       *   fileName: string
       *   // 模块ID
       *   // [required]
       *   moduleId: string
       *   // 单号
       *   num?: string
       *   // 车销业务员Gid
       *   // [required]
       *   vehSaleEmpGid: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // [title] PrintResponseDTO
       *   data?: {
       *     // 打印文件流
       *     // [required]
       *     printFileBytes: string
       *     // 打印文件访问路径
       *     // [required]
       *     printFileUrl: string
       *   }
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      getPrintFileUsingPOST<
        Config extends Alova2MethodConfig<Response_PrintResponseDTO_> & {
          data: PrintRequestDTO;
        }
      >(
        config: Config
      ): Alova2Method<Response_PrintResponseDTO_, 'printInterface.getPrintFileUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 获取打印文本
       *
       * **path:** /vbs-service/rest/print/get/printstr
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 单据类型
       *   cls?: string
       *   // 打印模板文件名
       *   // [required]
       *   fileName: string
       *   // 模块ID
       *   // [required]
       *   moduleId: string
       *   // 单号
       *   num?: string
       *   // 车销业务员Gid
       *   // [required]
       *   vehSaleEmpGid: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // 返回数据
       *   data?: string
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      printUsingPOST<
        Config extends Alova2MethodConfig<Response_string_> & {
          data: PrintRequestDTO;
        }
      >(
        config: Config
      ): Alova2Method<Response_string_, 'printInterface.printUsingPOST', Config>;
      /**
       * ---
       *
       * [GET] 获取打印模板
       *
       * **path:** /vbs-service/rest/print/template/gets
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   // 模块ID
       *   // [required]
       *   moduleId: string
       *   // 使用场景： 1-草稿，2-单据
       *   // [required]
       *   usage: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // 返回数据
       *   data?: Array<{
       *     // 文件名
       *     fileName?: string
       *     // 模块ID
       *     moduleId?: string
       *     // 页面缩放比例 ：默认为整宽不变形
       *     pagePercent?: string
       *     // 打印模式, 整单打印-singlePrint（def），合并打印-mergPrint
       *     printMode?: string
       *     // 模板名
       *     templateName?: string
       *     // 上传日期
       *     uploadTime?: string
       *   }>
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      getTemplatesUsingGET<
        Config extends Alova2MethodConfig<Response_List_PrintTemplateResponseDTO_> & {
          params: {
            /**
             * 模块ID
             * [required]
             */
            moduleId: string;
            /**
             * 使用场景： 1-草稿，2-单据
             * [required]
             */
            usage: number;
          };
        }
      >(
        config: Config
      ): Alova2Method<Response_List_PrintTemplateResponseDTO_, 'printInterface.getTemplatesUsingGET', Config>;
    };
    signInterface: {
      /**
       * ---
       *
       * [GET] 获取签到距离
       *
       * **path:** /vbs-service/rest/sign/distance
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   // 纬度
       *   // [required]
       *   latitude: number
       *   // 经度
       *   // [required]
       *   longitude: number
       *   // 门店
       *   // [required]
       *   storeGid: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // 返回数据
       *   data?: number
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      getDistanceUsingGET<
        Config extends Alova2MethodConfig<Response_bigdecimal_> & {
          params: {
            /**
             * 纬度
             * [required]
             */
            latitude: number;
            /**
             * 经度
             * [required]
             */
            longitude: number;
            /**
             * 门店
             * [required]
             */
            storeGid: number;
          };
        }
      >(
        config: Config
      ): Alova2Method<Response_bigdecimal_, 'signInterface.getDistanceUsingGET', Config>;
      /**
       * ---
       *
       * [GET] 未签退的其他门店
       *
       * **path:** /vbs-service/rest/sign/get/nosignoutstore
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   // 门店
       *   // [required]
       *   storeGid: number
       *   // 车销业务员
       *   // [required]
       *   vehSaleEmpGid: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // 返回数据
       *   data?: number
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      getNoSignOutStoreUsingGET<
        Config extends Alova2MethodConfig<Response_int_> & {
          params: {
            /**
             * 门店
             * [required]
             */
            storeGid: number;
            /**
             * 车销业务员
             * [required]
             */
            vehSaleEmpGid: number;
          };
        }
      >(
        config: Config
      ): Alova2Method<Response_int_, 'signInterface.getNoSignOutStoreUsingGET', Config>;
      /**
       * ---
       *
       * [GET] 查询门店签到状态
       *
       * **path:** /vbs-service/rest/sign/get/state
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   // 门店
       *   // [required]
       *   storeGid: number
       *   // 车销业务员
       *   // [required]
       *   vehSaleEmpGid: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // 返回数据
       *   data?: number
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      getSignStateUsingGET<
        Config extends Alova2MethodConfig<Response_int_> & {
          params: {
            /**
             * 门店
             * [required]
             */
            storeGid: number;
            /**
             * 车销业务员
             * [required]
             */
            vehSaleEmpGid: number;
          };
        }
      >(
        config: Config
      ): Alova2Method<Response_int_, 'signInterface.getSignStateUsingGET', Config>;
      /**
       * ---
       *
       * [POST] 根据门店获取签到记录
       *
       * **path:** /vbs-service/rest/sign/getbystore
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   // 门店
       *   // [required]
       *   storeGid: number
       *   // 车销业务员
       *   // [required]
       *   vehSaleEmpGid: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // 返回数据
       *   data?: Array<{
       *     // 附件
       *     attachDetails?: Array<{
       *       // 文件Id
       *       fileId?: string
       *       // 文件名称
       *       fileName?: string
       *       // 文件地址
       *       fileUrl?: string
       *       // 行号
       *       line?: number
       *       // 签到类型
       *       signType?: number
       *     }>
       *     // 是否异常
       *     isException?: boolean
       *     // 纬度
       *     latitude?: string
       *     // 经度
       *     longitude?: string
       *     // 手机号
       *     phone?: string
       *     // 签到时间
       *     signInTime?: string
       *     // 签到时长
       *     signMinute?: number
       *     // 签退时间
       *     signOutTime?: string
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     store?: {
       *       // 代码
       *       code?: string
       *       // 实体GID
       *       // [required]
       *       gid: number
       *       // 名称
       *       name?: string
       *     }
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     vehSaleEmp?: GCN
       *   }>
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      getStoreSignByStoreUsingPOST<
        Config extends Alova2MethodConfig<Response_List_StoreSignDTO_> & {
          params: {
            /**
             * 门店
             * [required]
             */
            storeGid: number;
            /**
             * 车销业务员
             * [required]
             */
            vehSaleEmpGid: number;
          };
        }
      >(
        config: Config
      ): Alova2Method<Response_List_StoreSignDTO_, 'signInterface.getStoreSignByStoreUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 签到
       *
       * **path:** /vbs-service/rest/sign/in
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 附件
       *   attachDetails?: Array<{
       *     // 文件Id
       *     fileId?: string
       *     // 文件名称
       *     fileName?: string
       *     // 文件地址
       *     fileUrl?: string
       *     // 行号
       *     line?: number
       *     // 签到类型
       *     signType?: number
       *   }>
       *   // 是否异常
       *   isException?: boolean
       *   // 纬度
       *   latitude?: string
       *   // 经度
       *   longitude?: string
       *   // 手机号
       *   phone?: string
       *   // 签到时间
       *   signInTime?: string
       *   // 签到时长
       *   signMinute?: number
       *   // 签退时间
       *   signOutTime?: string
       *   // [title] GCN
       *   // 包含Gid的基础对象
       *   store?: {
       *     // 代码
       *     code?: string
       *     // 实体GID
       *     // [required]
       *     gid: number
       *     // 名称
       *     name?: string
       *   }
       *   // [title] GCN
       *   // 包含Gid的基础对象
       *   vehSaleEmp?: GCN
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      signInUsingPOST<
        Config extends Alova2MethodConfig<Response_Void_> & {
          data: StoreSignDTO;
        }
      >(
        config: Config
      ): Alova2Method<Response_Void_, 'signInterface.signInUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 签退
       *
       * **path:** /vbs-service/rest/sign/out
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 附件
       *   attachDetails?: Array<{
       *     // 文件Id
       *     fileId?: string
       *     // 文件名称
       *     fileName?: string
       *     // 文件地址
       *     fileUrl?: string
       *     // 行号
       *     line?: number
       *     // 签到类型
       *     signType?: number
       *   }>
       *   // 是否异常
       *   isException?: boolean
       *   // 纬度
       *   latitude?: string
       *   // 经度
       *   longitude?: string
       *   // 手机号
       *   phone?: string
       *   // 签到时间
       *   signInTime?: string
       *   // 签到时长
       *   signMinute?: number
       *   // 签退时间
       *   signOutTime?: string
       *   // [title] GCN
       *   // 包含Gid的基础对象
       *   store?: {
       *     // 代码
       *     code?: string
       *     // 实体GID
       *     // [required]
       *     gid: number
       *     // 名称
       *     name?: string
       *   }
       *   // [title] GCN
       *   // 包含Gid的基础对象
       *   vehSaleEmp?: GCN
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      signOutUsingPOST<
        Config extends Alova2MethodConfig<Response_Void_> & {
          data: StoreSignDTO;
        }
      >(
        config: Config
      ): Alova2Method<Response_Void_, 'signInterface.signOutUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 签退校验
       *
       * **path:** /vbs-service/rest/sign/out/verify
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   // 门店
       *   // [required]
       *   storeGid: number
       *   // 车销业务员
       *   // [required]
       *   vehSaleEmpGid: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // [title] SignOutVerifyDTO
       *   // 签退校验
       *   data?: {
       *     // 消息
       *     message?: string
       *     // 模块Id
       *     moduleId?: string
       *     // 是否校验通过
       *     valid?: boolean
       *   }
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      signOutVerifyUsingPOST<
        Config extends Alova2MethodConfig<Response_SignOutVerifyDTO_> & {
          params: {
            /**
             * 门店
             * [required]
             */
            storeGid: number;
            /**
             * 车销业务员
             * [required]
             */
            vehSaleEmpGid: number;
          };
        }
      >(
        config: Config
      ): Alova2Method<Response_SignOutVerifyDTO_, 'signInterface.signOutVerifyUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 查询
       *
       * **path:** /vbs-service/rest/sign/query
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 要求返回的部份信息
       *   fetchParts?: string[]
       *   // 关键字
       *   keyword?: string
       *   // 页码，从0开始，默认为0
       *   // [required]
       *   page: number
       *   // 每页条数，默认为50
       *   // [required]
       *   pageSize: number
       *   // 线路UUID
       *   sortLineUuid?: string
       *   // 排序条件
       *   sorts?: Array<{
       *     // 排序方式，默认倒叙
       *     asc?: boolean
       *     // 排序字段
       *     // [required]
       *     field: string
       *   }>
       *   // 门店Gid
       *   storeGid?: number
       *   // 业务员Gid
       *   vehSaleEmpGid?: number
       *   // 物流中心
       *   wmsGid?: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:接口调用成功，其他(5000+):正常业务异常
       *   code?: number
       *   // 响应实体
       *   data?: Array<{
       *     // 附件
       *     attachDetails?: Array<{
       *       // 文件Id
       *       fileId?: string
       *       // 文件名称
       *       fileName?: string
       *       // 文件地址
       *       fileUrl?: string
       *       // 行号
       *       line?: number
       *       // 签到类型
       *       signType?: number
       *     }>
       *     // 是否异常
       *     isException?: boolean
       *     // 纬度
       *     latitude?: string
       *     // 经度
       *     longitude?: string
       *     // 手机号
       *     phone?: string
       *     // 签到时间
       *     signInTime?: string
       *     // 签到时长
       *     signMinute?: number
       *     // 签退时间
       *     signOutTime?: string
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     store?: {
       *       // 代码
       *       code?: string
       *       // 实体GID
       *       // [required]
       *       gid: number
       *       // 名称
       *       name?: string
       *     }
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     vehSaleEmp?: GCN
       *   }>
       *   // 输入字段提示。一般为表单提交时，后端对表单进行整体校验，返回给前端相应字段的校验结果。
       *   fields?: Record<string, string>
       *   // 是否还有更多记录，一般只在列表查询时返回。
       *   more?: boolean
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       *   // 请求响应的记录数，一般只在列表查询时返回。
       *   total?: number
       * }
       * ```
       */
      queryStoreSignByStoreUsingPOST<
        Config extends Alova2MethodConfig<BaseResponse_List_StoreSignDTO_> & {
          data: StoreQueryFilter;
        }
      >(
        config: Config
      ): Alova2Method<BaseResponse_List_StoreSignDTO_, 'signInterface.queryStoreSignByStoreUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 上报门店经纬度
       *
       * **path:** /vbs-service/rest/sign/report/location
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 纬度
       *   latitude?: number
       *   // 经度
       *   longitude?: number
       *   // [title] GCN
       *   // 包含Gid的基础对象
       *   store?: {
       *     // 代码
       *     code?: string
       *     // 实体GID
       *     // [required]
       *     gid: number
       *     // 名称
       *     name?: string
       *   }
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      reportLocationUsingPOST<
        Config extends Alova2MethodConfig<Response_Void_> & {
          data: StoreReportRequestDTO;
        }
      >(
        config: Config
      ): Alova2Method<Response_Void_, 'signInterface.reportLocationUsingPOST', Config>;
    };
    loginInterface: {
      /**
       * ---
       *
       * [POST] 用户登录
       *
       * **path:** /vbs-service/rest/user/login
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 登录账号
       *   // [required]
       *   loginCode: string
       *   // 登录密码
       *   // [required]
       *   password: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // [title] User
       *   // 车销业务员
       *   data?: {
       *     // [title] FaEmp
       *     // 员工
       *     faEmp?: {
       *       // 代码
       *       code?: string
       *       // 实体GID
       *       // [required]
       *       gid: number
       *       // 是否启用
       *       isEnable?: number
       *       // 名称
       *       name?: string
       *       // [title] GCN
       *       // 包含Gid的基础对象
       *       org?: {
       *         // 代码
       *         code?: string
       *         // 实体GID
       *         // [required]
       *         gid: number
       *         // 名称
       *         name?: string
       *       }
       *       // 手机号
       *       phone?: string
       *     }
       *     // 选项列表
       *     options?: Array<{
       *       // 模块号
       *       moduleNo?: number
       *       // 选项名
       *       optionCaption?: string
       *       // 选项值
       *       optionValue?: string
       *       // 组织
       *       orgGid?: number
       *     }>
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     org?: GCN
       *     // 权限列表
       *     permissions?: Array<{
       *       // 模块名
       *       module?: string
       *       // 模块描述
       *       moduleName?: string
       *       // 角色ID
       *       roleId?: string
       *       // 权限状态
       *       state?: number
       *     }>
       *     // 角色代码.  01-车销业务员，02-仓管
       *     roleCode?: string
       *     // 线路
       *     sortLines?: Array<{
       *       // 线路分类体系代码
       *       archCode?: string
       *       // 线路分类体系名称
       *       archName?: string
       *       // 线路分类体系Uuid
       *       archUuid?: string
       *       code?: string
       *       name?: string
       *       uuid?: string
       *     }>
       *     // 秘钥
       *     token?: string
       *     // [title] VehSaleEmp
       *     // 车销业务员
       *     vehSaleEmp?: {
       *       // 车牌号
       *       carNumber?: string
       *       // 代码
       *       code?: string
       *       // [title] FaEmp
       *       // 员工
       *       faEmp?: FaEmp
       *       // 实体GID
       *       // [required]
       *       gid: number
       *       // 是否启用
       *       isEnable?: number
       *       // 名称
       *       name?: string
       *       // [title] GCN
       *       // 包含Gid的基础对象
       *       org?: GCN
       *       // 手机号
       *       phone?: string
       *       // [title] GCN
       *       // 包含Gid的基础对象
       *       vehSaleWrh?: GCN
       *       // [title] GCN
       *       // 包含Gid的基础对象
       *       wms?: GCN
       *     }
       *   }
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      loginUsingPOST<
        Config extends Alova2MethodConfig<Response_User_> & {
          data: LoginCredentials;
        }
      >(
        config: Config
      ): Alova2Method<Response_User_, 'loginInterface.loginUsingPOST', Config>;
    };
    vehsaleInterface: {
      /**
       * ---
       *
       * [POST] 作废单据
       *
       * **path:** /vbs-service/rest/vehsale/abort
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 单号
       *   // [required]
       *   num: string
       *   // 版本号
       *   // [required]
       *   version: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      abortUsingPOST_1<
        Config extends Alova2MethodConfig<Response_Void_> & {
          data: BillBaseRequestDTO;
        }
      >(
        config: Config
      ): Alova2Method<Response_Void_, 'vehsaleInterface.abortUsingPOST_1', Config>;
      /**
       * ---
       *
       * [GET] 获取单据详情
       *
       * **path:** /vbs-service/rest/vehsale/get
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   // 是否查询明细
       *   fetchDetail?: boolean
       *   // 单号
       *   // [required]
       *   num: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // [title] VehSaleResponseDTO
       *   // 获取车销单
       *   data?: {
       *     // 附件明细
       *     attachDetails?: Array<{
       *       // 附件标识
       *       fileId?: string
       *       // 附件名称
       *       fileName?: string
       *       // 附件路径
       *       fileUrl?: string
       *       // 行号
       *       line?: number
       *       // 备注
       *       note?: string
       *     }>
       *     // 车牌号
       *     carNumber?: string
       *     // 商品明细
       *     details?: Array<{
       *       // 申请数量
       *       applyQty?: number
       *       // [title] CodeName
       *       // 代码名称对象
       *       category?: {
       *         // 代码
       *         code?: string
       *         // 名称
       *         name?: string
       *       }
       *       // 商品条码
       *       gdCode?: string
       *       // [title] GCN
       *       // 包含Gid的基础对象
       *       goods?: {
       *         // 代码
       *         code?: string
       *         // 实体GID
       *         // [required]
       *         gid: number
       *         // 名称
       *         name?: string
       *       }
       *       // 图片
       *       imageDetails?: Array<{
       *         // 文件ID
       *         fileId?: string
       *         // 文件名
       *         fileName?: string
       *         // 文件类型
       *         fileType?: 'img' | 'video'
       *         // 外部URL
       *         fileUrl?: string
       *         // 行号
       *         line?: number
       *       }>
       *       // 备注
       *       note?: string
       *       // 规格信息
       *       qpcDetails?: Array<{
       *         // 申请数量
       *         applyQty?: number
       *         // 申请包装数量
       *         applyQtyStr?: string
       *         // 单位
       *         munit?: string
       *         // 规格价
       *         price?: number
       *         // 规格
       *         qpc?: number
       *         // 规格描述
       *         qpcStr?: string
       *         // 确认数量
       *         qty?: number
       *         // 确认包装数量
       *         qtyStr?: string
       *         // 含税金额
       *         total?: number
       *       }>
       *       // 确认数量
       *       qty?: number
       *     }>
       *     // 创建时间
       *     filDate?: string
       *     // 创建人
       *     filler?: string
       *     // 商品种类
       *     goodsCount?: number
       *     // 最后修改人
       *     lastModifyOper?: string
       *     // 最后修改时间
       *     lstupdTime?: string
       *     // 备注
       *     note?: string
       *     // 单号
       *     num?: string
       *     // 发生日期
       *     ocrDate?: string
       *     // 总数量
       *     qty?: number
       *     // 线路
       *     sortLines?: Array<{
       *       // 线路分类体系代码
       *       archCode?: string
       *       // 线路分类体系名称
       *       archName?: string
       *       // 线路分类体系Uuid
       *       archUuid?: string
       *       code?: string
       *       name?: string
       *       uuid?: string
       *     }>
       *     // 单据状态
       *     stat?: number
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     store?: GCN
       *     // 收货门店地址
       *     storeAddress?: string
       *     // 总金额
       *     total?: number
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     vehSaleEmp?: GCN
       *     // 车销业务员姓名
       *     vehSaleEmpName?: string
       *     // 版本号
       *     version?: number
       *   }
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      getUsingGET_1<
        Config extends Alova2MethodConfig<Response_VehSaleResponseDTO_> & {
          params: {
            /**
             * 是否查询明细
             */
            fetchDetail?: boolean;
            /**
             * 单号
             * [required]
             */
            num: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<Response_VehSaleResponseDTO_, 'vehsaleInterface.getUsingGET_1', Config>;
      /**
       * ---
       *
       * [POST] 列表查询
       *
       * **path:** /vbs-service/rest/vehsale/query
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 开始日期
       *   beginDate?: string
       *   // 要求返回的部份信息
       *   fetchParts?: string[]
       *   // 结束日期
       *   finishDate?: string
       *   // 关键字
       *   keyword?: string
       *   // 单号
       *   num?: string
       *   // 页码，从0开始，默认为0
       *   // [required]
       *   page: number
       *   // 每页条数，默认为50
       *   // [required]
       *   pageSize: number
       *   // 排序条件
       *   sorts?: Array<{
       *     // 排序方式，默认倒叙
       *     asc?: boolean
       *     // 排序字段
       *     // [required]
       *     field: string
       *   }>
       *   // 状态
       *   stat?: 100 | 1000 | 1010 | 110 | 300 | 310
       *   // 状态列表
       *   stats?: number[]
       *   // 门店Gid
       *   storeGid?: number
       *   // 业务员Gid
       *   vehSaleEmpGid?: number
       *   // 物流中心
       *   wmsGid?: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:接口调用成功，其他(5000+):正常业务异常
       *   code?: number
       *   // 响应实体
       *   data?: Array<{
       *     // 商品种类
       *     goodsCount?: number
       *     // 最后修改时间
       *     lstupdTime?: string
       *     // 单号
       *     num?: string
       *     // 总数量
       *     qty?: number
       *     // 单据状态
       *     stat?: number
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     store?: {
       *       // 代码
       *       code?: string
       *       // 实体GID
       *       // [required]
       *       gid: number
       *       // 名称
       *       name?: string
       *     }
       *     // 收货门店地址
       *     storeAddress?: string
       *     // 总金额
       *     total?: number
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     vehSaleEmp?: GCN
       *     // 业务员姓名
       *     vehSaleEmpName?: string
       *     // 版本号
       *     version?: number
       *   }>
       *   // 输入字段提示。一般为表单提交时，后端对表单进行整体校验，返回给前端相应字段的校验结果。
       *   fields?: Record<string, string>
       *   // 是否还有更多记录，一般只在列表查询时返回。
       *   more?: boolean
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       *   // 请求响应的记录数，一般只在列表查询时返回。
       *   total?: number
       * }
       * ```
       */
      queryUsingPOST_1<
        Config extends Alova2MethodConfig<BaseResponse_List_VehSaleQueryResponseDTO_> & {
          data: VehSaleQueryFilter;
        }
      >(
        config: Config
      ): Alova2Method<BaseResponse_List_VehSaleQueryResponseDTO_, 'vehsaleInterface.queryUsingPOST_1', Config>;
      /**
       * ---
       *
       * [POST] 获取单据明细详情
       *
       * **path:** /vbs-service/rest/vehsale/query/details
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // [title] Category
       *   // 分类
       *   category?: {
       *     // 代码
       *     // [required]
       *     code: string
       *     // 名称
       *     name?: string
       *   }
       *   // 要求返回的部份信息
       *   fetchParts?: string[]
       *   // 单号
       *   // [required]
       *   num: string
       *   // 页码，从0开始，默认为0
       *   // [required]
       *   page: number
       *   // 每页条数，默认为50
       *   // [required]
       *   pageSize: number
       *   // 排序条件
       *   sorts?: Array<{
       *     // 排序方式，默认倒叙
       *     asc?: boolean
       *     // 排序字段
       *     // [required]
       *     field: string
       *   }>
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:接口调用成功，其他(5000+):正常业务异常
       *   code?: number
       *   // 响应实体
       *   data?: Array<{
       *     // 申请数量
       *     applyQty?: number
       *     // [title] CodeName
       *     // 代码名称对象
       *     category?: {
       *       // 代码
       *       code?: string
       *       // 名称
       *       name?: string
       *     }
       *     // 商品条码
       *     gdCode?: string
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     goods?: {
       *       // 代码
       *       code?: string
       *       // 实体GID
       *       // [required]
       *       gid: number
       *       // 名称
       *       name?: string
       *     }
       *     // 图片
       *     imageDetails?: Array<{
       *       // 文件ID
       *       fileId?: string
       *       // 文件名
       *       fileName?: string
       *       // 文件类型
       *       fileType?: 'img' | 'video'
       *       // 外部URL
       *       fileUrl?: string
       *       // 行号
       *       line?: number
       *     }>
       *     // 备注
       *     note?: string
       *     // 规格信息
       *     qpcDetails?: Array<{
       *       // 申请数量
       *       applyQty?: number
       *       // 申请包装数量
       *       applyQtyStr?: string
       *       // 单位
       *       munit?: string
       *       // 规格价
       *       price?: number
       *       // 规格
       *       qpc?: number
       *       // 规格描述
       *       qpcStr?: string
       *       // 确认数量
       *       qty?: number
       *       // 确认包装数量
       *       qtyStr?: string
       *       // 含税金额
       *       total?: number
       *     }>
       *     // 确认数量
       *     qty?: number
       *   }>
       *   // 输入字段提示。一般为表单提交时，后端对表单进行整体校验，返回给前端相应字段的校验结果。
       *   fields?: Record<string, string>
       *   // 是否还有更多记录，一般只在列表查询时返回。
       *   more?: boolean
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       *   // 请求响应的记录数，一般只在列表查询时返回。
       *   total?: number
       * }
       * ```
       */
      queryDetailsUsingPOST_1<
        Config extends Alova2MethodConfig<BaseResponse_List_VehSaleDtlResponseDTO_> & {
          data: BillDtlQueryFilter;
        }
      >(
        config: Config
      ): Alova2Method<BaseResponse_List_VehSaleDtlResponseDTO_, 'vehsaleInterface.queryDetailsUsingPOST_1', Config>;
      /**
       * ---
       *
       * [POST] 提交单据
       *
       * **path:** /vbs-service/rest/vehsale/submit
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 单据附件提交明细
       *   attachDetails?: Array<{
       *     // 附件标识
       *     fileId?: string
       *     // 附件名称
       *     fileName?: string
       *     // 附件路径
       *     fileUrl?: string
       *   }>
       *   // 备注
       *   note?: string
       *   // [title] GCN
       *   // 包含Gid的基础对象
       *   store?: {
       *     // 代码
       *     code?: string
       *     // 实体GID
       *     // [required]
       *     gid: number
       *     // 名称
       *     name?: string
       *   }
       *   // [title] GCN
       *   // 包含Gid的基础对象
       *   vehSaleEmp?: GCN
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // 返回数据
       *   data?: string
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      submitUsingPOST_1<
        Config extends Alova2MethodConfig<Response_string_> & {
          data: VehSaleSubmitRequestDTO;
        }
      >(
        config: Config
      ): Alova2Method<Response_string_, 'vehsaleInterface.submitUsingPOST_1', Config>;
      /**
       * ---
       *
       * [POST] 新建单据前校验
       *
       * **path:** /vbs-service/rest/vehsale/verify/create
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   // 车销业务员Gid
       *   // [required]
       *   vehSaleEmpGid: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // 返回数据
       *   data?: boolean
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      verifyCreateUsingPOST_1<
        Config extends Alova2MethodConfig<Response_boolean_> & {
          params: {
            /**
             * 车销业务员Gid
             * [required]
             */
            vehSaleEmpGid: number;
          };
        }
      >(
        config: Config
      ): Alova2Method<Response_boolean_, 'vehsaleInterface.verifyCreateUsingPOST_1', Config>;
    };
    vehsalebckInterface: {
      /**
       * ---
       *
       * [POST] 作废单据
       *
       * **path:** /vbs-service/rest/vehsalebck/abort
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 单号
       *   // [required]
       *   num: string
       *   // 版本号
       *   // [required]
       *   version: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      abortUsingPOST<
        Config extends Alova2MethodConfig<Response_Void_> & {
          data: BillBaseRequestDTO;
        }
      >(
        config: Config
      ): Alova2Method<Response_Void_, 'vehsalebckInterface.abortUsingPOST', Config>;
      /**
       * ---
       *
       * [GET] 获取单据详情
       *
       * **path:** /vbs-service/rest/vehsalebck/get
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   // 是否查询明细
       *   fetchDetail?: boolean
       *   // 单号
       *   // [required]
       *   num: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // [title] VehSaleBckResponseDTO
       *   // 获取车销退货单
       *   data?: {
       *     // 附件明细
       *     attachDetails?: Array<{
       *       // 附件标识
       *       fileId?: string
       *       // 附件名称
       *       fileName?: string
       *       // 附件路径
       *       fileUrl?: string
       *       // 行号
       *       line?: number
       *       // 备注
       *       note?: string
       *     }>
       *     // 车牌号
       *     carNumber?: string
       *     // 商品明细
       *     details?: Array<{
       *       // 申请数量
       *       applyQty?: number
       *       // [title] CodeName
       *       // 代码名称对象
       *       category?: {
       *         // 代码
       *         code?: string
       *         // 名称
       *         name?: string
       *       }
       *       // 商品条码
       *       gdCode?: string
       *       // [title] GCN
       *       // 包含Gid的基础对象
       *       goods?: {
       *         // 代码
       *         code?: string
       *         // 实体GID
       *         // [required]
       *         gid: number
       *         // 名称
       *         name?: string
       *       }
       *       // 图片
       *       imageDetails?: Array<{
       *         // 文件ID
       *         fileId?: string
       *         // 文件名
       *         fileName?: string
       *         // 文件类型
       *         fileType?: 'img' | 'video'
       *         // 外部URL
       *         fileUrl?: string
       *         // 行号
       *         line?: number
       *       }>
       *       // 备注
       *       note?: string
       *       // 规格信息
       *       qpcDetails?: Array<{
       *         // 申请数量
       *         applyQty?: number
       *         // 申请包装数量
       *         applyQtyStr?: string
       *         // 单位
       *         munit?: string
       *         // 规格价
       *         price?: number
       *         // 规格
       *         qpc?: number
       *         // 规格描述
       *         qpcStr?: string
       *         // 确认数量
       *         qty?: number
       *         // 确认包装数量
       *         qtyStr?: string
       *         // 含税金额
       *         total?: number
       *       }>
       *       // 确认数量
       *       qty?: number
       *     }>
       *     // 创建时间
       *     filDate?: string
       *     // 创建人
       *     filler?: string
       *     // 商品种类
       *     goodsCount?: number
       *     // 最后修改人
       *     lastModifyOper?: string
       *     // 最后修改时间
       *     lstupdTime?: string
       *     // 备注
       *     note?: string
       *     // 单号
       *     num?: string
       *     // 发生日期
       *     ocrDate?: string
       *     // 总数量
       *     qty?: number
       *     // 线路
       *     sortLines?: Array<{
       *       // 线路分类体系代码
       *       archCode?: string
       *       // 线路分类体系名称
       *       archName?: string
       *       // 线路分类体系Uuid
       *       archUuid?: string
       *       code?: string
       *       name?: string
       *       uuid?: string
       *     }>
       *     // 单据状态
       *     stat?: number
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     store?: GCN
       *     // 退货门店地址
       *     storeAddress?: string
       *     // 总金额
       *     total?: number
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     vehSaleEmp?: GCN
       *     // 车销业务员姓名
       *     vehSaleEmpName?: string
       *     // 版本号
       *     version?: number
       *   }
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      getUsingGET<
        Config extends Alova2MethodConfig<Response_VehSaleBckResponseDTO_> & {
          params: {
            /**
             * 是否查询明细
             */
            fetchDetail?: boolean;
            /**
             * 单号
             * [required]
             */
            num: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<Response_VehSaleBckResponseDTO_, 'vehsalebckInterface.getUsingGET', Config>;
      /**
       * ---
       *
       * [POST] 列表查询
       *
       * **path:** /vbs-service/rest/vehsalebck/query
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 开始日期
       *   beginDate?: string
       *   // 要求返回的部份信息
       *   fetchParts?: string[]
       *   // 结束日期
       *   finishDate?: string
       *   // 关键字
       *   keyword?: string
       *   // 单号
       *   num?: string
       *   // 页码，从0开始，默认为0
       *   // [required]
       *   page: number
       *   // 每页条数，默认为50
       *   // [required]
       *   pageSize: number
       *   // 排序条件
       *   sorts?: Array<{
       *     // 排序方式，默认倒叙
       *     asc?: boolean
       *     // 排序字段
       *     // [required]
       *     field: string
       *   }>
       *   // 状态
       *   stat?: 100 | 110 | 300 | 310 | 700 | 710
       *   // 状态列表
       *   stats?: number[]
       *   // 门店Gid
       *   storeGid?: number
       *   // 业务员Gid
       *   vehSaleEmpGid?: number
       *   // 物流中心
       *   wmsGid?: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:接口调用成功，其他(5000+):正常业务异常
       *   code?: number
       *   // 响应实体
       *   data?: Array<{
       *     // 商品种类
       *     goodsCount?: number
       *     // 最后修改时间
       *     lstupdTime?: string
       *     // 单号
       *     num?: string
       *     // 总数量
       *     qty?: number
       *     // 单据状态
       *     stat?: number
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     store?: {
       *       // 代码
       *       code?: string
       *       // 实体GID
       *       // [required]
       *       gid: number
       *       // 名称
       *       name?: string
       *     }
       *     // 退货门店地址
       *     storeAddress?: string
       *     // 总金额
       *     total?: number
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     vehSaleEmp?: GCN
       *     // 业务员姓名
       *     vehSaleEmpName?: string
       *     // 版本号
       *     version?: number
       *   }>
       *   // 输入字段提示。一般为表单提交时，后端对表单进行整体校验，返回给前端相应字段的校验结果。
       *   fields?: Record<string, string>
       *   // 是否还有更多记录，一般只在列表查询时返回。
       *   more?: boolean
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       *   // 请求响应的记录数，一般只在列表查询时返回。
       *   total?: number
       * }
       * ```
       */
      queryUsingPOST<
        Config extends Alova2MethodConfig<BaseResponse_List_VehSaleBckQueryResponseDTO_> & {
          data: VehSaleBckQueryFilter;
        }
      >(
        config: Config
      ): Alova2Method<BaseResponse_List_VehSaleBckQueryResponseDTO_, 'vehsalebckInterface.queryUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 获取单据明细详情
       *
       * **path:** /vbs-service/rest/vehsalebck/query/details
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // [title] Category
       *   // 分类
       *   category?: {
       *     // 代码
       *     // [required]
       *     code: string
       *     // 名称
       *     name?: string
       *   }
       *   // 要求返回的部份信息
       *   fetchParts?: string[]
       *   // 单号
       *   // [required]
       *   num: string
       *   // 页码，从0开始，默认为0
       *   // [required]
       *   page: number
       *   // 每页条数，默认为50
       *   // [required]
       *   pageSize: number
       *   // 排序条件
       *   sorts?: Array<{
       *     // 排序方式，默认倒叙
       *     asc?: boolean
       *     // 排序字段
       *     // [required]
       *     field: string
       *   }>
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:接口调用成功，其他(5000+):正常业务异常
       *   code?: number
       *   // 响应实体
       *   data?: Array<{
       *     // 申请数量
       *     applyQty?: number
       *     // [title] CodeName
       *     // 代码名称对象
       *     category?: {
       *       // 代码
       *       code?: string
       *       // 名称
       *       name?: string
       *     }
       *     // 商品条码
       *     gdCode?: string
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     goods?: {
       *       // 代码
       *       code?: string
       *       // 实体GID
       *       // [required]
       *       gid: number
       *       // 名称
       *       name?: string
       *     }
       *     // 图片
       *     imageDetails?: Array<{
       *       // 文件ID
       *       fileId?: string
       *       // 文件名
       *       fileName?: string
       *       // 文件类型
       *       fileType?: 'img' | 'video'
       *       // 外部URL
       *       fileUrl?: string
       *       // 行号
       *       line?: number
       *     }>
       *     // 备注
       *     note?: string
       *     // 规格信息
       *     qpcDetails?: Array<{
       *       // 申请数量
       *       applyQty?: number
       *       // 申请包装数量
       *       applyQtyStr?: string
       *       // 单位
       *       munit?: string
       *       // 规格价
       *       price?: number
       *       // 规格
       *       qpc?: number
       *       // 规格描述
       *       qpcStr?: string
       *       // 确认数量
       *       qty?: number
       *       // 确认包装数量
       *       qtyStr?: string
       *       // 含税金额
       *       total?: number
       *     }>
       *     // 确认数量
       *     qty?: number
       *   }>
       *   // 输入字段提示。一般为表单提交时，后端对表单进行整体校验，返回给前端相应字段的校验结果。
       *   fields?: Record<string, string>
       *   // 是否还有更多记录，一般只在列表查询时返回。
       *   more?: boolean
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       *   // 请求响应的记录数，一般只在列表查询时返回。
       *   total?: number
       * }
       * ```
       */
      queryDetailsUsingPOST<
        Config extends Alova2MethodConfig<BaseResponse_List_VehSaleBckDtlResponseDTO_> & {
          data: BillDtlQueryFilter;
        }
      >(
        config: Config
      ): Alova2Method<BaseResponse_List_VehSaleBckDtlResponseDTO_, 'vehsalebckInterface.queryDetailsUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 提交单据
       *
       * **path:** /vbs-service/rest/vehsalebck/submit
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 单据附件提交明细
       *   attachDetails?: Array<{
       *     // 附件标识
       *     fileId?: string
       *     // 附件名称
       *     fileName?: string
       *     // 附件路径
       *     fileUrl?: string
       *   }>
       *   // 备注
       *   note?: string
       *   // [title] GCN
       *   // 包含Gid的基础对象
       *   store?: {
       *     // 代码
       *     code?: string
       *     // 实体GID
       *     // [required]
       *     gid: number
       *     // 名称
       *     name?: string
       *   }
       *   // [title] GCN
       *   // 包含Gid的基础对象
       *   vehSaleEmp?: GCN
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // 返回数据
       *   data?: string
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      submitUsingPOST<
        Config extends Alova2MethodConfig<Response_string_> & {
          data: VehSaleSubmitRequestDTO;
        }
      >(
        config: Config
      ): Alova2Method<Response_string_, 'vehsalebckInterface.submitUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 新建单据前校验
       *
       * **path:** /vbs-service/rest/vehsalebck/verify/create
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   // 车销业务员Gid
       *   // [required]
       *   vehSaleEmpGid: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // 返回数据
       *   data?: boolean
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      verifyCreateUsingPOST<
        Config extends Alova2MethodConfig<Response_boolean_> & {
          params: {
            /**
             * 车销业务员Gid
             * [required]
             */
            vehSaleEmpGid: number;
          };
        }
      >(
        config: Config
      ): Alova2Method<Response_boolean_, 'vehsalebckInterface.verifyCreateUsingPOST', Config>;
    };
    vehsaledraftInterface: {
      /**
       * ---
       *
       * [GET] 获取草稿商品的类别
       *
       * **path:** /vbs-service/rest/vehsaledraft/draft/category/get
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   // 类型
       *   // [required]
       *   cls: string
       *   // 业务员Gid
       *   // [required]
       *   vehSaleEmpGid: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // 返回数据
       *   data?: Array<{
       *     // 代码
       *     // [required]
       *     code: string
       *     // 名称
       *     // [required]
       *     name: string
       *   }>
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      getCategoryUsingGET<
        Config extends Alova2MethodConfig<Response_List_CategoryDTO_> & {
          params: {
            /**
             * 类型
             * [required]
             */
            cls: string;
            /**
             * 业务员Gid
             * [required]
             */
            vehSaleEmpGid: number;
          };
        }
      >(
        config: Config
      ): Alova2Method<Response_List_CategoryDTO_, 'vehsaledraftInterface.getCategoryUsingGET', Config>;
      /**
       * ---
       *
       * [POST] 选中
       *
       * **path:** /vbs-service/rest/vehsaledraft/draft/checked
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 是否选中
       *   // [required]
       *   checked: boolean
       *   // 单据类型
       *   // [required]
       *   cls: string
       *   // 商品Gid
       *   gdGid?: number
       *   // 业务员GID
       *   // [required]
       *   vehSaleEmpGid: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // 返回数据
       *   data?: number[]
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      checkedUsingPOST<
        Config extends Alova2MethodConfig<Response_List_int_> & {
          data: VehSaleCheckedRequestDTO;
        }
      >(
        config: Config
      ): Alova2Method<Response_List_int_, 'vehsaledraftInterface.checkedUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 清空草稿
       *
       * **path:** /vbs-service/rest/vehsaledraft/draft/clear
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   // 单据类型
       *   // [required]
       *   cls: string
       *   // 业务员Gid
       *   // [required]
       *   vehSaleEmpGid: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      clearDraftUsingPOST<
        Config extends Alova2MethodConfig<Response_Void_> & {
          params: {
            /**
             * 单据类型
             * [required]
             */
            cls: string;
            /**
             * 业务员Gid
             * [required]
             */
            vehSaleEmpGid: number;
          };
        }
      >(
        config: Config
      ): Alova2Method<Response_Void_, 'vehsaledraftInterface.clearDraftUsingPOST', Config>;
      /**
       * ---
       *
       * [GET] 查询草稿基础信息
       *
       * **path:** /vbs-service/rest/vehsaledraft/draft/info/get
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   // 单据类型
       *   // [required]
       *   cls: string
       *   // 业务员Gid
       *   // [required]
       *   vehSaleEmpGid: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // [title] VehSaleDraftInfoDTO
       *   // 草稿的基础信息
       *   data?: {
       *     // 分类的品相数
       *     categorySkuCount?: Record<string, number>
       *     // 品相数
       *     skuCount?: number
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     store?: {
       *       // 代码
       *       code?: string
       *       // 实体GID
       *       // [required]
       *       gid: number
       *       // 名称
       *       name?: string
       *     }
       *     // 商品总额
       *     total?: number
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     wms?: GCN
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     wrh?: GCN
       *   }
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      getDraftInfoUsingGET<
        Config extends Alova2MethodConfig<Response_VehSaleDraftInfoDTO_> & {
          params: {
            /**
             * 单据类型
             * [required]
             */
            cls: string;
            /**
             * 业务员Gid
             * [required]
             */
            vehSaleEmpGid: number;
          };
        }
      >(
        config: Config
      ): Alova2Method<Response_VehSaleDraftInfoDTO_, 'vehsaledraftInterface.getDraftInfoUsingGET', Config>;
      /**
       * ---
       *
       * [POST] 删除草稿
       *
       * **path:** /vbs-service/rest/vehsaledraft/draft/remove
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = Array<{
       *   // 单据类型
       *   cls?: string
       *   // 商品Gid
       *   // [required]
       *   gdGid: number
       *   // 车销业务员Gid
       *   // [required]
       *   vehSaleEmpGid: number
       *   // 版本号
       *   // [required]
       *   version: number
       * }>
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // [title] VehSaleDraftResponseDTO
       *   // 草稿返回值
       *   data?: {
       *     // 分类的品相数
       *     categorySkuCount?: Record<string, number>
       *     // 品项数
       *     goodsCount?: number
       *     // 总金额
       *     total?: number
       *     // 版本号
       *     version?: number
       *   }
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      removeDraftUsingPOST<
        Config extends Alova2MethodConfig<Response_VehSaleDraftResponseDTO_> & {
          data: VehSaleRemoveDraftRequestDTO[];
        }
      >(
        config: Config
      ): Alova2Method<Response_VehSaleDraftResponseDTO_, 'vehsaledraftInterface.removeDraftUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 删除选中的数据
       *
       * **path:** /vbs-service/rest/vehsaledraft/draft/remove/checked
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   // 类型
       *   // [required]
       *   cls: string
       *   // 业务员Gid
       *   // [required]
       *   vehSaleEmpGid: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // [title] VehSaleDraftResponseDTO
       *   // 草稿返回值
       *   data?: {
       *     // 分类的品相数
       *     categorySkuCount?: Record<string, number>
       *     // 品项数
       *     goodsCount?: number
       *     // 总金额
       *     total?: number
       *     // 版本号
       *     version?: number
       *   }
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      removeCheckedUsingPOST<
        Config extends Alova2MethodConfig<Response_VehSaleDraftResponseDTO_> & {
          params: {
            /**
             * 类型
             * [required]
             */
            cls: string;
            /**
             * 业务员Gid
             * [required]
             */
            vehSaleEmpGid: number;
          };
        }
      >(
        config: Config
      ): Alova2Method<Response_VehSaleDraftResponseDTO_, 'vehsaledraftInterface.removeCheckedUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 获取草稿
       *
       * **path:** /vbs-service/rest/vehsaledraft/get
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 单据类型
       *   // [required]
       *   cls: string
       *   // 要求返回的部份信息
       *   fetchParts?: string[]
       *   // 关键字
       *   keyword?: string
       *   // 页码，从0开始，默认为0
       *   // [required]
       *   page: number
       *   // 每页条数，默认为50
       *   // [required]
       *   pageSize: number
       *   // 类别代码
       *   sortCode?: string
       *   // 排序条件
       *   sorts?: Array<{
       *     // 排序方式，默认倒叙
       *     asc?: boolean
       *     // 排序字段
       *     // [required]
       *     field: string
       *   }>
       *   // 门店Gid
       *   storeGid?: number
       *   // 业务员Gid
       *   vehSaleEmpGid?: number
       *   // 物流中心
       *   wmsGid?: number
       *   // 仓位Gid
       *   wrhGid?: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:接口调用成功，其他(5000+):正常业务异常
       *   code?: number
       *   // 响应实体
       *   data?: Array<{
       *     // 单据类型
       *     cls?: string
       *     // [title] VehSaleGoodsDTO
       *     // 商品
       *     // [required]
       *     goods: {
       *       // 建议领货数量
       *       advUseSignQty?: number
       *       // 建议领货包装数量
       *       advUseSignQtyStr?: string
       *       // 业务库存数
       *       busInvQty?: number
       *       // 业务库存包装数
       *       busInvQtyStr?: string
       *       // [title] CodeName
       *       // 代码名称对象
       *       category?: {
       *         // 代码
       *         code?: string
       *         // 名称
       *         name?: string
       *       }
       *       // 代码
       *       code?: string
       *       // 商品条码
       *       gdCode?: string
       *       // 实体GID
       *       // [required]
       *       gid: number
       *       // 商品图片
       *       imageDetails?: Array<{
       *         // 文件ID
       *         fileId?: string
       *         // 文件名
       *         fileName?: string
       *         // 文件类型
       *         fileType?: 'img' | 'video'
       *         // 外部URL
       *         fileUrl?: string
       *         // 行号
       *         line?: number
       *       }>
       *       // 最小单位
       *       minMunit?: string
       *       // 单位
       *       munit?: string
       *       // 名称
       *       name?: string
       *       // 规格价
       *       price?: number
       *       // 规格
       *       qpc?: number
       *       // 规格列表
       *       qpcDetails?: Array<{
       *         // 计量单位
       *         munit?: string
       *         // 规格
       *         qpc?: number
       *         // 规格说明
       *         qpcStr?: string
       *       }>
       *       // 规格说明
       *       qpcStr?: string
       *       // 单品价
       *       singlePrice?: number
       *       // 领货单位
       *       useSignMunit?: string
       *       // 车销仓库存数量
       *       vehSaleWrhQty?: number
       *       // 车销仓库存包装数量
       *       vehSaleWrhQtyStr?: string
       *     }
       *     // 规格信息
       *     // [required]
       *     qpcDetails: Array<{
       *       // 单位
       *       // [required]
       *       munit: string
       *       // 规格价
       *       // [required]
       *       price: number
       *       // 规格
       *       // [required]
       *       qpc: number
       *       // 规格说明
       *       // [required]
       *       qpcStr: string
       *       // 数量
       *       // [required]
       *       qty: number
       *       // 包装数量
       *       // [required]
       *       qtyStr: string
       *       // 单品价
       *       // [required]
       *       singlePrice: number
       *       // 金额
       *       // [required]
       *       total: number
       *       // uuid
       *       uuid?: string
       *     }>
       *     // 唯一标识
       *     uuid?: string
       *     // 版本号
       *     // [required]
       *     version: number
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     wms?: {
       *       // 代码
       *       code?: string
       *       // 实体GID
       *       // [required]
       *       gid: number
       *       // 名称
       *       name?: string
       *     }
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     wrh?: GCN
       *   }>
       *   // 输入字段提示。一般为表单提交时，后端对表单进行整体校验，返回给前端相应字段的校验结果。
       *   fields?: Record<string, string>
       *   // 是否还有更多记录，一般只在列表查询时返回。
       *   more?: boolean
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       *   // 请求响应的记录数，一般只在列表查询时返回。
       *   total?: number
       * }
       * ```
       */
      getDraftUsingPOST<
        Config extends Alova2MethodConfig<BaseResponse_List_VehSaleGetDraftGoodsResponseDTO_> & {
          data: VehSaleUseSignDraftQueryFilter;
        }
      >(
        config: Config
      ): Alova2Method<
        BaseResponse_List_VehSaleGetDraftGoodsResponseDTO_,
        'vehsaledraftInterface.getDraftUsingPOST',
        Config
      >;
      /**
       * ---
       *
       * [POST] 获取商品
       *
       * **path:** /vbs-service/rest/vehsaledraft/goods/query
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 单据类型
       *   // [required]
       *   cls: string
       *   // 要求返回的部份信息
       *   fetchParts?: string[]
       *   // 关键字
       *   keyword?: string
       *   // 页码，从0开始，默认为0
       *   // [required]
       *   page: number
       *   // 每页条数，默认为50
       *   // [required]
       *   pageSize: number
       *   // 类别代码
       *   sortCode?: string
       *   // 排序条件
       *   sorts?: Array<{
       *     // 排序方式，默认倒叙
       *     asc?: boolean
       *     // 排序字段
       *     // [required]
       *     field: string
       *   }>
       *   // 门店Gid
       *   storeGid?: number
       *   // 业务员Gid
       *   vehSaleEmpGid?: number
       *   // 物流中心
       *   wmsGid?: number
       *   // 仓位Gid
       *   wrhGid?: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:接口调用成功，其他(5000+):正常业务异常
       *   code?: number
       *   // 响应实体
       *   data?: Array<{
       *     // 单据类型
       *     cls?: string
       *     // [title] VehSaleGoodsDTO
       *     // 商品
       *     // [required]
       *     goods: {
       *       // 建议领货数量
       *       advUseSignQty?: number
       *       // 建议领货包装数量
       *       advUseSignQtyStr?: string
       *       // 业务库存数
       *       busInvQty?: number
       *       // 业务库存包装数
       *       busInvQtyStr?: string
       *       // [title] CodeName
       *       // 代码名称对象
       *       category?: {
       *         // 代码
       *         code?: string
       *         // 名称
       *         name?: string
       *       }
       *       // 代码
       *       code?: string
       *       // 商品条码
       *       gdCode?: string
       *       // 实体GID
       *       // [required]
       *       gid: number
       *       // 商品图片
       *       imageDetails?: Array<{
       *         // 文件ID
       *         fileId?: string
       *         // 文件名
       *         fileName?: string
       *         // 文件类型
       *         fileType?: 'img' | 'video'
       *         // 外部URL
       *         fileUrl?: string
       *         // 行号
       *         line?: number
       *       }>
       *       // 最小单位
       *       minMunit?: string
       *       // 单位
       *       munit?: string
       *       // 名称
       *       name?: string
       *       // 规格价
       *       price?: number
       *       // 规格
       *       qpc?: number
       *       // 规格列表
       *       qpcDetails?: Array<{
       *         // 计量单位
       *         munit?: string
       *         // 规格
       *         qpc?: number
       *         // 规格说明
       *         qpcStr?: string
       *       }>
       *       // 规格说明
       *       qpcStr?: string
       *       // 单品价
       *       singlePrice?: number
       *       // 领货单位
       *       useSignMunit?: string
       *       // 车销仓库存数量
       *       vehSaleWrhQty?: number
       *       // 车销仓库存包装数量
       *       vehSaleWrhQtyStr?: string
       *     }
       *     // 规格信息
       *     // [required]
       *     qpcDetails: Array<{
       *       // 单位
       *       // [required]
       *       munit: string
       *       // 规格价
       *       // [required]
       *       price: number
       *       // 规格
       *       // [required]
       *       qpc: number
       *       // 规格说明
       *       // [required]
       *       qpcStr: string
       *       // 数量
       *       // [required]
       *       qty: number
       *       // 包装数量
       *       // [required]
       *       qtyStr: string
       *       // 单品价
       *       // [required]
       *       singlePrice: number
       *       // 金额
       *       // [required]
       *       total: number
       *       // uuid
       *       uuid?: string
       *     }>
       *     // 唯一标识
       *     uuid?: string
       *     // 版本号
       *     // [required]
       *     version: number
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     wms?: {
       *       // 代码
       *       code?: string
       *       // 实体GID
       *       // [required]
       *       gid: number
       *       // 名称
       *       name?: string
       *     }
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     wrh?: GCN
       *   }>
       *   // 输入字段提示。一般为表单提交时，后端对表单进行整体校验，返回给前端相应字段的校验结果。
       *   fields?: Record<string, string>
       *   // 是否还有更多记录，一般只在列表查询时返回。
       *   more?: boolean
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       *   // 请求响应的记录数，一般只在列表查询时返回。
       *   total?: number
       * }
       * ```
       */
      queryGoodsUsingPOST<
        Config extends Alova2MethodConfig<BaseResponse_List_VehSaleGetDraftGoodsResponseDTO_> & {
          data: VehSaleUseSignDraftQueryFilter;
        }
      >(
        config: Config
      ): Alova2Method<
        BaseResponse_List_VehSaleGetDraftGoodsResponseDTO_,
        'vehsaledraftInterface.queryGoodsUsingPOST',
        Config
      >;
      /**
       * ---
       *
       * [POST] 提交草稿
       *
       * **path:** /vbs-service/rest/vehsaledraft/submit
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 单据类型
       *   // [required]
       *   cls: string
       *   // 商品条码
       *   // [required]
       *   gdCode: string
       *   // [title] GCN
       *   // 包含Gid的基础对象
       *   // [required]
       *   goods: {
       *     // 代码
       *     code?: string
       *     // 实体GID
       *     // [required]
       *     gid: number
       *     // 名称
       *     name?: string
       *   }
       *   // 单位
       *   // [required]
       *   munit: string
       *   // 规格价
       *   // [required]
       *   price: number
       *   // 规格
       *   // [required]
       *   qpc: number
       *   // 规格说明
       *   // [required]
       *   qpcStr: string
       *   // 数量
       *   // [required]
       *   qty: number
       *   // 包装数量
       *   // [required]
       *   qtyStr: string
       *   // 单价
       *   // [required]
       *   singlePrice: number
       *   // [title] GCN
       *   // 包含Gid的基础对象
       *   store?: GCN
       *   // 金额
       *   // [required]
       *   total: number
       *   // uuid
       *   uuid?: string
       *   // [title] GCN
       *   // 包含Gid的基础对象
       *   // [required]
       *   vehSaleEmp: GCN
       *   // 版本号
       *   // [required]
       *   version: number
       *   // [title] GCN
       *   // 包含Gid的基础对象
       *   wms?: GCN
       *   // [title] GCN
       *   // 包含Gid的基础对象
       *   wrh?: GCN
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // [title] VehSaleDraftResponseDTO
       *   // 草稿返回值
       *   data?: {
       *     // 分类的品相数
       *     categorySkuCount?: Record<string, number>
       *     // 品项数
       *     goodsCount?: number
       *     // 总金额
       *     total?: number
       *     // 版本号
       *     version?: number
       *   }
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      submitDraftUsingPOST<
        Config extends Alova2MethodConfig<Response_VehSaleDraftResponseDTO_> & {
          data: VehSaleDraftGoodsSubmitDTO;
        }
      >(
        config: Config
      ): Alova2Method<Response_VehSaleDraftResponseDTO_, 'vehsaledraftInterface.submitDraftUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 提交购物车校验
       *
       * **path:** /vbs-service/rest/vehsaledraft/verifysubmit
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   // 单据类型
       *   // [required]
       *   cls: string
       *   // 车销业务员
       *   // [required]
       *   vehSaleEmpGid: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // 返回数据
       *   data?: Array<{
       *     // 单据类型
       *     cls?: string
       *     // [title] VehSaleGoodsDTO
       *     // 商品
       *     // [required]
       *     goods: {
       *       // 建议领货数量
       *       advUseSignQty?: number
       *       // 建议领货包装数量
       *       advUseSignQtyStr?: string
       *       // 业务库存数
       *       busInvQty?: number
       *       // 业务库存包装数
       *       busInvQtyStr?: string
       *       // [title] CodeName
       *       // 代码名称对象
       *       category?: {
       *         // 代码
       *         code?: string
       *         // 名称
       *         name?: string
       *       }
       *       // 代码
       *       code?: string
       *       // 商品条码
       *       gdCode?: string
       *       // 实体GID
       *       // [required]
       *       gid: number
       *       // 商品图片
       *       imageDetails?: Array<{
       *         // 文件ID
       *         fileId?: string
       *         // 文件名
       *         fileName?: string
       *         // 文件类型
       *         fileType?: 'img' | 'video'
       *         // 外部URL
       *         fileUrl?: string
       *         // 行号
       *         line?: number
       *       }>
       *       // 最小单位
       *       minMunit?: string
       *       // 单位
       *       munit?: string
       *       // 名称
       *       name?: string
       *       // 规格价
       *       price?: number
       *       // 规格
       *       qpc?: number
       *       // 规格列表
       *       qpcDetails?: Array<{
       *         // 计量单位
       *         munit?: string
       *         // 规格
       *         qpc?: number
       *         // 规格说明
       *         qpcStr?: string
       *       }>
       *       // 规格说明
       *       qpcStr?: string
       *       // 单品价
       *       singlePrice?: number
       *       // 领货单位
       *       useSignMunit?: string
       *       // 车销仓库存数量
       *       vehSaleWrhQty?: number
       *       // 车销仓库存包装数量
       *       vehSaleWrhQtyStr?: string
       *     }
       *     // 规格信息
       *     // [required]
       *     qpcDetails: Array<{
       *       // 单位
       *       // [required]
       *       munit: string
       *       // 规格价
       *       // [required]
       *       price: number
       *       // 规格
       *       // [required]
       *       qpc: number
       *       // 规格说明
       *       // [required]
       *       qpcStr: string
       *       // 数量
       *       // [required]
       *       qty: number
       *       // 包装数量
       *       // [required]
       *       qtyStr: string
       *       // 单品价
       *       // [required]
       *       singlePrice: number
       *       // 金额
       *       // [required]
       *       total: number
       *       // uuid
       *       uuid?: string
       *     }>
       *     // 唯一标识
       *     uuid?: string
       *     // 版本号
       *     // [required]
       *     version: number
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     wms?: {
       *       // 代码
       *       code?: string
       *       // 实体GID
       *       // [required]
       *       gid: number
       *       // 名称
       *       name?: string
       *     }
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     wrh?: GCN
       *   }>
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      verifySubmitUsingPOST<
        Config extends Alova2MethodConfig<Response_List_VehSaleGetDraftGoodsResponseDTO_> & {
          params: {
            /**
             * 单据类型
             * [required]
             */
            cls: string;
            /**
             * 车销业务员
             * [required]
             */
            vehSaleEmpGid: number;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        Response_List_VehSaleGetDraftGoodsResponseDTO_,
        'vehsaledraftInterface.verifySubmitUsingPOST',
        Config
      >;
    };
    vehsaleusesignInterface: {
      /**
       * ---
       *
       * [POST] 作废单据
       *
       * **path:** /vbs-service/rest/vehsaleusesign/abort
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 单号
       *   // [required]
       *   num: string
       *   // 版本号
       *   // [required]
       *   version: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      abortUsingPOST_4<
        Config extends Alova2MethodConfig<Response_Void_> & {
          data: BillBaseRequestDTO;
        }
      >(
        config: Config
      ): Alova2Method<Response_Void_, 'vehsaleusesignInterface.abortUsingPOST_4', Config>;
      /**
       * ---
       *
       * [POST] 审核单据
       *
       * **path:** /vbs-service/rest/vehsaleusesign/audit
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 单据审核请求参数明细
       *   details?: Array<{
       *     // 商品GId
       *     // [required]
       *     gdGid: number
       *     // 规格价
       *     price?: number
       *     // 规格
       *     // [required]
       *     qpc: number
       *     // 规格描述
       *     // [required]
       *     qpcStr: string
       *     // 单品数量
       *     qty?: number
       *   }>
       *   // 单号
       *   // [required]
       *   num: string
       *   // 版本号
       *   // [required]
       *   version: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      auditUsingPOST_3<
        Config extends Alova2MethodConfig<Response_Void_> & {
          data: BillAuditRequestDTO;
        }
      >(
        config: Config
      ): Alova2Method<Response_Void_, 'vehsaleusesignInterface.auditUsingPOST_3', Config>;
      /**
       * ---
       *
       * [POST] 复制单据
       *
       * **path:** /vbs-service/rest/vehsaleusesign/copy
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   // 单号
       *   // [required]
       *   num: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // 返回数据
       *   data?: boolean
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      copyUsingPOST<
        Config extends Alova2MethodConfig<Response_boolean_> & {
          params: {
            /**
             * 单号
             * [required]
             */
            num: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<Response_boolean_, 'vehsaleusesignInterface.copyUsingPOST', Config>;
      /**
       * ---
       *
       * [GET] 获取单据详情
       *
       * **path:** /vbs-service/rest/vehsaleusesign/get
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   // 是否查询明细
       *   fetchDetail?: boolean
       *   // 单号
       *   // [required]
       *   num: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // [title] VehSaleUseSignResponseDTO
       *   // 获取车销领货单
       *   data?: {
       *     // 附件明细
       *     attachDetails?: Array<{
       *       // 附件标识
       *       fileId?: string
       *       // 附件名称
       *       fileName?: string
       *       // 附件路径
       *       fileUrl?: string
       *       // 行号
       *       line?: number
       *       // 备注
       *       note?: string
       *     }>
       *     // 车牌号
       *     carNumber?: string
       *     // 商品明细
       *     details?: Array<{
       *       // 申请数量
       *       applyQty?: number
       *       // [title] CodeName
       *       // 代码名称对象
       *       category?: {
       *         // 代码
       *         code?: string
       *         // 名称
       *         name?: string
       *       }
       *       // 默认领货单位
       *       defUseSignMunit?: string
       *       // 默认领货规格
       *       defUseSignQpc?: number
       *       // 默认领货包装规格
       *       defUseSignQpcStr?: string
       *       // 商品条码
       *       gdCode?: string
       *       // [title] GCN
       *       // 包含Gid的基础对象
       *       goods?: {
       *         // 代码
       *         code?: string
       *         // 实体GID
       *         // [required]
       *         gid: number
       *         // 名称
       *         name?: string
       *       }
       *       // 图片
       *       imageDetails?: Array<{
       *         // 文件ID
       *         fileId?: string
       *         // 文件名
       *         fileName?: string
       *         // 文件类型
       *         fileType?: 'img' | 'video'
       *         // 外部URL
       *         fileUrl?: string
       *         // 行号
       *         line?: number
       *       }>
       *       // 最小单位
       *       minMunit?: string
       *       // 备注
       *       note?: string
       *       // 规格明细
       *       qpcDetails?: Array<{
       *         // 申请数量
       *         applyQty?: number
       *         // 申请包装数量
       *         applyQtyStr?: string
       *         // 单位
       *         munit?: string
       *         // 规格价
       *         price?: number
       *         // 规格
       *         qpc?: number
       *         // 规格描述
       *         qpcStr?: string
       *         // 确认数量
       *         qty?: number
       *         // 确认包装数量
       *         qtyStr?: string
       *         // 含税金额
       *         total?: number
       *       }>
       *       // 确认数量
       *       qty?: number
       *       // 车销仓库存数量
       *       vehSaleWrhQty?: number
       *       // 车销仓库存包装数量
       *       vehSaleWrhQtyStr?: string
       *     }>
       *     // 创建时间
       *     filDate?: string
       *     // 创建人
       *     filler?: string
       *     // 商品种类
       *     goodsCount?: number
       *     // 最后修改人
       *     lastModifyOper?: string
       *     // 最后修改时间
       *     lstupdTime?: string
       *     // 备注
       *     note?: string
       *     // 单号
       *     num?: string
       *     // 发生日期
       *     ocrDate?: string
       *     // 总数量
       *     qty?: number
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     sender?: GCN
       *     // 单据状态
       *     stat?: number
       *     // 含税总金额
       *     total?: number
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     vehSaleEmp?: GCN
       *     // 业务员姓名
       *     vehSaleEmpName?: string
       *     // 版本号
       *     version?: number
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     wrh?: GCN
       *   }
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      getUsingGET_5<
        Config extends Alova2MethodConfig<Response_VehSaleUseSignResponseDTO_> & {
          params: {
            /**
             * 是否查询明细
             */
            fetchDetail?: boolean;
            /**
             * 单号
             * [required]
             */
            num: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<Response_VehSaleUseSignResponseDTO_, 'vehsaleusesignInterface.getUsingGET_5', Config>;
      /**
       * ---
       *
       * [POST] 合并审核
       *
       * **path:** /vbs-service/rest/vehsaleusesign/merge/audit
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 单号列表
       *   bills?: Array<{
       *     // 单号
       *     // [required]
       *     num: string
       *     // 版本号
       *     // [required]
       *     version: number
       *   }>
       *   // 单据审核请求参数明细
       *   details?: Array<{
       *     // 商品GId
       *     // [required]
       *     gdGid: number
       *     // 规格价
       *     price?: number
       *     // 规格
       *     // [required]
       *     qpc: number
       *     // 规格描述
       *     // [required]
       *     qpcStr: string
       *     // 单品数量
       *     qty?: number
       *   }>
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      mergeAuditUsingPOST<
        Config extends Alova2MethodConfig<Response_Void_> & {
          data: BillMargeAuditRequestDTO;
        }
      >(
        config: Config
      ): Alova2Method<Response_Void_, 'vehsaleusesignInterface.mergeAuditUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 合并审核查询-包含校验
       *
       * **path:** /vbs-service/rest/vehsaleusesign/merge/audit/query
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = string[]
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // [title] VehSaleUseSignMergeResponseDTO
       *   data?: {
       *     // 单据列表
       *     bills?: Array<{
       *       // 单号
       *       // [required]
       *       num: string
       *       // 版本号
       *       // [required]
       *       version: number
       *     }>
       *     // 商品种类
       *     goodsCount?: number
       *     // 总数量
       *     qty?: number
       *     // 含税总金额
       *     total?: number
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     vehSaleEmp?: {
       *       // 代码
       *       code?: string
       *       // 实体GID
       *       // [required]
       *       gid: number
       *       // 名称
       *       name?: string
       *     }
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     wrh?: GCN
       *   }
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      mergeAuditQueryUsingPOST<
        Config extends Alova2MethodConfig<Response_VehSaleUseSignMergeResponseDTO_> & {
          data: string[];
        }
      >(
        config: Config
      ): Alova2Method<
        Response_VehSaleUseSignMergeResponseDTO_,
        'vehsaleusesignInterface.mergeAuditQueryUsingPOST',
        Config
      >;
      /**
       * ---
       *
       * [POST] 列表查询
       *
       * **path:** /vbs-service/rest/vehsaleusesign/query
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 开始日期
       *   beginDate?: string
       *   // 要求返回的部份信息
       *   fetchParts?: string[]
       *   // 结束日期
       *   finishDate?: string
       *   // 关键字
       *   keyword?: string
       *   // 单号
       *   num?: string
       *   // 页码，从0开始，默认为0
       *   // [required]
       *   page: number
       *   // 每页条数，默认为50
       *   // [required]
       *   pageSize: number
       *   // 排序条件
       *   sorts?: Array<{
       *     // 排序方式，默认倒叙
       *     asc?: boolean
       *     // 排序字段
       *     // [required]
       *     field: string
       *   }>
       *   // 状态
       *   stat?: 100 | 110 | 1300 | 1310
       *   // 状态列表
       *   stats?: number[]
       *   // 业务员Gid
       *   vehSaleEmpGid?: number
       *   // 物流中心
       *   wmsGid?: number
       *   // 发货仓位Gid
       *   wrhGid?: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:接口调用成功，其他(5000+):正常业务异常
       *   code?: number
       *   // 响应实体
       *   data?: Array<{
       *     // 商品种类
       *     goodsCount?: number
       *     // 最后修改时间
       *     lstupdTime?: string
       *     // 单号
       *     num?: string
       *     // 总数量
       *     qty?: number
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     sender?: {
       *       // 代码
       *       code?: string
       *       // 实体GID
       *       // [required]
       *       gid: number
       *       // 名称
       *       name?: string
       *     }
       *     // 单据状态
       *     stat?: number
       *     // 含税总金额
       *     total?: number
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     vehSaleEmp?: GCN
       *     // 业务员姓名
       *     vehSaleEmpName?: string
       *     // 版本号
       *     version?: number
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     wrh?: GCN
       *   }>
       *   // 输入字段提示。一般为表单提交时，后端对表单进行整体校验，返回给前端相应字段的校验结果。
       *   fields?: Record<string, string>
       *   // 是否还有更多记录，一般只在列表查询时返回。
       *   more?: boolean
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       *   // 请求响应的记录数，一般只在列表查询时返回。
       *   total?: number
       * }
       * ```
       */
      queryUsingPOST_5<
        Config extends Alova2MethodConfig<BaseResponse_List_VehSaleUseSignQueryResponseDTO_> & {
          data: VehSaleUseSignQueryFilter;
        }
      >(
        config: Config
      ): Alova2Method<
        BaseResponse_List_VehSaleUseSignQueryResponseDTO_,
        'vehsaleusesignInterface.queryUsingPOST_5',
        Config
      >;
      /**
       * ---
       *
       * [POST] 获取单据明细详情
       *
       * **path:** /vbs-service/rest/vehsaleusesign/query/details
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // [title] Category
       *   // 分类
       *   category?: {
       *     // 代码
       *     // [required]
       *     code: string
       *     // 名称
       *     name?: string
       *   }
       *   // 要求返回的部份信息
       *   fetchParts?: string[]
       *   // 单号
       *   // [required]
       *   num: string
       *   // 页码，从0开始，默认为0
       *   // [required]
       *   page: number
       *   // 每页条数，默认为50
       *   // [required]
       *   pageSize: number
       *   // 排序条件
       *   sorts?: Array<{
       *     // 排序方式，默认倒叙
       *     asc?: boolean
       *     // 排序字段
       *     // [required]
       *     field: string
       *   }>
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:接口调用成功，其他(5000+):正常业务异常
       *   code?: number
       *   // 响应实体
       *   data?: Array<{
       *     // 申请数量
       *     applyQty?: number
       *     // [title] CodeName
       *     // 代码名称对象
       *     category?: {
       *       // 代码
       *       code?: string
       *       // 名称
       *       name?: string
       *     }
       *     // 默认领货单位
       *     defUseSignMunit?: string
       *     // 默认领货规格
       *     defUseSignQpc?: number
       *     // 默认领货包装规格
       *     defUseSignQpcStr?: string
       *     // 商品条码
       *     gdCode?: string
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     goods?: {
       *       // 代码
       *       code?: string
       *       // 实体GID
       *       // [required]
       *       gid: number
       *       // 名称
       *       name?: string
       *     }
       *     // 图片
       *     imageDetails?: Array<{
       *       // 文件ID
       *       fileId?: string
       *       // 文件名
       *       fileName?: string
       *       // 文件类型
       *       fileType?: 'img' | 'video'
       *       // 外部URL
       *       fileUrl?: string
       *       // 行号
       *       line?: number
       *     }>
       *     // 最小单位
       *     minMunit?: string
       *     // 备注
       *     note?: string
       *     // 规格明细
       *     qpcDetails?: Array<{
       *       // 申请数量
       *       applyQty?: number
       *       // 申请包装数量
       *       applyQtyStr?: string
       *       // 单位
       *       munit?: string
       *       // 规格价
       *       price?: number
       *       // 规格
       *       qpc?: number
       *       // 规格描述
       *       qpcStr?: string
       *       // 确认数量
       *       qty?: number
       *       // 确认包装数量
       *       qtyStr?: string
       *       // 含税金额
       *       total?: number
       *     }>
       *     // 确认数量
       *     qty?: number
       *     // 车销仓库存数量
       *     vehSaleWrhQty?: number
       *     // 车销仓库存包装数量
       *     vehSaleWrhQtyStr?: string
       *   }>
       *   // 输入字段提示。一般为表单提交时，后端对表单进行整体校验，返回给前端相应字段的校验结果。
       *   fields?: Record<string, string>
       *   // 是否还有更多记录，一般只在列表查询时返回。
       *   more?: boolean
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       *   // 请求响应的记录数，一般只在列表查询时返回。
       *   total?: number
       * }
       * ```
       */
      queryDetailsUsingPOST_3<
        Config extends Alova2MethodConfig<BaseResponse_List_VehSaleUseSignDtlResponseDTO_> & {
          data: BillDtlQueryFilter;
        }
      >(
        config: Config
      ): Alova2Method<
        BaseResponse_List_VehSaleUseSignDtlResponseDTO_,
        'vehsaleusesignInterface.queryDetailsUsingPOST_3',
        Config
      >;
      /**
       * ---
       *
       * [POST] 提交单据
       *
       * **path:** /vbs-service/rest/vehsaleusesign/submit
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 单据附件提交明细
       *   attachDetails?: Array<{
       *     // 附件标识
       *     fileId?: string
       *     // 附件名称
       *     fileName?: string
       *     // 附件路径
       *     fileUrl?: string
       *   }>
       *   // 备注
       *   note?: string
       *   // [title] GCN
       *   // 包含Gid的基础对象
       *   vehSaleEmp?: {
       *     // 代码
       *     code?: string
       *     // 实体GID
       *     // [required]
       *     gid: number
       *     // 名称
       *     name?: string
       *   }
       *   // [title] GCN
       *   // 包含Gid的基础对象
       *   wrh?: GCN
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // 返回数据
       *   data?: string
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      submitUsingPOST_4<
        Config extends Alova2MethodConfig<Response_string_> & {
          data: VehSaleUseSignSubmitRequestDTO;
        }
      >(
        config: Config
      ): Alova2Method<Response_string_, 'vehsaleusesignInterface.submitUsingPOST_4', Config>;
      /**
       * ---
       *
       * [POST] 新建单据前校验
       *
       * **path:** /vbs-service/rest/vehsaleusesign/verify/create
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   // 车销业务员Gid
       *   // [required]
       *   vehSaleEmpGid: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // 返回数据
       *   data?: boolean
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      verifyCreateUsingPOST_3<
        Config extends Alova2MethodConfig<Response_boolean_> & {
          params: {
            /**
             * 车销业务员Gid
             * [required]
             */
            vehSaleEmpGid: number;
          };
        }
      >(
        config: Config
      ): Alova2Method<Response_boolean_, 'vehsaleusesignInterface.verifyCreateUsingPOST_3', Config>;
    };
    vehsaleusesignarvInterface: {
      /**
       * ---
       *
       * [POST] 作废单据
       *
       * **path:** /vbs-service/rest/vehsaleusesignarv/abort
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 单号
       *   // [required]
       *   num: string
       *   // 版本号
       *   // [required]
       *   version: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      abortUsingPOST_2<
        Config extends Alova2MethodConfig<Response_Void_> & {
          data: BillBaseRequestDTO;
        }
      >(
        config: Config
      ): Alova2Method<Response_Void_, 'vehsaleusesignarvInterface.abortUsingPOST_2', Config>;
      /**
       * ---
       *
       * [GET] 获取可回货商品
       *
       * **path:** /vbs-service/rest/vehsaleusesignarv/arvgoods/get
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // 返回数据
       *   data?: Array<{
       *     // 建议领货数量
       *     advUseSignQty?: number
       *     // 建议领货包装数量
       *     advUseSignQtyStr?: string
       *     // 业务库存数
       *     busInvQty?: number
       *     // 业务库存包装数
       *     busInvQtyStr?: string
       *     // [title] CodeName
       *     // 代码名称对象
       *     category?: {
       *       // 代码
       *       code?: string
       *       // 名称
       *       name?: string
       *     }
       *     // 代码
       *     code?: string
       *     // 商品条码
       *     gdCode?: string
       *     // 实体GID
       *     // [required]
       *     gid: number
       *     // 商品图片
       *     imageDetails?: Array<{
       *       // 文件ID
       *       fileId?: string
       *       // 文件名
       *       fileName?: string
       *       // 文件类型
       *       fileType?: 'img' | 'video'
       *       // 外部URL
       *       fileUrl?: string
       *       // 行号
       *       line?: number
       *     }>
       *     // 最小单位
       *     minMunit?: string
       *     // 单位
       *     munit?: string
       *     // 名称
       *     name?: string
       *     // 规格价
       *     price?: number
       *     // 规格
       *     qpc?: number
       *     // 规格列表
       *     qpcDetails?: Array<{
       *       // 计量单位
       *       munit?: string
       *       // 规格
       *       qpc?: number
       *       // 规格说明
       *       qpcStr?: string
       *     }>
       *     // 规格说明
       *     qpcStr?: string
       *     // 单品价
       *     singlePrice?: number
       *     // 领货单位
       *     useSignMunit?: string
       *     // 车销仓库存数量
       *     vehSaleWrhQty?: number
       *     // 车销仓库存包装数量
       *     vehSaleWrhQtyStr?: string
       *   }>
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      getArvGoodsUsingGET<Config extends Alova2MethodConfig<Response_List_VehSaleGoodsDTO_>>(
        config?: Config
      ): Alova2Method<Response_List_VehSaleGoodsDTO_, 'vehsaleusesignarvInterface.getArvGoodsUsingGET', Config>;
      /**
       * ---
       *
       * [GET] 是否存在可回货商品
       *
       * **path:** /vbs-service/rest/vehsaleusesignarv/arvgoods/has
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // 返回数据
       *   data?: boolean
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      hasArvGoodsUsingGET<Config extends Alova2MethodConfig<Response_boolean_>>(
        config?: Config
      ): Alova2Method<Response_boolean_, 'vehsaleusesignarvInterface.hasArvGoodsUsingGET', Config>;
      /**
       * ---
       *
       * [POST] 审核单据
       *
       * **path:** /vbs-service/rest/vehsaleusesignarv/audit
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 单据审核请求参数明细
       *   details?: Array<{
       *     // 商品GId
       *     // [required]
       *     gdGid: number
       *     // 规格价
       *     price?: number
       *     // 规格
       *     // [required]
       *     qpc: number
       *     // 规格描述
       *     // [required]
       *     qpcStr: string
       *     // 单品数量
       *     // [required]
       *     qty: number
       *     // 兑奖物数量
       *     // [required]
       *     tgpQty: number
       *   }>
       *   // 单号
       *   // [required]
       *   num: string
       *   // 版本号
       *   // [required]
       *   version: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      auditUsingPOST<
        Config extends Alova2MethodConfig<Response_Void_> & {
          data: VehSaleUseSignArvAuditRequestDTO;
        }
      >(
        config: Config
      ): Alova2Method<Response_Void_, 'vehsaleusesignarvInterface.auditUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 自动回货
       *
       * **path:** /vbs-service/rest/vehsaleusesignarv/autoarv
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      autoArvUsingPOST<Config extends Alova2MethodConfig<Response_Void_>>(
        config?: Config
      ): Alova2Method<Response_Void_, 'vehsaleusesignarvInterface.autoArvUsingPOST', Config>;
      /**
       * ---
       *
       * [GET] 获取单据详情
       *
       * **path:** /vbs-service/rest/vehsaleusesignarv/get
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   // 是否查询明细
       *   fetchDetail?: boolean
       *   // 单号
       *   // [required]
       *   num: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // [title] VehSaleUseSignArvResponseDTO
       *   // 获取车销回货单
       *   data?: {
       *     // 附件明细
       *     attachDetails?: Array<{
       *       // 附件标识
       *       fileId?: string
       *       // 附件名称
       *       fileName?: string
       *       // 附件路径
       *       fileUrl?: string
       *       // 行号
       *       line?: number
       *       // 备注
       *       note?: string
       *     }>
       *     // 车牌号
       *     carNumber?: string
       *     // 商品明细
       *     details?: Array<{
       *       // [title] CodeName
       *       // 代码名称对象
       *       category?: {
       *         // 代码
       *         code?: string
       *         // 名称
       *         name?: string
       *       }
       *       // 商品条码
       *       gdCode?: string
       *       // [title] GCN
       *       // 包含Gid的基础对象
       *       goods?: {
       *         // 代码
       *         code?: string
       *         // 实体GID
       *         // [required]
       *         gid: number
       *         // 名称
       *         name?: string
       *       }
       *       // 图片
       *       imageDetails?: Array<{
       *         // 文件ID
       *         fileId?: string
       *         // 文件名
       *         fileName?: string
       *         // 文件类型
       *         fileType?: 'img' | 'video'
       *         // 外部URL
       *         fileUrl?: string
       *         // 行号
       *         line?: number
       *       }>
       *       // 行号
       *       line?: number
       *       // 最小单位
       *       minMunit?: string
       *       // 单位
       *       munit?: string
       *       // 备注
       *       note?: string
       *       // 规格价
       *       price?: number
       *       // 规格
       *       qpc?: number
       *       // 规格描述
       *       qpcStr?: string
       *       // 确认数量
       *       qty?: number
       *       // 确认包装数量
       *       qtyStr?: string
       *       // 应回数量
       *       shouldQty?: number
       *       // 应回包装数量
       *       shouldQtyStr?: string
       *       // 兑奖物数量
       *       tgpQty?: number
       *       // 兑奖物包装数量
       *       tgpQtyStr?: string
       *       // 含税金额
       *       total?: number
       *     }>
       *     // 差异项数
       *     diffCount?: number
       *     // 差异总金额
       *     diffTotal?: number
       *     // 创建时间
       *     filDate?: string
       *     // 创建人
       *     filler?: string
       *     // 生成单据明细
       *     genBillDetails?: Array<{
       *       // 生成单据类型
       *       genCls?: string
       *       // 生成单据单号
       *       genNum?: string
       *       // 生成单据时间
       *       genTime?: string
       *     }>
       *     // 商品种类
       *     goodsCount?: number
       *     // 最后修改人
       *     lastModifyOper?: string
       *     // 最后修改时间
       *     lstupdTime?: string
       *     // 备注
       *     note?: string
       *     // 单号
       *     num?: string
       *     // 发生日期
       *     ocrDate?: string
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     receiver?: GCN
       *     // 单据状态
       *     stat?: number
       *     // 回货总金额
       *     total?: number
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     vehSaleEmp?: GCN
       *     // 车销业务员姓名
       *     vehSaleEmpName?: string
       *     // 版本号
       *     version?: number
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     wrh?: GCN
       *   }
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      getUsingGET_2<
        Config extends Alova2MethodConfig<Response_VehSaleUseSignArvResponseDTO_> & {
          params: {
            /**
             * 是否查询明细
             */
            fetchDetail?: boolean;
            /**
             * 单号
             * [required]
             */
            num: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<Response_VehSaleUseSignArvResponseDTO_, 'vehsaleusesignarvInterface.getUsingGET_2', Config>;
      /**
       * ---
       *
       * [POST] 获取单据明细详情
       *
       * **path:** /vbs-service/rest/vehsaleusesignarv/get/details
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // [title] Category
       *   // 分类
       *   category?: {
       *     // 代码
       *     // [required]
       *     code: string
       *     // 名称
       *     name?: string
       *   }
       *   // 要求返回的部份信息
       *   fetchParts?: string[]
       *   // 单号
       *   // [required]
       *   num: string
       *   // 页码，从0开始，默认为0
       *   // [required]
       *   page: number
       *   // 每页条数，默认为50
       *   // [required]
       *   pageSize: number
       *   // 排序条件
       *   sorts?: Array<{
       *     // 排序方式，默认倒叙
       *     asc?: boolean
       *     // 排序字段
       *     // [required]
       *     field: string
       *   }>
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:接口调用成功，其他(5000+):正常业务异常
       *   code?: number
       *   // 响应实体
       *   data?: Array<{
       *     // [title] CodeName
       *     // 代码名称对象
       *     category?: {
       *       // 代码
       *       code?: string
       *       // 名称
       *       name?: string
       *     }
       *     // 商品条码
       *     gdCode?: string
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     goods?: {
       *       // 代码
       *       code?: string
       *       // 实体GID
       *       // [required]
       *       gid: number
       *       // 名称
       *       name?: string
       *     }
       *     // 图片
       *     imageDetails?: Array<{
       *       // 文件ID
       *       fileId?: string
       *       // 文件名
       *       fileName?: string
       *       // 文件类型
       *       fileType?: 'img' | 'video'
       *       // 外部URL
       *       fileUrl?: string
       *       // 行号
       *       line?: number
       *     }>
       *     // 行号
       *     line?: number
       *     // 最小单位
       *     minMunit?: string
       *     // 单位
       *     munit?: string
       *     // 备注
       *     note?: string
       *     // 规格价
       *     price?: number
       *     // 规格
       *     qpc?: number
       *     // 规格描述
       *     qpcStr?: string
       *     // 确认数量
       *     qty?: number
       *     // 确认包装数量
       *     qtyStr?: string
       *     // 应回数量
       *     shouldQty?: number
       *     // 应回包装数量
       *     shouldQtyStr?: string
       *     // 兑奖物数量
       *     tgpQty?: number
       *     // 兑奖物包装数量
       *     tgpQtyStr?: string
       *     // 含税金额
       *     total?: number
       *   }>
       *   // 输入字段提示。一般为表单提交时，后端对表单进行整体校验，返回给前端相应字段的校验结果。
       *   fields?: Record<string, string>
       *   // 是否还有更多记录，一般只在列表查询时返回。
       *   more?: boolean
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       *   // 请求响应的记录数，一般只在列表查询时返回。
       *   total?: number
       * }
       * ```
       */
      getDetailsUsingPOST<
        Config extends Alova2MethodConfig<BaseResponse_List_VehSaleUseSignArvDtlResponseDTO_> & {
          data: BillDtlQueryFilter;
        }
      >(
        config: Config
      ): Alova2Method<
        BaseResponse_List_VehSaleUseSignArvDtlResponseDTO_,
        'vehsaleusesignarvInterface.getDetailsUsingPOST',
        Config
      >;
      /**
       * ---
       *
       * [POST] 列表查询
       *
       * **path:** /vbs-service/rest/vehsaleusesignarv/query
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 开始日期
       *   beginDate?: string
       *   // 要求返回的部份信息
       *   fetchParts?: string[]
       *   // 结束日期
       *   finishDate?: string
       *   // 关键字
       *   keyword?: string
       *   // 单号
       *   num?: string
       *   // 页码，从0开始，默认为0
       *   // [required]
       *   page: number
       *   // 每页条数，默认为50
       *   // [required]
       *   pageSize: number
       *   // 排序条件
       *   sorts?: Array<{
       *     // 排序方式，默认倒叙
       *     asc?: boolean
       *     // 排序字段
       *     // [required]
       *     field: string
       *   }>
       *   // 状态
       *   stat?: 100 | 1300 | 1310
       *   // 状态列表
       *   stats?: number[]
       *   // 业务员Gid
       *   vehSaleEmpGid?: number
       *   // 物流中心
       *   wmsGid?: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:接口调用成功，其他(5000+):正常业务异常
       *   code?: number
       *   // 响应实体
       *   data?: Array<{
       *     // 商品种类
       *     goodsCount?: number
       *     // 最后修改时间
       *     lstupdTime?: string
       *     // 单号
       *     num?: string
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     receiver?: {
       *       // 代码
       *       code?: string
       *       // 实体GID
       *       // [required]
       *       gid: number
       *       // 名称
       *       name?: string
       *     }
       *     // 单据状态
       *     stat?: number
       *     // 总金额
       *     total?: number
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     vehSaleEmp?: GCN
       *     vehSaleEmpName?: string
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     wrh?: GCN
       *   }>
       *   // 输入字段提示。一般为表单提交时，后端对表单进行整体校验，返回给前端相应字段的校验结果。
       *   fields?: Record<string, string>
       *   // 是否还有更多记录，一般只在列表查询时返回。
       *   more?: boolean
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       *   // 请求响应的记录数，一般只在列表查询时返回。
       *   total?: number
       * }
       * ```
       */
      queryUsingPOST_2<
        Config extends Alova2MethodConfig<BaseResponse_List_VehSaleUseSignArvQueryResponseDTO_> & {
          data: VehSaleUseSignArvQueryFilter;
        }
      >(
        config: Config
      ): Alova2Method<
        BaseResponse_List_VehSaleUseSignArvQueryResponseDTO_,
        'vehsaleusesignarvInterface.queryUsingPOST_2',
        Config
      >;
      /**
       * ---
       *
       * [POST] 提交单据
       *
       * **path:** /vbs-service/rest/vehsaleusesignarv/submit
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 单据附件提交明细
       *   attachDetails?: Array<{
       *     // 附件标识
       *     fileId?: string
       *     // 附件名称
       *     fileName?: string
       *     // 附件路径
       *     fileUrl?: string
       *   }>
       *   // 商品明细
       *   details?: Array<{
       *     // 车销商品
       *     // [required]
       *     gdGid: number
       *     // 兑奖物数量
       *     // [required]
       *     tgpQty: number
       *   }>
       *   // 备注
       *   note?: string
       *   // [title] GCN
       *   // 包含Gid的基础对象
       *   vehSaleEmp?: {
       *     // 代码
       *     code?: string
       *     // 实体GID
       *     // [required]
       *     gid: number
       *     // 名称
       *     name?: string
       *   }
       *   // [title] GCN
       *   // 包含Gid的基础对象
       *   wrh?: GCN
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // 返回数据
       *   data?: string
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      submitUsingPOST_2<
        Config extends Alova2MethodConfig<Response_string_> & {
          data: VehSaleUseSignArvSubmitRequestDTO;
        }
      >(
        config: Config
      ): Alova2Method<Response_string_, 'vehsaleusesignarvInterface.submitUsingPOST_2', Config>;
      /**
       * ---
       *
       * [POST] 回货前校验
       *
       * **path:** /vbs-service/rest/vehsaleusesignarv/verify
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // [title] VehSaleUseSignArvVerifyResponseDTO
       *   // 车销回货单校验结果
       *   data?: {
       *     // 单据编号
       *     nums?: string[]
       *     // 门店GID
       *     storeGid?: number
       *   }
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      verifyUsingPOST<Config extends Alova2MethodConfig<Response_VehSaleUseSignArvVerifyResponseDTO_>>(
        config?: Config
      ): Alova2Method<
        Response_VehSaleUseSignArvVerifyResponseDTO_,
        'vehsaleusesignarvInterface.verifyUsingPOST',
        Config
      >;
    };
    vehsaleusesignarvdiffInterface: {
      /**
       * ---
       *
       * [POST] 审核单据
       *
       * **path:** /vbs-service/rest/vehsaleusesignarvdiff/audit
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 单据审核请求参数明细
       *   details?: Array<{
       *     // 商品GId
       *     // [required]
       *     gdGid: number
       *     // 规格价
       *     price?: number
       *     // 规格
       *     // [required]
       *     qpc: number
       *     // 规格描述
       *     // [required]
       *     qpcStr: string
       *     // 单品数量
       *     qty?: number
       *   }>
       *   // 单号
       *   // [required]
       *   num: string
       *   // 版本号
       *   // [required]
       *   version: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      auditUsingPOST_1<
        Config extends Alova2MethodConfig<Response_Void_> & {
          data: BillAuditRequestDTO;
        }
      >(
        config: Config
      ): Alova2Method<Response_Void_, 'vehsaleusesignarvdiffInterface.auditUsingPOST_1', Config>;
      /**
       * ---
       *
       * [POST] 是否存差异数据
       *
       * **path:** /vbs-service/rest/vehsaleusesignarvdiff/bill/has
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 开始日期
       *   beginDate?: string
       *   // 结束日期
       *   finishDate?: string
       *   // 物流中心
       *   wms?: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // 返回数据
       *   data?: boolean
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      hasDiffDataUsingPOST<
        Config extends Alova2MethodConfig<Response_boolean_> & {
          data: VehSaleUseSignArvDiffHasFilter;
        }
      >(
        config: Config
      ): Alova2Method<Response_boolean_, 'vehsaleusesignarvdiffInterface.hasDiffDataUsingPOST', Config>;
      /**
       * ---
       *
       * [GET] 获取单据详情
       *
       * **path:** /vbs-service/rest/vehsaleusesignarvdiff/get
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   // 是否查询明细
       *   fetchDetail?: boolean
       *   // 单号
       *   // [required]
       *   num: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // [title] VehSaleUseSignArvDiffResponseDTO
       *   // 获取车销回货差异单
       *   data?: {
       *     // 车牌号
       *     carNumber?: string
       *     // 商品明细
       *     details?: Array<{
       *       // [title] CodeName
       *       // 代码名称对象
       *       category?: {
       *         // 代码
       *         code?: string
       *         // 名称
       *         name?: string
       *       }
       *       // 商品条码
       *       gdCode?: string
       *       // [title] GCN
       *       // 包含Gid的基础对象
       *       goods?: {
       *         // 代码
       *         code?: string
       *         // 实体GID
       *         // [required]
       *         gid: number
       *         // 名称
       *         name?: string
       *       }
       *       // 图片
       *       imageDetails?: Array<{
       *         // 文件ID
       *         fileId?: string
       *         // 文件名
       *         fileName?: string
       *         // 文件类型
       *         fileType?: 'img' | 'video'
       *         // 外部URL
       *         fileUrl?: string
       *         // 行号
       *         line?: number
       *       }>
       *       // 行号
       *       line?: number
       *       // 最小单位
       *       minMunit?: string
       *       // 单位
       *       munit?: string
       *       // 备注
       *       note?: string
       *       // 规格价
       *       price?: number
       *       // 已处理差异数量
       *       procQty?: number
       *       // 已处理差异包装数量
       *       procQtyStr?: string
       *       // 规格
       *       qpc?: number
       *       // 规格描述
       *       qpcStr?: string
       *       // 差异数量
       *       qty?: number
       *       // 差异包装数量
       *       qtyStr?: string
       *       // 含税金额
       *       total?: number
       *     }>
       *     // 创建时间
       *     filDate?: string
       *     // 创建人
       *     filler?: string
       *     // 生成单据明细
       *     genDetails?: Array<{
       *       // 生成单据类型
       *       genCls?: string
       *       // 生成单据单号
       *       genNum?: string
       *       // 生成单据时间
       *       genTime?: string
       *     }>
       *     // 商品种类
       *     goodsCount?: number
       *     // 最后修改人
       *     lastModifyOper?: string
       *     // 最后修改时间
       *     lstupdTime?: string
       *     // 备注
       *     note?: string
       *     // 单号
       *     num?: string
       *     // 发生日期
       *     ocrDate?: string
       *     // 已处理差异数
       *     procQty?: number
       *     // 已处理差异金额
       *     procTotal?: number
       *     // 差异数量
       *     qty?: number
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     receiver?: GCN
       *     // 单据状态
       *     stat?: number
       *     // 总金额
       *     total?: number
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     vehSaleEmp?: GCN
       *     // 车销业务员姓名
       *     vehSaleEmpName?: string
       *     // 车销回货单单号
       *     vehSaleUseSignArvNum?: string
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     wrh?: GCN
       *   }
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      getUsingGET_3<
        Config extends Alova2MethodConfig<Response_VehSaleUseSignArvDiffResponseDTO_> & {
          params: {
            /**
             * 是否查询明细
             */
            fetchDetail?: boolean;
            /**
             * 单号
             * [required]
             */
            num: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        Response_VehSaleUseSignArvDiffResponseDTO_,
        'vehsaleusesignarvdiffInterface.getUsingGET_3',
        Config
      >;
      /**
       * ---
       *
       * [POST] 获取单据明细详情
       *
       * **path:** /vbs-service/rest/vehsaleusesignarvdiff/get/details
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // [title] Category
       *   // 分类
       *   category?: {
       *     // 代码
       *     // [required]
       *     code: string
       *     // 名称
       *     name?: string
       *   }
       *   // 要求返回的部份信息
       *   fetchParts?: string[]
       *   // 单号
       *   // [required]
       *   num: string
       *   // 页码，从0开始，默认为0
       *   // [required]
       *   page: number
       *   // 每页条数，默认为50
       *   // [required]
       *   pageSize: number
       *   // 排序条件
       *   sorts?: Array<{
       *     // 排序方式，默认倒叙
       *     asc?: boolean
       *     // 排序字段
       *     // [required]
       *     field: string
       *   }>
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:接口调用成功，其他(5000+):正常业务异常
       *   code?: number
       *   // 响应实体
       *   data?: Array<{
       *     // [title] CodeName
       *     // 代码名称对象
       *     category?: {
       *       // 代码
       *       code?: string
       *       // 名称
       *       name?: string
       *     }
       *     // 商品条码
       *     gdCode?: string
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     goods?: {
       *       // 代码
       *       code?: string
       *       // 实体GID
       *       // [required]
       *       gid: number
       *       // 名称
       *       name?: string
       *     }
       *     // 图片
       *     imageDetails?: Array<{
       *       // 文件ID
       *       fileId?: string
       *       // 文件名
       *       fileName?: string
       *       // 文件类型
       *       fileType?: 'img' | 'video'
       *       // 外部URL
       *       fileUrl?: string
       *       // 行号
       *       line?: number
       *     }>
       *     // 行号
       *     line?: number
       *     // 最小单位
       *     minMunit?: string
       *     // 单位
       *     munit?: string
       *     // 备注
       *     note?: string
       *     // 规格价
       *     price?: number
       *     // 已处理差异数量
       *     procQty?: number
       *     // 已处理差异包装数量
       *     procQtyStr?: string
       *     // 规格
       *     qpc?: number
       *     // 规格描述
       *     qpcStr?: string
       *     // 差异数量
       *     qty?: number
       *     // 差异包装数量
       *     qtyStr?: string
       *     // 含税金额
       *     total?: number
       *   }>
       *   // 输入字段提示。一般为表单提交时，后端对表单进行整体校验，返回给前端相应字段的校验结果。
       *   fields?: Record<string, string>
       *   // 是否还有更多记录，一般只在列表查询时返回。
       *   more?: boolean
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       *   // 请求响应的记录数，一般只在列表查询时返回。
       *   total?: number
       * }
       * ```
       */
      getDetailsUsingPOST_1<
        Config extends Alova2MethodConfig<BaseResponse_List_VehSaleUseSignArvDiffDtlResponseDTO_> & {
          data: BillDtlQueryFilter;
        }
      >(
        config: Config
      ): Alova2Method<
        BaseResponse_List_VehSaleUseSignArvDiffDtlResponseDTO_,
        'vehsaleusesignarvdiffInterface.getDetailsUsingPOST_1',
        Config
      >;
      /**
       * ---
       *
       * [POST] 列表查询
       *
       * **path:** /vbs-service/rest/vehsaleusesignarvdiff/query
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 开始日期
       *   beginDate?: string
       *   // 要求返回的部份信息
       *   fetchParts?: string[]
       *   // 结束日期
       *   finishDate?: string
       *   // 关键字
       *   keyword?: string
       *   // 单号
       *   num?: string
       *   // 页码，从0开始，默认为0
       *   // [required]
       *   page: number
       *   // 每页条数，默认为50
       *   // [required]
       *   pageSize: number
       *   // 排序条件
       *   sorts?: Array<{
       *     // 排序方式，默认倒叙
       *     asc?: boolean
       *     // 排序字段
       *     // [required]
       *     field: string
       *   }>
       *   // 状态
       *   stat?: 100 | 300
       *   // 状态列表
       *   stats?: number[]
       *   // 车销业务员Gid
       *   vehSaleEmpGid?: number
       *   // 物流中心
       *   wmsGid?: number
       *   // 仓位Gid
       *   wrhGid?: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:接口调用成功，其他(5000+):正常业务异常
       *   code?: number
       *   // 响应实体
       *   data?: Array<{
       *     // 商品种类
       *     goodsCount?: number
       *     // 最后修改时间
       *     lstupdTime?: string
       *     // 单号
       *     num?: string
       *     // 单据状态
       *     stat?: number
       *     // 总金额
       *     total?: number
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     vehSaleEmp?: {
       *       // 代码
       *       code?: string
       *       // 实体GID
       *       // [required]
       *       gid: number
       *       // 名称
       *       name?: string
       *     }
       *     vehSaleEmpName?: string
       *     // 车销回货单单号
       *     vehSaleUseSignArvNum?: string
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     wrh?: GCN
       *   }>
       *   // 输入字段提示。一般为表单提交时，后端对表单进行整体校验，返回给前端相应字段的校验结果。
       *   fields?: Record<string, string>
       *   // 是否还有更多记录，一般只在列表查询时返回。
       *   more?: boolean
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       *   // 请求响应的记录数，一般只在列表查询时返回。
       *   total?: number
       * }
       * ```
       */
      queryUsingPOST_3<
        Config extends Alova2MethodConfig<BaseResponse_List_VehSaleUseSignArvDiffQueryResponseDTO_> & {
          data: VehSaleUseSignArvDiffQueryFilter;
        }
      >(
        config: Config
      ): Alova2Method<
        BaseResponse_List_VehSaleUseSignArvDiffQueryResponseDTO_,
        'vehsaleusesignarvdiffInterface.queryUsingPOST_3',
        Config
      >;
      /**
       * ---
       *
       * [POST] 提交赔付
       *
       * **path:** /vbs-service/rest/vehsaleusesignarvdiff/submitpay
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 单据附件提交明细
       *   attachDetails?: Array<{
       *     // 附件标识
       *     fileId?: string
       *     // 附件名称
       *     fileName?: string
       *     // 附件路径
       *     fileUrl?: string
       *   }>
       *   // 单据商品提交明细
       *   goodsDetails?: Array<{
       *     // 车销商品
       *     // [required]
       *     gdGid: number
       *     // 数量
       *     qty?: number
       *   }>
       *   // 差异单号
       *   // [required]
       *   num: string
       *   // 车销业务员
       *   vehSaleEmpGid?: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // 返回数据
       *   data?: string
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      submitPayUsingPOST<
        Config extends Alova2MethodConfig<Response_string_> & {
          data: BillGenBySrcRequestDTO;
        }
      >(
        config: Config
      ): Alova2Method<Response_string_, 'vehsaleusesignarvdiffInterface.submitPayUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 提交车销
       *
       * **path:** /vbs-service/rest/vehsaleusesignarvdiff/submitsale
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 单据附件提交明细
       *   attachDetails?: Array<{
       *     // 附件标识
       *     fileId?: string
       *     // 附件名称
       *     fileName?: string
       *     // 附件路径
       *     fileUrl?: string
       *   }>
       *   // 单据商品提交明细
       *   goodsDetails?: Array<{
       *     // 车销商品
       *     // [required]
       *     gdGid: number
       *     // 数量
       *     qty?: number
       *   }>
       *   // 差异单号
       *   // [required]
       *   num: string
       *   // 收货门店
       *   storeGid?: number
       *   // 车销业务员
       *   vehSaleEmpGid?: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // 返回数据
       *   data?: string
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      submitSaleUsingPOST<
        Config extends Alova2MethodConfig<Response_string_> & {
          data: VehSaleDiffSaleRequestDTO;
        }
      >(
        config: Config
      ): Alova2Method<Response_string_, 'vehsaleusesignarvdiffInterface.submitSaleUsingPOST', Config>;
      /**
       * ---
       *
       * [POST] 获取需要开单的商品明细
       *
       * **path:** /vbs-service/rest/vehsaleusesignarvdiff/vehsale/goods/query
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 单据商品提交明细
       *   goodsDetails?: Array<{
       *     // 车销商品
       *     // [required]
       *     gdGid: number
       *     // 数量
       *     qty?: number
       *   }>
       *   // 单号
       *   // [required]
       *   num: string
       *   // 门店GID
       *   // [required]
       *   storeGid: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // 返回数据
       *   data?: Array<{
       *     // [title] CodeName
       *     // 代码名称对象
       *     category?: {
       *       // 代码
       *       code?: string
       *       // 名称
       *       name?: string
       *     }
       *     // 商品条码
       *     gdCode?: string
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     goods?: {
       *       // 代码
       *       code?: string
       *       // 实体GID
       *       // [required]
       *       gid: number
       *       // 名称
       *       name?: string
       *     }
       *     // 图片
       *     imageDetails?: Array<{
       *       // 文件ID
       *       fileId?: string
       *       // 文件名
       *       fileName?: string
       *       // 文件类型
       *       fileType?: 'img' | 'video'
       *       // 外部URL
       *       fileUrl?: string
       *       // 行号
       *       line?: number
       *     }>
       *     // 行号
       *     line?: number
       *     // 最小单位
       *     minMunit?: string
       *     // 单位
       *     munit?: string
       *     // 备注
       *     note?: string
       *     // 规格价
       *     price?: number
       *     // 已处理差异数量
       *     procQty?: number
       *     // 已处理差异包装数量
       *     procQtyStr?: string
       *     // 规格
       *     qpc?: number
       *     // 规格描述
       *     qpcStr?: string
       *     // 差异数量
       *     qty?: number
       *     // 差异包装数量
       *     qtyStr?: string
       *     // 含税金额
       *     total?: number
       *   }>
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      queryVehSaleGoodsUsingPOST<
        Config extends Alova2MethodConfig<Response_List_VehSaleUseSignArvDiffDtlResponseDTO_> & {
          data: VehSaleUseSignArvDiffVehSaleGoodsQueryDTO;
        }
      >(
        config: Config
      ): Alova2Method<
        Response_List_VehSaleUseSignArvDiffDtlResponseDTO_,
        'vehsaleusesignarvdiffInterface.queryVehSaleGoodsUsingPOST',
        Config
      >;
    };
    vehsaleusesignbckInterface: {
      /**
       * ---
       *
       * [POST] 作废单据
       *
       * **path:** /vbs-service/rest/vehsaleusesignbck/abort
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 单号
       *   // [required]
       *   num: string
       *   // 版本号
       *   // [required]
       *   version: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      abortUsingPOST_3<
        Config extends Alova2MethodConfig<Response_Void_> & {
          data: BillBaseRequestDTO;
        }
      >(
        config: Config
      ): Alova2Method<Response_Void_, 'vehsaleusesignbckInterface.abortUsingPOST_3', Config>;
      /**
       * ---
       *
       * [POST] 审核单据
       *
       * **path:** /vbs-service/rest/vehsaleusesignbck/audit
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 单据审核请求参数明细
       *   details?: Array<{
       *     // 商品GId
       *     // [required]
       *     gdGid: number
       *     // 规格价
       *     price?: number
       *     // 规格
       *     // [required]
       *     qpc: number
       *     // 规格描述
       *     // [required]
       *     qpcStr: string
       *     // 单品数量
       *     qty?: number
       *   }>
       *   // 单号
       *   // [required]
       *   num: string
       *   // 版本号
       *   // [required]
       *   version: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      auditUsingPOST_2<
        Config extends Alova2MethodConfig<Response_Void_> & {
          data: BillAuditRequestDTO;
        }
      >(
        config: Config
      ): Alova2Method<Response_Void_, 'vehsaleusesignbckInterface.auditUsingPOST_2', Config>;
      /**
       * ---
       *
       * [GET] 获取单据详情
       *
       * **path:** /vbs-service/rest/vehsaleusesignbck/get
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   // 是否查询明细
       *   fetchDetail?: boolean
       *   // 单号
       *   // [required]
       *   num: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // [title] VehSaleUseSignBckResponseDTO
       *   // 获取车销领货退货单
       *   data?: {
       *     // 附件明细
       *     attachDetails?: Array<{
       *       // 附件标识
       *       fileId?: string
       *       // 附件名称
       *       fileName?: string
       *       // 附件路径
       *       fileUrl?: string
       *       // 行号
       *       line?: number
       *       // 备注
       *       note?: string
       *     }>
       *     // 车牌号
       *     carNumber?: string
       *     // 商品明细
       *     details?: Array<{
       *       // 申请数量
       *       applyQty?: number
       *       // [title] CodeName
       *       // 代码名称对象
       *       category?: {
       *         // 代码
       *         code?: string
       *         // 名称
       *         name?: string
       *       }
       *       // 商品条码
       *       gdCode?: string
       *       // [title] GCN
       *       // 包含Gid的基础对象
       *       goods?: {
       *         // 代码
       *         code?: string
       *         // 实体GID
       *         // [required]
       *         gid: number
       *         // 名称
       *         name?: string
       *       }
       *       // 图片
       *       imageDetails?: Array<{
       *         // 文件ID
       *         fileId?: string
       *         // 文件名
       *         fileName?: string
       *         // 文件类型
       *         fileType?: 'img' | 'video'
       *         // 外部URL
       *         fileUrl?: string
       *         // 行号
       *         line?: number
       *       }>
       *       // 备注
       *       note?: string
       *       // 规格数量信息
       *       qpcDetails?: Array<{
       *         // 申请数量
       *         applyQty?: number
       *         // 申请包装数量
       *         applyQtyStr?: string
       *         // 单位
       *         munit?: string
       *         // 规格价
       *         price?: number
       *         // 规格
       *         qpc?: number
       *         // 规格描述
       *         qpcStr?: string
       *         // 确认数量
       *         qty?: number
       *         // 确认包装数量
       *         qtyStr?: string
       *         // 含税金额
       *         total?: number
       *       }>
       *       // 确认数量
       *       qty?: number
       *     }>
       *     // 创建时间
       *     filDate?: string
       *     // 创建人
       *     filler?: string
       *     // 商品种类
       *     goodsCount?: number
       *     // 最后修改人
       *     lastModifyOper?: string
       *     // 最后修改时间
       *     lstupdTime?: string
       *     // 备注
       *     note?: string
       *     // 单号
       *     num?: string
       *     // 发生日期
       *     ocrDate?: string
       *     // 总数量
       *     qty?: number
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     receiver?: GCN
       *     // 单据状态
       *     stat?: number
       *     // 含税总金额
       *     total?: number
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     vehSaleEmp?: GCN
       *     // 业务员姓名
       *     vehSaleEmpName?: string
       *     // 版本号
       *     version?: number
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     wrh?: GCN
       *   }
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      getUsingGET_4<
        Config extends Alova2MethodConfig<Response_VehSaleUseSignBckResponseDTO_> & {
          params: {
            /**
             * 是否查询明细
             */
            fetchDetail?: boolean;
            /**
             * 单号
             * [required]
             */
            num: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<Response_VehSaleUseSignBckResponseDTO_, 'vehsaleusesignbckInterface.getUsingGET_4', Config>;
      /**
       * ---
       *
       * [POST] 列表查询
       *
       * **path:** /vbs-service/rest/vehsaleusesignbck/query
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 开始日期
       *   beginDate?: string
       *   // 要求返回的部份信息
       *   fetchParts?: string[]
       *   // 结束日期
       *   finishDate?: string
       *   // 关键字
       *   keyword?: string
       *   // 单号
       *   num?: string
       *   // 页码，从0开始，默认为0
       *   // [required]
       *   page: number
       *   // 每页条数，默认为50
       *   // [required]
       *   pageSize: number
       *   // 排序条件
       *   sorts?: Array<{
       *     // 排序方式，默认倒叙
       *     asc?: boolean
       *     // 排序字段
       *     // [required]
       *     field: string
       *   }>
       *   // 状态
       *   stat?: 100 | 110 | 1300 | 1310
       *   // 状态列表
       *   stats?: number[]
       *   // 业务员Gid
       *   vehSaleEmpGid?: number
       *   // 物流中心
       *   wmsGid?: number
       *   // 收货仓位Gid
       *   wrhGid?: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:接口调用成功，其他(5000+):正常业务异常
       *   code?: number
       *   // 响应实体
       *   data?: Array<{
       *     // 商品种类
       *     goodsCount?: number
       *     // 最后修改时间
       *     lstupdTime?: string
       *     // 单号
       *     num?: string
       *     // 总数量
       *     qty?: number
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     receiver?: {
       *       // 代码
       *       code?: string
       *       // 实体GID
       *       // [required]
       *       gid: number
       *       // 名称
       *       name?: string
       *     }
       *     // 单据状态
       *     stat?: number
       *     // 含税总金额
       *     total?: number
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     vehSaleEmp?: GCN
       *     // 业务员姓名
       *     vehSaleEmpName?: string
       *     // 版本号
       *     version?: number
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     wrh?: GCN
       *   }>
       *   // 输入字段提示。一般为表单提交时，后端对表单进行整体校验，返回给前端相应字段的校验结果。
       *   fields?: Record<string, string>
       *   // 是否还有更多记录，一般只在列表查询时返回。
       *   more?: boolean
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       *   // 请求响应的记录数，一般只在列表查询时返回。
       *   total?: number
       * }
       * ```
       */
      queryUsingPOST_4<
        Config extends Alova2MethodConfig<BaseResponse_List_VehSaleUseSignBckQueryResponseDTO_> & {
          data: VehSaleUseSignBckQueryFilter;
        }
      >(
        config: Config
      ): Alova2Method<
        BaseResponse_List_VehSaleUseSignBckQueryResponseDTO_,
        'vehsaleusesignbckInterface.queryUsingPOST_4',
        Config
      >;
      /**
       * ---
       *
       * [POST] 获取单据明细详情
       *
       * **path:** /vbs-service/rest/vehsaleusesignbck/query/details
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // [title] Category
       *   // 分类
       *   category?: {
       *     // 代码
       *     // [required]
       *     code: string
       *     // 名称
       *     name?: string
       *   }
       *   // 要求返回的部份信息
       *   fetchParts?: string[]
       *   // 单号
       *   // [required]
       *   num: string
       *   // 页码，从0开始，默认为0
       *   // [required]
       *   page: number
       *   // 每页条数，默认为50
       *   // [required]
       *   pageSize: number
       *   // 排序条件
       *   sorts?: Array<{
       *     // 排序方式，默认倒叙
       *     asc?: boolean
       *     // 排序字段
       *     // [required]
       *     field: string
       *   }>
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:接口调用成功，其他(5000+):正常业务异常
       *   code?: number
       *   // 响应实体
       *   data?: Array<{
       *     // 申请数量
       *     applyQty?: number
       *     // [title] CodeName
       *     // 代码名称对象
       *     category?: {
       *       // 代码
       *       code?: string
       *       // 名称
       *       name?: string
       *     }
       *     // 商品条码
       *     gdCode?: string
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     goods?: {
       *       // 代码
       *       code?: string
       *       // 实体GID
       *       // [required]
       *       gid: number
       *       // 名称
       *       name?: string
       *     }
       *     // 图片
       *     imageDetails?: Array<{
       *       // 文件ID
       *       fileId?: string
       *       // 文件名
       *       fileName?: string
       *       // 文件类型
       *       fileType?: 'img' | 'video'
       *       // 外部URL
       *       fileUrl?: string
       *       // 行号
       *       line?: number
       *     }>
       *     // 备注
       *     note?: string
       *     // 规格数量信息
       *     qpcDetails?: Array<{
       *       // 申请数量
       *       applyQty?: number
       *       // 申请包装数量
       *       applyQtyStr?: string
       *       // 单位
       *       munit?: string
       *       // 规格价
       *       price?: number
       *       // 规格
       *       qpc?: number
       *       // 规格描述
       *       qpcStr?: string
       *       // 确认数量
       *       qty?: number
       *       // 确认包装数量
       *       qtyStr?: string
       *       // 含税金额
       *       total?: number
       *     }>
       *     // 确认数量
       *     qty?: number
       *   }>
       *   // 输入字段提示。一般为表单提交时，后端对表单进行整体校验，返回给前端相应字段的校验结果。
       *   fields?: Record<string, string>
       *   // 是否还有更多记录，一般只在列表查询时返回。
       *   more?: boolean
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       *   // 请求响应的记录数，一般只在列表查询时返回。
       *   total?: number
       * }
       * ```
       */
      queryDetailsUsingPOST_2<
        Config extends Alova2MethodConfig<BaseResponse_List_VehSaleUseSignBckDtlResponseDTO_> & {
          data: BillDtlQueryFilter;
        }
      >(
        config: Config
      ): Alova2Method<
        BaseResponse_List_VehSaleUseSignBckDtlResponseDTO_,
        'vehsaleusesignbckInterface.queryDetailsUsingPOST_2',
        Config
      >;
      /**
       * ---
       *
       * [POST] 提交单据
       *
       * **path:** /vbs-service/rest/vehsaleusesignbck/submit
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 单据附件提交明细
       *   attachDetails?: Array<{
       *     // 附件标识
       *     fileId?: string
       *     // 附件名称
       *     fileName?: string
       *     // 附件路径
       *     fileUrl?: string
       *   }>
       *   // 备注
       *   note?: string
       *   // [title] GCN
       *   // 包含Gid的基础对象
       *   vehSaleEmp?: {
       *     // 代码
       *     code?: string
       *     // 实体GID
       *     // [required]
       *     gid: number
       *     // 名称
       *     name?: string
       *   }
       *   // [title] GCN
       *   // 包含Gid的基础对象
       *   wrh?: GCN
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // 返回数据
       *   data?: string
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      submitUsingPOST_3<
        Config extends Alova2MethodConfig<Response_string_> & {
          data: VehSaleUseSignSubmitRequestDTO;
        }
      >(
        config: Config
      ): Alova2Method<Response_string_, 'vehsaleusesignbckInterface.submitUsingPOST_3', Config>;
      /**
       * ---
       *
       * [POST] 新建单据前校验
       *
       * **path:** /vbs-service/rest/vehsaleusesignbck/verify/create
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   // 车销业务员Gid
       *   // [required]
       *   vehSaleEmpGid: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // 返回数据
       *   data?: boolean
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      verifyCreateUsingPOST_2<
        Config extends Alova2MethodConfig<Response_boolean_> & {
          params: {
            /**
             * 车销业务员Gid
             * [required]
             */
            vehSaleEmpGid: number;
          };
        }
      >(
        config: Config
      ): Alova2Method<Response_boolean_, 'vehsaleusesignbckInterface.verifyCreateUsingPOST_2', Config>;
    };
    vehsalewrhinvInterface: {
      /**
       * ---
       *
       * [POST] 获取车销仓库存信息
       *
       * **path:** /vbs-service/rest/vehsalewrhinv/get
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // [title] VehSaleWrhInvResponseDTO
       *   // 车销库存
       *   data?: {
       *     // 上次回货日期
       *     lastVehSaleUseSignArvDate?: string
       *     // 上次领货日期
       *     lastVehSaleUseSignDate?: string
       *     // 可用库存数
       *     qty?: number
       *     // 可用品项数
       *     skuCount?: number
       *     // 可用库存金额
       *     total?: number
       *     // 不可用库存数
       *     unusableQty?: number
       *     // 不可用库存金额
       *     unusableTotal?: number
       *     // 上次回货金额
       *     vehSaleUseSignArvTotal?: number
       *     // 上次领货金额
       *     vehSaleUseSignTotal?: number
       *   }
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      getTemplatesUsingPOST<Config extends Alova2MethodConfig<Response_VehSaleWrhInvResponseDTO_>>(
        config?: Config
      ): Alova2Method<Response_VehSaleWrhInvResponseDTO_, 'vehsalewrhinvInterface.getTemplatesUsingPOST', Config>;
      /**
       * ---
       *
       * [GET] 获取车销库存仓位
       *
       * **path:** /vbs-service/rest/vehsalewrhinv/get/wrh
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   // 业务员Gid
       *   // [required]
       *   vehSaleEmpGid: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // [title] GCN
       *   // 包含Gid的基础对象
       *   data?: {
       *     // 代码
       *     code?: string
       *     // 实体GID
       *     // [required]
       *     gid: number
       *     // 名称
       *     name?: string
       *   }
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      getWrhUsingGET<
        Config extends Alova2MethodConfig<Response_GCN_> & {
          params: {
            /**
             * 业务员Gid
             * [required]
             */
            vehSaleEmpGid: number;
          };
        }
      >(
        config: Config
      ): Alova2Method<Response_GCN_, 'vehsalewrhinvInterface.getWrhUsingGET', Config>;
      /**
       * ---
       *
       * [POST] 获取车销库存商品
       *
       * **path:** /vbs-service/rest/vehsalewrhinv/goods/query
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 要求返回的部份信息
       *   fetchParts?: string[]
       *   // 关键字
       *   keyword?: string
       *   // 页码，从0开始，默认为0
       *   // [required]
       *   page: number
       *   // 每页条数，默认为50
       *   // [required]
       *   pageSize: number
       *   // 类别
       *   sortCode?: string
       *   // 排序条件
       *   sorts?: Array<{
       *     // 排序方式，默认倒叙
       *     asc?: boolean
       *     // 排序字段
       *     // [required]
       *     field: string
       *   }>
       *   // 业务员Gid
       *   vehSaleEmpGid?: number
       *   // 物流中心
       *   wmsGid?: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:接口调用成功，其他(5000+):正常业务异常
       *   code?: number
       *   // 响应实体
       *   data?: Array<{
       *     // 建议领货数量
       *     advUseSignQty?: number
       *     // 建议领货包装数量
       *     advUseSignQtyStr?: string
       *     // 业务库存数
       *     busInvQty?: number
       *     // 业务库存包装数
       *     busInvQtyStr?: string
       *     // [title] CodeName
       *     // 代码名称对象
       *     category?: {
       *       // 代码
       *       code?: string
       *       // 名称
       *       name?: string
       *     }
       *     // 代码
       *     code?: string
       *     // 商品条码
       *     gdCode?: string
       *     // 实体GID
       *     // [required]
       *     gid: number
       *     // 商品图片
       *     imageDetails?: Array<{
       *       // 文件ID
       *       fileId?: string
       *       // 文件名
       *       fileName?: string
       *       // 文件类型
       *       fileType?: 'img' | 'video'
       *       // 外部URL
       *       fileUrl?: string
       *       // 行号
       *       line?: number
       *     }>
       *     // 最小单位
       *     minMunit?: string
       *     // 单位
       *     munit?: string
       *     // 名称
       *     name?: string
       *     // 规格价
       *     price?: number
       *     // 规格
       *     qpc?: number
       *     // 规格列表
       *     qpcDetails?: Array<{
       *       // 计量单位
       *       munit?: string
       *       // 规格
       *       qpc?: number
       *       // 规格说明
       *       qpcStr?: string
       *     }>
       *     // 规格说明
       *     qpcStr?: string
       *     // 单品价
       *     singlePrice?: number
       *     // 领货单位
       *     useSignMunit?: string
       *     // 车销仓库存数量
       *     vehSaleWrhQty?: number
       *     // 车销仓库存包装数量
       *     vehSaleWrhQtyStr?: string
       *   }>
       *   // 输入字段提示。一般为表单提交时，后端对表单进行整体校验，返回给前端相应字段的校验结果。
       *   fields?: Record<string, string>
       *   // 是否还有更多记录，一般只在列表查询时返回。
       *   more?: boolean
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       *   // 请求响应的记录数，一般只在列表查询时返回。
       *   total?: number
       * }
       * ```
       */
      queryGoodsUsingPOST_1<
        Config extends Alova2MethodConfig<BaseResponse_List_VehSaleGoodsDTO_> & {
          data: GoodsQueryFilter;
        }
      >(
        config: Config
      ): Alova2Method<BaseResponse_List_VehSaleGoodsDTO_, 'vehsalewrhinvInterface.queryGoodsUsingPOST_1', Config>;
    };
    wholesaleInterface: {
      /**
       * ---
       *
       * [GET] 获取单据详情
       *
       * **path:** /vbs-service/rest/wholesale/get
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   // 是否查询明细
       *   fetchDetail?: boolean
       *   // 单号
       *   // [required]
       *   num: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:成功，其他(5000+):业务异常
       *   // [required]
       *   code: number
       *   // [title] WholeSaleResponseDTO
       *   // 获取批发单
       *   data?: {
       *     // 附件明细
       *     attachDetails?: Array<{
       *       // 附件标识
       *       fileId?: string
       *       // 附件名称
       *       fileName?: string
       *       // 附件路径
       *       fileUrl?: string
       *       // 行号
       *       line?: number
       *       // 备注
       *       note?: string
       *     }>
       *     // 商品明细
       *     details?: Array<{
       *       // [title] CodeName
       *       // 代码名称对象
       *       category?: {
       *         // 代码
       *         code?: string
       *         // 名称
       *         name?: string
       *       }
       *       // 商品条码
       *       gdCode?: string
       *       // [title] GCN
       *       // 包含Gid的基础对象
       *       goods?: {
       *         // 代码
       *         code?: string
       *         // 实体GID
       *         // [required]
       *         gid: number
       *         // 名称
       *         name?: string
       *       }
       *       // 图片
       *       imageDetails?: Array<{
       *         // 文件ID
       *         fileId?: string
       *         // 文件名
       *         fileName?: string
       *         // 文件类型
       *         fileType?: 'img' | 'video'
       *         // 外部URL
       *         fileUrl?: string
       *         // 行号
       *         line?: number
       *       }>
       *       // 行号
       *       line?: number
       *       // 最小单位
       *       minMunit?: string
       *       // 单位
       *       munit?: string
       *       // 备注
       *       note?: string
       *       // 规格价
       *       price?: number
       *       // 规格
       *       qpc?: number
       *       // 规格描述
       *       qpcStr?: string
       *       // 数量
       *       qty?: number
       *       // 包装数量
       *       qtyStr?: string
       *       // 含税金额
       *       total?: number
       *     }>
       *     // 创建时间
       *     filDate?: string
       *     // 创建人
       *     filler?: string
       *     // 商品种类
       *     goodsCount?: number
       *     // 最后修改人
       *     lastModifyOper?: string
       *     // 最后修改时间
       *     lstupdTime?: string
       *     // 备注
       *     note?: string
       *     // 单号
       *     num?: string
       *     // 发生日期
       *     ocrDate?: string
       *     // 来源类型
       *     srcCls?: string
       *     // 来源单号
       *     srcNum?: string
       *     // 单据状态
       *     stat?: number
       *     // 总金额
       *     total?: number
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     vehSaleEmp?: GCN
       *     vehSaleEmpName?: string
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     wrh?: GCN
       *   }
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       * }
       * ```
       */
      getUsingGET_6<
        Config extends Alova2MethodConfig<Response_WholeSaleResponseDTO_> & {
          params: {
            /**
             * 是否查询明细
             */
            fetchDetail?: boolean;
            /**
             * 单号
             * [required]
             */
            num: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<Response_WholeSaleResponseDTO_, 'wholesaleInterface.getUsingGET_6', Config>;
      /**
       * ---
       *
       * [POST] 获取单据明细详情
       *
       * **path:** /vbs-service/rest/wholesale/get/details
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 要求返回的部份信息
       *   fetchParts?: string[]
       *   // 单号
       *   // [required]
       *   num: string
       *   // 页码，从0开始，默认为0
       *   // [required]
       *   page: number
       *   // 每页条数，默认为50
       *   // [required]
       *   pageSize: number
       *   // 排序条件
       *   sorts?: Array<{
       *     // 排序方式，默认倒叙
       *     asc?: boolean
       *     // 排序字段
       *     // [required]
       *     field: string
       *   }>
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:接口调用成功，其他(5000+):正常业务异常
       *   code?: number
       *   // 响应实体
       *   data?: Array<{
       *     // [title] CodeName
       *     // 代码名称对象
       *     category?: {
       *       // 代码
       *       code?: string
       *       // 名称
       *       name?: string
       *     }
       *     // 商品条码
       *     gdCode?: string
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     goods?: {
       *       // 代码
       *       code?: string
       *       // 实体GID
       *       // [required]
       *       gid: number
       *       // 名称
       *       name?: string
       *     }
       *     // 图片
       *     imageDetails?: Array<{
       *       // 文件ID
       *       fileId?: string
       *       // 文件名
       *       fileName?: string
       *       // 文件类型
       *       fileType?: 'img' | 'video'
       *       // 外部URL
       *       fileUrl?: string
       *       // 行号
       *       line?: number
       *     }>
       *     // 行号
       *     line?: number
       *     // 最小单位
       *     minMunit?: string
       *     // 单位
       *     munit?: string
       *     // 备注
       *     note?: string
       *     // 规格价
       *     price?: number
       *     // 规格
       *     qpc?: number
       *     // 规格描述
       *     qpcStr?: string
       *     // 数量
       *     qty?: number
       *     // 包装数量
       *     qtyStr?: string
       *     // 含税金额
       *     total?: number
       *   }>
       *   // 输入字段提示。一般为表单提交时，后端对表单进行整体校验，返回给前端相应字段的校验结果。
       *   fields?: Record<string, string>
       *   // 是否还有更多记录，一般只在列表查询时返回。
       *   more?: boolean
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       *   // 请求响应的记录数，一般只在列表查询时返回。
       *   total?: number
       * }
       * ```
       */
      getDetailsUsingPOST_2<
        Config extends Alova2MethodConfig<BaseResponse_List_WholeSaleDtlResponseDTO_> & {
          data: WholeSaleDetailsFilter;
        }
      >(
        config: Config
      ): Alova2Method<BaseResponse_List_WholeSaleDtlResponseDTO_, 'wholesaleInterface.getDetailsUsingPOST_2', Config>;
      /**
       * ---
       *
       * [POST] 列表查询
       *
       * **path:** /vbs-service/rest/wholesale/query
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 开始日期
       *   beginDate?: string
       *   // 要求返回的部份信息
       *   fetchParts?: string[]
       *   // 结束日期
       *   finishDate?: string
       *   // 关键字
       *   keyword?: string
       *   // 单号
       *   num?: string
       *   // 页码，从0开始，默认为0
       *   // [required]
       *   page: number
       *   // 每页条数，默认为50
       *   // [required]
       *   pageSize: number
       *   // 排序条件
       *   sorts?: Array<{
       *     // 排序方式，默认倒叙
       *     asc?: boolean
       *     // 排序字段
       *     // [required]
       *     field: string
       *   }>
       *   // 单据状态 可选值：1300、100、1310、110
       *   stat?: number
       *   // 状态列表
       *   stats?: number[]
       *   // 业务员Gid
       *   vehSaleEmpGid?: number
       *   // 物流中心
       *   wmsGid?: number
       *   // 仓位Gid
       *   wrhGid?: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 响应码。2000:接口调用成功，其他(5000+):正常业务异常
       *   code?: number
       *   // 响应实体
       *   data?: Array<{
       *     // 商品种类
       *     goodsCount?: number
       *     // 最后修改时间
       *     lstupdTime?: string
       *     // 单号
       *     num?: string
       *     // 单据状态
       *     stat?: number
       *     // 总金额
       *     total?: number
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     vehSaleEmp?: {
       *       // 代码
       *       code?: string
       *       // 实体GID
       *       // [required]
       *       gid: number
       *       // 名称
       *       name?: string
       *     }
       *     vehSaleEmpName?: string
       *     // [title] GCN
       *     // 包含Gid的基础对象
       *     wrh?: GCN
       *   }>
       *   // 输入字段提示。一般为表单提交时，后端对表单进行整体校验，返回给前端相应字段的校验结果。
       *   fields?: Record<string, string>
       *   // 是否还有更多记录，一般只在列表查询时返回。
       *   more?: boolean
       *   // 提示信息，一般在异常时返回
       *   msg?: string
       *   // 请求响应的记录数，一般只在列表查询时返回。
       *   total?: number
       * }
       * ```
       */
      queryUsingPOST_6<
        Config extends Alova2MethodConfig<BaseResponse_List_WholeSaleQueryResponseDTO_> & {
          data: WholeSaleQueryFilter;
        }
      >(
        config: Config
      ): Alova2Method<BaseResponse_List_WholeSaleQueryResponseDTO_, 'wholesaleInterface.queryUsingPOST_6', Config>;
    };
  }

  var Apis: Apis;
}
