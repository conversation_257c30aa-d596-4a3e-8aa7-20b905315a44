/*
 * @Author: we<PERSON><PERSON>
 * @Date: 2025-03-27 17:06:25
 * @LastEditTime: 2025-06-26 20:16:51
 * @LastEditors: weisheng
 * @Description: 路由配置和导航守卫
 * @FilePath: /lsym-cx-mini/src/router/index.ts
 * 记得注释
 */
/// <reference types="@uni-helper/vite-plugin-uni-pages/client" />
import { pages, subPackages } from 'virtual:uni-pages'
import { usePermissionChecker } from '@/composables/usePermission'

function generateRoutes() {
  const routes = pages.map((page) => {
    const newPath = `/${page.path}`
    return { ...page, path: newPath }
  })
  if (subPackages && subPackages.length > 0) {
    subPackages.forEach((subPackage) => {
      const subRoutes = subPackage.pages.map((page: any) => {
        const newPath = `/${subPackage.root}/${page.path}`
        return { ...page, path: newPath }
      })
      routes.push(...subRoutes)
    })
  }
  return routes
}

const router = createRouter({
  routes: generateRoutes(),
})

// 路由白名单，这些页面不需要登录也可以访问
const whiteList = ['login']

/**
 * 全局路由前置守卫
 * 1. 如果用户未登录且目标页面不在白名单中，则重定向到登录页
 * 2. 如果用户已登录且目标页面是登录页，则重定向到首页
 * 3. 其他情况正常放行
 */
router.beforeEach((to, _from, next) => {
  // 获取用户存储
  const userStore = useUserStore()
  const { alert: showAlert } = useGlobalMessage()

  // 检查用户是否已登录
  const isLoggedIn = userStore.isLoggedIn

  // 获取目标页面名称
  const toName = to.name as string

  // 判断页面是否在白名单中
  const isWhitePage = whiteList.includes(toName)

  // 如果用户未登录且目标页面不在白名单中，则重定向到登录页
  if (!isLoggedIn && !isWhitePage) {
    return next({ name: 'login', navType: 'replaceAll' })
  }

  // 如果用户已登录且目标页面是登录页，则重定向到首页
  if (isLoggedIn && toName === 'login') {
    return next({ name: 'home', navType: 'replaceAll' })
  }

  // 如果用户已登录，检查页面权限
  if (isLoggedIn && !isWhitePage) {
    const { checkRoutePermission } = usePermissionChecker()
    const hasPermission = checkRoutePermission((to as any)?.meta?.permissions, (to as any)?.meta?.requireAll)
    if (!hasPermission) {
      showAlert({
        msg: '抱歉，您没有权限，请联系管理员',
        zIndex: 10000,
      })
      return next(false) // 阻止路由跳转
    }
  }

  // 其他情况正常放行
  next()
})

/**
 * 全局路由后置守卫
 * 1. 记录路由跳转日志
 * 2. 检查用户登录状态，如果未登录且页面不在白名单中，则重定向到登录页
 * 3. 如果用户已登录且目标页面是登录页，则重定向到首页
 */
router.afterEach((to, from) => {
  // 记录路由跳转日志，方便调试
  if (from.name || to.name) {
    console.log('路由跳转:', from.name || '初始页面', '->', to.name || '未命名页面')
  }

  // 获取用户存储
  const userStore = useUserStore()

  // 检查用户是否已登录
  const isLoggedIn = userStore.isLoggedIn

  // 获取目标页面名称
  const toName = to.name as string

  // 判断页面是否在白名单中
  const isWhitePage = whiteList.includes(toName)

  // 如果用户未登录且目标页面不在白名单中，则重定向到登录页
  if (!isLoggedIn && !isWhitePage) {
    console.log('afterEach: 用户未登录，重定向到登录页')
    router.replaceAll({ name: 'login' })
    return
  }

  // 如果用户已登录且目标页面是登录页，则重定向到首页
  if (isLoggedIn && toName === 'login') {
    console.log('afterEach: 用户已登录，重定向到首页')
    router.replaceAll({ name: 'home' })
  }
})

export default router
