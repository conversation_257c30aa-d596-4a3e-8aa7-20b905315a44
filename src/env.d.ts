/*
 * @Author: weish<PERSON>
 * @Date: 2025-03-27 17:06:25
 * @LastEditTime: 2025-06-11 10:35:20
 * @LastEditors: weisheng
 * @Description:
 * @FilePath: /lsym-cx-mini/src/env.d.ts
 * 记得注释
 */
/// <reference types="vite/client" />

interface ImportMetaEnv {
  /** API 基础地址 */
  readonly VITE_API_BASE_URL: string
  /** 环境名称 */
  readonly VITE_ENV_NAME: 'development' | 'test' | 'uat' | 'production'
  /** Swagger 文档地址 */
  readonly VITE_SWAGGER_URL: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}
