<!--
 * @Description: 车销单详情页面
-->
<script setup lang="ts">
import { computed, ref } from 'vue'
import SalesBackDetailSkuCard from './cmp/SalesBackDetailSkuCard.vue'
import DetailSkeleton from '@/business/DetailSkeleton.vue'
import DetailStatusHeader from '@/business/DetailStatusHeader.vue'
import DetailStatsCard from '@/business/DetailStatsCard.vue'
import DetailTableHeader from '@/business/DetailTableHeader.vue'
import DetailInfoCard from '@/business/DetailInfoCard.vue'
import { getBillStatus } from '@/utils/bill'
import { createGlobalLoadingMiddleware } from '@/api/core/middleware'

const { emitRefreshData: emitRefreshSalesList } = useSendRefreshData('sales-list') // 刷新列表，本页面提交成功需要刷新列表

const router = useRouter()
const { error: showError, success: showSuccess } = useGlobalToast()
const { confirm: showConfirm } = useGlobalMessage()
const globalLoading = useGlobalLoading()
const { checkPermission } = usePermissionChecker()

// 车销单号
const id = ref('')

// 使用 useRequest 获取单据详情
const { data: order, loading: orderLoading, send: loadOrderDetail } = useRequest(
  (num: string) => Apis.vehsalebckInterface.getUsingGET({
    params: {
      num,
      fetchDetail: false,
    },
  }),
  {
    immediate: false,
  },
).onError((error) => {
  showError(error.error.message || '获取车销单详情失败')
})

const orderDetail = computed(() => {
  return order.value?.data
})

// 加载商品数据
const {
  data: skuData,
  loading: skuLoading,
  send: loadSkuData,
} = useRequest(
  (num: string) => Apis.vehsalebckInterface.queryDetailsUsingPOST({
    data: {
      num,
      page: 0,
      pageSize: 3,
    },
  }),
  {
    immediate: false,
  },
).onError((error) => {
  showError(error.error.message || '获取领货单商品失败')
})

// 商品列表
const skuList = computed(() => {
  return skuData.value?.data || []
})

// 是否还有更多商品
const hasMoreSku = computed(() => {
  return skuData.value?.more
})

// 车销单状态
const orderStatus = computed(() => {
  if (!orderDetail.value)
    return 'initial'
  return getBillStatus(orderDetail.value.stat!, false)
})

// 是否显示作废按钮
const showAbortButton = computed(() => {
  return ['applied', 'audited', 'completed'].includes(orderStatus.value) && checkPermission('vehSaleBckAbort')
})

// 统计数据 - 上部分数据
const topStats = computed(() => {
  if (!orderDetail.value) {
    return [
      { title: '开单金额(元)', value: '0.00' },
    ]
  }
  return [
    { title: '开单金额(元)', value: orderDetail.value.total || 0 },
  ]
})

// 统计数据 - 下部分数据
const bottomStats = computed(() => {
  return [
    { title: '商品项数(种)', value: orderDetail.value?.goodsCount || 0 },
    { title: '单品数(个)', value: orderDetail.value?.qty || 0 },
  ]
})

// 详情信息项
const infoItems = computed(() => {
  return [
    { label: '门店', value: `${orderDetail.value?.store?.name}[${orderDetail.value?.store?.code}]` },
    { label: '地址', value: `${orderDetail.value?.storeAddress || `--`}` },
    { label: '所属线路', value: `${orderDetail.value?.sortLines?.map(line => line.name).join(',') || `--`}` },
    { label: '业务员', value: `${orderDetail.value?.vehSaleEmpName}[${orderDetail.value?.vehSaleEmp?.code}]` },
    { label: '创建人', value: `${orderDetail.value?.filler}` },
    { label: '操作人', value: orderDetail.value?.lastModifyOper },
    { label: '操作时间', value: orderDetail.value?.lstupdTime },
  ]
})

// 跳转到商品清单详情
function goToProductList() {
  router.push({
    name: 'sales-back-detail-sku-list',
    params: { id: id.value },
  })
}

// 作废车销单
const {
  send: handleAbortRequest,
} = useRequest(
  (num: string) => Apis.vehsalebckInterface.abortUsingPOST({
    data: {
      num,
      version: orderDetail.value?.version || 1,
    },
  }),
  {
    immediate: false,
    middleware: createGlobalLoadingMiddleware({
      loadingText: '作废中...',
    }),
  },
).onSuccess(() => {
  globalLoading.close()
  showSuccess({
    msg: '作废成功',
    duration: 500,
    closed() {
      emitRefreshSalesList()
      router.replace({
        name: 'sales-back-detail',
        params: {
          id: id.value,
        },
      })
    },
  })
}).onError((error) => {
  globalLoading.close()
  showError(error.error?.message || '作废失败')
})

// 作废操作
function abortOrder() {
  if (!id.value) {
    showError('缺少记录ID')
    return
  }

  showConfirm({
    title: '提示',
    msg: '确认作废该车销退单吗？',
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    success: (res) => {
      if (res.action === 'confirm') {
        handleAbortRequest(id.value)
      }
    },
  })
}
onLoad((options: any) => {
  id.value = options.id as string
  loadOrderDetail(id.value)
  loadSkuData(id.value)
})

// 获取路由参数中的单号
onLoad((options: any) => {
  if (options.id) {
    id.value = options.id as string
    // 获取单据详情
    loadOrderDetail(id.value)
    loadSkuData(id.value)
  }
  else {
    showError('缺少单据编号')
  }
})
</script>

<template>
  <view class="sales-detail-page relative min-h-screen w-screen flex flex-col bg-[#F9F9F9]">
    <!-- 主要内容 -->
    <view class="flex-1 px-3 py-2 space-y-2">
      <!-- 骨架屏 -->
      <DetailSkeleton v-if="orderLoading || skuLoading" />
      <!-- 实际内容 -->
      <block v-else>
        <!-- 状态栏 -->
        <DetailStatusHeader
          :status="orderStatus"
          :order-no="orderDetail?.num || ''"
          custom-class="mb-3"
        />

        <!-- 统计卡片 -->
        <DetailStatsCard
          :top-items="topStats"
          :bottom-items="bottomStats"
          custom-class="mb-3"
        />

        <!-- 商品清单 -->
        <view class="flex flex-col rounded-lg bg-white py-3">
          <DetailTableHeader
            title="商品清单"
            :headers="['规格', '数量']"
            :show-more="hasMoreSku"
            @more-click="goToProductList"
          />

          <!-- 商品列表 -->
          <view class="space-y-3">
            <view v-for="sku in skuList" :key="sku.goods?.gid" class="border-b border-[#F5F5F5] last:border-0">
              <SalesBackDetailSkuCard :sku="sku" />
            </view>
          </view>
        </view>

        <!-- 基本信息 -->
        <DetailInfoCard :items="infoItems" />
      </block>
    </view>

    <!-- 底部按钮 -->
    <view
      v-if="showAbortButton" class="sticky bottom-0 bottom-0 left-0 left-0 right-0 right-0 z-10 box-border w-full bg-white p-3"
      style="--wot-button-medium-height: 44px;"
    >
      <view class="flex items-center justify-between">
        <wd-button
          custom-class="flex-auto"
          type="error"
          plain
          block
          :round="false"
          @click="abortOrder"
        >
          作废
        </wd-button>
      </view>
      <wd-gap height="0" safe-area-bottom />
    </view>
  </view>
</template>

<style lang="scss">
.sales-detail-page {
  min-height: 100vh;
}
</style>

<route lang="json">
{
  "name": "sales-back-detail",
  "style": {
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "详情"
  },
  "meta": {
    "permissions": ["vehSaleBckView"]
  }
}
</route>
