<script setup lang="ts">
import SalesBackEditSkuCard from './cmp/SalesBackEditSkuCard.vue'
import SalesBackEditSkeleton from './cmp/SalesBackEditSkeleton.vue'
import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
import type { VehSaleGetDraftGoodsResponseDTO, VehSaleGetDraftQpcResponseDTO } from '@/api/globals'

const globalToast = useGlobalToast()
const router = useRouter()
const { store } = storeToRefs(useSalesStore())

const { mescrollInit, downCallback, getMescroll } = useMescroll(onPageScroll, onReachBottom)
const { getDraftInfo, skuQueryParams, skuList, skuListLoading, skuTotal, reloadSkuList, skuPage, onSkuListSuccess, onSkuListError, draftInfoLoading, submitDraft } = useDraft('vehSaleBck')
skuQueryParams.storeGid = store.value?.gid

const { emitRefreshData } = useSendRefreshData('sales-back-edit') // 刷新编辑页面数据

function handleRefresh() {
  const mescroll = getMescroll()
  if (mescroll && mescroll.resetUpScroll) {
    mescroll.resetUpScroll()
  }
}

onSkuListSuccess((resp) => {
  getMescroll().endBySize(resp.data.data?.length || 0, skuTotal.value)
  if (skuTotal.value === 0) {
    getMescroll().showEmpty()
  }
})

onSkuListError((err) => {
  globalToast.error(err.error?.message || '加载车销退货商品失败')
  getMescroll().endErr()
})

// 上拉加载商品数据
async function upCallback(mescroll: any) {
  if (mescroll.num === 1) {
    await reloadSkuList()
  }
  else {
    skuPage.value = mescroll.num
  }
}

// 在组件挂载时获取状态栏高度和初始化数据
onLoad(async () => {
  await getDraftInfo()
})

// 改变商品数量
async function handleSpecChange(spec: VehSaleGetDraftQpcResponseDTO, sku: VehSaleGetDraftGoodsResponseDTO) {
  const index = skuList.value.findIndex(item => item.goods.gid === sku.goods.gid)
  const oldSku = CommonUtil.deepClone(skuList.value[index])
  skuList.value[index] = CommonUtil.deepClone(sku)

  try {
    const result = await submitDraft({
      gdCode: sku.goods.gdCode!,
      goods: {
        gid: sku.goods.gid,
        name: sku.goods.name,
        code: sku.goods.code,
      },
      munit: spec.munit,
      price: spec.price,
      qpc: spec.qpc,
      qpcStr: spec.qpcStr,
      qty: spec.qty,
      qtyStr: spec.qtyStr,
      singlePrice: spec.singlePrice,
      total: spec.singlePrice.multiply(spec.qty).scale(2),
      uuid: spec.uuid,
      version: sku.version,
      store: store.value!,
    })
    skuList.value[index].version = result.data?.version || skuList.value[index].version
    emitRefreshData()
  }
  catch (error) {
    console.log(error)
    skuList.value[index] = oldSku
  }
}

// 处理搜索事件
function handleSearch() {
  handleRefresh()
}

// 处理搜索框清除事件
function handleSearchClear() {
  skuQueryParams.keyword = undefined
  nextTick(() => {
    handleRefresh()
  })
}

// 返回上一页
function goBack() {
  router.back()
}
</script>

<template>
  <view class="pick-edit-search-page relative box-border min-h-screen w-screen overflow-x-hidden pt-52px">
    <!-- 搜索栏 -->
    <view class="fixed top-0 z-10 box-border w-full bg-white">
      <SearchBar
        v-model="skuQueryParams.keyword"
        placeholder="请输入商品信息"
        :show-filter="false"
        @search="handleSearch"
        @clear="handleSearchClear"
      >
        <template #action>
          <view class="cursor-pointer px-2" @click="goBack">
            <text class="text-28rpx text-[#343A40]">
              取消
            </text>
          </view>
        </template>
      </SearchBar>
    </view>

    <!-- 骨架屏 -->
    <SalesBackEditSkeleton v-if="draftInfoLoading" />
    <view v-else class="main relative z-1 w-screen bg-white">
      <!-- 主内容区域 -->
      <mescroll-body
        :down="downOption"
        :up="upOption"
        @init="mescrollInit"
        @down="downCallback"
        @up="upCallback"
      >
        <view class="p-3 pb-0">
          <SkuListSkeleton v-if="skuPage === 1 && skuListLoading" />
          <SalesBackEditSkuCard
            v-for="sku in skuList"
            :key="sku.goods.gid"
            :sku="sku"
            @change="handleSpecChange"
          />
        </view>
      </mescroll-body>
    </view>
    <wd-gap height="0" safe-area-bottom />
  </view>
</template>

<style lang="scss" scoped>
.pick-edit-search-page {
  min-height: 100vh;

  :deep(.mescroll-body) {
    min-height: calc(100vh - 52px) !important;
    min-height: calc(100vh - 52px - const(safe-area-inset-bottom)) !important;
    min-height: calc(100vh - 52px - env(safe-area-inset-bottom)) !important;
  }

}
</style>

<route lang="json">
{
  "name": "sales-back-edit-search",
  "style": {
    "navigationBarTitleText": "搜索商品",
    "backgroundColor": "#F9F9F9"
  }
}
</route>
