<!--
 * @Author: weish<PERSON>
 * @Date: 2025-06-04 15:04:33
 * @LastEditTime: 2025-06-13 10:27:03
 * @LastEditors: weisheng
 * @Description:
 * @FilePath: /lsym-cx-mini/src/pagesSalesMan/compensation/cmp/CompensationDetailSkuCard.vue
 * 记得注释
-->
<script setup lang="ts">
import type { WholeSaleDtlResponseDTO } from '@/api/globals'
import type { OrderStatus } from '@/utils/bill'

interface Props {
  sku: WholeSaleDtlResponseDTO
  status: OrderStatus
}

defineProps<Props>()
</script>

<template>
  <view class="compensation-sku-card rounded-lg bg-white px-3 py-3">
    <view class="flex flex-col">
      <!-- 商品名称 -->
      <view class="mb-1.5 text-28rpx text-[#2D2D2D] font-medium leading-[1.5em]">
        {{ sku.goods?.name || '未知商品' }}
      </view>

      <!-- 价格信息 -->
      <view class="mb-1.5 flex items-end">
        <text class="text-26rpx text-[#F57F00] leading-[1em]">
          ¥
        </text>
        <view class="flex items-end">
          <text class="text-36rpx text-[#F57F00] font-medium leading-[0.944em]">
            {{ formatPrice(sku.price || 0).integer }}
          </text>
          <text v-if="formatPrice(sku.price || 0).decimal" class="text-26rpx text-[#F57F00] font-medium leading-[1em]">
            .{{ formatPrice(sku.price || 0).decimal }}
          </text>
        </view>
        <text class="ml-0.5 text-3 text-[#8A8A8A] leading-[1em]">
          /{{ sku.munit || '-' }}
        </text>
      </view>

      <!-- 数量/赔付额信息 -->
      <view class="flex items-center justify-between">
        <text class="mr-2 text-28rpx text-[#2D2D2D] leading-[1.5em]">
          数量/赔付额
        </text>
        <text class="text-28rpx text-[#F14646] font-medium leading-[1.286em]">
          {{ sku.qty || 0 }}{{ sku.minMunit || '-' }}/计¥{{ (sku.total || 0).toFixed(2) }}
        </text>
      </view>
    </view>
  </view>
</template>
