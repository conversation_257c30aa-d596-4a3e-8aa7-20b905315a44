<script setup lang="ts">
import SkuImage from '@/components/SkuImage.vue'
import type { BillAttachDtlResponseDTO } from '@/api/globals'

interface Props {
  attachDetails?: BillAttachDtlResponseDTO[]
  customClass?: string
}

const props = withDefaults(defineProps<Props>(), {
  attachDetails: () => [],
  customClass: '',
})

// 图片列表，过滤掉空的URL
const imageList = computed(() => {
  return props.attachDetails
})

// 是否有附件
const hasAttachments = computed(() => {
  return imageList.value.length > 0
})

function handleClick(index: number) {
  uni.previewImage({
    urls: imageList.value.map(item => item.fileUrl!),
    current: index,
  })
}
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <view v-if="hasAttachments" :class="`compensation-attachments rounded-lg bg-white ${customClass}`">
    <!-- 标题栏 -->
    <view class="flex items-center py-3">
      <view class="mr-2 h-[28rpx] w-[8rpx] flex-shrink-0 rounded-[2rpx] bg-[var(--frequentapplication-primary-content,#1C64FD)]" />
      <text class="text-15px text-[#2C3036] font-medium leading-[1.467em]">
        赔付信息
      </text>
    </view>

    <!-- 图片网格 -->
    <view class="pb-3">
      <view class="grid grid-cols-5 -m-1">
        <view
          v-for="(attachment, index) in imageList"
          :key="index"
          class="m-1 aspect-square"
        >
          <SkuImage
            :enable-preview="false"
            :src="attachment.fileUrl"
            width="100%"
            height="100%"
            mode="aspectFill"
            radius="1px"
            custom-class="border border-[#E2E3E5]"
            @click="handleClick(index)"
          />
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
</style>
