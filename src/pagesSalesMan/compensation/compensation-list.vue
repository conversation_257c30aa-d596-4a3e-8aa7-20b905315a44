<script setup lang="ts">
import { reactive, ref } from 'vue'
import { isArray } from 'wot-design-uni/components/common/util'
import CompensationCard from './cmp/CompensationCard.vue'
import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
import type TimeFilter from '@/components/TimeFilter.vue'
import type { WholeSaleQueryFilter, WholeSaleQueryResponseDTO } from '@/api/globals'

const { currentWms } = useWmsStore()
const userStore = useUserStore()
// mescroll相关方法 - 使用hooks
const { mescrollInit, getMescroll, downCallback } = useMescroll(onPageScroll, onReachBottom)

const router = useRouter()
const globalToast = useGlobalToast()
const timeFilterRef = ref<InstanceType<typeof TimeFilter>>()

const isSalesman = computed(() => userStore.userRole === 'salesman')

// 仓库选择
const { wrhList, wrhLoading, handleWrhSelect, wrhId, currentWrh } = useWrhSelect(isSalesman.value ? userStore.vehSaleEmp?.gid : undefined, isSalesman.value ? undefined : currentWms?.gid)
function handleSelectWrh(event: { value: string, selectedItems: { label: string, value: string, code: string, name: string } }) {
  handleWrhSelect(event)
  handleRefresh()
}
// 查询参数
const queryParams = reactive<Omit<WholeSaleQueryFilter, 'page' | 'pageSize' | 'wrhGid'>>({
  keyword: '',
  beginDate: '',
  finishDate: '',
  sorts: [
    {
      field: 'lstupdTime',
      asc: false,
    },
  ],
})

// 使用 usePagination 实现分页查询
const {
  // 数据列表
  data: orderList,
  reload: reloadOrders,
  total,
  page,
} = usePagination(
  (page, pageSize) => Apis.wholesaleInterface.queryUsingPOST_6({
    data: {
      ...queryParams,
      wrhGid: wrhId.value ? Number(wrhId.value) : undefined,
      page: page - 1, // 后端页码从0开始，usePagination从1开始
      pageSize,
      wmsGid: userStore.userRole === 'salesman' ? undefined : currentWms?.gid,
      vehSaleEmpGid: userStore.userRole === 'salesman' ? userStore.vehSaleEmp?.gid : undefined,
    },
  }),
  {
    immediate: false,
    append: true,
    initialData: [],
    initialPageSize: 10,
    initialPage: 1,
    total: response => response.total,
    data: response => (response.data || []),
  },
).onSuccess((resp) => {
  getMescroll().endBySize(resp.data.data?.length || 0, total.value)
}).onError((error) => {
  globalToast.error(error.error?.message || '加载买赔单列表失败')
  getMescroll().endErr()
})

// 上拉加载更多 - 结合usePagination使用
async function upCallback(mescroll: any) {
  if (mescroll.num === 1) {
    await reloadOrders()
  }
  else {
    page.value = mescroll.num
  }
}

// 处理路由参数
onLoad((options: any) => {
  // 如果有时间范围参数，设置查询参数
  if (options.beginDate) {
    queryParams.beginDate = options.beginDate as string
  }

  if (options.endDate) {
    queryParams.finishDate = options.endDate as string
  }
})

function handleRefresh() {
  // 初始加载数据
  if (getMescroll()) {
    getMescroll().resetUpScroll()
  }
}

/**
 * 筛选时间的回调
 */
function handleTimeFilterConfirm(payload: { range: string | [string, string] }) {
  if (isArray(payload.range)) {
    queryParams.beginDate = payload.range[0]
    queryParams.finishDate = payload.range[1]
  }
  else {
    queryParams.beginDate = payload.range
    queryParams.finishDate = payload.range
  }
  handleRefresh()
}

// 查看买赔单详情
function viewOrderDetail(order: WholeSaleQueryResponseDTO) {
  if (!order.num) {
    globalToast.error('记录ID不存在')
    return
  }

  router.push({
    name: 'compensation-detail',
    params: { id: order.num },
  })
}
</script>

<template>
  <view class="compensation-list box-border min-h-screen bg-[#F9F9F9] pt-[calc(52px+88rpx)]">
    <!-- 搜索和筛选区域 - 固定在顶部 -->
    <view class="fixed-top-section">
      <SearchBar
        v-model="queryParams.keyword"
        placeholder="单号/仓位/业务员"
        :has-filter="!!queryParams.beginDate || !!queryParams.finishDate"
        @filter="timeFilterRef?.open()"
        @clear="handleRefresh"
        @search="handleRefresh"
      />

      <!-- 仓库筛选 -->
      <view class="h-88rpx flex items-center px-3">
        <wd-select-picker
          v-model="wrhId"
          :columns="wrhList"
          :loading="wrhLoading"
          title="选择仓位"
          label="仓位"
          type="radio"
          hide-label
          filterable
          use-default-slot
          @confirm="handleSelectWrh"
        >
          <template #default>
            <view
              class="flex items-center rounded bg-white px-3 py-1"
            >
              <text class="max-w-32 truncate text-28rpx text-[#2D2D2D] font-500">
                {{ currentWrh?.label || '全部仓位' }}
              </text>
              <text class="i-carbon-caret-down text-4 text-[var(--textapplication-text-3)]" />
            </view>
          </template>
        </wd-select-picker>
      </view>
    </view>

    <!-- 买赔单列表 -->
    <mescroll-body
      :down="downOption" :up="upOption" height="100%" safearea @init="mescrollInit"
      @down="downCallback" @up="upCallback"
    >
      <view class="mx-3">
        <CompensationCard
          v-for="order in orderList"
          :key="order.num"
          :order="order"
          @click="viewOrderDetail(order)"
        />
      </view>
    </mescroll-body>

    <!-- 筛选弹窗 -->
    <TimeFilter ref="timeFilterRef" @confirm="handleTimeFilterConfirm" />
  </view>
</template>

<style lang="scss" scoped>
.compensation-list {
  :deep(.mescroll-body) {
    min-height: calc(100vh - 52px - 42px) !important;
    min-height: calc(100vh - 52px - 42px - constant(safe-area-inset-bottom)) !important;
    min-height: calc(100vh - 52px - 42px - env(safe-area-inset-bottom)) !important;
  }
}

/* 固定在顶部的搜索栏和筛选条件 */
.fixed-top-section {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 10;
  background-color: #F9F9F9;
}
</style>

<route lang="json">
{
  "name": "compensation-list",
  "style": {
    "navigationBarTitleText": "买赔单"
  },
  "meta": {
    "permissions": ["wholeSaleView"]
  }
}
</route>
