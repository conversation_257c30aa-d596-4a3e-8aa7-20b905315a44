<!--
 * @Description: 买赔单详情页面
-->
<script setup lang="ts">
import { computed, ref } from 'vue'
import CompensationDetailSkuCard from './cmp/CompensationDetailSkuCard.vue'
import CompensationDetailAttachments from './cmp/CompensationDetailAttachments.vue'

const router = useRouter()
const { error: showError } = useGlobalToast()

// 单号
const id = ref<string>('')
// 关联单据显示模式
const relatedBillsMode = ref<'jump' | 'copy'>('jump')

// 加载单头详情
const {
  data: order,
  loading: orderLoading,
  send: loadOrderDetail,
} = useRequest(
  (num: string) => Apis.wholesaleInterface.getUsingGET_6({
    params: {
      num,
      fetchDetail: false, // 不直接获取商品详情
    },
  }),
  {
    immediate: false,
  },
).onError((error) => {
  showError(error.error.message || '获取买赔单详情失败')
})

// 订单详情（响应式计算）
const orderDetail = computed(() => order.value?.data)

// 加载商品数据
const {
  data: skuData,
  loading: skuLoading,
  send: loadSkuData,
} = useRequest(
  (num: string) => Apis.wholesaleInterface.getDetailsUsingPOST_2({
    data: {
      num,
      page: 0,
      pageSize: 3,
    },
  }),
  {
    immediate: false,
  },
).onError((error) => {
  showError(error.error.message || '获取买赔单商品失败')
})

// 商品列表
const skuList = computed(() => {
  return skuData.value?.data || []
})

// 是否还有更多商品
const hasMoreSku = computed(() => {
  return skuData.value?.more
})

// 买赔单状态
const orderStatus = computed(() => {
  if (!orderDetail.value)
    return 'initial'
  return getBillStatus(orderDetail.value.stat || 0)
})

// 统计数据 - 上部分数据
const topStats = computed(() => {
  if (!orderDetail.value) {
    return [
      { title: '赔付金额(元)', value: '0.00' },
    ]
  }
  return [
    { title: '赔付金额(元)', value: orderDetail.value.total || 0 },
  ]
})

// 统计数据 - 下部分数据
const bottomStats = computed(() => {
  return [
    { title: '赔付项数', value: orderDetail.value?.goodsCount || 0 },
  ]
})

// 跳转到商品清单详情
function goToSkuList() {
  router.push({
    name: 'compensation-detail-sku-list',
    params: {
      id: id.value,
      status: orderStatus.value,
    },
  })
}

/**
 * 跳转
 */
function handleBillClick(bill: { genCls?: string, genNum?: string }) {
  if (bill.genCls && bill.genNum) {
    router.push({
      name: 'back-diff-detail',
      params: { id: bill.genNum, mode: 'copy' },
    })
  }
}

// 页面加载
onLoad((options?: Record<string, any>) => {
  if (options?.id) {
    id.value = options.id
    // 检查是否传递了mode参数
    if (options.mode === 'copy') {
      relatedBillsMode.value = 'copy'
    }
    loadOrderDetail(id.value)
    loadSkuData(id.value)
  }
})
</script>

<template>
  <view class="compensation-detail-page relative min-h-screen w-screen flex flex-col bg-white">
    <!-- 主要内容 -->
    <view class="flex-1 p-3">
      <!-- 骨架屏 -->
      <DetailSkeleton v-if="orderLoading || skuLoading" />
      <!-- 实际内容 -->
      <block v-else>
        <!-- 状态栏 -->
        <DetailStatusHeader
          :status="orderStatus"
          :order-no="orderDetail?.num || ''"

          custom-class="mb-3"
        />

        <!-- 统计卡片 -->
        <DetailStatsCard
          :top-items="topStats"
          :bottom-items="bottomStats"
          custom-class="mb-3"
        />

        <!-- 商品清单 -->
        <view class="mb-3 flex flex-col rounded-lg bg-white py-3">
          <DetailTableHeader
            title="商品清单"
            :headers="['商品信息', '赔付额']"
            :show-more="hasMoreSku"
            @more-click="goToSkuList"
          />

          <!-- 商品列表 -->
          <view class="space-y-3">
            <view v-for="sku in skuList" :key="sku.goods?.gid" class="border-b border-[#F5F5F5] last:border-0">
              <CompensationDetailSkuCard :sku="sku" :status="orderStatus" />
            </view>
          </view>
        </view>

        <!-- 订单信息 -->
        <DetailInfoCard
          :items="[
            { label: '仓位', value: orderDetail?.wrh?.name },
            { label: '业务员', value: `${orderDetail?.vehSaleEmpName}[${orderDetail?.vehSaleEmp?.code}]` },
            { label: '操作人', value: orderDetail?.lastModifyOper },
            { label: '操作时间', value: orderDetail?.lstupdTime },
          ]"
        />

        <!-- 赔付信息 -->
        <CompensationDetailAttachments
          v-if="orderDetail?.attachDetails?.length"
          :attach-details="orderDetail?.attachDetails"
          custom-class="mb-3"
        />

        <!-- 关联单据 -->
        <DetailRelatedBills
          v-if="orderDetail?.srcNum"
          custom-class="px-0!"
          :related-bills=" [{
            genCls: orderDetail?.srcCls,
            genNum: orderDetail?.srcNum,
          }]"
          :mode="relatedBillsMode"
          @bill-click="handleBillClick"
        />
      </block>
    </view>
    <wd-gap :height="0" safe-area-bottom />
  </view>
</template>

<style lang="scss" scoped>
.compensation-detail-page {
  min-height: 100vh;
}
</style>

<route lang="json">
{
  "name": "compensation-detail",
  "style": {
    "navigationBarTitleText": "买赔单详情",
    "backgroundColor": "#F9F9F9"
  },
  "meta": {
    "permissions": ["wholeSaleView"]
  }
}
</route>
