<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue'
import dayjs from 'dayjs'
import { useQueue } from 'wot-design-uni'
import BackEditCard from './cmp/BackEditCard.vue'
import BackEditHeader from './cmp/BackEditHeader.vue'
import BackEditSkeleton from './cmp/BackEditSkeleton.vue'
import EditSort from '@/business/EditSort.vue'
import { useCategorySelect } from '@/composables/useCategorySelect'
import { useGlobalToast } from '@/composables/useGlobalToast'
import type { GCN, VehSaleGoodsDTO, VehSaleUseSignArvSubmitRequestDTO } from '@/api/globals'
import type { SortItem } from '@/composables/useDraft'

const globalToast = useGlobalToast()
const globalLoading = useGlobalLoading()
const { confirm: showConfirm } = useGlobalMessage()
const router = useRouter()
const userStore = useUserStore()
const { closeOutside } = useQueue()

const { emitRefreshData: emitRefreshSalesmanHome } = useSendRefreshData('salesman-home')

// 仓位相关
const currentWrh = ref<GCN>()
const skuList = ref<(VehSaleGoodsDTO & { tgpQty: number })[]>([])

// 商品列表数据（接口获取） - 只有获取到仓位后才请求
const {
  data: skuListData,
  loading: skuListLoading,
  send: loadSkuList,
} = useRequest(
  () => Apis.vehsaleusesignarvInterface.getArvGoodsUsingGET(),
  { immediate: false,
  },
).onError((error) => {
  globalLoading.close()
  globalToast.error(error.error.message || '获取商品列表失败')
}).onSuccess(() => {
  globalLoading.close()
  skuList.value = skuListData.value?.data?.map(item => ({
    ...item,
    tgpQty: 0,
  })) || []
})

// 获取仓位信息
const {
  data: wrhData,
  loading: wrhLoading,
  send: loadWrh,
} = useRequest(
  () => {
    const vehSaleEmpGid = userStore.userInfo?.vehSaleEmp?.gid
    if (!vehSaleEmpGid) {
      throw new Error('缺少业务员信息')
    }
    return Apis.vehsalewrhinvInterface.getWrhUsingGET({
      params: {
        vehSaleEmpGid,
      },
    })
  },
  { immediate: false },
).onError((error) => {
  globalLoading.close()
  globalToast.error(error.error.message || '获取仓位信息失败')
}).onSuccess(() => {
  globalLoading.close()
  currentWrh.value = wrhData.value?.data
  loadSkuList()
})

// 分类相关
const { categoryList: categoryListData, categoryId, categoryLoading } = useCategorySelect(true)

// 排序相关
const sortList = ref<SortItem[]>([
  { field: 'vehSaleWrhQty', name: '应回', asc: 0 },
])

/**
 * 根据分类过滤商品列表并排序
 */
const showSkuList = computed(() => {
  let filteredList = skuList.value

  // 先按分类过滤
  if (categoryId.value) {
    filteredList = filteredList.filter(item => item.category?.code === categoryId.value)
  }

  // 再按排序条件排序
  const activeSortItem = sortList.value.find(sort => sort.asc !== 0)
  if (activeSortItem) {
    filteredList = [...filteredList].sort((a, b) => {
      const field = activeSortItem.field
      let aVal = a[field as keyof typeof a]
      let bVal = b[field as keyof typeof b]

      // 处理不同类型的数据
      if (typeof aVal === 'string' && typeof bVal === 'string') {
        // 字符串比较
        aVal = aVal.toLowerCase()
        bVal = bVal.toLowerCase()
      }
      else if (typeof aVal === 'number' && typeof bVal === 'number') {
        // 数字比较
      }
      else {
        // 其他类型转为字符串比较
        aVal = String(aVal || '').toLowerCase()
        bVal = String(bVal || '').toLowerCase()
      }

      let result = 0
      if (aVal < bVal)
        result = -1
      else if (aVal > bVal)
        result = 1

      // 根据升序/降序返回结果
      return activeSortItem.asc === 1 ? result : -result
    })
  }

  return filteredList
})

// 处理分类列表，添加数量显示
const categoryList = computed(() => {
  return categoryListData.value.map((item) => {
    // 计算该分类下有数量的商品数
    const count = skuList.value.filter(sku =>
      (item.code === '' || sku.category?.code === item.code) && sku.tgpQty > 0,
    ).length
    return {
      label: item.label,
      value: item.value,
      code: item.code,
      count,
    }
  })
})

const showSkeleton = computed(() => {
  return (skuListLoading.value || categoryLoading.value || wrhLoading.value)
})

// 切换分类
function changeCategory(code: string) {
  categoryId.value = code
}

// 排序变化处理
function handleSortChange(sorts: SortItem[]) {
  sortList.value = sorts
}

// 商品数量变更事件（如需收集所有商品的数量变化可在此处实现）
function handleQuantityChange(payload: { sku: VehSaleGoodsDTO & { tgpQty?: number }, quantity: number }) {
  // 在 skuList 中找到对应的商品并更新 tgpQty
  const index = skuList.value.findIndex(item => item.gid === payload.sku.gid)
  if (index !== -1) {
    skuList.value[index].tgpQty = payload.quantity
  }
}

// 商品列表数据（接口获取） - 只有选择仓位后才请求
const {
  send: submit,
} = useRequest(
  (data: VehSaleUseSignArvSubmitRequestDTO) => Apis.vehsaleusesignarvInterface.submitUsingPOST_2({ data }),
  { immediate: false,
  },
).onError((error) => {
  globalLoading.close()
  globalToast.error(error.error.message || '获取商品列表失败')
}).onSuccess((resp) => {
  globalLoading.close()
  globalToast.success({
    msg: '提交成功',
    duration: 500,
    closed: () => {
      router.replace({
        name: 'back-detail',
        params: {
          id: resp.data.data!,
        },
      })
      emitRefreshSalesmanHome()
    },
  })
})

// 提交回货（如需收集所有商品数量可在此处处理）
function handleSubmit() {
  if (!currentWrh.value?.gid) {
    globalToast.warning('仓位信息尚未加载完成，请稍后再试')
    return
  }

  showConfirm({
    msg: '确定提交回货吗？',
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    success(res) {
      if (res.action === 'confirm') {
        submit({
          vehSaleEmp: userStore.userInfo?.vehSaleEmp,
          wrh: {
            gid: Number(currentWrh.value?.gid),
          },
          details: skuList.value
            .filter(item => item.tgpQty && item.tgpQty > 0) // 只提交有兑奖物数量的商品
            .map(item => ({
              gdGid: item.gid,
              tgpQty: item.tgpQty,
            })),
        })
      }
    },
  })
}

// 跳转到今日车销数据
function handleClickTodayData() {
  if (!currentWrh.value?.gid) {
    globalToast.warning('仓位信息尚未加载完成，请稍后再试')
    return
  }

  router.push({
    name: 'sales-list',
    params: {
      beginDate: dayjs().format('YYYY-MM-DD'),
      endDate: dayjs().format('YYYY-MM-DD'),
    },
  })
}

// 在组件挂载时加载仓位信息
onMounted(async () => {
  // 获取分类数据
  await Promise.resolve()

  // 检查用户信息
  if (!userStore.userInfo?.vehSaleEmp?.gid) {
    globalToast.error('用户信息异常，请重新登录')
    return
  }

  // 加载仓位信息
  loadWrh()
})
</script>

<template>
  <view class="back-edit box-border min-h-screen w-screen" @click="closeOutside">
    <BackEditHeader
      :current-wrh="currentWrh"
      custom-class="w-screen box-border"
      @click-today-data="handleClickTodayData"
    />

    <!-- 骨架屏 -->
    <BackEditSkeleton v-if="showSkeleton" />

    <!-- 主内容 -->
    <block v-else>
      <!-- 分类Tabs -->
      <view class="back-edit-main box-border flex flex-col bg-white">
        <wd-tabs v-model="categoryId" sticky custom-class="w-full h-100% min-h-100% flex-1 z-1 tabs-custom" @change="changeCategory">
          <wd-tab
            v-for="cat in categoryList"
            :key="cat.code"
            :title="cat.label"
            :name="cat.code"
            :badge-props="cat.count ? { isDot: true } : undefined"
          >
            <!-- 排序组件 -->
            <EditSort
              v-if="showSkuList.length > 0"
              v-model:sorts="sortList"
              @change="handleSortChange"
            />

            <view v-if="showSkuList.length > 0" class="min-h-full px-2.5">
              <BackEditCard
                v-for="(item, idx) in showSkuList"
                :key="item.gid || item.gdCode || idx"
                :sku="item"
                @quantity-change="handleQuantityChange"
              />
            </view>
            <EmptyStatus v-else tip="无此分类商品" custom-class="pt-8!" />
          </wd-tab>
        </wd-tabs>
      </view>

      <!-- 底部提交按钮 -->
      <view class="bottom-button-wrap fixed bottom-0 left-0 right-0 z-1 bg-white px-3 shadow-md">
        <view class="py-3">
          <wd-button type="primary" size="large" :round="false" block @click="handleSubmit">
            提交
          </wd-button>
        </view>
        <wd-gap :height="0" safe-area-bottom />
      </view>
    </block>
  </view>
</template>

<style lang="scss" scoped>
.back-edit {
  position: relative;
  padding-bottom: calc(48rpx + 44px)!important;
  padding-bottom: calc(48rpx + 44px + constant(safe-area-inset-bottom)) !important;
  padding-bottom: calc(48rpx + 44px + env(safe-area-inset-bottom)) !important;
  &-main {
    min-height: calc(100vh - 48rpx - 44px - 74rpx) !important;
    min-height: calc(100vh - 48rpx - 44px - 74rpx - constant(safe-area-inset-bottom)) !important;
    min-height: calc(100vh - 48rpx - 44px - 74rpx - env(safe-area-inset-bottom)) !important;
  }

  // tabs样式
  :deep(.tabs-custom){
    // 修复徽标导致文字溢出的问题
    .wd-tabs__nav-item-badge {
      display: flex;
      max-width: 100%;
      min-width: 0;

      .wd-badge {
        display: flex;
        max-width: 100%;
        min-width: 0;

        .wd-tabs__nav-item-text {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          flex: 1;
          min-width: 0;
        }
      }
    }
  }
}
</style>

<route lang="json">
  {
    "name": "back-edit",
    "style": {
      "navigationBarTitleText": "回货商品清单"
    },
    "meta": {
      "permissions": ["vehSaleUseSignArvCreate"]
    }
  }
  </route>
