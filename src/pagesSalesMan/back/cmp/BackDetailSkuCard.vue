<!--
 * @Author: weish<PERSON>
 * @Date: 2025-05-30 18:05:33
 * @LastEditTime: 2025-07-01 14:12:22
 * @LastEditors: weisheng
 * @Description:
 * @FilePath: /lsym-cx-mini/src/pagesSalesMan/back/cmp/BackDetailSkuCard.vue
 * 记得注释
-->
<script setup lang="ts">
import { computed } from 'vue'
import type { VehSaleUseSignArvDtlResponseDTO } from '@/api/globals'
import type { OrderStatus } from '@/utils/bill'

const props = defineProps<{
  sku: VehSaleUseSignArvDtlResponseDTO
  status: OrderStatus
}>()

// 列宽计算，参考PickDetailSkuCard.vue
const displayCount = computed(() => {
  return props.status === 'audited' ? 3 : 2
})
const itemWidth = computed(() => {
  return `${Math.floor((100 / displayCount.value) * 100) / 100}%`
})

const diffQty = computed(() => {
  if (!props.sku)
    return ''
  const shouldQty = Number(props.sku.shouldQty)
  const qty = Number(props.sku.qty).add(Number(props.sku.tgpQty))
  const diff = shouldQty.minus(qty)

  if (diff === 0) {
    return ''
  }
  const amount = diff.multiply(Number(props.sku.price)).divide(Number(props.sku.qpc) || 1).scale(2)
  return diff > 0 ? `少${diff}${props.sku.minMunit}/计¥${amount}` : `多${Math.abs(diff)}${props.sku.minMunit}/计¥${amount}`
})
</script>

<template>
  <view class="back-detail-sku-card mb-2 rounded-lg bg-white px-3 py-2">
    <!-- 商品名 -->
    <view class="line-clamp-2 mb-1 break-all text-28rpx text-[#2D2D2D] font-medium leading-[1.5]">
      {{ sku.goods?.name }}
    </view>
    <!-- 价格和标签区 -->
    <view class="mb-2 flex items-center">
      <view class="flex flex-wrap items-end">
        <view v-if="sku.tgpQty" class="mr-2 inline-block rounded bg-[#FFF5F5] px-2 py-0.5 text-26rpx text-[#F14646]">
          含兑奖物
        </view>
        <view class="mr-2 inline-block break-all rounded bg-[#F5F6F7] px-2 py-0.5 text-26rpx text-[#5C5C5C]">
          {{ sku.gdCode }}
        </view>
        <view class="inline-block break-all rounded bg-[#F5F6F7] px-2 py-0.5 text-26rpx text-[#5C5C5C]">
          {{ sku.qpcStr }}
        </view>
      </view>
    </view>
    <!-- 价格和标签区 -->
    <view class="mb-2 flex items-center">
      <view class="flex items-end">
        <text class="text-26rpx text-[#F57F00]">
          ¥
        </text>
        <text class="text-36rpx text-[#F57F00] font-medium leading-none">
          {{ formatPrice(sku.price || 0).integer }}
        </text>
        <text v-if="formatPrice(sku.price || 0).decimal" class="text-26rpx text-[#F57F00]">
          .{{ formatPrice(sku.price || 0).decimal }}
        </text>
        <text class="ml-1 text-3 text-[#8A8A8A]">
          /{{ sku.munit }}
        </text>
      </view>
    </view>

    <!-- 件数/应回/实回 -->
    <view class="flex items-center justify-between border-t border-[#F5F5F5] pb-1 pt-2">
      <text :style="{ width: itemWidth }" class="text-28rpx text-[#5C5C5C]">
        件数
      </text>
      <text
        :style="{ width: itemWidth }" class="text-center text-28rpx text-[#2D2D2D]" :class="[
          status === 'audited' ? 'text-center' : 'text-right',
        ]"
      >
        {{ getQpcQty(sku.shouldQtyStr!, sku.munit!, sku.minMunit!) }}
      </text>
      <text
        v-if="status === 'audited'"
        :style="{ width: itemWidth }"
        class="text-right text-28rpx text-[#2D2D2D]"
      >
        {{ getQpcQty(sku.qtyStr!, sku.munit!, sku.minMunit!) }}
      </text>
    </view>

    <view v-if="sku.tgpQty " class="flex items-center justify-between border-t border-[#F5F5F5] pb-1 pt-2">
      <text class="text-28rpx text-[#5C5C5C]">
        兑奖物
      </text>
      <text
        class="text-center text-28rpx text-[#2D2D2D]"
      >
        {{ sku.tgpQty }}{{ sku.minMunit }}
      </text>
    </view>

    <view v-if="diffQty" class="flex items-center justify-between border-t border-[#F5F5F5] pb-1 pt-2">
      <text class="text-28rpx text-[#5C5C5C]">
        差异/金额
      </text>
      <text
        class="text-center text-28rpx text-[var(--frequentapplication-red-content)]"
      >
        {{ diffQty }}
      </text>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.back-detail-sku-card {
  border-radius: 8px;
  background: #fff;
  margin-bottom: 8px;
  box-sizing: border-box;

}
</style>
