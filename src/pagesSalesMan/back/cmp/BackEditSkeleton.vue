<!--
 * @Author: weish<PERSON>
 * @Date: 2025-05-22 14:13:57
 * @LastEditTime: 2025-06-03 17:55:48
 * @LastEditors: weisheng
 * @Description:
 * @FilePath: /lsym-cx-mini/src/pagesSalesMan/back/cmp/BackEditSkeleton.vue
 * 记得注释
-->
<!--
 * @Description: 回货单编辑页骨架屏组件
-->
<script setup lang="ts">
// 组件不需要任何props或状态
</script>

<template>
  <view class="back-edit-skeleton flex">
    <!-- 主内容区域骨架屏 -->
    <view class="w-full flex flex-auto flex-col">
      <!-- 头部Tabs骨架屏（5个圆角tab） -->
      <view class="header-section bg-white px-4 pb-2 pt-4">
        <view class="mr-2 flex items-center justify-between">
          <wd-skeleton v-for="i in 5" :key="i" :row-col="[{ width: '64px', height: '28px', borderRadius: '16px' }]" animation="flashed" custom-class="tab-skeleton" />
        </view>
      </view>

      <!-- 商品列表骨架屏（横向卡片，紧凑） -->
      <view class="flex-1 px-4 pb-4 pt-2">
        <view v-for="i in 6" :key="i" class="mb-3">
          <view class="flex items-center bg-white shadow-sm">
            <!-- 左侧图片占位 -->
            <wd-skeleton :row-col="[{ width: '160rpx', height: '160rpx', borderRadius: '8px' }]" animation="flashed" />
            <!-- 右侧文本占位 -->
            <view class="ml-3 flex flex-1 flex-col gap-2">
              <wd-skeleton :row-col="[{ width: '60%', height: '18px' }]" animation="flashed" />
              <wd-skeleton :row-col="[{ width: '40%', height: '14px' }]" animation="flashed" />
              <wd-skeleton :row-col="[{ width: '30%', height: '14px' }]" animation="flashed" />
              <wd-skeleton :row-col="[{ width: '50%', height: '16px' }]" animation="flashed" />
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.back-edit-skeleton {
  width: 100vw;
  height: 100vh;
  background-color: #F9F9F9;
}
.tab-skeleton {
  border-radius: 16px !important;
}
</style>
