<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import SkuImage from '@/components/SkuImage.vue'
import type { VehSaleGoodsDTO } from '@/api/globals'

const props = defineProps({
  sku: {
    type: Object as PropType<VehSaleGoodsDTO & { tgpQty: number }>,
    required: true,
  },
})
const emit = defineEmits(['quantity-change'])

/**
 * 商品信息
 */
const skuValue = ref<VehSaleGoodsDTO & { tgpQty?: number }>(CommonUtil.deepClone(props.sku))

watch(() => props.sku, (newVal) => {
  if (!CommonUtil.isEqual(skuValue.value, newVal)) {
    skuValue.value = CommonUtil.deepClone(newVal)
  }
}, {
  deep: true,
})

// 内部展开状态
const expanded = ref(false)

// 数量本地状态
const quantity = ref(props.sku.tgpQty || 0)

// 监听props变化，同步本地状态
watch(() => props.sku.tgpQty, (newVal) => {
  quantity.value = newVal || 0
}, { immediate: true })

// 步进器操作
function handleQuantityChange({ value }: { value: number }) {
  if (value < 0)
    value = 0
  quantity.value = value
  emit('quantity-change', { sku: props.sku, quantity: value })
}

// 展开/收起
function toggleExpand() {
  expanded.value = !expanded.value
}

/**
 * 商品图片列表
 */
const skuImgList = computed(() => {
  const imgList = skuValue.value.imageDetails && skuValue.value.imageDetails.length > 0 ? skuValue.value.imageDetails.map(item => item.fileUrl) : []
  return imgList.filter(item => CommonUtil.isDef(item))
})

/**
 * 商品主图
 */
const skuMainImg = computed(() => {
  return skuImgList.value.length > 0 ? skuImgList.value[0] : ''
})
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <view class="back-edit-card flex overflow-hidden rounded-lg bg-white px-3 py-3">
    <!-- 商品图片 -->
    <view class="relative h-172rpx w-172rpx flex-none">
      <SkuImage
        :src="skuMainImg"
        :preview-src="skuImgList"
        width="172rpx"
        height="172rpx"
        mode="widthFix"
        custom-class="mr-3 flex-none"
      />
    </view>
    <!-- 商品信息区 -->
    <view class="ml-3 flex-1">
      <view class="mb-1">
        <text class="line-clamp-2 break-all text-28rpx text-[#2D2D2D] font-medium">
          {{ skuValue.name }}
        </text>
      </view>
      <view class="mb-1">
        <view class="inline-block break-all rounded bg-[#F5F6F7] px-1 py-0 text-26rpx text-[#5C5C5C]">
          {{ skuValue.gdCode }}
        </view>
      </view>
      <view class="flex items-center justify-between">
        <text class="text-26rpx text-[#8A8A8A]">
          件数：{{ getQpcQty(skuValue.vehSaleWrhQtyStr!, skuValue.munit!, skuValue.minMunit!) }}({{ skuValue.vehSaleWrhQty }}{{ skuValue.minMunit }})
        </text>
      </view>
      <!-- 展开/收起按钮 -->
      <view class="mt-1 w-full flex justify-end">
        <view class="flex cursor-pointer select-none items-center" @click="toggleExpand">
          <text class="mr-1 text-26rpx text-[#1C64FD] font-medium">
            {{ expanded ? '收起兑奖物' : (quantity > 0 ? '编辑兑奖物' : '添加兑奖物(可选)') }}
          </text>

          <text v-if="expanded" class="i-tdesign-chevron-up text-28rpx text-[#1C64FD]" />
          <text v-else class="i-tdesign-chevron-down text-28rpx text-[#1C64FD]" />
        </view>
      </view>
      <!-- 兑奖物内容区 -->
      <view v-if="expanded" class="mt-2 w-full border-t border-[#EFEFEF] pt-3">
        <view class="flex items-center justify-between">
          <view class="flex items-center">
            <wd-tooltip placement="top-start" content="门店凭兑奖物兑换了商品，可在此添加数量">
              <view class="i-carbon-information text-3 text-[var(--textapplication-text-4)]" />
            </wd-tooltip>
            <view class="ml-1 flex items-center">
              <text class="text-26rpx text-[var(--textApplication-text-1)]">
                兑奖物
              </text>
            </view>
          </view>
          <view class="flex items-center">
            <wd-input-number
              v-model="quantity"
              :min="0"
              input-width="100%"
              custom-class="input-number-adjust"
              allow-null
              :precision="0"
              @change="handleQuantityChange"
            />
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.back-edit-card {
  min-height: 110px;
  :deep(){
    .wd-tooltip__target{
      display: flex;
      align-items: center;
    }
  }
}

// 自定义数量输入步进器样式
:deep(.input-number-adjust) {
  // 按钮
  .wd-input-number__action {
    width: 56rpx;
    height: 56rpx;
  }
  // 输入框
  .wd-input-number__inner{
    width: 100rpx;
    height: 56rpx;
    .wd-input-number__input{
      height: 100%;
      font-size: 30rpx;
      font-weight: 500;
    }
  }
}
</style>
