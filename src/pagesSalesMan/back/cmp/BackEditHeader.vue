<!--
 * @Author: weish<PERSON>
 * @Date: 2025-05-22 11:18:38
 * @LastEditTime: 2025-06-20 14:56:25
 * @LastEditors: weisheng
 * @Description:
 * @FilePath: /lsym-cx-mini/src/pagesSalesMan/back/cmp/BackEditHeader.vue
 * 记得注释
-->
<script setup lang="ts">
import type { GCN } from '@/api/globals'

defineProps({
  // 当前仓位
  currentWrh: {
    type: Object as PropType<GCN | null>,
    default: null,
  },
  // 自定义类名
  customClass: {
    type: String,
    default: '',
  },
  // 自定义样式
  customStyle: {
    type: String,
    default: '',
  },
})

const _emit = defineEmits<{
  (e: 'clickTodayData'): void
}>()
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <view class="back-edit-header box-border h-74rpx w-full flex items-center justify-between bg-[var(--greyapplication-bottomlayer)] px-28rpx" :class="customClass" :style="customStyle">
    <view class="flex items-center text-28rpx text-[var(--textapplication-text-2)]">
      <text class="i-tdesign-home mr-1" />
      <text class="mr-2">
        仓位
      </text>
      <text class="mr-2 max-w-320rpx truncate text-[var(--textapplication-text-1)]">
        {{ currentWrh?.name || '加载中...' }}
      </text>
    </view>
    <view class="inline-flex items-center text-28rpx" @click="$emit('clickTodayData')">
      <text class="text-[#1C64FD]">
        今日车销数据
      </text>
      <text class="i-carbon-chevron-right text-[#1C64FD]" />
    </view>
  </view>
</template>

<style lang="scss" scoped>
</style>
