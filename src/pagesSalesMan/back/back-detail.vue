<script setup lang="ts">
import { computed, ref } from 'vue'
import BackDetailSkuCard from './cmp/BackDetailSkuCard.vue'
import { createGlobalLoadingMiddleware } from '@/api/core/middleware'
import type { VehSaleUseSignArvGenBillDTO } from '@/api/globals'

const { emitRefreshData: emitRefreshBackList } = useSendRefreshData('back-list') // 刷新列表，本页面提交成功需要刷新列表

const router = useRouter()
const userStore = useUserStore()
const { confirm: showConfirm } = useGlobalMessage()
const { success: showSuccess, error: showError } = useGlobalToast()
const globalLoading = createGlobalLoadingMiddleware({ loadingText: '作废中...' })
const { checkPermission } = usePermissionChecker()
// 单号
const id = ref<string>('')
// 关联单据显示模式
const relatedBillsMode = ref<'jump' | 'copy'>('jump')

// 加载单头详情
const {
  data: order,
  loading: orderLoading,
  send: loadOrderDetail,
} = useRequest(
  (num: string) => Apis.vehsaleusesignarvInterface.getUsingGET_2({
    params: {
      num,
      fetchDetail: false,
    },
  }),
  {
    immediate: false,
  },
).onError((error) => {
  showError(error.error.message || '获取回货单详情失败')
})

// 单头详情
const orderDetail = computed(() => {
  return order.value?.data
})

// 加载商品数据
const {
  data: skuData,
  loading: skuLoading,
  send: loadSkuData,
} = useRequest(
  (num: string) => Apis.vehsaleusesignarvInterface.getDetailsUsingPOST({
    data: {
      num,
      page: 0,
      pageSize: 3,
    },
  }),
  {
    immediate: false,
  },
).onError((error) => {
  showError(error.error.message || '获取回货单商品失败')
})

// 商品列表
const skuList = computed(() => {
  return skuData.value?.data || []
})

// 是否还有更多商品
const hasMoreSku = computed(() => {
  return skuData.value?.more
})

// 回货单状态
const orderStatus = computed(() => {
  if (!orderDetail.value)
    return 'initial'
  return getBillStatus(orderDetail.value.stat || 0)
})

// 统计数据 - 上部分数据
const topStats = computed(() => {
  if (!orderDetail.value) {
    return [
      { title: '回货应回金额(元)', value: '0.00' },
    ]
  }
  return [
    { title: orderStatus.value === 'audited' ? '回货实回金额(元)' : '回货应回金额(元)', value: orderDetail.value.total || 0 },
  ]
})

// 统计数据 - 下部分数据
const bottomStats = computed(() => {
  return [
    { title: '商品项数(种)', value: orderDetail.value?.goodsCount || 0 },
  ]
})

// 是否显示作废按钮
const showAbortButton = computed(() => {
  return orderStatus.value === 'applied' && checkPermission('vehSaleUseSignArvAbort')
})

// 是否显示打印按钮
const showPrintButton = computed(() => {
  return (orderStatus.value === 'applied' || orderStatus.value === 'audited') && userStore.userRole === 'salesman'
})

// 作废回货单
const { send: handleAbortRequest } = useRequest(
  (num: string) => Apis.vehsaleusesignarvInterface.abortUsingPOST_2({
    data: { num, version: orderDetail.value?.version || 1 },
  }),
  {
    immediate: false,
    middleware: globalLoading,
  },
).onSuccess(() => {
  showSuccess({
    msg: '作废成功',
    duration: 500,
    closed() {
      emitRefreshBackList()
      router.replace({ name: 'back-detail', params: { id: id.value } })
    },
  })
}).onError((error) => {
  showError(error.error?.message || '作废失败')
})

// 作废操作
function abortOrder() {
  if (!id.value) {
    showError('缺少记录ID')
    return
  }
  showConfirm({
    title: '提示',
    msg: '确认作废该回货单吗？',
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    success: (res: any) => {
      if (res.action === 'confirm') {
        handleAbortRequest(id.value)
      }
    },
  })
}

// 打印
function printOrder() {
  if (!id.value) {
    showError('缺少记录ID')
    return
  }
  showConfirm({
    title: '提示',
    msg: '确认打印该回货单吗？',
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    success: (res: any) => {
      if (res.action === 'confirm') {
        router.push({
          name: 'print',
          params: { id: id.value, moduleId: 'vehSaleUseSignArv' },
        })
      }
    },
  })
}

// 跳转到商品清单详情
function goToSkuList() {
  router.push({
    name: 'back-detail-sku-list',
    params: {
      id: id.value,
      status: orderStatus.value,
    },
  })
}

/**
 * 处理关联单据点击事件
 */
function handleBillClick(bill: VehSaleUseSignArvGenBillDTO) {
  // cls 有三种
  // 车销回货差异、车销领货、车销领货退货
  // 根据单据类型跳转到对应的详情页面
  const routes = {
    车销回货差异: 'back-diff-detail', // 差异单详情
    车销领货: 'pick-detail', // 领货单详情
    车销领货退货: 'return-pick-detail', // 领货退货详情
    车销: 'sales-detail', // 车销单详情
  }

  const routeName = routes[bill.genCls as keyof typeof routes]
  if (routeName && bill.genNum) {
    router.push({
      name: routeName,
      params: { id: bill.genNum, mode: 'copy' },
    })
  }
  else {
    console.warn('无法识别的单据类型或缺少单号:', bill)
  }
}

onLoad((options: any) => {
  id.value = options.id as string
  // 检查是否传递了mode参数
  if (options.mode === 'copy') {
    relatedBillsMode.value = 'copy'
  }
  loadOrderDetail(id.value)
  loadSkuData(id.value)
})
</script>

<template>
  <view class="receipt-detail-page relative min-h-screen w-screen flex flex-col bg-white">
    <!-- 主要内容 -->
    <view class="flex-1 p-3">
      <!-- 骨架屏 -->
      <DetailSkeleton v-if="orderLoading || skuLoading" />
      <!-- 实际内容 -->
      <block v-else>
        <!-- 状态栏 -->
        <DetailStatusHeader
          :status="orderStatus"
          :order-no="orderDetail?.num || ''"
          custom-class="mb-3"
        />

        <!-- 统计卡片 -->
        <DetailStatsCard
          :top-items="topStats"
          :bottom-items="bottomStats"
          custom-class="mb-3"
        />

        <!-- 商品清单 -->
        <view class="flex flex-col rounded-lg bg-white py-3">
          <DetailTableHeader
            title="商品清单"
            :headers="orderStatus === 'audited' ? ['商品信息', '应回', '实回'] : ['商品信息', '应回']"
            :show-more="hasMoreSku"
            @more-click="goToSkuList"
          />

          <!-- 商品列表 -->
          <view class="space-y-3">
            <view v-for="(sku, idx) in skuList" :key="sku.goods?.gid || idx" class="border-b border-[#F5F5F5] last:border-0">
              <BackDetailSkuCard :sku="sku" :status="orderStatus" />
            </view>
          </view>
        </view>

        <!-- 订单信息 -->
        <DetailInfoCard
          :items="[
            { label: '回货仓位', value: orderDetail?.wrh?.name },
            { label: '业务员', value: `${orderDetail?.vehSaleEmpName}[${orderDetail?.vehSaleEmp?.code}]` },
            { label: '操作人', value: orderDetail?.lastModifyOper },
            { label: '操作时间', value: orderDetail?.lstupdTime },
          ]"
        />

        <!-- 关联单据 -->
        <DetailRelatedBills
          :related-bills="orderDetail?.genBillDetails || []"
          :mode="relatedBillsMode"
          @bill-click="handleBillClick"
        />
      </block>
    </view>
    <!-- 底部按钮 -->
    <view
      v-if="showAbortButton || showPrintButton"
      class="sticky bottom-0 left-0 right-0 z-10 box-border w-full bg-white p-3"
      style="--wot-button-medium-height: 44px;"
    >
      <view class="flex items-center justify-between">
        <wd-button
          v-if="showAbortButton"
          :custom-class="showPrintButton ? 'flex-auto' : 'w-full!'"
          type="error"
          plain
          block
          size="large"
          :round="false"
          @click="abortOrder"
        >
          作废
        </wd-button>
        <wd-button
          v-if="showPrintButton"
          :custom-class="showAbortButton ? 'flex-auto ml-3!' : 'w-full!'"
          block
          type="primary"
          size="large"
          :round="false"
          @click="printOrder"
        >
          打印
        </wd-button>
      </view>
      <wd-gap height="0" safe-area-bottom />
    </view>
  </view>
</template>

<style lang="scss" scoped>
.receipt-detail-page {
  min-height: 100vh;
}
</style>

<route lang="json">
{
  "name": "back-detail",
  "style": {
    "navigationBarTitleText": "详情",
    "backgroundColor": "#F9F9F9"
  },
  "meta": {
    "permissions": ["vehSaleUseSignArvView"]
  }
}
</route>
