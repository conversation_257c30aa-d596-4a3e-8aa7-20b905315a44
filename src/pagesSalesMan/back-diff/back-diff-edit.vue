<!--
 * @Description: 回货差异详情编辑页面 - 录入差异解决
-->
<script setup lang="ts">
import { deepClone } from 'wot-design-uni/components/common/util'
import BackDiffEditSkuCard from './cmp/BackDiffEditSkuCard.vue'
import BuyCompensationPop from './cmp/BuyCompensationPop.vue'
import OpenBillPop from './cmp/OpenBillPop.vue'
import type { BillSubmitAttachDtlRequestDTO, VehSaleUseSignArvDiffDtlResponseDTO } from '@/api/globals'
import type { SortItem } from '@/composables/useDraft'
import { createGlobalLoadingMiddleware } from '@/api/core/middleware'
// 获取OSS配置
const { ossInfo } = useOssInfo()
const router = useRouter()
const { success: showSuccess, error: showError } = useGlobalToast()
const globalLoading = useGlobalLoading()
const { lockPage, unlockPage, pageStyle } = useLockPage()

const { emitRefreshData: emitRefreshBackDiffList } = useSendRefreshData('back-diff-list') // 刷新列表，本页面提交成功需要刷新列表
const { checkAnyPermission, checkPermission } = usePermissionChecker()
const hasPermission = computed(() => {
  return checkAnyPermission(['wholeSaleCreate', 'vehSaleCreate'])
})
// 单号
const id = ref<string>('')
// 商品数据
const skuData = ref<(VehSaleUseSignArvDiffDtlResponseDTO & { value: number })[]>([])

// 买赔弹窗组件引用
const buyCompensationPopRef = ref<InstanceType<typeof BuyCompensationPop>>()
// 开单弹窗组件引用
const openBillPopRef = ref<InstanceType<typeof OpenBillPop>>()

// 加载单头详情
const {
  data: order,
  loading: orderLoading,
  send: loadOrderDetail,
} = useRequest(
  (num: string) => Apis.vehsaleusesignarvdiffInterface.getUsingGET_3({
    params: {
      num,
      fetchDetail: true,
    },
  }),
  {
    immediate: false,
  },
)
  .onSuccess((resp) => {
    if (resp.data.data) {
      skuData.value = (resp.data.data.details || []).map((item) => {
        // const unhandledQty = Number(item.qty || 0).minus(Number(item.procQty || 0))
        return {
          ...item,
          value: 0,
        }
      })
    }
  })
  .onError((error) => {
    showError(error.error.message || '获取差异单详情失败')
  })

// 订单详情
const orderDetail = computed(() => order.value?.data)

// 差异处理状态
const orderStatus = computed(() => {
  if (!orderDetail.value)
    return 'initial'
  return getBillStatus(orderDetail.value.stat || 0, true)
})

// 获取分类相关数据和方法
const { categoryList, categoryId } = useCategorySelect()

// 表头配置
const tableHeaders = ref([
  { title: '规格' },
  { title: '差异数', field: 'qty', sortable: true },
  { title: '未处理', field: 'unhandledQty', sortable: true },
  { title: '操作数' },
])

// 当前排序条件
const currentSort = ref<SortItem[]>([])

// 商品列表（根据分类筛选）
const skuList = computed(() => {
  const allSkus = deepClone(skuData.value || [])

  let filterSkuList = !categoryId.value
    ? allSkus
    : allSkus.filter((sku) => {
        const goodsCategory = sku?.category?.code
        return goodsCategory === categoryId.value
      })
  const sortByQty = currentSort.value.find(item => item.field === 'qty')
  const sortByUnhandledQty = currentSort.value.find(item => item.field === 'unhandledQty')
  filterSkuList = filterSkuList.sort((a, b) => {
    if (sortByQty) {
      return sortByQty.asc === 1 ? Number(a.qty) - Number(b.qty) : sortByQty.asc === -1 ? Number(b.qty) - Number(a.qty) : 0
    }
    if (sortByUnhandledQty) {
      const aUnhandledQty = Number(a.qty || 0).minus(Number(a.procQty || 0))
      const bUnhandledQty = Number(b.qty || 0).minus(Number(b.procQty || 0))
      return sortByUnhandledQty.asc === 1 ? aUnhandledQty - bUnhandledQty : sortByUnhandledQty.asc === -1 ? bUnhandledQty - aUnhandledQty : 0
    }
    return 0
  })

  // 前端匹配分类
  return filterSkuList
})

/**
 * 处理排序变化
 * @param sortItems 排序项列表
 */
function handleSortChange(sortItems: SortItem[]) {
  currentSort.value = sortItems
}

/**
 * 处理商品数量变更
 */
function handleQuantityUpdate({ sku }: { sku: VehSaleUseSignArvDiffDtlResponseDTO & { value: number } }) {
  const index = skuData.value.findIndex(item => item.goods?.gid === sku.goods?.gid)
  if (index > -1) {
    skuData.value[index] = sku
  }
}

// 办理买赔
const {
  send: handleCompensation,
} = useRequest(
  (num: string, imgList: BillSubmitAttachDtlRequestDTO[]) => Apis.vehsaleusesignarvdiffInterface.submitPayUsingPOST({
    data: {
      num,
      goodsDetails: (skuData.value.filter(item => item.value > 0)).map(item => ({
        gdGid: item.goods?.gid || 0,
        qty: item.value || 0,
      })),
      vehSaleEmpGid: orderDetail.value?.vehSaleEmp?.gid || 0,
      attachDetails: imgList,
    },
  }),
  {
    immediate: false,
    middleware: createGlobalLoadingMiddleware({
      loadingText: '办理中...',
    }),
  },
).onSuccess((resp) => {
  globalLoading.close()
  showSuccess({
    msg: '办理买赔成功',
    duration: 500,
    closed() {
      emitRefreshBackDiffList()
      router.replace({
        name: 'compensation-detail',
        params: {
          id: resp.data.data!,
        },
      })
    },
  })
}).onError((error) => {
  globalLoading.close()
  showError(error.error?.message || '办理买赔失败')
})

// 去开单
const {
  send: handleOpenBill,
} = useRequest(
  (num: string, storeGid: number) => Apis.vehsaleusesignarvdiffInterface.submitSaleUsingPOST({
    data: {
      num,
      storeGid,
      vehSaleEmpGid: orderDetail.value?.vehSaleEmp?.gid || 0,
      goodsDetails: (skuData.value.filter(item => item.value > 0)).map(item => ({
        gdGid: item.goods?.gid || 0,
        qty: item.value || 0,
      })),
    },
  }),
  {
    immediate: false,
    middleware: createGlobalLoadingMiddleware({
      loadingText: '开单中...',
    }),
  },
).onSuccess((resp) => {
  globalLoading.close()
  const billNum = resp.data.data
  if (billNum) {
    showSuccess({
      msg: '开单成功',
      duration: 500,
      closed() {
        emitRefreshBackDiffList()
        // 跳转到新开的单据详情页面
        router.replace({
          name: 'sales-detail',
          params: { id: billNum },
        })
      },
    })
  }
  else {
    router.back()
  }
}).onError((error) => {
  globalLoading.close()
  showError(error.error?.message || '开单失败')
})

/**
 * 办理买赔操作
 */
function compensationAction() {
  if (!orderDetail.value) {
    showError('缺少单据信息')
    return
  }
  if (skuData.value.every(item => item.value === 0)) {
    showError('商品数量都为0，请重新选择！')
    return
  }

  // 计算买赔统计信息
  const totalItems = skuData.value.filter(item => item.value > 0).length
  const totalAmount = skuData.value.reduce((sum, item) => sum + (item.value || 0) * (item.price || 0), 0)

  if (!ossInfo.value) {
    showError('获取上传配置失败')
    return
  }

  // 打开买赔弹窗
  buyCompensationPopRef.value?.open({
    title: '买赔详情清单',
    skuList: skuData.value.filter(item => item.value > 0),
    totalItems,
    totalAmount,
    ossInfo: ossInfo.value,
    fileList: [],
    onConfirm: (imgList: BillSubmitAttachDtlRequestDTO[]) => {
      // 提交买赔请求
      handleCompensation(orderDetail.value!.num!, imgList)
    },
  })
}

const genBills = computed(() => {
  const genBills = orderDetail.value?.genDetails || []
  if (orderDetail.value?.vehSaleUseSignArvNum) {
    genBills.push({
      genCls: '回货单',
      genNum: orderDetail.value?.vehSaleUseSignArvNum,
    })
  }
  return genBills
})

// 关联单据显示模式
const relatedBillsMode = ref<'jump' | 'copy'>('jump')

/**
 * 处理关联单据点击事件
 */
function handleBillClick(bill: {
  genCls?: string
  genNum?: string
}) {
  // 根据单据类型跳转到对应的详情页面
  const routes = {
    买赔单: 'compensation-detail', // 买赔单详情
    车销: 'sales-detail', // 车销单详情
    回货单: 'back-detail', // 回货单详情
  }

  const routeName = routes[bill.genCls as keyof typeof routes]
  if (routeName && bill.genNum) {
    router.push({
      name: routeName,
      params: { id: bill.genNum, mode: 'copy' },
    })
  }
  else {
    console.warn('无法识别的单据类型或缺少单号:', bill)
  }
}

/**
 * 去开单操作
 */
function openBillAction() {
  if (!orderDetail.value) {
    showError('缺少单据信息')
    return
  }
  if (skuData.value.every(item => item.value === 0)) {
    showError('商品数量都为0，请重新选择！')
    return
  }
  // 计算开单统计信息
  const totalItems = skuData.value.filter(item => item.value > 0).length
  const totalAmount = skuData.value.reduce((sum, item) => sum + (item.value || 0) * (item.price || 0), 0)

  // 打开开单确认弹窗
  openBillPopRef.value?.open({
    title: '开单确认',
    skuList: skuData.value.filter(item => item.value > 0),
    totalItems,
    totalAmount,
    billNum: orderDetail.value!.num!,
    onConfirm: (storeGid: number) => {
      // 提交开单请求，传入选中的门店GID
      handleOpenBill(orderDetail.value!.num!, storeGid)
    },
  })
}

// 页面加载
onLoad((options?: Record<string, any>) => {
  if (options?.id) {
    id.value = options.id
    // 检查是否传递了mode参数
    if (options.mode === 'copy') {
      relatedBillsMode.value = 'copy'
    }
    loadOrderDetail(id.value)
  }
})
</script>

<template>
  <PrivacyPopup />
  <view class="back-diff-edit box-border min-h-screen w-screen bg-white" :style="pageStyle">
    <DetailSkeleton v-if="orderLoading" />
    <view v-else class="py-3">
      <!-- 状态头部 -->
      <!-- 状态栏 -->
      <DetailStatusHeader
        :status="orderStatus"
        :order-no="orderDetail?.num || ''"
        custom-class="mb-3 px-3"
      />

      <!-- 分类Tabs - 固定在顶部 -->
      <view class="relative bg-white">
        <!-- 表头 - 固定在tabs下方 -->
        <view class="absolute top-42px z-1 w-full bg-white">
          <DetailTableHeader
            :headers="tableHeaders"
            :show-title="false"
            @sort-change="handleSortChange"
          />
        </view>
        <wd-tabs v-model="categoryId" custom-class="w-full! box-border">
          <wd-tab v-for="cat in categoryList" :key="cat.code" :title="cat.label" :name="cat.code">
            <view class="px-3">
              <wd-gap height="37px" />
              <!-- 商品列表 -->
              <view v-for="(sku, index) in skuList" :key="sku.goods?.gid || index" class="mb-3">
                <BackDiffEditSkuCard :sku="sku" @update:sku="handleQuantityUpdate" />
              </view>
              <EmptyStatus v-if="skuList.length === 0" tip="无此分类商品" custom-class="py-16!" />

              <!-- 订单信息 -->
              <DetailInfoCard
                v-if="cat.value === categoryId"
                :items="[
                  { label: '回货仓位', value: orderDetail?.wrh?.name },
                  { label: '业务员', value: `${orderDetail?.vehSaleEmpName}[${orderDetail?.vehSaleEmp?.code}]` },
                  { label: '操作人', value: orderDetail?.lastModifyOper },
                  { label: '操作时间', value: orderDetail?.lstupdTime },
                ]"
              />

              <!-- 关联单据 -->
              <DetailRelatedBills
                v-if="genBills?.length && cat.value === categoryId"
                :related-bills="genBills"
                :mode="relatedBillsMode"
                @bill-click="handleBillClick"
              />
            </view>
          </wd-tab>
        </wd-tabs>
      </view>
    </view>

    <wd-gap :height="hasPermission ? 'calc(44px + 48rpx)' : ''" safe-area-bottom />

    <!-- 底部操作按钮 -->
    <view
      v-if="hasPermission"
      class="fixed bottom-0 left-0 right-0 z-10 box-border w-full bg-white p-3" style="--wot-button-medium-height: 44px; box-shadow: 0px -4px 12px 0px rgba(0, 0, 0, 0.05);"
    >
      <view class="flex items-center justify-between">
        <wd-button
          v-if="checkPermission('wholeSaleCreate')"
          custom-class="flex-auto mr-2!"
          type="error"
          plain
          block
          :round="false"
          @click="compensationAction"
        >
          办理买赔
        </wd-button>
        <wd-button
          v-if="checkPermission('vehSaleCreate')"
          custom-class="flex-auto"
          block
          type="primary"
          :round="false"
          @click="openBillAction"
        >
          去开单
        </wd-button>
      </view>
      <wd-gap height="0" safe-area-bottom />
    </view>
  </view>

  <!-- 买赔弹窗 -->
  <BuyCompensationPop ref="buyCompensationPopRef" @opened="lockPage" @closed="unlockPage" />
  <OpenBillPop v-if="orderDetail" ref="openBillPopRef" :veh-sale-emp-gid="orderDetail?.vehSaleEmp?.gid" @opened="lockPage" @closed="unlockPage" />
</template>

<route lang="json">
{
  "name": "back-diff-edit",
  "style": {
    "navigationBarTitleText": "回货差异详情"
  },
  "meta": {
    "permissions": ["vehSaleUseSignArvDiffView"]
  }
}
</route>
