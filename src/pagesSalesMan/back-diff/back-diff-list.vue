<script setup lang="ts">
import { computed, reactive, ref } from 'vue'
import { isArray } from 'wot-design-uni/components/common/util'
import BackDiffCard from './cmp/BackDiffCard.vue'
import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
import { useGlobalToast } from '@/composables/useGlobalToast'
import TimeFilter from '@/components/TimeFilter.vue'
import type { OrderStatus } from '@/utils/bill'
import { useWrhSelect } from '@/composables/useWrhSelect'
import { useVehSaleEmpSelect } from '@/composables/useVehSaleEmpSelect'
import type { VehSaleUseSignArvDiffQueryResponseDTO } from '@/api/globals'

// mescroll相关方法 - 使用hooks
const { mescrollInit, getMescroll, downCallback } = useMescroll(onPageScroll, onReachBottom)

const router = useRouter()
const globalToast = useGlobalToast()
const timeFilterRef = ref()
const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)
const { currentWms } = useWmsStore()

// 监听刷新数据事件
useWatchRefreshData({
  callback: () => {
    handleRefresh()
  },
  name: 'back-diff-list',
})

const isSalesman = computed(() => userStore.userRole === 'salesman')

// 仓位选择
const { wrhList, wrhLoading, handleWrhSelect, wrhId, currentWrh } = useWrhSelect(isSalesman.value ? userStore.vehSaleEmp?.gid : undefined, isSalesman.value ? undefined : currentWms?.gid)

function handleSelectWrh(event: { value: string, selectedItems: { label: string, value: string, code: string, name: string } }) {
  handleWrhSelect(event)
  handleRefresh()
}

// 判断当前用户角色
const isWarehouseManager = computed(() => userStore.userRole === 'warehouse')

// 业务员选择 - 使用 useVehSaleEmpSelect
const { empList, empLoading, handleEmpSelect, empId, currentEmp } = useVehSaleEmpSelect(currentWms?.gid)

// 过滤业务员列表选项 - 业务员角色时不显示"全部业务员"选项
const empOptions = computed(() => {
  if (isSalesman.value) {
    return empList.value.filter(item => item.value !== '')
  }
  return empList.value
})

// 处理业务员选择
function handleSelectEmp(event: { value: string, selectedItems: { label: string, value: string, code: string, name: string } }) {
  handleEmpSelect(event)
  handleRefresh()
}

// 当前选中的Tab - 待处理/已完成
// 100: 待处理
// 300: 已完成
const currentTab = ref<OrderStatus>('applied')

// 查询参数
const queryParams = reactive({
  keyword: '',
  stat: 100 as (100 | 300 | undefined),
  beginDate: '',
  finishDate: '',
  sorts: [
    {
      field: 'lstupdTime',
      asc: false,
    },
  ],
})

// 是否初始化
const inited = ref(false)

// 使用 usePagination 实现分页查询 - 使用差异处理接口
const {
  // 数据列表
  data: recordList,
  reload: reloadRecords,
  total,
  page,
} = usePagination(
  (page, pageSize) => {
    // 根据角色设置业务员参数
    let vehSaleEmpGid: number | undefined
    let wmsGid: number | undefined

    if (isSalesman.value) {
      // 业务员角色：使用当前登录业务员的GID
      vehSaleEmpGid = userInfo.value?.vehSaleEmp?.gid
    }
    else if (isWarehouseManager.value) {
      // 仓管角色：使用选择的业务员GID（如果有选择）
      if (empId.value) {
        vehSaleEmpGid = Number(empId.value)
      }
      wmsGid = currentWms?.gid
    }

    return Apis.vehsaleusesignarvdiffInterface.queryUsingPOST_3({
      data: {
        ...queryParams,
        page: page - 1, // 后端页码从0开始，usePagination从1开始
        pageSize,
        vehSaleEmpGid,
        wrhGid: wrhId.value ? Number(wrhId.value) : undefined,
        wmsGid,
      },
    })
  },
  {
    immediate: false,
    append: true,
    initialData: [],
    initialPageSize: 10,
    initialPage: 1,
    total: response => response.total,
    data: response => (response.data || []),
  },
).onSuccess((resp) => {
  getMescroll().endBySize(resp.data.data?.length || 0, total.value)
}).onError((error) => {
  globalToast.error(error.error?.message || '加载差异处理列表失败')
  getMescroll().endErr()
})

// 上拉加载更多 - 结合usePagination使用
async function upCallback(mescroll: any) {
  if (mescroll.num === 1) {
    await reloadRecords()
  }
  else {
    page.value = mescroll.num
  }
}

// 处理路由参数
onLoad((options: any) => {
  // 设置初始Tab状态
  // 100: 待处理
  // 110: 已完成
  if (options.stat) {
    switch (Number(options.stat)) {
      case 100:
        currentTab.value = 'applied'
        queryParams.stat = 100
        break
      case 110:
        currentTab.value = 'audited'
        queryParams.stat = 300
        break
      default:
        currentTab.value = 'applied'
        queryParams.stat = 100
        break
    }
  }

  // 如果有时间范围参数，设置查询参数
  if (options.beginDate) {
    queryParams.beginDate = options.beginDate
  }

  if (options.endDate) {
    queryParams.finishDate = options.endDate
  }

  // 如果有物流中心参数，设置物流中心
  if (options.wmsId) {
    wrhId.value = String(options.wmsId)
  }

  inited.value = true
})

function handleRefresh() {
  // 初始加载数据
  if (getMescroll()) {
    getMescroll().resetUpScroll()
  }
}

// 切换Tab
function changeTab(event: { index: number, name: OrderStatus }) {
  currentTab.value = event.name
  // 根据当前选中的Tab设置查询参数
  switch (event.name) {
    case 'applied':
      queryParams.stat = 100 // 待处理
      break
    case 'audited':
      queryParams.stat = 300 // 已完成
      break
    default:
      queryParams.stat = 100
      break
  }

  handleRefresh()
}

/**
 * 筛选时间的回调
 */
function handleTimeFilterConfirm(payload: { range: string | [string, string] }) {
  if (isArray(payload.range)) {
    queryParams.beginDate = payload.range[0]
    queryParams.finishDate = payload.range[1]
  }
  else {
    queryParams.beginDate = payload.range
    queryParams.finishDate = payload.range
  }
  handleRefresh()
}

// 查看差异处理详情
function viewRecordDetail(record: VehSaleUseSignArvDiffQueryResponseDTO | undefined) {
  if (!record) {
    globalToast.error('记录ID不存在')
    return
  }
  if (isWarehouseManager.value) {
    if (record.stat === 100) {
      router.push({
        name: 'back-diff-edit',
        params: { id: record.num! },
      })
    }
    else {
      router.push({
        name: 'back-diff-detail',
        params: { id: record.num! },
      })
    }
  }
  else {
    router.push({
      name: 'back-diff-detail',
      params: { id: record.num! },
    })
  }
}
</script>

<template>
  <view class="back-diff-list box-border min-h-screen bg-[#F9F9F9] pt-[calc(52px+42px+88rpx)]">
    <!-- 搜索和筛选区域 - 固定在顶部 -->
    <view class="fixed-top-section">
      <SearchBar
        v-model="queryParams.keyword"
        placeholder="回货单号/差异单号/业务员/仓位/商品"
        :has-filter="!!queryParams.beginDate || !!queryParams.finishDate"
        @filter="timeFilterRef?.open()"
        @clear="handleRefresh"
        @search="handleRefresh"
      />

      <!-- 类型选择Tab -->
      <wd-tabs v-model="currentTab" auto-line-width @change="changeTab">
        <wd-tab title="待处理" name="applied" />
        <wd-tab title="已完成" name="audited" />
      </wd-tabs>

      <!-- 筛选区域 -->
      <view class="h-88rpx flex items-center px-3">
        <!-- 物流中心筛选 -->
        <wd-select-picker
          v-if="inited"
          v-model="wrhId"
          :columns="wrhList"
          :loading="wrhLoading"
          title="选择仓位"
          label="仓位"
          type="radio"
          use-default-slot filterable hide-label
          @confirm="handleSelectWrh"
        >
          <template #default>
            <view class="flex items-center rounded bg-white px-3 py-1">
              <text class="max-w-32 truncate text-28rpx text-[#2D2D2D] font-500">
                {{ currentWrh ? currentWrh.label : '全部仓位' }}
              </text>
              <text class="i-carbon-caret-down text-4 text-[var(--textapplication-text-3)]" />
            </view>
          </template>
        </wd-select-picker>

        <!-- 业务员筛选 - 仓管角色显示 -->
        <wd-select-picker
          v-if="inited && isWarehouseManager"
          v-model="empId"
          :columns="empOptions"
          :loading="empLoading"
          title="选择业务员"
          label="业务员"
          custom-class="ml-2!"
          type="radio"
          use-default-slot filterable hide-label
          @confirm="handleSelectEmp"
        >
          <template #default>
            <view class="flex items-center rounded bg-white px-3 py-1">
              <text class="max-w-32 truncate text-28rpx text-[#2D2D2D] font-500">
                {{ currentEmp ? currentEmp.label : '全部业务员' }}
              </text>
              <text class="i-carbon-caret-down text-4 text-[var(--textapplication-text-3)]" />
            </view>
          </template>
        </wd-select-picker>
      </view>
    </view>

    <!-- 差异处理记录列表 -->
    <mescroll-body
      v-if="inited"
      :down="downOption" :up="upOption" height="100%" safearea @init="mescrollInit"
      @down="downCallback" @up="upCallback"
    >
      <view class="mx-3">
        <BackDiffCard
          v-for="record in recordList"
          :key="record.num"
          :record="record"
          @click="viewRecordDetail(record)"
        />
      </view>
    </mescroll-body>

    <!-- 筛选弹窗 -->
    <TimeFilter v-if="inited" ref="timeFilterRef" @confirm="handleTimeFilterConfirm" />
  </view>
</template>

<style lang="scss" scoped>
.back-diff-list {
  :deep(.mescroll-body) {
    min-height: calc(100vh - 52px - 42px - 132rpx) !important;
    min-height: calc(100vh - 52px - 42px - 132rpx - constant(safe-area-inset-bottom)) !important;
    min-height: calc(100vh - 52px - 42px - 132rpx - env(safe-area-inset-bottom)) !important;
  }
}

/* 固定在顶部的搜索栏和筛选条件 */
.fixed-top-section {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 10;
  background-color: #F9F9F9;
}
</style>

<route lang="json">
{
  "name": "back-diff-list",
  "style": {
    "navigationBarTitleText": "回货差异"
  },
  "meta": {
    "permissions": ["vehSaleUseSignArvDiffView"]
  }
}
</route>
