<!--
 * @Description: 回货差异详情页面
-->
<script setup lang="ts">
import { computed, ref } from 'vue'
import BackDiffDetailSkuCard from './cmp/BackDiffDetailSkuCard.vue'
import DetailSkeleton from '@/business/DetailSkeleton.vue'

const router = useRouter()
const { error: showError } = useGlobalToast()

// 单号
const id = ref<string>('')
// 关联单据显示模式
const relatedBillsMode = ref<'jump' | 'copy'>('jump')

const userStore = useUserStore()
const isSalesman = computed(() => userStore.userRole === 'salesman')

// 加载单头详情
const {
  data: order,
  loading: orderLoading,
  onSuccess: onOrderDetailSuccess,
  send: loadOrderDetail,
} = useRequest(
  (num: string) => Apis.vehsaleusesignarvdiffInterface.getUsingGET_3({
    params: {
      num,
      fetchDetail: false, // 不直接获取商品详情
    },
  }),
  {
    immediate: false,
  },
).onError((error) => {
  showError(error.error.message || '获取差异单详情失败')
})

// 订单详情（响应式计算）
const orderDetail = computed(() => order.value?.data)

const genBills = computed(() => {
  const genBills = orderDetail.value?.genDetails || []
  if (orderDetail.value?.vehSaleUseSignArvNum) {
    genBills.push({
      genCls: '回货单',
      genNum: orderDetail.value?.vehSaleUseSignArvNum,
    })
  }
  return genBills
})

// 加载商品数据
const {
  data: skuData,
  loading: skuLoading,
  send: loadSkuData,
} = useRequest(
  (num: string) => Apis.vehsaleusesignarvdiffInterface.getDetailsUsingPOST_1({
    data: {
      num,
      page: 0,
      pageSize: 3,
    },
  }),
  {
    immediate: false,
  },
).onError((error) => {
  showError(error.error.message || '获取差异单商品失败')
})

// 商品列表
const skuList = computed(() => {
  return skuData.value?.data || []
})

// 是否还有更多商品
const hasMoreSku = computed(() => {
  return skuData.value?.more
})

// 差异处理状态
const orderStatus = computed(() => {
  if (!orderDetail.value)
    return 'initial'
  return getBillStatus(orderDetail.value.stat || 0, true)
})

// 统计数据 - 上部分数据
const topStats = computed(() => {
  if (!orderDetail.value) {
    return [
      { title: '差异总金额(元)', value: '0.00' },
      { title: '已处理金额(元)', value: '0.00' },
    ]
  }
  return [
    { title: '差异总金额(元)', value: (orderDetail.value.total || 0).scale(2) },
    { title: '已处理金额(元)', value: (orderDetail.value.procTotal || 0).scale(2) },
  ]
})

// 统计数据 - 下部分数据
const bottomStats = computed(() => {
  if (!orderDetail.value) {
    return [
      { title: '商品种类(种)', value: 0 },
      { title: '已处理差异数', value: 0 },
    ]
  }
  return [
    { title: '差异总数', value: orderDetail.value.qty || 0 },
    { title: '已处理总数', value: orderDetail.value.procQty || 0 },
  ]
})

onOrderDetailSuccess(() => {
  if (!isSalesman.value && orderStatus.value === 'pending') {
    router.replace({
      name: 'back-diff-edit',
      params: {
        id: id.value,
        mode: relatedBillsMode.value,
      },
    })
  }
})

// 跳转到商品清单详情
function goToSkuList() {
  router.push({
    name: 'back-diff-detail-sku-list',
    params: {
      id: id.value,
      status: orderStatus.value,
    },
  })
}

/**
 * 处理关联单据点击事件
 */
function handleBillClick(bill: {
  genCls?: string
  genNum?: string
}) {
  // 根据单据类型跳转到对应的详情页面
  const routes = {
    买赔单: 'compensation-detail', // 买赔单详情
    车销: 'sales-detail', // 车销单详情
    回货单: 'back-detail', // 回货单详情
  }

  const routeName = routes[bill.genCls as keyof typeof routes]
  if (routeName && bill.genNum) {
    router.push({
      name: routeName,
      params: { id: bill.genNum, mode: 'copy' },
    })
  }
  else {
    console.warn('无法识别的单据类型或缺少单号:', bill)
  }
}

// 页面加载
onLoad((options?: Record<string, any>) => {
  if (options?.id) {
    id.value = options.id
    // 检查是否传递了mode参数
    if (options.mode === 'copy') {
      relatedBillsMode.value = 'copy'
    }
    loadOrderDetail(id.value)
    loadSkuData(id.value)
  }
})
</script>

<template>
  <view class="back-diff-detail-page relative min-h-screen w-screen flex flex-col bg-white">
    <!-- 主要内容 -->
    <view class="flex-1 p-3">
      <!-- 骨架屏 -->
      <DetailSkeleton v-if="orderLoading || skuLoading" />
      <!-- 实际内容 -->
      <block v-else>
        <!-- 状态栏 -->
        <DetailStatusHeader
          :status="orderStatus"
          :order-no="orderDetail?.num || ''"

          custom-class="mb-3"
        />

        <!-- 统计卡片 -->
        <DetailStatsCard
          :top-items="topStats"
          :bottom-items="bottomStats"
          custom-class="mb-3"
        />

        <!-- 商品清单 -->
        <view class="mb-3 flex flex-col rounded-lg bg-white py-3">
          <DetailTableHeader
            title="商品清单"
            :headers="['商品信息', '差异', '已处理']"
            :show-more="hasMoreSku"
            @more-click="goToSkuList"
          />

          <!-- 商品列表 -->
          <view class="space-y-3">
            <view v-for="sku in skuList" :key="sku.goods?.gid" class="border-b border-[#F5F5F5] last:border-0">
              <BackDiffDetailSkuCard :sku="sku" :status="orderStatus" />
            </view>
          </view>
        </view>

        <!-- 订单信息 -->
        <DetailInfoCard
          :items="[
            { label: '回货仓位', value: orderDetail?.wrh?.name },
            { label: '业务员', value: `${orderDetail?.vehSaleEmpName}[${orderDetail?.vehSaleEmp?.code}]` },
            { label: '创建人', value: orderDetail?.filler },
            { label: '操作人', value: orderDetail?.lastModifyOper },
            { label: '操作时间', value: orderDetail?.lstupdTime },
          ]"
        />
        <!-- 关联单据 -->
        <DetailRelatedBills
          v-if="genBills?.length"
          :related-bills="genBills"
          :mode="relatedBillsMode"
          @bill-click="handleBillClick"
        />
      </block>
    </view>
    <wd-gap :height="0" safe-area-bottom />
  </view>
</template>

<style lang="scss" scoped>
.back-diff-detail-page {
  min-height: 100vh;
}
</style>

<route lang="json">
{
  "name": "back-diff-detail",
  "style": {
    "navigationBarTitleText": "回货差异详情",
    "backgroundColor": "#F9F9F9"
  },
  "meta": {
    "permissions": ["vehSaleUseSignArvDiffView"]
  }
}
</route>
