<!--
 * @Description: 开单确认弹出框组件
-->
<script setup lang="ts">
import { createGlobalLoadingMiddleware } from '@/api/core/middleware'
import type { VehSaleUseSignArvDiffDtlResponseDTO } from '@/api/globals'

export interface OpenBillConfig {
  title?: string // 弹窗主标题
  skuList?: (VehSaleUseSignArvDiffDtlResponseDTO & { value: number })[] // 商品列表
  totalItems?: number // 买赔品项数
  totalAmount?: number // 买赔总金额
  defaultStoreGid?: number // 默认门店GID
  billNum?: string // 单号，用于查询商品价格
  onConfirm?: (storeGid: number) => void // 确认回调
}
const props = defineProps<{
  vehSaleEmpGid?: number // 车销业务员GID
}>()

const emit = defineEmits<{
  (e: 'opened'): void
  (e: 'closed'): void
  (e: 'submit', storeGid: number): void
}>()

const state = reactive({
  show: false,
  config: {} as OpenBillConfig,
  selectedStoreGid: '', // 选中的门店GID
  updatedSkuList: [] as (VehSaleUseSignArvDiffDtlResponseDTO & { value: number })[], // 更新后的商品列表
})

// 门店选择相关
const { storeList, storeLoading, handleStoreSelect, storeGid } = useStoreSelect({ vehSaleEmpGid: props.vehSaleEmpGid })

const { error: showError } = useGlobalToast()

// 查询商品价格
const {
  send: queryGoodsPrice,
} = useRequest(
  (billNum: string, storeGid: number) => Apis.vehsaleusesignarvdiffInterface.queryVehSaleGoodsUsingPOST({
    data: {
      num: billNum,
      storeGid,
      goodsDetails: state.config.skuList?.map(item => ({
        gdGid: item.goods?.gid || 0,
        qty: item.value || 0,
      })) || [],
    },
  }),
  {
    immediate: false,
    middleware: createGlobalLoadingMiddleware({
      loadingText: '操作中...',
    }),
  },
).onSuccess((resp) => {
  if (resp.data.data && CommonUtil.isArray(resp.data.data)) {
    // 更新商品价格
    const updatedSkuList = state.config.skuList?.map((sku) => {
      const updatedGood = resp.data.data?.find((item: any) => item.goods?.gid === sku.goods?.gid)
      if (updatedGood) {
        return {
          ...sku,
          price: updatedGood.price || sku.price,
          qpc: updatedGood.qpc || sku.qpc,
        }
      }
      return sku
    }) || []

    state.updatedSkuList = updatedSkuList
    state.config.skuList = updatedSkuList
  }
}).onError((error) => {
  showError(error.error?.message || '更新商品价格失败')
})

const stores = computed(() => {
  return storeList.value.filter(item => item.value !== '')
})

// 当前选中门店的显示文本
const currentStoreLabel = computed(() => {
  if (!state.selectedStoreGid) {
    return '请选择门店'
  }
  const found = stores.value.find(item => item.value === state.selectedStoreGid)
  return found ? found.label : '请选择门店'
})

/**
 * 获取单个商品的价格
 * @param price 价格
 * @param qpc 规格
 */
function getSinglePrice(price: number, qpc: number) {
  return price.divide(qpc)
}

/**
 * 打开弹窗
 * @param config 配置项
 */
function open(config: OpenBillConfig) {
  state.show = true
  state.config = {
    title: '开单确认',
    ...CommonUtil.deepClone(config),
  }

  // 初始化商品列表
  state.updatedSkuList = []

  // 设置默认门店
  if (config.defaultStoreGid) {
    storeGid.value = String(config.defaultStoreGid)
    state.selectedStoreGid = String(config.defaultStoreGid)
  }
  else {
    // 清空选择
    state.selectedStoreGid = ''
    storeGid.value = ''
  }
}

/**
 * 关闭弹窗
 */
function close() {
  state.show = false
}

/**
 * 处理门店选择
 */
function handleSelectStore(event: { value: string, selectedItems: { label: string, value: string } }) {
  handleStoreSelect(event)
  state.selectedStoreGid = event.value

  // 当门店发生变化且有单号时，重新查询商品价格
  if (event.value && state.config.billNum) {
    queryGoodsPrice(state.config.billNum, Number(event.value))
  }
}

/**
 * 提交开单
 */
function handleSubmit() {
  if (!state.selectedStoreGid) {
    return
  }

  const selectedStoreGid = Number(state.selectedStoreGid)
  emit('submit', selectedStoreGid)

  if (state.config.onConfirm) {
    state.config.onConfirm(selectedStoreGid)
  }

  state.show = false
}

defineExpose({
  open,
  close,
})
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <wd-action-sheet
    v-model="state.show"
    :title="state.config.title"
    @opened="emit('opened')"
    @closed="emit('closed')"
  >
    <view class="open-bill-pop px-3">
      <!-- 提示信息 -->
      <view class="mb-3 border border-white/60 rounded bg-[#fff6eb] p-2">
        <view class="flex items-center">
          <text class="text-26rpx text-[#f57f00] leading-[1.4]">
            将按照如下信息开具车销单，请认真核对。
          </text>
        </view>
      </view>

      <!-- 门店选择 -->
      <view class="border border-[#e6e6e6] rounded bg-white p-3">
        <view class="flex items-center justify-between">
          <view class="flex items-center">
            <image src="@/static/icon/ic_store.svg" class="mr-1 h-4 w-4" />
            <text class="text-28rpx text-[#5c5c5c]">
              门店
            </text>
          </view>
          <wd-select-picker
            v-model="state.selectedStoreGid"
            :columns="stores"
            :loading="storeLoading"
            title="选择门店"
            type="radio"
            use-default-slot
            filterable
            @confirm="handleSelectStore"
          >
            <template #default>
              <view class="flex items-center gap-1">
                <text class="text-[16px] text-[#2d2d2d] font-medium">
                  {{ currentStoreLabel }}
                </text>
                <text class="i-carbon-chevron-right text-4 text-[#5c5c5c]" />
              </view>
            </template>
          </wd-select-picker>
        </view>
      </view>

      <!-- 统计信息 -->
      <view class="mb-3 flex gap-3">
        <view class="flex-1 border border-[#e6e6e6] rounded bg-white p-3">
          <view class="mb-1.5 text-26rpx text-[#636d78]">
            买赔品项数
          </view>
          <view class="text-[22px] text-[#343a40] font-bold" style="font-family: 'DIN Alternate', sans-serif;">
            {{ state.config.totalItems || 0 }}
          </view>
        </view>
        <view class="flex-1 border border-[#e6e6e6] rounded bg-white p-3">
          <view class="mb-1.5 text-26rpx text-[#636d78]">
            买赔总金额
          </view>
          <view class="text-[22px] text-[#343a40] font-bold" style="font-family: 'DIN Alternate', sans-serif;">
            ¥ {{ ((state.updatedSkuList.length > 0 ? state.updatedSkuList : state.config.skuList || []).reduce((sum, item) => sum + (item.value || 0) * (item.price || 0), 0)).toFixed(2) }}
          </view>
        </view>
      </view>

      <!-- 商品信息表头 -->
      <view class="mb-2 flex justify-between rounded bg-[#f5f5f5] p-2">
        <view class="text-26rpx text-[#8a8a8a]">
          商品信息
        </view>
        <view class="text-26rpx text-[#8a8a8a]">
          数量
        </view>
      </view>

      <!-- 商品列表 - 使用scroll-view -->
      <scroll-view scroll-y class="mb-3 h-[300px]">
        <view class="flex flex-col">
          <view v-for="(sku, index) in (state.updatedSkuList.length > 0 ? state.updatedSkuList : state.config.skuList)" :key="sku.goods?.gid || index" class="px-2">
            <view class="flex flex-col rounded-lg bg-white py-3">
              <!-- 商品名称 -->
              <view class="mb-2 text-28rpx text-[#2d2d2d] font-medium leading-[1.5]">
                {{ sku.goods?.name || '未知商品' }}
              </view>

              <!-- 商品信息 -->
              <view class="mb-2 flex">
                <view class="mr-2 rounded bg-[#f5f6f7] px-1 text-26rpx text-[#5c5c5c]">
                  {{ sku.goods?.code || '' }}
                </view>

                <!-- 价格显示 -->
                <view class="flex items-end gap-0.5">
                  <text class="text-26rpx text-[#f57f00] leading-none">
                    ¥
                  </text>
                  <text class="text-[18px] text-[#f57f00] font-medium leading-[0.94]">
                    {{ formatPrice(getSinglePrice(sku.price || 0, sku.qpc || 1)).integer }}
                  </text>
                  <text v-if="formatPrice(getSinglePrice(sku.price || 0, sku.qpc || 1)).decimal" class="text-26rpx text-[#f57f00] font-medium leading-none">
                    .{{ formatPrice(getSinglePrice(sku.price || 0, sku.qpc || 1)).decimal }}
                  </text>
                  <text class="text-[12px] text-[#8a8a8a] leading-none">
                    /{{ sku.minMunit || '个' }}
                  </text>
                </view>
              </view>

              <!-- 数量信息 -->
              <view class="flex items-center justify-between">
                <view class="text-28rpx text-[#2d2d2d]">
                  数量/开单额
                </view>
                <view class="text-28rpx text-[#1c64fd] font-medium">
                  {{ sku.value || 0 }}{{ sku.minMunit || '个' }}/计¥{{ ((sku.value || 0).multiply(getSinglePrice(sku.price || 0, sku.qpc || 1))).scale(2) }}
                </view>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>

      <!-- 底部按钮区域 -->
      <view class="py-3">
        <wd-button
          type="primary"
          block
          size="large"
          custom-class="w-full!"
          :round="false"
          :disabled="!state.selectedStoreGid"
          @click="handleSubmit"
        >
          提交
        </wd-button>
      </view>
    </view>
  </wd-action-sheet>
</template>

<style scoped lang="scss">
.open-bill-pop {
  min-height: 60vh;
}
</style>
