<!--
 * @Description: 差异详情商品卡片组件
-->
<script setup lang="ts">
import type { VehSaleUseSignArvDiffDtlResponseDTO } from '@/api/globals'
import type { OrderStatus } from '@/utils/bill'

interface Props {
  sku: VehSaleUseSignArvDiffDtlResponseDTO
  status: OrderStatus
}

defineProps<Props>()

// 计算商品差异描述
function getDiffDesc(qty?: number, procQty?: number, minMunit?: string) {
  const remainDiff = Number(qty || 0).minus(Number(procQty || 0))
  if (remainDiff > 0) {
    return `少${remainDiff}${minMunit || '-'}`
  }
  else if (remainDiff < 0) {
    return `多${Math.abs(remainDiff)}${minMunit || '-'}`
  }
  return '无差异'
}
</script>

<template>
  <view class="sku-card box-border p-2">
    <!-- 商品名称 -->
    <view class="mb-1.5">
      <text class="text-28rpx text-[#2D2D2D] font-medium leading-[1.5em]">
        {{ sku.goods?.name || '-' }}
      </text>
    </view>

    <!-- 价格和标签区域 -->
    <view class="mb-1.5 flex items-center">
      <!-- 价格 -->
      <view class="flex items-end">
        <text class="text-26rpx text-[#F57F00] leading-[1em]">
          ¥
        </text>
        <text class="text-36rpx text-[#F57F00] font-medium leading-[0.944em]">
          {{ formatPrice(sku.price || 0).integer }}
        </text>
        <text v-if="formatPrice(sku.price || 0).decimal" class="text-26rpx text-[#F57F00] font-medium leading-[1em]">
          .{{ formatPrice(sku.price || 0).decimal }}
        </text>
        <text class="ml-0.5 text-3 text-[#8A8A8A] leading-[1em]">
          /{{ sku.munit || '-' }}
        </text>
      </view>

      <!-- 标签组 -->
      <view class="ml-2 flex items-center">
        <!-- 商品编码 -->
        <view class="rounded bg-[#F5F6F7] px-1 py-0.5">
          <text class="text-26rpx text-[#5C5C5C] leading-[1.385em]">
            {{ sku.goods?.code || '-' }}
          </text>
        </view>

        <!-- 规格 -->
        <view class="ml-2 rounded bg-[#F5F6F7] px-1 py-0.5">
          <text class="text-26rpx text-[#5C5C5C] leading-[1.385em]">
            {{ sku.qpcStr || '-' }}
          </text>
        </view>
      </view>
    </view>

    <!-- 件数对比 -->
    <view class="mb-1.5">
      <view class="flex items-center justify-between">
        <text class="w-1/3 text-28rpx text-[#5C5C5C] leading-[1.5em]">
          件数
        </text>
        <text class="w-1/3 text-center text-28rpx text-[#2D2D2D] leading-[1.5em]">
          {{ sku.qtyStr }}{{ sku.munit }}
        </text>
        <text class="w-1/3 text-right text-28rpx text-[#2D2D2D] leading-[1.5em]">
          {{ sku.procQtyStr }} {{ sku.munit }}
        </text>
      </view>
    </view>

    <!-- 商品差异 -->
    <view v-if="sku.qty !== sku.procQty" class="flex items-center justify-between">
      <text class="text-28rpx text-[#2D2D2D] leading-[1.5em]">
        未处理
      </text>
      <text class="text-28rpx text-[var(--frequentapplication-primary-content)] font-medium leading-[1.286em]">
        {{ getDiffDesc(sku.qty, sku.procQty, sku.minMunit) }}
      </text>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.sku-card {
  border-bottom: 1px solid #F5F5F5;

  &:last-child {
    border-bottom: none;
  }
}
</style>
