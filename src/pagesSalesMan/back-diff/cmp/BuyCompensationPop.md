# 买赔弹窗组件 (BuyCompensationPop)

办理买赔的弹出框组件，支持两步骤流程：核对差异 -> 上传买赔凭证。

## 功能特性

- 📋 **两步骤流程**：核对差异信息 + 上传买赔凭证
- 🎯 **步骤指示器**：使用 `wd-steps` 组件显示当前进度
- 📊 **统计信息**：显示买赔品项数和买赔总金额
- 🛍️ **商品清单**：展示需要买赔的商品详情
- 📸 **图片上传**：集成图片上传功能
- 🎨 **设计还原**：完全按照Figma设计图实现

## 基本用法

```vue
<script setup lang="ts">
import BuyCompensationPop from '@/business/BuyCompensationPop.vue'
import type { BillSubmitAttachDtlRequestDTO } from '@/api/globals'

const buyCompensationPopRef = ref<InstanceType<typeof BuyCompensationPop>>()
const { ossInfo } = useOssInfo()

function openBuyCompensation() {
  buyCompensationPopRef.value?.open({
    title: '买赔详情清单',
    skuList: [
      {
        goods: { name: '商品名称', gid: 123 },
        price: 10.5,
        munit: '个',
        value: 2
      }
    ],
    totalItems: 1,
    totalAmount: 21.0,
    ossInfo: ossInfo.value,
    fileList: [],
    onConfirm: (imgList: BillSubmitAttachDtlRequestDTO[]) => {
      console.log('提交的图片列表:', imgList)
      // 处理提交逻辑
    }
  })
}
</script>

<template>
  <view>
    <!-- 触发按钮 -->
    <wd-button @click="openBuyCompensation">
      办理买赔
    </wd-button>

    <!-- 买赔弹窗 -->
    <BuyCompensationPop ref="buyCompensationPopRef" />
  </view>
</template>
```

## API

### Props

组件本身无需传入 props，通过 `open` 方法传入配置。

### Methods

#### open(config)

打开买赔弹窗

**参数：**

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| config | `BuyCompensationConfig` | 是 | 弹窗配置对象 |

**BuyCompensationConfig 类型：**

```typescript
interface BuyCompensationConfig {
  title?: string // 弹窗主标题，默认：'买赔详情清单'
  skuList?: (VehSaleUseSignArvDiffDtlResponseDTO & { value: number })[] // 商品列表
  totalItems?: number // 买赔品项数
  totalAmount?: number // 买赔总金额
  ossInfo?: OssInfo // OSS上传配置信息
  fileList?: UploadFile[] // 初始文件列表
  onConfirm?: (imgList: BillSubmitAttachDtlRequestDTO[]) => void // 确认回调
}
```

#### close()

关闭买赔弹窗

### Events

| 事件名 | 说明 | 参数 |
|--------|------|------|
| opened | 弹窗打开时触发 | - |
| closed | 弹窗关闭时触发 | - |
| submit | 提交时触发 | `BillSubmitAttachDtlRequestDTO[]` |

## 使用场景

- 回货差异处理页面的买赔功能
- 需要两步骤确认的买赔流程
- 需要上传凭证的买赔操作

## 依赖组件

- `wd-action-sheet` - 弹窗容器
- `wd-steps` / `wd-step` - 步骤指示器
- `wd-upload` - 图片上传
- `wd-button` - 按钮

## 样式定制

组件使用了完整的CSS样式，包括：

- 步骤指示器样式
- 统计信息卡片样式
- 商品列表项样式
- 价格显示样式
- 上传区域样式

所有样式都通过 scoped CSS 进行了隔离，可以安全使用。

## 注意事项

1. 使用前需要确保已获取到有效的 OSS 配置信息
2. 商品列表中的 `value` 字段表示买赔数量
3. 组件会自动计算赔付金额：`数量 × 单价`
4. 第二步的图片上传是可选的，可以不上传直接提交
