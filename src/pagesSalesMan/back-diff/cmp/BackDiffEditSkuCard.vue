<!--
 * @Description: 回货差异商品编辑卡片组件
-->
<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import type { VehSaleUseSignArvDiffDtlResponseDTO } from '@/api/globals'

interface Props {
  sku: VehSaleUseSignArvDiffDtlResponseDTO & { value: number }
}

interface Emits {
  (e: 'update:sku', payload: { sku: VehSaleUseSignArvDiffDtlResponseDTO & { value: number } }): void
}

const props = defineProps<Props>()

const emit = defineEmits<Emits>()

const innerSku = ref<VehSaleUseSignArvDiffDtlResponseDTO & { value: number }>(CommonUtil.deepClone(props.sku))

watch(() => props.sku, (newValue) => {
  if (!CommonUtil.isEqual(newValue, innerSku.value)) {
    innerSku.value = CommonUtil.deepClone(newValue)
  }
})

// 未处理数量（差异数量 - 已处理数量）
const maxProcQty = computed(() => {
  const diffQty = Number(props.sku.qty || 0).minus(Number(props.sku.procQty || 0))
  return Math.max(0, diffQty)
})

/**
 * 商品图片列表
 */
const skuImgList = computed(() => {
  const imgList = props.sku.imageDetails && props.sku.imageDetails.length > 0 ? props.sku.imageDetails.map((item: any) => item.fileUrl) : []
  return imgList.filter((item: any) => CommonUtil.isDef(item))
})

/**
 * 商品主图
 */
const skuMainImg = computed(() => {
  return skuImgList.value.length > 0 ? skuImgList.value[0] : ''
})

/**
 * 处理数量变化
 * @param param0 { value: number } 数量变化事件对象
 * @param param0.value 数量
 */
function handleQtyChange({ value }: { value: number }) {
  if (!CommonUtil.isEqual(value, innerSku.value.value)) {
    innerSku.value.value = value
    emit('update:sku', { sku: innerSku.value })
  }
}
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <view class="back-diff-edit-sku-card rounded-lg bg-white p-3">
    <view class="mb-2 flex">
      <!-- 商品图片 -->
      <SkuImage
        :src="skuMainImg"
        :preview-src="skuImgList"
        mode="widthFix"
        custom-class="mr-3 flex-none"
      />

      <!-- 商品信息 -->
      <view class="ml-3 flex flex-auto flex-col">
        <!-- 商品名称 -->
        <text class="mb-2 text-28rpx text-[#2D2D2D] font-medium leading-5">
          {{ sku.goods?.name || '未知商品' }}
        </text>

        <!-- 规格信息 -->
        <view class="mb-2 flex">
          <view v-if="sku.gdCode" class="mr-2 rounded bg-[#F5F6F7] px-1 py-0.5">
            <text class="text-3 text-[#5C5C5C]">
              {{ sku.gdCode }}
            </text>
          </view>
        </view>
      </view>
    </view>

    <!-- 差异和处理信息 -->
    <view class="flex items-center justify-between">
      <!-- 差异信息 -->
      <text class="w-1/4 text-3 text-[#5C5C5C]">
        1*1
      </text>
      <text class="w-1/4 text-center text-28rpx text-[#2D2D2D] font-medium">
        {{ sku.qty || 0 }}{{ sku.minMunit || '-' }}
      </text>
      <text class="w-1/4 text-center text-28rpx text-[#1C64FD] font-medium">
        {{ maxProcQty || 0 }}{{ sku.minMunit || '-' }}
      </text>

      <!-- 数量操作器 -->
      <wd-input-number
        custom-class="flex-none! input-number-adjust"
        :model-value="innerSku.value"
        allow-null
        :immediate="false"
        :precision="0"
        :min="0"
        :max="maxProcQty"
        input-width="100%"
        @change="handleQtyChange"
      />
    </view>
  </view>
</template>

<style lang="scss" scoped>
.back-diff-edit-sku-card {
  &:not(:last-child) {
    margin-bottom: 16rpx;
  }
}

// 自定义数量输入步进器样式
:deep(.input-number-adjust) {
  // 按钮
  .wd-input-number__action {
    width: 56rpx;
    height: 56rpx;
  }
  // 输入框
  .wd-input-number__inner{
    width: 100rpx;
    height: 56rpx;
    .wd-input-number__input{
      height: 100%;
      font-size: 30rpx;
      font-weight: 500;
    }
  }
}
</style>
