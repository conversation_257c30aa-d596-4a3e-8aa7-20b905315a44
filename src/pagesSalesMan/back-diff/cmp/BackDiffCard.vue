<!--
 * @Description: 差异处理列表卡片组件
-->
<script setup lang="ts">
import type { VehSaleUseSignArvDiffQueryResponseDTO } from '@/api/globals'

const props = defineProps({
  record: {
    type: Object as PropType<VehSaleUseSignArvDiffQueryResponseDTO>,
    default: () => ({}),
    required: true,
  },
})

const emit = defineEmits(['click'])

// 点击卡片
function handleClick() {
  emit('click', props.record.num)
}
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <view class="record-card mb-2 w-[calc(100%-48rpx)] rounded-lg bg-white p-3" @click="handleClick">
    <!-- 顶部：回货单号 + 状态 -->
    <view class="mb-2 flex items-center justify-between">
      <view class="flex items-center break-all">
        <text class="text-4 text-[#2D2D2D] font-medium">
          回货单号：{{ record.vehSaleUseSignArvNum || record.num }}
        </text>
      </view>
    </view>

    <!-- 差异单号 -->
    <view class="mb-2 flex break-all">
      <text class="flex-none text-26rpx text-[#8A8A8A] leading-[1.385em]">
        差异单号：
      </text>
      <text class="text-26rpx text-[#2D2D2D] leading-[1.385em]">
        {{ record.num }}
      </text>
    </view>

    <!-- 回货仓位 + 业务员 (双列布局) -->
    <view class="flex flex-wrap">
      <view class="mb-2 min-w-[50%] flex break-all">
        <text class="flex-none flex-none text-26rpx text-[#8A8A8A] leading-[1.385em]">
          回货仓位：
        </text>
        <text class="text-26rpx text-[#2D2D2D] leading-[1.385em]">
          {{ record.wrh?.name }}
        </text>
      </view>
      <view class="mb-2 min-w-[50%] flex break-all">
        <text class="flex-none flex-none text-26rpx text-[#8A8A8A] leading-[1.385em]">
          业务员：
        </text>
        <text class="text-26rpx text-[#2D2D2D] font-medium leading-[1.385em]">
          {{ record.vehSaleEmpName }}[{{ record.vehSaleEmp?.code }}]
        </text>
      </view>
    </view>

    <!-- 操作时间 -->
    <view class="mb-2 flex break-all">
      <text class="flex-none text-26rpx text-[#8A8A8A] leading-[1.385em]">
        操作时间：
      </text>
      <text class="text-26rpx text-[#2D2D2D] font-500 leading-[1.385em]">
        {{ formatDate(record.lstupdTime) }}
      </text>
    </view>

    <!-- 底部统计信息 -->
    <view class="flex items-end justify-between rounded bg-[#F5F5F5] px-3 py-1">
      <view class="flex items-center">
        <text class="text-28rpx text-[#8A8A8A] leading-[1em]">
          共
        </text>
        <text class="mx-1 text-4 text-[#1A1A1A] font-medium leading-[1.5em]">
          {{ record.goodsCount || 0 }}
        </text>
        <text class="text-28rpx text-[#8A8A8A] leading-[1em]">
          种商品
        </text>
      </view>

      <view class="flex items-center">
        <text class="text-28rpx text-[#8A8A8A] leading-[1em]">
          共
        </text>
        <view class="mx-1 flex items-center">
          <text class="text-26rpx text-[#8A8A8A] leading-[1em]">
            ¥
          </text>
          <text class="text-4 text-[#1A1A1A] font-medium leading-[1.5em]">
            {{ (record.total || 0).toFixed(2) }}
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
</style>
