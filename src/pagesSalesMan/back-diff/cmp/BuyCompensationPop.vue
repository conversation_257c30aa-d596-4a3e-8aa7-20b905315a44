<!--
 * @Description: 办理买赔弹出框组件 - 支持两步骤流程
-->
<script setup lang="ts">
import type { UploadBuildFormData, UploadFile, UploadSuccessEvent } from 'wot-design-uni/components/wd-upload/types'
import type { OssInfo } from '@/composables/UseOssInfo'
import type { BillSubmitAttachDtlRequestDTO, VehSaleUseSignArvDiffDtlResponseDTO } from '@/api/globals'

export interface BuyCompensationConfig {
  title?: string // 弹窗主标题
  skuList?: (VehSaleUseSignArvDiffDtlResponseDTO & { value: number })[] // 商品列表
  totalItems?: number // 买赔品项数
  totalAmount?: number // 买赔总金额
  ossInfo?: OssInfo // OSS信息
  fileList?: UploadFile[] // 文件列表
  onConfirm?: (imgList: BillSubmitAttachDtlRequestDTO[]) => void // 确认回调
}

const emit = defineEmits<{
  (e: 'opened'): void
  (e: 'closed'): void
  (e: 'submit', value: BillSubmitAttachDtlRequestDTO[]): void
}>()

const { error: showError } = useGlobalToast()

const state = reactive({
  show: false,
  currentStep: 1, // 当前步骤 1-核对差异 2-上传买赔
  config: {} as BuyCompensationConfig,
})

// 组件引用
const stepsRef = ref()

/**
 * 获取单个商品的价格
 * @param price 价格
 * @param qpc 规格
 */
function getSinglePrice(price: number, qpc: number) {
  return price.divide(qpc)
}

/**
 * 打开弹窗
 * @param config 配置项
 */
function open(config: BuyCompensationConfig) {
  state.show = true
  state.currentStep = 1
  state.config = {
    title: '买赔详情清单',
    ...config,
  }
}

/**
 * 弹窗打开时的回调
 * 用于更新steps组件样式，解决微信小程序弹出框中组件样式异常问题
 */
function handleOpened() {
  emit('opened')
  // 在弹出框打开后更新组件样式
  nextTick(() => {
    if (stepsRef.value && typeof stepsRef.value.updateStepStyle === 'function') {
      stepsRef.value.updateStepStyle()
    }
  })
}

/**
 * 下一步
 */
function nextStep() {
  if (state.currentStep < 2) {
    state.currentStep += 1
  }
}

/**
 * 上一步
 */
function prevStep() {
  if (state.currentStep > 1) {
    state.currentStep -= 1
  }
}

/**
 * 构建 formData
 */
const buildFormData: UploadBuildFormData = ({ file, formData, resolve }) => {
  if (!state.config.ossInfo) {
    return
  }
  const { OSSAccessKeyId, policy, signature, success_action_status, key } = state.config.ossInfo
  const imageName = file.url.substring(file.url.lastIndexOf('/') + 1)

  formData = {
    ...formData,
    key: `${key}/${imageName}`,
    OSSAccessKeyId,
    policy,
    signature,
    success_action_status,
  }
  resolve(formData)
}

/**
 * 上传成功处理
 */
function handleSuccess(value: UploadSuccessEvent) {
  const { file, fileList } = value
  const { host, key } = state.config.ossInfo!
  const imageName = file.url.substring(file.url.lastIndexOf('/') + 1)
  file.url = `${host}/${key}/${imageName}`
  file.thumb = `${host}/${key}/${imageName}`
  fileList.forEach((item) => {
    if (item.uid === file.uid) {
      item.url = file.url
      item.thumb = file.thumb
      item.name = imageName
    }
  })
  state.config.fileList = fileList
}

/**
 * 最终提交
 */
function handleSubmit() {
  // 验证是否上传了图片
  if (!state.config.fileList || state.config.fileList.length === 0) {
    showError('请上传支付截图且保证订单号金额数字清晰可见')
    return
  }

  const imgList: BillSubmitAttachDtlRequestDTO[] = (state.config.fileList || []).map((file) => {
    return {
      fileId: file.name,
      fileName: file.name,
      fileUrl: file.url,
    }
  })

  emit('submit', imgList)

  if (state.config.onConfirm) {
    state.config.onConfirm(imgList)
  }
  state.show = false
}

/**
 * 关闭弹窗
 */
function close() {
  state.show = false
}

defineExpose({
  open,
  close,
})
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <wd-action-sheet
    v-model="state.show"
    :title="state.config.title"
    @opened="handleOpened"
    @closed="emit('closed')"
  >
    <view class="buy-compensation-pop px-3">
      <!-- 步骤指示器 -->
      <view class="mb-3 border-b border-[#f5f5f5] py-4">
        <wd-steps
          ref="stepsRef"
          align-center
          :active="state.currentStep - 1"
        >
          <wd-step title="核对差异" />
          <wd-step title="上传买赔" />
        </wd-steps>
      </view>

      <!-- 第一步：核对差异 -->
      <view v-if="state.currentStep === 1" class="mb-3 min-h-[400px]">
        <!-- 提示信息 -->
        <view class="mb-3 border border-white/60 rounded bg-[#fff6eb] p-2">
          <view class="flex items-center gap-2">
            <text class="text-26rpx text-[#f57f00] leading-[1.4]">
              将按照如下信息生成买赔单，请认真核对。
            </text>
          </view>
        </view>

        <!-- 统计信息 -->
        <view class="mb-3 flex gap-3">
          <view class="flex flex-1 flex-col gap-1.5 border border-[#e6e6e6] rounded bg-white p-3">
            <view class="text-26rpx text-[#636d78] leading-[1.38]">
              买赔品项数
            </view>
            <view class="text-[22px] text-[#343a40] font-bold leading-[1.16]" style="font-family: 'DIN Alternate', sans-serif;">
              {{ state.config.totalItems || 0 }}
            </view>
          </view>
          <view class="flex flex-1 flex-col gap-1.5 border border-[#e6e6e6] rounded bg-white p-3">
            <view class="text-26rpx text-[#636d78] leading-[1.38]">
              买赔总金额
            </view>
            <view class="text-[22px] text-[#343a40] font-bold leading-[1.16]" style="font-family: 'DIN Alternate', sans-serif;">
              ¥ {{ (state.config.totalAmount || 0).toFixed(2) }}
            </view>
          </view>
        </view>

        <!-- 商品信息表头 -->
        <view class="mb-2 flex items-center justify-between rounded bg-[#f5f5f5] p-2">
          <view class="text-26rpx text-[#8a8a8a] leading-[1.38]">
            商品信息
          </view>
          <view class="text-26rpx text-[#8a8a8a] leading-[1.38]">
            买赔信息
          </view>
        </view>

        <!-- 商品列表 - 使用scroll-view -->
        <scroll-view scroll-y class="h-[300px] flex flex-col gap-2">
          <view v-for="(sku, index) in state.config.skuList" :key="sku.goods?.gid || index" class="px-2">
            <view class="flex flex-col gap-2 rounded-lg bg-white p-1">
              <view class="text-28rpx text-[#2d2d2d] font-medium leading-[1.5]">
                {{ sku.goods?.name || '未知商品' }}
              </view>
              <view class="flex gap-1.5">
                <view class="rounded bg-[#f5f6f7] px-1 text-26rpx text-[#5c5c5c] leading-[1.38]">
                  {{ sku.goods?.code || '' }}
                </view>
                <view class="flex items-end gap-0.5">
                  <text class="text-26rpx text-[#f57f00] leading-none">
                    ¥
                  </text>
                  <text class="text-[18px] text-[#f57f00] font-medium leading-[0.94]">
                    {{ formatPrice(getSinglePrice(sku.price || 0, sku.qpc || 1)).integer }}
                  </text>
                  <text v-if="formatPrice(getSinglePrice(sku.price || 0, sku.qpc || 1)).decimal" class="text-26rpx text-[#f57f00] font-medium leading-none">
                    .{{ formatPrice(getSinglePrice(sku.price || 0, sku.qpc || 1)).decimal }}
                  </text>
                  <text class="text-[12px] text-[#8a8a8a] leading-none">
                    /{{ sku.minMunit || '个' }}
                  </text>
                </view>
              </view>
              <view class="flex items-center justify-between">
                <view class="text-28rpx text-[#2d2d2d] leading-[1.5]">
                  数量/赔付额
                </view>
                <view class="text-28rpx text-[#f14646] font-medium leading-[1.29]">
                  {{ sku.value || 0 }}{{ sku.minMunit || '个' }}/计¥{{ ((sku.value || 0).multiply(getSinglePrice(sku.price || 0, sku.qpc || 1))).scale(2) }}
                </view>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>

      <!-- 第二步：上传买赔 -->
      <view v-if="state.currentStep === 2" class="mb-3 min-h-[400px]">
        <!-- 上传区域标题 -->
        <view class="mb-4">
          <view class="mb-2 flex items-center gap-1 text-[16px] text-[#2d2d2d] font-medium">
            <text class="text-26rpx text-[#f14646]">
              *
            </text>
            上传买赔图片
          </view>
          <view class="text-26rpx text-[#8a8a8a]">
            请上传支付截图且保证订单号金额数字清晰可见
          </view>
        </view>

        <!-- 上传组件 -->
        <view class="min-h-[200px]">
          <wd-upload
            v-if="state.config.ossInfo"
            :file-list="state.config.fileList || []"
            :action="state.config.ossInfo.host"
            :build-form-data="buildFormData"
            :limit="10"
            multiple
            @success="handleSuccess"
          />
        </view>
      </view>

      <!-- 底部按钮区域 -->
      <view class="py-3" style="box-shadow: 0px -4px 12px 0px rgba(0, 0, 0, 0.05);">
        <!-- 第一步的按钮 -->
        <view v-if="state.currentStep === 1" class="w-full">
          <wd-button type="primary" block size="large" custom-class="w-full!" :round="false" @click="nextStep">
            上传买赔
          </wd-button>
        </view>

        <!-- 第二步的按钮 -->
        <view v-if="state.currentStep === 2" class="w-full flex gap-3">
          <wd-button
            type="info"
            size="large"
            :round="false"
            custom-class="flex-auto mr-2!"
            @click="prevStep"
          >
            上一步
          </wd-button>
          <wd-button
            type="primary"
            size="large"
            :round="false"
            custom-class="flex-auto"
            @click="handleSubmit"
          >
            确认提交
          </wd-button>
        </view>
      </view>
    </view>
  </wd-action-sheet>
</template>

<style scoped lang="scss">
.buy-compensation-pop {
  min-height: 60vh;
}
</style>
