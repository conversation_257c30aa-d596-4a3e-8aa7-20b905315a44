# OpenBillPop 开单确认弹窗组件

## 功能概述

开单确认弹窗组件用于在回货差异处理页面中展示开单确认界面，包含门店选择、商品信息展示和开单统计功能。

## 主要特性

- ✅ **门店选择**: 集成门店选择器，支持搜索和筛选，**必须选择指定门店**
- ✅ **统计信息**: 展示买赔品项数和买赔总金额
- ✅ **商品列表**: 滚动展示商品详细信息，包括价格和数量
- ✅ **开单确认**: 选择门店后提交开单请求
- ✅ **响应式设计**: 适配小程序界面，支持UnoCSS样式

## 使用方法

### 1. 基本用法

```vue
<script setup>
import OpenBillPop from './cmp/OpenBillPop.vue'

const openBillPopRef = ref()

function openBill() {
  openBillPopRef.value?.open({
    title: '开单确认',
    skuList: filteredSkuData,
    totalItems: 10,
    totalAmount: 299.99,
    defaultStoreGid: 1001, // 可选：指定默认门店
    onConfirm: (storeGid) => {
      console.log('选择的门店GID:', storeGid)
      // 处理开单逻辑
    }
  })
}

function handleOpenBill(storeGid) {
  // 处理开单提交
  console.log('提交开单，门店GID:', storeGid)
}
</script>

<template>
  <OpenBillPop
    ref="openBillPopRef"
    @opened="lockPage"
    @closed="unlockPage"
    @submit="handleOpenBill"
  />
</template>
```

### 2. 配置选项

```typescript
interface OpenBillConfig {
  title?: string // 弹窗标题，默认'开单确认'
  skuList?: SkuItem[] // 商品列表数据
  totalItems?: number // 买赔品项数
  totalAmount?: number // 买赔总金额
  defaultStoreGid?: number // 默认选中的门店GID
  onConfirm?: (storeGid: number) => void // 确认回调函数
}
```

## API 接口

### 组件方法

| 方法名 | 说明 | 参数 | 返回值 |
|--------|------|------|--------|
| `open(config)` | 打开弹窗 | `OpenBillConfig` | `void` |
| `close()` | 关闭弹窗 | - | `void` |

### 组件事件

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| `opened` | 弹窗打开时触发 | - |
| `closed` | 弹窗关闭时触发 | - |
| `submit` | 提交开单时触发 | `storeGid: number` |

### 数据类型

```typescript
// 商品数据结构
interface SkuItem {
  goods?: {
    gid?: number
    name?: string
  }
  price?: number // 商品价格
  munit?: string // 计量单位
  value?: number // 开单数量
}
```

## 门店选择说明

- **必选门店**: 用户必须从门店列表中选择一个具体门店
- **门店筛选**: 支持门店搜索和筛选功能
- **默认门店**: 可通过`defaultStoreGid`设置默认选中的门店
- **提交验证**: 未选择门店时，提交按钮处于禁用状态

## 样式特点

- 使用 **UnoCSS** 实现样式，保持与项目一致的设计风格
- 支持 **scroll-view** 滚动，商品列表固定高度300px
- **响应式布局**，适配不同屏幕尺寸
- **微信小程序** 兼容性优化，包含 `virtualHost` 配置

## 依赖组件

- `wd-action-sheet`: 底部弹出框
- `wd-select-picker`: 门店选择器
- `wd-button`: 提交按钮
- `useStoreSelect`: 门店选择逻辑

## 使用场景

1. **回货差异处理**: 在差异处理页面进行开单确认
2. **门店选择**: 支持多门店场景下的指定门店选择
3. **数据确认**: 开单前的商品信息和金额确认

## 注意事项

- 确保传入的 `skuList` 数据结构正确
- `defaultStoreGid` 会自动设置为默认选中门店
- 门店选择器依赖 `useStoreSelect` 组合式函数
- 组件使用 `virtualHost: true` 优化小程序渲染性能
- **重要**: 用户必须选择一个具体门店才能提交开单请求

## 相关组件

- `BuyCompensationPop`: 买赔弹窗组件
- `BackDiffEditSkuCard`: 商品编辑卡片组件
