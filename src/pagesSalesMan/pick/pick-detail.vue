<!--
 * @Description: 领货单详情页面
-->
<script setup lang="ts">
import { computed, ref } from 'vue'
import PickDetailSkuCard from './cmp/PickDetailSkuCard.vue'
import DetailSkeleton from '@/business/DetailSkeleton.vue'
import { createGlobalLoadingMiddleware } from '@/api/core/middleware'

const { emitRefreshData: emitRefreshPickList } = useSendRefreshData('pick-list') // 刷新列表，本页面提交成功需要刷新列表

const router = useRouter()
const userStore = useUserStore()
const { confirm: showConfirm } = useGlobalMessage()
const { success: showSuccess, error: showError } = useGlobalToast()
const globalLoading = useGlobalLoading()

const { checkPermission } = usePermissionChecker()

// 单号
const id = ref<string>('')

// 加载单头详情
const {
  data: order,
  loading: orderLoading,
  send: loadOrderDetail,
} = useRequest(
  (num: string) => Apis.vehsaleusesignInterface.getUsingGET_5({
    params: {
      num,
      fetchDetail: false,
    },
  }),
  {
    immediate: false,
  },
).onError((error) => {
  showError(error.error.message || '获取领货单详情失败')
})

// 单头详情
const orderDetail = computed(() => {
  return order.value?.data
})

// 加载商品数据
const {
  data: skuData,
  loading: skuLoading,
  send: loadSkuData,
} = useRequest(
  (num: string) => Apis.vehsaleusesignInterface.queryDetailsUsingPOST_3({
    data: {
      num,
      page: 0,
      pageSize: 3,
    },
  }),
  {
    immediate: false,
  },
).onError((error) => {
  showError(error.error.message || '获取领货单商品失败')
})

// 商品列表
const skuList = computed(() => {
  return skuData.value?.data || []
})

// 是否还有更多商品
const hasMoreSku = computed(() => {
  return skuData.value?.more
})

// 领货单状态
const orderStatus = computed(() => {
  if (!orderDetail.value)
    return 'initial'
  return getBillStatus(orderDetail.value.stat || 0)
})

// 统计数据 - 上部分数据
const topStats = computed(() => {
  if (!orderDetail.value) {
    return [
      { title: '领货金额(元)', value: '0.00' },
    ]
  }
  return [
    { title: '领货金额(元)', value: orderDetail.value.total || 0 },
  ]
})

// 统计数据 - 下部分数据
const bottomStats = computed(() => {
  // 示例：可以根据实际需求添加更多数据项
  return [
    { title: '商品项数(种)', value: orderDetail.value?.goodsCount || 0 },
    { title: '单品数(个)', value: orderDetail.value?.qty || 0 },
  ]
})

// 跳转到商品清单详情
function goToSkuList() {
  router.push({
    name: 'pick-detail-sku-list',
    params: {
      id: id.value,
      status: orderStatus.value,
    },
  })
}

// 是否显示作废按钮
const showAbortButton = computed(() => {
  return orderStatus.value === 'applied' && checkPermission('vehSaleUseSignAbort')
})

// 是否显示打印按钮
const showPrintButton = computed(() => {
  return (orderStatus.value === 'applied' || orderStatus.value === 'audited') && userStore.userRole === 'salesman'
})

/**
 * 领货校验
 * 校验通过则跳转到领货页面
 * 校验失败则提示
 */
const { send: navigateToPick } = useRequest(Apis.vehsaleusesignInterface.verifyCreateUsingPOST_3({ params: {
  vehSaleEmpGid: userStore.vehSaleEmp?.gid || 0,
} }), {
  immediate: false,
  middleware: createGlobalLoadingMiddleware({
    loadingText: '校验中...',
    delay: 200,
  }),
}).onSuccess(() => {
  router.replace({ name: 'pick-edit' })
}).onError((err) => {
  globalLoading.close()
  showError(err.error.message || '校验失败')
})

// 复制订单
const { send: copy } = useRequest(
  (num: string) => Apis.vehsaleusesignInterface.copyUsingPOST({
    params: {
      num,
    },
  }),
  {
    immediate: false,
    middleware: createGlobalLoadingMiddleware({
      loadingText: '操作中...',
    }),
  },
).onSuccess(() => {
  globalLoading.close()
  showSuccess({
    msg: '复制成功',
    duration: 500,
    closed: () => {
      navigateToPick()
    },
  })
}).onError((err) => {
  globalLoading.close()
  showError(err.error?.message || '复制失败')
})

/**
 * 领货校验
 * 校验通过则跳转到领货页面
 * 校验失败则提示
 */
const { send: verifyCreate } = useRequest(() => Apis.vehsaleusesignInterface.verifyCreateUsingPOST_3({ params: {
  vehSaleEmpGid: userStore.vehSaleEmp?.gid || 0,
} }), {
  immediate: false,
  middleware: createGlobalLoadingMiddleware({
    loadingText: '校验中...',
    delay: 200,
  }),
}).onSuccess(() => {
  copy(id.value)
}).onError((err) => {
  globalLoading.close()
  showError(err.error.message || '校验失败')
})

// 打印
function printOrder() {
  if (!id.value) {
    showError('缺少记录ID')
    return
  }
  showConfirm({
    title: '提示',
    msg: '确认打印该领货单吗？',
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    success: (res) => {
      if (res.action === 'confirm') {
        // handleAbortRequest(id.value)
        router.push({
          name: 'print',
          params: { id: id.value, moduleId: 'vehSaleUseSign' },
        })
      }
    },
  })
}

// 作废领货单
const {
  send: handleAbortRequest,
} = useRequest(
  (num: string) => Apis.vehsaleusesignInterface.abortUsingPOST_4({
    data: {
      num,
      version: orderDetail.value?.version || 1,
    },
  }),
  {
    immediate: false,
    middleware: createGlobalLoadingMiddleware({
      loadingText: '作废中...',
    }),
  },
).onSuccess(() => {
  globalLoading.close()
  showSuccess({
    msg: '作废成功',
    duration: 500,
    closed() {
      emitRefreshPickList()
      router.replace({
        name: 'pick-detail',
        params: {
          id: id.value,
        },
      })
    },
  })
}).onError((error) => {
  globalLoading.close()
  showError(error.error?.message || '作废失败')
})

// 作废操作
function abortOrder() {
  if (!id.value) {
    showError('缺少记录ID')
    return
  }

  showConfirm({
    title: '提示',
    msg: '确认作废该领货单吗？',
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    success: (res) => {
      if (res.action === 'confirm') {
        handleAbortRequest(id.value)
      }
    },
  })
}
onLoad((options: any) => {
  id.value = options.id as string
  loadOrderDetail(id.value)
  loadSkuData(id.value)
})
</script>

<template>
  <view class="receipt-detail-page relative min-h-screen w-screen flex flex-col bg-white">
    <!-- 主要内容 -->
    <view class="flex-1 p-3">
      <!-- 骨架屏 -->
      <DetailSkeleton v-if="orderLoading || skuLoading" />
      <!-- 实际内容 -->
      <block v-else>
        <!-- 状态栏 -->
        <DetailStatusHeader
          :status="orderStatus"
          :order-no="orderDetail?.num || ''"
          action-text="再来一单"
          custom-class="mb-3"
          :enable-action="userStore.userRole === 'salesman' && checkPermission('vehSaleUseSignCreate')"
          @action="verifyCreate"
        />

        <!-- 统计卡片 -->
        <DetailStatsCard
          :top-items="topStats"
          :bottom-items="bottomStats"
          custom-class="mb-3"
        />

        <!-- 商品清单 -->
        <view class="flex flex-col rounded-lg bg-white py-3">
          <DetailTableHeader
            title="商品清单"
            :headers="orderStatus === 'audited' ? ['规格', '报领', '实领'] : ['规格', '报领']"
            :show-more="hasMoreSku"
            @more-click="goToSkuList"
          />

          <!-- 商品列表 -->
          <view class="space-y-3">
            <view v-for="sku in skuList" :key="sku.goods?.gid" class="border-b border-[#F5F5F5] last:border-0">
              <PickDetailSkuCard :sku="sku" :status="orderStatus" />
            </view>
          </view>
        </view>

        <!-- 订单信息 -->
        <DetailInfoCard
          :items="[{ label: '出货仓位', value: orderDetail?.wrh?.name },
                   { label: '业务员', value: `${orderDetail?.vehSaleEmpName}[${orderDetail?.vehSaleEmp?.code}]` },
                   { label: '创建人', value: orderDetail?.filler },
                   { label: '操作人', value: orderDetail?.lastModifyOper },
                   { label: '操作时间', value: orderDetail?.lstupdTime }]"
        />
      </block>
    </view>

    <!-- 底部按钮 -->
    <view
      v-if="showAbortButton || showPrintButton"
      class="sticky bottom-0 bottom-0 left-0 left-0 right-0 right-0 z-10 box-border w-full bg-white p-3"
      style="--wot-button-medium-height: 44px;"
    >
      <view class="flex items-center justify-between">
        <wd-button
          v-if="showAbortButton"
          :custom-class="showPrintButton ? 'flex-auto' : 'flex-auto'"
          type="error"
          plain
          block
          :round="false"
          @click="abortOrder"
        >
          作废
        </wd-button>
        <wd-button
          v-if="showPrintButton"
          :custom-class="showAbortButton ? 'flex-auto ml-3!' : 'flex-auto'"
          block
          type="primary"
          :round="false"
          @click="printOrder"
        >
          打印
        </wd-button>
      </view>
      <wd-gap height="0" safe-area-bottom />
    </view>
  </view>
</template>

<style lang="scss" scoped>
.receipt-detail-page {
  min-height: 100vh;
}
</style>

<route lang="json">
{
  "name": "pick-detail",
  "style": {
    "navigationBarTitleText": "详情",
    "backgroundColor": "#F9F9F9"
  },
  "meta": {
    "permissions": ["vehSaleUseSignView"]
  }
}
</route>
