<script setup lang="ts">
import { computed, ref } from 'vue'
import PickEditSkuCard from './cmp/PickEditSkuCard.vue'
import CartSkeleton from '@/business/CartSkeleton.vue'
import { useCategorySelect } from '@/composables/useCategorySelect'
import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
import type { VehSaleGetDraftGoodsResponseDTO, VehSaleGetDraftQpcResponseDTO, VehSaleUseSignSubmitRequestDTO } from '@/api/globals'
import CartNavBar from '@/business/CartNavBar.vue'
import type { CartSku } from '@/composables/useDraft'
import { createGlobalLoadingMiddleware } from '@/api/core/middleware'
import type DraftSubmitVerify from '@/business/DraftSubmitVerify.vue'
import EditSort from '@/business/EditSort.vue'

const userStore = useUserStore()
const router = useRouter()

const { warning: showWarning, error: showError, success: showSuccess, info: showInfo } = useGlobalToast()
const { confirm: showConfirm } = useGlobalMessage()
const globalLoading = useGlobalLoading()
const draftSubmitVerifyRef = ref<InstanceType<typeof DraftSubmitVerify>>()

const { navBarTotalHeight } = storeToRefs(useDeviceInfo())
const { categoryList: categoryListData, categoryLoading, getCategoryList } = useCategorySelect(false)
const { draftInfo, cartSkuQueryParams, cartSkuList, getDraftInfo, cartSkuTotal, reloadCartSkuList, cartSkuPage, onCartSkuListSuccess, onCartSkuListError, draftInfoLoading, submitDraft, removeDraft, checkedSkuCount, updateSkuChecked, isAllChecked, selectAllCartSku, checkedSkuList, removeCheckedSku, setChecked, verifySubmit, verifySubmitData } = useDraft('vehSaleUseSign')
const { lockPage, unlockPage, pageStyle } = useLockPage()

const { emitRefreshData } = useSendRefreshData('pick-edit') // 刷新编辑页面数据
const { emitRedirect } = useSendRedirect('pick-edit') // 跳转到详情页面

const cartMode = ref<'common' | 'operation'>('common') // 购物车模式，common: 普通模式，operation: 操作模式

// 处理分类列表，添加数量显示
const categoryList = computed(() => {
  return categoryListData.value.map((item) => {
    const count = draftInfo.value?.data?.categorySkuCount?.[item.code] || 0
    return {
      label: item.label,
      value: item.value,
      code: item.code,
      count: item.label === '全部' ? draftInfo.value?.data?.skuCount : count,
    }
  })
})

const loading = computed(() => categoryLoading.value || draftInfoLoading.value || !cartSkuQueryParams.wrhGid)

// mescroll相关
const { mescrollInit, downCallback, getMescroll } = useMescroll(onPageScroll, onReachBottom)

onLoad(async (options: any) => {
  // 先获取draftInfo
  await getDraftInfo()
  await getCategoryList()
  if (options.wrhGid) {
    cartSkuQueryParams.wrhGid = Number(options.wrhGid)
  }
})

function reload() {
  getDraftInfo()
  handleRefresh()
}

// mescroll上拉加载
async function upCallback(mescroll: any) {
  if (mescroll.num === 1) {
    await reloadCartSkuList()
  }
  else {
    cartSkuPage.value = mescroll.num
  }
}

onCartSkuListSuccess((resp: any) => {
  getMescroll().endBySize(resp.data.data?.length || 0, cartSkuTotal.value)
  if (cartSkuTotal.value === 0) {
    getMescroll().showEmpty()
  }
})

onCartSkuListError((err: any) => {
  uni.showToast({ title: err.error?.message || '加载购物车商品失败', icon: 'none' })
  getMescroll().endErr()
})

// 购物车品项数量
const skuCount = computed(() => draftInfo.value?.data?.skuCount || 0)
// 购物车总金额
const total = computed(() => draftInfo.value?.data?.total || 0)

// 改变商品数量
async function handleSpecChange(spec: VehSaleGetDraftQpcResponseDTO, sku: CartSku) {
  const index = cartSkuList.value.findIndex(item => item.goods.gid === sku.goods.gid)
  const oldSku = CommonUtil.deepClone(cartSkuList.value[index])
  cartSkuList.value[index] = CommonUtil.deepClone(sku)
  const verifyIndex = verifySubmitData.value && verifySubmitData.value.data && verifySubmitData.value.data.length > 0 ? verifySubmitData.value.data.findIndex(item => item.goods.gid === sku.goods.gid) : -1
  if (verifyIndex !== -1) {
    verifySubmitData.value.data![verifyIndex] = sku
  }
  try {
    const result = await submitDraft({
      gdCode: sku.goods.gdCode!,
      goods: {
        gid: sku.goods.gid,
        name: sku.goods.name,
        code: sku.goods.code,
      },
      munit: spec.munit,
      price: spec.price,
      qpc: spec.qpc,
      qpcStr: spec.qpcStr,
      qty: spec.qty,
      qtyStr: spec.qtyStr,
      singlePrice: spec.singlePrice,
      total: spec.singlePrice.multiply(spec.qty).scale(2),
      uuid: spec.uuid,
      version: sku.version,
      wrh: {
        gid: Number(cartSkuQueryParams.wrhGid),
      },
    })
    cartSkuList.value[index].version = result.data?.version || cartSkuList.value[index].version
    if (verifyIndex !== -1) {
      verifySubmitData.value.data![verifyIndex].version = result.data?.version || verifySubmitData.value.data![verifyIndex].version
    }
    nextTick(() => {
      emitRefreshData()
    })
  }
  catch (error) {
    console.log(error)
    cartSkuList.value[index] = oldSku
    if (verifyIndex !== -1) {
      verifySubmitData.value.data![verifyIndex] = oldSku
    }
  }
}

async function handleBeforeModeChange(_: 'common' | 'operation') {
  return new Promise<boolean>((resolve) => {
    setChecked({
      checked: false,
    }).then(() => {
      resolve(true)
    }).catch(() => {
      resolve(false)
    })
  })
}

// 删除商品
async function handleDeleteSku(sku: VehSaleGetDraftGoodsResponseDTO) {
  showConfirm({
    title: '确认删除',
    msg: '确认将删除商品？',
    confirmButtonText: '确认',
    cancelButtonText: '再想想',
    success: async (res) => {
      if (res.action === 'confirm') {
        try {
          await removeDraft([{
            gdGid: sku.goods.gid!,
            version: sku.version,
          }])
          const updateSku = CommonUtil.deepClone(sku)
          updateSku.version = 0
          updateSku.qpcDetails = (updateSku.qpcDetails || []).map(item => ({
            ...item,
            qty: 0,
            total: 0,
          }))
          handleRefresh()
          emitRefreshData()
        }
        catch (_) {
          console.log(_)
        }
      }
    },
  })
}

/**
 * 删除所有选中状态的商品
 */
async function handleDeleteSelectedSku() {
  if (checkedSkuList.value.length === 0) {
    return showWarning('请先选择商品')
  }

  showConfirm({
    title: '确认删除',
    msg: `确认将这 ${checkedSkuList.value.length} 个商品删除？`,
    confirmButtonText: '确认',
    cancelButtonText: '再想想',
    success: async (res) => {
      if (res.action === 'confirm') {
        try {
          await removeCheckedSku()
          handleRefresh()
          emitRefreshData()
        }
        catch (_) {
          console.log(_)
        }
      }
    },
  })
}

/**
 * 刷新购物车商品
 */
function handleRefresh() {
  const mescroll = getMescroll()
  if (mescroll && mescroll.resetUpScroll) {
    mescroll.resetUpScroll()
  }
}

// 提交订单请求
const { send: submit } = useRequest(
  (data: VehSaleUseSignSubmitRequestDTO) => Apis.vehsaleusesignInterface.submitUsingPOST_4({
    data,
  }),
  {
    immediate: false,
    middleware: createGlobalLoadingMiddleware({
      loadingText: '提交中...',
    }),
  },
).onSuccess((resp) => {
  globalLoading.close()
  showSuccess({
    msg: '提交成功',
    duration: 500,
    closed: () => {
      emitRedirect(resp.data.data!)
      router.back()
    },
  })
}).onError((err) => {
  globalLoading.close()
  showError(err.error?.message || '提交失败')
})

/**
 * 实际提交订单逻辑
 */
async function doSubmit() {
  return new Promise<boolean>((resolve) => {
    const submitData = {
      vehSaleEmp: userStore.vehSaleEmp,
      wrh: {
        gid: Number(cartSkuQueryParams.wrhGid),
      },
      attachDetails: [],
    }
    verifySubmit(submitData).then((result) => {
      if (!result || !result.data || !result.data.length) {
        submit(submitData)
        resolve(true)
      }
      else {
        resolve(false)
        globalLoading.close()
        showWarning('部分商品库存不足，请调整数量!')
        draftSubmitVerifyRef.value?.open({
          beforeConfirm: async () => {
            const res = await doSubmit()
            return res
          },
        })
      }
    }).catch((err) => {
      globalLoading.close()
      showError(err.error?.message || '校验失败')
      resolve(false)
    })
  })
}

/**
 * 提交订单
 */
async function handleSubmit() {
  return new Promise<boolean>((resolve) => {
    // 先校验购物车是否为空
    if (skuCount.value === 0) {
      showInfo('请先添加商品')
      resolve(false)
      return
    }

    // 购物车不为空，弹出二次确认
    showConfirm({
      title: '确认提交',
      msg: '确认提交本次领货',
      confirmButtonText: '确认',
      cancelButtonText: '再想想',
      success: async (res) => {
        if (res.action === 'confirm') {
          const result = await doSubmit()
          resolve(result)
        }
        else {
          resolve(false)
        }
      },
    })
  })
}
</script>

<template>
  <DraftSubmitVerify ref="draftSubmitVerifyRef" title="请确认商品数量" @opened="lockPage" @closed="unlockPage" @confirm="reload" @cancel="reload">
    <template #body>
      <view class="mx-3 mb-3 box-border flex items-center rounded-2 bg-[var(--frequentapplication-orange-background)] px-20rpx py-2 text-26rpx text-[var(--frequentapplication-orange-content)]">
        以下商品库存不足，已自动更新为最大可领货数
      </view>
      <template v-if="verifySubmitData && verifySubmitData.data">
        <PickEditSkuCard
          v-for="sku in verifySubmitData.data"
          :key="sku.goods.gid"
          :sku="sku"
          @change="handleSpecChange"
        />
      </template>
    </template>
  </DraftSubmitVerify>
  <view class="cart-page box-border min-h-screen w-screen bg-[#F9F9F9]" :style="pageStyle">
    <CartNavBar v-model:mode="cartMode" :left-text="`购物车(${skuCount})`" :before-mode-change="handleBeforeModeChange" />
    <CartSkeleton v-if="loading" />

    <block v-else>
      <!-- 分类Tabs -->
      <wd-tabs v-model="cartSkuQueryParams.sortCode" :custom-style="`top:${navBarTotalHeight}px`" custom-class="w-screen bg-white fixed!  z-3 tabs-custom" @change="handleRefresh">
        <wd-tab v-for="cat in categoryList" :key="cat.code" :title="cat.label" :name="cat.code" :badge-props="cat.count ? { isDot: true } : undefined" />
      </wd-tabs>

      <!-- 排序组件 -->
      <view class="fixed right-0 z-1 box-border w-screen bg-white px-3" :style="`top: calc(${navBarTotalHeight}px + 42px)`">
        <EditSort v-model:sorts="cartSkuQueryParams.sorts" @change="handleRefresh" />
      </view>

      <wd-gap height="calc(42px + 36px)" />
      <!-- 骨架屏 -->
      <mescroll-body
        v-if="cartSkuQueryParams.wrhGid"
        :down="downOption"
        :up="upOption"
        @init="mescrollInit"
        @down="downCallback"
        @up="upCallback"
      >
        <view class="p-3 pb-0">
          <wd-swipe-action
            v-for="sku in cartSkuList"
            :key="sku.goods.gid"
            :disabled="cartMode === 'operation'"
          >
            <PickEditSkuCard
              :sku="sku"
              :checked="sku.checked"
              :mode="cartMode"
              :all-selected="isAllChecked"
              @change="handleSpecChange"
              @check="updateSkuChecked"
            />
            <template #right>
              <view class="h-full w-120rpx flex items-center justify-center bg-[var(--frequentapplication-red-content)]" @click="handleDeleteSku(sku)">
                <text class="i-tdesign-delete text-5 text-white" />
              </view>
            </template>
          </wd-swipe-action>
        </view>
      </mescroll-body>
    </block>
    <CartBar :all-selected="isAllChecked" :show-cart="false" :selected-count="checkedSkuCount" :amount="total" :mode="cartMode" safe-area-bottom fixed button-text="提交" @click-select-all="selectAllCartSku" @click-delete="handleDeleteSelectedSku" @click-button="handleSubmit" />
  </view>
</template>

<style lang="scss" scoped>
.cart-page {
  padding-bottom: 120rpx !important;
  padding-bottom: calc(120rpx + const(safe-area-inset-bottom)) !important;
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom)) !important;
  :deep(.mescroll-body) {
    min-height: calc(100vh - 42px - 36px - 120rpx - var(--navbar-total-height)) !important;
    min-height: calc(100vh - 42px - 36px - 120rpx - var(--navbar-total-height) - const(safe-area-inset-bottom)) !important;
    min-height: calc(100vh - 42px - 36px - 120rpx - var(--navbar-total-height) - env(safe-area-inset-bottom)) !important;
  }

  // tabs样式
  :deep(.tabs-custom){
    // 修复徽标导致文字溢出的问题
    .wd-tabs__nav-item-badge {
      display: flex;
      max-width: 100%;
      min-width: 0;

      .wd-badge {
        display: flex;
        max-width: 100%;
        min-width: 0;

        .wd-tabs__nav-item-text {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          flex: 1;
          min-width: 0;
        }
      }
    }
  }
}
</style>

<route lang="json">
{
  "name": "pick-cart",
  "style": {
    "navigationBarTitleText": "购物车",
    "navigationStyle": "custom"
  },
  "meta": {
    "permissions": ["vehSaleUseSignCreate"]
  }
}
</route>
