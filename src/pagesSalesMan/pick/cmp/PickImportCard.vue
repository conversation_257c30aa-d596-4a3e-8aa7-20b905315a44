<!--
 * @Description: 历史领货单导入卡片组件
-->
<script setup lang="ts">
import type { VehSaleUseSignQueryResponseDTO } from '@/api/globals'

const props = defineProps({
  record: {
    type: Object as PropType<VehSaleUseSignQueryResponseDTO>,
    required: true,
    default: () => ({}),
  },
})

const emit = defineEmits(['import'])

// 获取状态文字
const statusText = computed(() => {
  return getBillStatusText(props.record.stat!)
})

// 获取状态颜色
const statusColor = computed(() => {
  return getBillStatusColor(props.record.stat!)
})

// 处理再来一单
function handleImport() {
  emit('import', props.record)
}
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <view class="pick-import-card mb-2 box-border w-full rounded-lg bg-white p-3">
    <!-- 头部：状态和单号 + 再来一单按钮 -->
    <view class="mb-3 flex items-center justify-between">
      <view class="flex items-center gap-1">
        <!-- 状态标签 -->
        <view
          class="rounded px-1 py-0.5 text-xs"
          :style="{ backgroundColor: `${statusColor}20`, color: statusColor }"
        >
          {{ statusText }}
        </view>
        <!-- 单号 -->
        <text class="text-4 text-[#2D2D2D] font-medium">
          {{ record.num }}
        </text>
      </view>

      <!-- 再来一单按钮 -->
      <wd-button
        type="primary"
        size="small"
        round
        custom-style="padding: 4px 16px; height: 28px; font-size: 14px;"
        @click="handleImport"
      >
        再来一单
      </wd-button>
    </view>

    <!-- 基本信息 -->
    <view class="mb-3 flex flex-wrap">
      <view class="mb-2 min-w-[50%] flex break-all">
        <text class="flex-none text-3 text-[#8A8A8A]">
          出货仓位：
        </text>
        <text class="text-3 text-[#2D2D2D]">
          {{ record.wrh?.name || '-' }}
        </text>
      </view>

      <view class="mb-2 min-w-[50%] flex break-all">
        <text class="flex-none text-3 text-[#8A8A8A]">
          业务员：
        </text>
        <text class="text-3 text-[#2D2D2D]">
          {{ record.vehSaleEmpName || '-' }}
        </text>
      </view>

      <view class="min-w-[50%] flex break-all">
        <text class="text-3 text-[#8A8A8A]">
          操作时间：
        </text>
        <text class="text-3 text-[#2D2D2D] font-500">
          {{ formatDate(record.lstupdTime) }}
        </text>
      </view>
    </view>

    <!-- 统计信息 -->
    <view class="flex items-center justify-between rounded bg-[#F5F5F5] px-3 py-2">
      <view class="flex items-center">
        <text class="text-28rpx text-[#8A8A8A]">
          共
        </text>
        <text class="mx-1 text-4 text-[#1A1A1A] font-medium">
          {{ record.goodsCount || 0 }}
        </text>
        <text class="text-28rpx text-[#8A8A8A]">
          种商品
        </text>
      </view>

      <view class="flex items-center">
        <text class="text-28rpx text-[#8A8A8A]">
          共
        </text>
        <view class="mx-1 flex items-end">
          <text class="text-3 text-[#8A8A8A]">
            ¥
          </text>
          <text class="text-4 text-[#1A1A1A] font-medium">
            {{ record.total || '0.00' }}
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.pick-import-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
}
</style>
