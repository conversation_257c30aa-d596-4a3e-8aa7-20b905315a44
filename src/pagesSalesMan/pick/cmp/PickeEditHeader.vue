<!--
 * @Author: weisheng
 * @Date: 2025-05-20 16:44:22
 * @LastEditTime: 2025-06-11 19:55:14
 * @LastEditors: weisheng
 * @Description:
 * @FilePath: /lsym-cx-mini/src/pagesSalesMan/pick/cmp/PickeEditHeader.vue
 * 记得注释
-->
<script setup lang="ts">
import type { SelectPickerBeforeConfirm, SelectPickerInstance } from 'wot-design-uni/components/wd-select-picker/types'

const props = defineProps({
  // 当前仓位
  currentWrh: {
    type: [Number, String],
    default: '',
  },
  // 自定义类名
  customClass: {
    type: String,
    default: '',
  },
  // 自定义样式
  customStyle: {
    type: String,
    default: '',
  },
  // 清空草稿
  clearAndUpdate: {
    type: Function,
  },
})

const emit = defineEmits<{
  (e: 'importRecent'): void
  (e: 'update:currentWrh', value: number): void
  (e: 'change', value: number): void
}>()

const userStore = useUserStore()
const { confirm: showConfirm } = useGlobalMessage()
const globalLoading = useGlobalLoading()
const globalToast = useGlobalToast()

const { wrhList, wrhLoading } = useWrhSelect(userStore.userInfo?.vehSaleEmp?.gid)

const wrhListData = computed(() => {
  return wrhList.value.filter(item => !!item.value)
})

const wrhSelectRef = ref<SelectPickerInstance>()

const confirmedWrhId = ref<string | number>(props.currentWrh)

const currentWrh = computed(() => {
  return wrhList.value.find(item => item.value === confirmedWrhId.value)
})

function handleSelect(event: { value: string, selectedItems: { label: string, value: string } }) {
  confirmedWrhId.value = event.value
  emit('update:currentWrh', confirmedWrhId.value as unknown as number)
  emit('change', confirmedWrhId.value as unknown as number)
}

const beforeConfirm: SelectPickerBeforeConfirm = (value, resolve) => {
  if (!value) {
    globalToast.warning('请选择仓位')
    resolve(false)
  }

  else if (value !== confirmedWrhId.value && confirmedWrhId.value) {
    showConfirm({
      title: '提示',
      msg: '切换仓位将会清空购物车，确认切换吗？',
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      async success(res) {
        if (res.action === 'confirm') {
          try {
            if (CommonUtil.isFunction(props.clearAndUpdate)) {
              await props.clearAndUpdate()
            }
            resolve(true)
            globalLoading.close()
            globalToast.success(`已切换至${currentWrh.value?.label}仓位`)
          }
          catch (error) {
            resolve(false)
            globalLoading.close()
            globalToast.error('切换仓位失败')
          }
        }
        else {
          resolve(false)
        }
      },
    })
  }
  else {
    resolve(true)
  }
}

watch(() => props.currentWrh, (newVal) => {
  if (!CommonUtil.isEqual(newVal, confirmedWrhId.value)) {
    confirmedWrhId.value = newVal
  }
})

defineExpose({
  openSelect: async () => {
    // 如果正在加载，等待加载完成
    if (wrhLoading.value) {
      globalLoading.loading('加载中...')
      await new Promise<void>((resolve) => {
        const unwatch = watch(wrhLoading, (loading) => {
          if (!loading) {
            unwatch()
            resolve()
          }
        })
      })
      globalLoading.close()
    }

    // 如果只有一个仓位，直接选中它
    if (wrhListData.value.length === 1) {
      const singleWrh = wrhListData.value[0]
      confirmedWrhId.value = singleWrh.value
      emit('update:currentWrh', confirmedWrhId.value as unknown as number)
      emit('change', confirmedWrhId.value as unknown as number)
      return
    }

    // 多个仓位时才弹出选择框
    wrhSelectRef.value?.open()
  },
})
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <view class="pick-edit-header box-border h-74rpx w-full flex items-center justify-between bg-[var(--greyapplication-bottomlayer)] px-28rpx" :style="customStyle" :class="customClass">
    <wd-select-picker
      ref="wrhSelectRef"
      v-model="confirmedWrhId"
      :disabled="wrhListData.length === 1"
      :close-on-click-modal="false"
      :columns="wrhListData"
      :loading="wrhLoading"
      title="选择仓位"
      label="仓位"
      type="radio"
      hide-label
      filterable
      use-default-slot
      :before-confirm="beforeConfirm"
      @confirm="handleSelect"
    >
      <template #default>
        <view class="flex items-center text-28rpx text-[var(--textapplication-text-2)]">
          <text class="i-tdesign-home mr-1" />
          <text class="mr-2">
            仓位
          </text>
          <text class="mr-2 max-w-320rpx truncate text-[var(--textapplication-text-1)]">
            {{ currentWrh?.label || '请选择仓位' }}
          </text>
          <text v-if="wrhListData.length > 1" class="i-carbon-chevron-right text-[var(--textapplication-text-1)]" />
        </view>
      </template>
    </wd-select-picker>
    <view
      class="inline-flex items-center text-28rpx"
      @click="emit('importRecent')"
    >
      <text class="i-carbon-time mr-1 text-[#1C64FD]" />
      <text class="text-[#1C64FD]">
        导入最近
      </text>
      <text class="i-carbon-chevron-right text-[#1C64FD]" />
    </view>
  </view>
</template>

<style lang="scss" scoped>
.pick-edit-header {
  :deep() {
    .wd-action-sheet__close {
      display: none;
    }
  }
}
</style>
