<!--
 * @Description: 选择领货单快速建单弹框
-->
<script setup lang="ts">
import { callInterceptor } from 'wot-design-uni/components/common/interceptor'
import dayjs from 'dayjs'
import PickImportCard from './PickImportCard.vue'
import type { VehSaleUseSignQueryResponseDTO } from '@/api/globals'

const emit = defineEmits(['import', 'opened', 'closed'])
const show = ref(false)
const globalToast = useGlobalToast()
const { userInfo } = storeToRefs(useUserStore())

type BeforeImport = (record: VehSaleUseSignQueryResponseDTO) => boolean | Promise<boolean>
const userStore = useUserStore()
const copyDays = userStore.getOption('PS4_CopyVehSaleUseSignBillDays') || 10

const state = reactive({
  beforeImport: null as BeforeImport | null, // 导入前回调
  wrhGid: null as number | null, // 仓位ID
})

// loadmore 组件状态
const loadmoreState = ref<'loading' | 'finished' | 'error'>('loading')

// 使用 usePagination 实现分页查询
const {
  // 数据列表
  data: recordList,
  reload: reloadRecords,
  loading,
  total,
  page,
} = usePagination(
  (page, pageSize) => Apis.vehsaleusesignInterface.queryUsingPOST_5({
    data: {
      sorts: [
        {
          field: 'lstupdTime',
          asc: false,
        },
      ],
      page: page - 1, // 后端页码从0开始，usePagination从1开始
      pageSize,
      beginDate: dayjs().subtract(Number(copyDays), 'day').format('YYYY-MM-DD'),
      finishDate: dayjs().format('YYYY-MM-DD'),
      vehSaleEmpGid: userInfo.value?.vehSaleEmp?.gid,
      ...(state.wrhGid && { wrhGid: state.wrhGid }),
    },

  }),
  {
    immediate: false,
    append: true,
    watchingStates: [],
    initialData: [],
    initialPageSize: 6,
    initialPage: 1,
    total: response => response.total,
    data: response => (response.data || []),
  },
).onSuccess((resp) => {
  // 更新loadmore状态
  updateLoadmoreState()
}).onError((error) => {
  globalToast.error(error.error?.message || '加载历史领货单失败')
  loadmoreState.value = 'error'
})

// 打开弹框
function open({ beforeImport, wrhGid }: { beforeImport?: BeforeImport, wrhGid?: number } = {}) {
  state.beforeImport = beforeImport || null
  state.wrhGid = wrhGid || null
  show.value = true
  // 重置loadmore状态
  loadmoreState.value = 'loading'
  // 重新加载数据
  reloadRecords()
}

// 关闭弹框
function handleClose() {
  show.value = false
}

// 更新loadmore状态
function updateLoadmoreState() {
  if (loading.value) {
    loadmoreState.value = 'loading'
  }
  else if (recordList.value.length >= (total.value || 0)) {
    loadmoreState.value = 'finished'
  }
  else if (recordList.value.length > 0 && recordList.value.length < (total.value || 0)) {
    loadmoreState.value = 'loading'
  }
}

// 处理loadmore重新加载
function handleLoadmore() {
  if (!loading.value && recordList.value.length < (total.value || 0)) {
    page.value = page.value + 1
  }
}

// 处理导入领货单
function handleImport(record: VehSaleUseSignQueryResponseDTO) {
  if (state.beforeImport) {
    callInterceptor(() => state.beforeImport!(record), {
      done: () => {
        emit('import', record)
        handleClose()
      },
    })
  }
  else {
    emit('import', record)
    handleClose()
  }
}

// 暴露open方法
defineExpose({
  open,
})
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <wd-action-sheet v-model="show" title="选择领货单快速建单" @opened="emit('opened')" @closed="emit('closed')">
    <view class="pick-import-container">
      <!-- 历史领货单列表 -->
      <view class="list-container bg-[#F9F9F9] p-3">
        <scroll-view
          scroll-y
          class="h-60vh"
          @scrolltolower="handleLoadmore"
        >
          <view v-if="recordList.length > 0" class="space-y-2">
            <PickImportCard
              v-for="record in recordList"
              :key="record.num"
              :record="record"
              @import="handleImport"
            />
          </view>

          <!-- 空状态 -->
          <EmptyStatus v-else-if="!loading" tip="暂无历史领货单记录" custom-class="pt-8!" />

          <!-- 加载更多组件 -->
          <wd-loadmore
            v-if="recordList.length > 0"
            :state="loadmoreState"
            @reload="handleLoadmore"
          />
        </scroll-view>
      </view>
    </view>
  </wd-action-sheet>
</template>

<style lang="scss" scoped>
.pick-import-container {
  .header {
    border-bottom: 1px solid #F5F5F5;
    background-color: #FFFFFF;
  }

  .list-container {
    min-height: 200px;

  }
}

:deep(.wd-action-sheet__container) {
  padding: 0 !important;
}
</style>
