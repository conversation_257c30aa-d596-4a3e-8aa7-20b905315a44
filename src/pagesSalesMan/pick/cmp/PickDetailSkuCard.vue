<script setup lang="ts">
import { isDef } from 'wot-design-uni/components/common/util'
import type { VehSaleUseSignDtlResponseDTO } from '@/api/globals'
import type { OrderStatus } from '@/utils/bill'

const props = defineProps({
  sku: {
    type: Object as PropType<VehSaleUseSignDtlResponseDTO>,
    required: true,
    default: () => ({ }),
  },

  /**
   * 订单状态
   * 已审核展示实领，其余状态不展示
   */
  status: {
    type: String as PropType<OrderStatus>,
    default: 'applied',
  },

})

/**
 * 商品图片列表
 */
const skuImgList = computed(() => {
  const imgList = props.sku.imageDetails && props.sku.imageDetails.length > 0 ? props.sku.imageDetails.map(item => item.fileUrl) : []
  return imgList.filter(item => isDef(item))
})

/**
 * 商品主图
 */
const skuMainImg = computed(() => {
  return skuImgList.value.length > 0 ? skuImgList.value[0] : ''
})

/**
 * 计算显示的选项数量
 */
const displayCount = computed(() => {
  return props.status === 'audited' ? 3 : 2
})

/**
 * 计算每个选项的宽度
 */
const itemWidth = computed(() => {
  return `${Math.floor((100 / displayCount.value) * 100) / 100}%`
})
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <view class="overflow-hidden">
    <view class="py-3">
      <view class="mb-2 flex">
        <!-- 商品图片 -->
        <SkuImage
          :src="skuMainImg"
          :preview-src="skuImgList"
          mode="widthFix"
          custom-class="mr-3 flex-none"
        />
        <view class="flex-auto">
          <view class="line-clamp-2 break-all text-28rpx text-[#2D2D2D] font-medium leading-[1.5]">
            {{ sku.goods?.name }}
          </view>
          <view class="mt-1 inline-block break-all rounded bg-[#F5F6F7] px-2 py-0 text-3 text-[#5C5C5C]">
            {{ sku.goods?.code }}
          </view>
        </view>
      </view>

      <view class="mt-2">
        <view
          v-for="(qpc, index) in sku.qpcDetails"
          :key="index"
          class="box-border w-full flex items-center justify-between px-3 py-2"
        >
          <text :style="{ width: itemWidth }">
            <text class="h-5 break-all rounded bg-[#F5F6F7] px-1 text-[26rpx] text-[var(--textapplication-text-2)] leading-[1]">
              {{ qpc.qpcStr }}
            </text>
          </text>
          <text
            :style="{ width: itemWidth }"
            class="break-all text-[26rpx] text-[var(--frequentapplication-primary-content)]" :class="[
              status === 'audited' ? 'text-center text-[var(--textapplication-text-1)]' : 'text-right',
            ]"
          >
            {{ Number(qpc.applyQty).divide(qpc.qpc || 1).scale(4) }}{{ qpc.munit }}
          </text>
          <text
            v-if="status === 'audited'"
            :style="{ width: itemWidth }"
            class="break-all text-right text-[26rpx]"
            :class="[
              qpc.qty !== qpc.applyQty
                ? 'text-[var(--frequentapplication-red-content)]'
                : 'text-[var(--frequentapplication-primary-content)]',
            ]"
          >
            {{ qpc.qtyStr }}{{ qpc.munit }}
          </text>
        </view>
      </view>
    </view>
  </view>
</template>
