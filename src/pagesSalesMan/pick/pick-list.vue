<script setup lang="ts">
import { reactive, ref } from 'vue'
import { isArray } from 'wot-design-uni/components/common/util'
import PickCard from './cmp/PickCard.vue'
import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
import { useGlobalToast } from '@/composables/useGlobalToast'
import TimeFilter from '@/components/TimeFilter.vue'
import type { OrderStatus } from '@/utils/bill'

const { mescrollInit, getMescroll, downCallback } = useMescroll(onPageScroll, onReachBottom)
const { userInfo } = storeToRefs(useUserStore())
const router = useRouter()
const globalToast = useGlobalToast()
const timeFilterRef = ref()

// 监听刷新数据事件
useWatchRefreshData({
  callback: () => {
    handleRefresh()
  },
  name: 'pick-list',
})

// 当前选中的Tab - 已申请/已审核/已作废
// 1300:已申请
// 100:已审核
// 1310:审核后作废
const currentTab = ref<OrderStatus>('applied')

// 查询参数
const queryParams = reactive({
  keyword: '',
  vehSaleEmpGid: undefined as number | undefined,
  wrhGid: undefined as number | undefined,
  stats: [1300] as (number[] | undefined),
  beginDate: '',
  finishDate: '',
  sorts: [
    {
      field: 'lstupdTime',
      asc: false,
    },
  ],
})

// 使用 usePagination 实现分页查询
const {
  // 数据列表
  data: recordList,
  reload: reloadRecords,
  total,
  page,
} = usePagination(
  (page, pageSize) => Apis.vehsaleusesignInterface.queryUsingPOST_5({
    data: {
      ...queryParams,
      page: page - 1, // 后端页码从0开始，usePagination从1开始
      pageSize,
      vehSaleEmpGid: userInfo.value?.vehSaleEmp?.gid,
    },
  }),
  {
    immediate: false,
    append: true,
    initialData: [],
    initialPageSize: 10,
    initialPage: 1,
    total: response => response.total,
    data: response => (response.data || []),
  },
).onSuccess((resp) => {
  getMescroll().endBySize(resp.data.data?.length || 0, total.value)
}).onError((error) => {
  globalToast.error(error.error?.message || '加载领货列表失败')
  getMescroll().endErr()
})

async function upCallback(mescroll: any) {
  if (mescroll.num === 1) {
    await reloadRecords()
  }
  else {
    page.value = mescroll.num
  }
}

// 处理路由参数
onLoad((options: any) => {
  // 设置初始Tab状态
  // 当前选中的Tab - 已申请/已审核/已作废
  // 1300:已申请
  // 100:已审核
  // 1310:申请后作废
  // 1310:审核后作废
  if (options.status) {
    switch (options.status) {
      case 'applied':
        currentTab.value = 'applied'
        queryParams.stats = [1300]
        break
      case 'audited':
        currentTab.value = 'audited'
        queryParams.stats = [100]
        break
      case 'canceled':
        currentTab.value = 'canceled'
        queryParams.stats = [110, 1310]
        break

      default:
        currentTab.value = 'applied'
        queryParams.stats = [1300]
        break
    }
  }
  else if (options.stat) {
    // 兼容旧的 stat 参数
    const statNumber = Number(options.stat)
    switch (statNumber) {
      case 1300:
        currentTab.value = 'applied'
        queryParams.stats = [1300]
        break
      case 100:
        currentTab.value = 'audited'
        queryParams.stats = [100]
        break
      case 1310:
        currentTab.value = 'canceled'
        queryParams.stats = [110, 1310]
        break

      default:
        currentTab.value = 'applied'
        queryParams.stats = [1300]
        break
    }
  }

  // 如果有时间范围参数，设置查询参数
  if (options.beginDate) {
    queryParams.beginDate = options.beginDate
  }

  if (options.endDate) {
    queryParams.finishDate = options.endDate
  }
})

function handleRefresh() {
  // 初始加载数据
  if (getMescroll()) {
    getMescroll().resetUpScroll()
  }
}

// 切换Tab
function changeTab(event: { index: number, name: OrderStatus }) {
  currentTab.value = event.name
  // 根据当前选中的Tab设置查询参数
  switch (event.name) {
    case 'applied':
      queryParams.stats = [1300]
      break
    case 'audited':
      queryParams.stats = [100]
      break
    case 'canceled':
      queryParams.stats = [110, 1310]
      break
    default:
      queryParams.stats = [1300]
      break
  }

  handleRefresh()
}

/**
 * 筛选时间的回调
 */
function handleTimeFilterConfirm(payload: { range: string | [string, string] }) {
  if (isArray(payload.range)) {
    queryParams.beginDate = payload.range[0]
    queryParams.finishDate = payload.range[1]
  }
  else {
    queryParams.beginDate = payload.range
    queryParams.finishDate = payload.range
  }

  handleRefresh()
}

// 查看领货记录详情
function viewRecordDetail(id: string | undefined) {
  if (!id) {
    globalToast.error('记录ID不存在')
    return
  }

  router.push({
    name: 'pick-detail',
    params: { id },
  })
}
</script>

<template>
  <view class="pick-list box-border min-h-screen bg-[#F9F9F9] pt-[calc(52px+42px)]">
    <!-- 搜索和筛选区域 - 固定在顶部 -->
    <view class="fixed-top-section">
      <SearchBar
        v-model="queryParams.keyword"
        placeholder="单号/仓位/商品"
        :has-filter="!!queryParams.beginDate || !!queryParams.finishDate"
        @filter="timeFilterRef?.open()"
        @clear="handleRefresh"
        @search="handleRefresh"
      />

      <!-- 类型选择Tab -->
      <wd-tabs v-model="currentTab" auto-line-width @change="changeTab">
        <wd-tab title="已申请" name="applied" />
        <wd-tab title="已审核" name="audited" />
        <wd-tab title="已作废" name="canceled" />
      </wd-tabs>
    </view>

    <!-- 领货记录列表 -->
    <mescroll-body
      :down="downOption" :up="upOption" height="100%" safearea @init="mescrollInit"
      @down="downCallback" @up="upCallback"
    >
      <view class="mx-3 pt-2">
        <PickCard
          v-for="record in recordList"
          :key="record.num"
          :record="record"
          @click="viewRecordDetail(record.num)"
        />
      </view>
    </mescroll-body>

    <!-- 筛选弹窗 -->
    <TimeFilter ref="timeFilterRef" @confirm="handleTimeFilterConfirm" />
  </view>
</template>

<style lang="scss" scoped>
.pick-list {

  :deep(.mescroll-body) {
    min-height: calc(100vh - 52px - 42px) !important;
    min-height: calc(100vh - 52px - 42px - constant(safe-area-inset-bottom)) !important;
    min-height: calc(100vh - 52px - 42px - env(safe-area-inset-bottom)) !important;
  }
}

/* 固定在顶部的搜索栏和筛选条件 */
.fixed-top-section {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 10;
  background-color: #F9F9F9;
}
</style>

<route lang="json">
{
  "name": "pick-list",
  "style": {
    "navigationBarTitleText": "领货列表"
  },
  "meta": {
    "permissions": ["vehSaleUseSignView"]
  }
}
</route>
