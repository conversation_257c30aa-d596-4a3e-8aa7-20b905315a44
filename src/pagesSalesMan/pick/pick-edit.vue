<script setup lang="ts">
import PickEditSkuCard from './cmp/PickEditSkuCard.vue'
import PickeEditHeader from './cmp/PickeEditHeader.vue'
import PickEditSkeleton from './cmp/PickEditSkeleton.vue'
import PickEditImport from './cmp/PickEditImport.vue'
import SkuListSkeleton from '@/business/SkuListSkeleton.vue'
import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
import type { VehSaleGetDraftGoodsResponseDTO, VehSaleGetDraftQpcResponseDTO, VehSaleUseSignQueryResponseDTO, VehSaleUseSignSubmitRequestDTO } from '@/api/globals'
import { createGlobalLoadingMiddleware } from '@/api/core/middleware'
import CartNavBar from '@/business/CartNavBar.vue'
import DraftSubmitVerify from '@/business/DraftSubmitVerify.vue'

const userStore = useUserStore()
const globalToast = useGlobalToast()
const globalLoading = useGlobalLoading()
const { confirm: showConfirm } = useGlobalMessage()
const router = useRouter()
const { mescrollInit, downCallback, getMescroll } = useMescroll(onPageScroll, onReachBottom)
const pickeEditHeaderRef = ref<InstanceType<typeof PickeEditHeader>>()
const { getDraftInfo, clearDraft, draftInfo, skuQueryParams, skuList, skuListLoading, skuTotal, reloadSkuList, skuPage, onSkuListSuccess, onSkuListError, draftInfoLoading, submitDraft, verifySubmit, verifySubmitData } = useDraft('vehSaleUseSign')
const { categoryList: categoryListData, getCategoryList, categoryLoading } = useCategorySelect(false)

const { lockPage, unlockPage, pageStyle } = useLockPage()

const pickEditImportRef = ref<InstanceType<typeof PickEditImport>>()
const draftSubmitVerifyRef = ref<InstanceType<typeof DraftSubmitVerify>>()

const { emitRefreshData: emitRefreshSalesmanHome } = useSendRefreshData('salesman-home')

// 监听刷新数据事件
useWatchRefreshData({
  callback: () => {
    reload()
  },
  name: 'pick-edit',
})
/**
 * 重新加载数据
 */
function reload() {
  getDraftInfo()
  handleRefresh()
}

/**
 * 监听跳转事件
 */
useWatchReditct({
  callback: (id) => {
    router.replace({
      name: 'pick-detail',
      params: { id },
    })
  },
  name: 'pick-edit',
})

const categoryList = computed(() => {
  return categoryListData.value.map((item) => {
    const count = draftInfo.value?.data?.categorySkuCount?.[item.code] || 0
    return {
      label: item.label,
      value: item.value,
      code: item.code,
      count: item.label === '全部' ? draftInfo.value?.data?.skuCount : count,
    }
  })
})

const loading = computed(() => {
  return categoryLoading.value || draftInfoLoading.value
})

function handleRefresh() {
  const mescroll = getMescroll()
  if (mescroll && mescroll.resetUpScroll) {
    mescroll.resetUpScroll()
  }
}

onSkuListSuccess((resp) => {
  getMescroll().endBySize(resp.data.data?.length || 0, skuTotal.value)
  if (skuTotal.value === 0) {
    getMescroll().showEmpty()
  }
})

onSkuListError((err) => {
  globalToast.error(err.error?.message || '加载领货商品失败')
  getMescroll().endErr()
})

// 上拉加载商品数据
async function upCallback(mescroll: any) {
  if (mescroll.num === 1) {
    await reloadSkuList()
  }
  else {
    skuPage.value = mescroll.num
  }
}

/**
 * 领货校验
 * 校验通过则跳转到领货页面
 * 校验失败则提示
 */
const { send: create } = useRequest(Apis.vehsaleusesignInterface.verifyCreateUsingPOST_3({ params: {
  vehSaleEmpGid: userStore.vehSaleEmp?.gid || 0,
} }), {
  immediate: false,
})

// 清除草稿并更新
async function clearAndUpdate() {
  await clearDraft()
  await create()
  await getDraftInfo()
}

// 在组件挂载时获取状态栏高度和初始化数据
onMounted(async () => {
  // 先获取draftInfo
  await Promise.all([
    getDraftInfo(),
    getCategoryList(),
  ])
  if (draftInfo.value.data && draftInfo.value.data.wrh) {
    skuQueryParams.wrhGid = draftInfo.value.data.wrh.gid
  }
  // 然后获取分类数据
  if (!skuQueryParams.wrhGid) {
    pickeEditHeaderRef.value?.openSelect()
  }
})

// 购物车品项数量
const skuCount = computed(() => {
  return draftInfo.value?.data?.skuCount || 0
})

// 购物车总金额
const total = computed(() => {
  return draftInfo.value?.data?.total || 0
})

// 改变商品数量
async function handleSpecChange(spec: VehSaleGetDraftQpcResponseDTO, sku: VehSaleGetDraftGoodsResponseDTO) {
  const index = skuList.value.findIndex(item => item.goods.gid === sku.goods.gid)
  const oldSku = CommonUtil.deepClone(skuList.value[index])
  skuList.value[index] = CommonUtil.deepClone(sku)
  const verifyIndex = verifySubmitData.value && verifySubmitData.value.data && verifySubmitData.value.data.length > 0 ? verifySubmitData.value.data.findIndex(item => item.goods.gid === sku.goods.gid) : -1
  if (verifyIndex !== -1) {
    verifySubmitData.value.data![verifyIndex] = sku
  }

  try {
    const result = await submitDraft({
      gdCode: sku.goods.gdCode!,
      goods: {
        gid: sku.goods.gid,
        name: sku.goods.name,
        code: sku.goods.code,
      },
      munit: spec.munit,
      price: spec.price,
      qpc: spec.qpc,
      qpcStr: spec.qpcStr,
      qty: spec.qty,
      qtyStr: spec.qtyStr,
      singlePrice: spec.singlePrice,
      total: spec.singlePrice.multiply(spec.qty).scale(2),
      uuid: spec.uuid,
      version: sku.version,
      wrh: {
        gid: skuQueryParams.wrhGid!,
      },
    })
    skuList.value[index].version = result.data?.version || skuList.value[index].version
    if (verifyIndex !== -1) {
      verifySubmitData.value.data![verifyIndex].version = result.data?.version || verifySubmitData.value.data![verifyIndex].version
    }
  }
  catch (error) {
    console.log(error)
    skuList.value[index] = oldSku
    if (verifyIndex !== -1) {
      verifySubmitData.value.data![verifyIndex] = oldSku
    }
  }
}

// 前往购物车页面
function goToCart() {
  // 检查购物车是否为空
  if (skuCount.value === 0) {
    globalToast.info('购物车还是空的')
    return
  }
  router.push({ name: 'pick-cart', params: {
    wrhGid: `${skuQueryParams.wrhGid}`,
  } })
}

// 提交订单请求
const { send: submit } = useRequest(
  (data: VehSaleUseSignSubmitRequestDTO) => Apis.vehsaleusesignInterface.submitUsingPOST_4({
    data,
  }),
  {
    immediate: false,
    middleware: createGlobalLoadingMiddleware({
      loadingText: '提交中...',
    }),
  },
).onSuccess((resp) => {
  globalLoading.close()
  emitRefreshSalesmanHome()
  globalToast.success({
    msg: '提交成功',
    duration: 500,
    closed: () => {
      router.replace({
        name: 'pick-detail',
        params: {
          id: resp.data.data!,
        },
      })
    },
  })
}).onError((err) => {
  globalLoading.close()
  globalToast.error(err.error?.message || '提交失败')
})

/**
 * 实际提交订单逻辑
 */
async function doSubmit() {
  return new Promise<boolean>((resolve) => {
    const submitData = {
      vehSaleEmp: userStore.vehSaleEmp,
      wrh: {
        gid: skuQueryParams.wrhGid!,
      },
      attachDetails: [],
    }
    verifySubmit(submitData).then((result) => {
      if (!result || !result.data || !result.data.length) {
        submit(submitData)
        resolve(true)
      }
      else {
        resolve(false)
        globalLoading.close()
        globalToast.warning('部分商品库存不足，请调整数量!')
        draftSubmitVerifyRef.value?.open({
          beforeConfirm: async () => {
            const res = await doSubmit()
            return res
          },
        })
      }
    }).catch((err) => {
      globalLoading.close()
      globalToast.error(err.error?.message || '校验失败')
      resolve(false)
    })
  })
}

/**
 * 提交订单
 */
async function handleSubmit() {
  return new Promise<boolean>((resolve) => {
    // 先校验购物车是否为空
    if (skuCount.value === 0) {
      globalToast.info('请先添加商品')
      resolve(false)
      return
    }

    // 购物车不为空，弹出二次确认
    showConfirm({
      title: '确认提交',
      msg: '确认提交本次领货',
      confirmButtonText: '确认',
      cancelButtonText: '再想想',
      success: async (res) => {
        if (res.action === 'confirm') {
          const result = await doSubmit()
          resolve(result)
        }
        else {
          resolve(false)
        }
      },
    })
  })
}

// 跳转到搜索页面
function goToSearch() {
  router.push({ name: 'pick-edit-search', params: {
    wrhGid: `${skuQueryParams.wrhGid}`,
  } })
}

/**
 * 领货校验
 * 校验通过则跳转到领货页面
 * 校验失败则提示
 */
const { send: navigateToPick } = useRequest(Apis.vehsaleusesignInterface.verifyCreateUsingPOST_3({ params: {
  vehSaleEmpGid: userStore.vehSaleEmp?.gid || 0,
} }), {
  immediate: false,
  middleware: createGlobalLoadingMiddleware({
    loadingText: '校验中...',
    delay: 200,
  }),
}).onSuccess(() => {
  router.replace({ name: 'pick-edit' })
}).onError((err) => {
  globalLoading.close()
  globalToast.error(err.error.message || '校验失败')
})

// 复制订单
const { send: copy } = useRequest(
  (num: string) => Apis.vehsaleusesignInterface.copyUsingPOST({
    params: {
      num,
    },
  }),
  {
    immediate: false,
    middleware: createGlobalLoadingMiddleware({
      loadingText: '导入中...',
    }),
  },
).onSuccess(() => {
  globalLoading.close()
  globalToast.success({
    msg: '导入成功',
    duration: 500,
    closed: () => {
      navigateToPick()
    },
  })
}).onError((err) => {
  globalLoading.close()
  globalToast.error(err.error?.message || '导入失败')
})

// 打开导入弹框
function handleOpenImport() {
  pickEditImportRef.value?.open({
    wrhGid: skuQueryParams.wrhGid,
    beforeImport: (_record: VehSaleUseSignQueryResponseDTO) => {
      return new Promise<boolean>((resolve) => {
        // 检查购物车是否为空
        if (skuCount.value > 0) {
          // 购物车不为空时，弹出二次确认弹窗
          showConfirm({
            title: '确认快速建单',
            msg: '购物车中存在未提交的领货商品，快速建单将全部替换商品，请确认再来一单',
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            success: (res) => {
              resolve(res.action === 'confirm')
            },
          })
        }
        else {
          // 购物车为空时，直接允许导入
          resolve(true)
        }
      })
    },
  })
}

// 导入领货单
function handleImport(record: VehSaleUseSignQueryResponseDTO) {
  copy(record.num!)
}
</script>

<template>
  <DraftSubmitVerify ref="draftSubmitVerifyRef" title="请确认商品数量" @opened="lockPage" @closed="unlockPage" @confirm="reload" @cancel="reload">
    <template #body>
      <view class="mx-3 mb-3 box-border flex items-center rounded-2 bg-[var(--frequentapplication-orange-background)] px-20rpx py-2 text-26rpx text-[var(--frequentapplication-orange-content)]">
        以下商品库存不足，已自动更新为最大可领货数
      </view>
      <template v-if="verifySubmitData && verifySubmitData.data">
        <PickEditSkuCard
          v-for="sku in verifySubmitData.data"
          :key="sku.goods.gid"
          :sku="sku"
          @change="handleSpecChange"
        />
      </template>
    </template>
  </DraftSubmitVerify>
  <PickEditImport ref="pickEditImportRef" @import="handleImport" @opened="lockPage" @closed="unlockPage" />
  <view class="pick-edit-page relative box-border min-h-screen w-screen overflow-x-hidden" :style="pageStyle">
    <!-- 自定义导航栏 -->
    <CartNavBar mode="common">
      <template #title>
        <view class="absolute left-50% top-50% w-full flex transform items-center justify-center -translate-x-1/2 -translate-y-1/2">
          <view class="inline-flex items-center justify-center w-auto!" @click="goToSearch">
            <text class="text-lg text-black font-medium">
              全部商品
            </text>
            <text class="i-carbon-search ml-2 text-4 text-black" />
          </view>
        </view>
      </template>
    </CartNavBar>

    <!-- 历史导入栏 -->
    <PickeEditHeader ref="pickeEditHeaderRef" v-model:current-wrh="skuQueryParams.wrhGid" :clear-and-update="clearAndUpdate" custom-class="fixed z-3" custom-style="top: var(--navbar-total-height)" @change="handleRefresh" @import-recent="handleOpenImport" />

    <!-- 骨架屏 -->
    <PickEditSkeleton v-if="loading" custom-class="top-[var(--navbar-total-height)]" />
    <view v-else class="main relative z-1 w-screen bg-white">
      <!-- 分类tabs -->
      <view class="fixed z-2 w-full" style="top: calc(var(--navbar-total-height) + 74rpx )">
        <wd-tabs v-model="skuQueryParams.sortCode" custom-class="w-full tabs-custom" @change="handleRefresh">
          <wd-tab
            v-for="(category, index) in categoryList"
            :key="index"
            :title="category.label || ''"
            :name="category.value"
            :badge-props="category.count ? { isDot: true } : undefined"
          />
        </wd-tabs>
        <!-- 头部区域 -->
        <view class="box-border w-full pl-3">
          <EditSort v-model:sorts="skuQueryParams.sorts" @change="handleRefresh" />
        </view>
      </view>

      <!-- 主内容区域 -->
      <view class="box-border flex flex-auto flex-col bg-white">
        <wd-gap height="calc(74rpx + 42px + 36px)" />

        <mescroll-body
          v-if="skuQueryParams.wrhGid && !categoryLoading"
          :down="downOption"
          :up="upOption"
          @init="mescrollInit"
          @down="downCallback"
          @up="upCallback"
        >
          <view class="p-3 pb-0">
            <SkuListSkeleton v-if="!loading && skuPage === 1 && skuListLoading" />
            <template v-else>
              <PickEditSkuCard
                v-for="sku in skuList"
                :key="sku.goods.gid"
                :sku="sku"
                @change="handleSpecChange"
              />
            </template>
          </view>
        </mescroll-body>
      </view>
    </view>
    <!-- 底部购物车 -->
    <CartBar
      :count="skuCount"
      :amount="total"
      safe-area-bottom fixed
      button-text="提交"
      @click-cart="goToCart"
      @click-button="handleSubmit"
    />
  </view>
</template>

<style lang="scss" scoped>
.pick-edit-page {
  min-height: 100vh;
  padding-bottom: 120rpx;
  padding-bottom: calc(120rpx + const(safe-area-inset-bottom)) !important;
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom)) !important;

   :deep(.mescroll-body) {
    min-height: calc(100vh - var(--navbar-total-height) - 74rpx - 36px - 42px - 120rpx) !important;
    min-height: calc(100vh - var(--navbar-total-height) - 74rpx - 36px - 42px - 120rpx - const(safe-area-inset-bottom)) !important;
    min-height: calc(100vh - var(--navbar-total-height) - 74rpx - 36px - 42px - 120rpx - env(safe-area-inset-bottom)) !important;
  }

  // tabs样式
  :deep(.tabs-custom){
    // 修复徽标导致文字溢出的问题
    .wd-tabs__nav-item-badge {
      display: flex;
      max-width: 100%;
      min-width: 0;

      .wd-badge {
        display: flex;
        max-width: 100%;
        min-width: 0;

        .wd-tabs__nav-item-text {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          flex: 1;
          min-width: 0;
        }
      }
    }
  }
}
</style>

<route lang="json">
{
  "name": "pick-edit",
  "style": {
    "navigationStyle": "custom",
    "navigationBarTitleText": "全部商品",
    "backgroundColor": "#F9F9F9"
  },
  "meta": {
    "permissions": ["vehSaleUseSignCreate"]
  }
}
</route>
