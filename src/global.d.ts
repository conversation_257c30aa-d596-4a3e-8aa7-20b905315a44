/*
 * @Author: we<PERSON><PERSON>
 * @Date: 2025-06-03 16:12:09
 * @LastEditTime: 2025-06-09 20:14:42
 * @LastEditors: weisheng
 * @Description:
 * @FilePath: /lsym-cx-mini/src/global.d.ts
 * 记得注释
 */
interface Number {
  scale: (n: number) => number
  floorScale: (n: number) => number
  /**
   * 加法
   */
  add: (n: number) => number
  /**
   * 乘法
   */
  multiply: (n: number) => number
  /**
   * 除法
   */
  divide: (n: number) => number
  /**
   * 减法
   */
  minus: (n: number) => number
}
