/*
 * @Author: we<PERSON><PERSON>
 * @Date: 2025-05-09 14:26:07
 * @LastEditTime: 2025-05-12 15:43:22
 * @LastEditors: weisheng
 * @Description:
 * @FilePath: /lsym-cx-mini/src/utils/date.ts
 * 记得注释
 */
import { isNumber, isString } from 'wot-design-uni/components/common/util'

/**
 * 日期格式化工具函数
 * 1. 如时间为今天则显示"今天"
 * 2. 如时间为昨天则仅展示"昨天"
 * 3. 其他则时间则展示，xx年X月X日
 * @param dateStr 日期字符串或Date对象
 * @returns 格式化后的日期字符串
 */
export function formatDate(dateStr: string | Date | null | undefined): string {
  if (!dateStr)
    return ''

  // 将输入转换为Date对象  iOS不支持yyyy-MM-dd 格式，需要转换为yyyy/MM/dd
  const date = isString(dateStr) ? new Date(dateStr.replace(/-/g, '/')) : dateStr

  // 检查日期是否有效
  if (Number.isNaN(date.getTime()))
    return ''

  // 今天的日期（不含时分秒）
  const today = new Date()
  today.setHours(0, 0, 0, 0)

  // 昨天的日期
  const yesterday = new Date(today)
  yesterday.setDate(yesterday.getDate() - 1)

  // 目标日期（不含时分秒）
  const targetDate = new Date(date)
  targetDate.setHours(0, 0, 0, 0)

  // 比较日期
  if (targetDate.getTime() === today.getTime()) {
    return '今天'
  }
  else if (targetDate.getTime() === yesterday.getTime()) {
    return '昨天'
  }
  else {
    const year = targetDate.getFullYear()
    const month = targetDate.getMonth() + 1
    const day = targetDate.getDate()
    return `${year}年${month}月${day}日`
  }
}

export function fullDate(dateStr: string | number | Date | null | undefined): string {
  if (!dateStr)
    return ''

  const date = isString(dateStr) ? new Date(dateStr.replace(/-/g, '/')) : (isNumber(dateStr) ? new Date(dateStr) : dateStr)
  // 返回年月日
  return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`
}

export default {
  formatDate,
  fullDate,
}
