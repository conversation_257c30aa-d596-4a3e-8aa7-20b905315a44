import empty from '@/static/icon/ic_empty.svg'

// mescroll配置
export const downOption = {
  auto: false, // 不自动加载
}

export const upOption = {
  auto: true, // 自动加载
  page: {
    num: 0, // 当前页码,默认0,回调之前会加1,即callback(1)
    size: 10, // 每页数据的数量
  },
  noMoreSize: 5, // 如果列表已无数据,可设置列表的总数量要大于等于5条才显示无更多数据
  empty: {
    tip: '暂无相关数据', // 提示
    icon: empty,
  },
  textNoMore: '-- 没有更多了 --', // 没有更多数据的提示文本
  toTop: {
    right: 20,
    bottom: 160,
    safearea: true,
  },
}
