# FontSize 指令测试文档

## 📝 功能说明

支持在打印文本中使用 `FontSize(大小,字体,样式)` 和 `EndFont` 指令来控制字体大小。

### 指令格式
- **FontSize指令**: `FontSize(10,微软雅黑,1)`
  - 参数1: 字体大小（数字）
  - 参数2: 字体名称（字符串）
  - 参数3: 字体样式（数字）
    - 0: 平体（正常）
    - 1: 加粗
    - 2: 斜体（热敏打印机用下划线代替）

- **EndFont指令**: `EndFont`
  - 恢复默认字体大小和样式

## 🧪 测试用例

### 测试用例1：基本FontSize指令
```
FontSize(10,微软雅黑,1)
车销领货单号:00012506110008
EndFont
业务员:车销业务员78901
```

**预期效果**：
- "车销领货单号:00012506110008" 使用10号加粗字体
- "业务员:车销业务员78901" 使用默认字体

### 测试用例2：多行FontSize作用域
```
FontSize(14,微软雅黑,1)
-----------车销领货-------------
  货  号  数  量
07890001      24
07890003       6
EndFont
结束!
```

**预期效果**：
- FontSize和EndFont之间的所有行都使用14号加粗字体
- "结束!" 使用默认字体

### 测试用例3：嵌套FontSize指令
```
FontSize(12,微软雅黑,1)
标题行
FontSize(16,微软雅黑,1)
重要信息
EndFont
普通信息
EndFont
结束行
```

**预期效果**：
- "标题行" 使用12号字体
- "重要信息" 使用16号字体（覆盖前面的设置）
- "普通信息" 恢复默认字体
- "结束行" 使用默认字体

### 测试用例4：字体样式测试
```
FontSize(12,微软雅黑,0)
正常文本
EndFont
FontSize(12,微软雅黑,1)
加粗文本
EndFont
FontSize(12,微软雅黑,2)
斜体文本（下划线）
EndFont
普通文本
```

**预期效果**：
- "正常文本" 使用12号正常字体
- "加粗文本" 使用12号加粗字体
- "斜体文本（下划线）" 使用12号下划线字体
- "普通文本" 使用默认字体

### 测试用例5：同行混合指令
```
FontSize(10,微软雅黑,1)车销领货单号:00012506110008
业务员:车销业务员78901EndFont
其他信息
```

**预期效果**：
- "车销领货单号:00012506110008" 和 "业务员:车销业务员78901" 使用10号加粗字体
- "其他信息" 使用默认字体

## 🔧 字体大小映射

| 字体大小范围 | ESC/POS指令 | 效果 |
|-------------|-------------|------|
| ≤ 8 | FONT_NORMAL | 正常字体 |
| 9-12 | FONT_NORMAL | 正常字体 |
| 13-16 | FONT_LARGE | 倍高字体 |
| > 16 | FONT_LARGE_WIDE | 倍高倍宽字体 |

## 🎨 字体样式映射

| 样式值 | 样式名称 | ESC/POS指令 | 效果 |
|--------|----------|-------------|------|
| 0 | 平体 | 无 | 正常显示 |
| 1 | 加粗 | BOLD_ON/BOLD_OFF | 粗体显示 |
| 2 | 斜体 | UNDERLINE_ON/UNDERLINE_OFF | 下划线显示（代替斜体） |

## 📊 实际测试

### 测试代码示例
```typescript
import { PrintCommandGenerator } from '@/utils/bluetooth/PrintCommandGenerator'

const generator = new PrintCommandGenerator()

// 测试文本
const testText = `FontSize(10,微软雅黑,1)
车销领货单号:00012506110008
EndFont
FontSize(12,微软雅黑,0)
业务员:车销业务员78901
EndFont
FontSize(14,微软雅黑,2)
-----------车销领货-------------
  货  号  数  量
07890001      24
07890003       6
EndFont
结束!`

// 生成打印指令
const commands = generator.generateTextCommands(testText)

console.log('生成的打印指令长度:', commands.length)
console.log('指令内容:', Array.from(commands).map(b => b.toString(16)).join(' '))
```

### 预期日志输出
```
📝 解析FontSize指令: 大小=10, 字体=微软雅黑, 样式=1
📝 遇到EndFont指令，恢复默认字体和样式
📝 解析FontSize指令: 大小=12, 字体=微软雅黑, 样式=0
📝 遇到EndFont指令，恢复默认字体和样式
📝 解析FontSize指令: 大小=14, 字体=微软雅黑, 样式=2
⚠️ 斜体样式在热敏打印机上可能不支持，使用下划线代替
📝 遇到EndFont指令，恢复默认字体和样式
```

## 🔍 调试信息

### 解析过程
1. **扫描每一行文本**
2. **检测FontSize指令**：使用正则表达式 `/FontSize\((\d+),([^,]+),(\d+)\)/`
3. **检测EndFont指令**：使用正则表达式 `/EndFont/`
4. **状态管理**：
   - `currentFontSize`: 当前字体大小
   - `currentFontStyle`: 当前字体样式
   - `inFontSizeBlock`: 是否在FontSize作用域内
5. **指令生成**：根据字体大小和样式生成对应的ESC/POS指令

### 状态转换图
```
[默认状态] --FontSize--> [字体设置状态] --EndFont--> [默认状态]
     ↑                        ↓
     └────────── 文本结束 ──────┘
```

## ⚠️ 注意事项

### 1. 指令格式要求
- FontSize指令必须严格按照格式：`FontSize(数字,字符串,数字)`
- 参数之间用英文逗号分隔
- 不能有多余的空格

### 2. 作用域规则
- FontSize指令影响后续所有行，直到遇到EndFont
- 如果没有EndFont，字体设置会持续到文本结束
- 嵌套的FontSize指令会覆盖前面的设置

### 3. 兼容性
- 支持中文字体名称
- 字体名称参数暂时不影响实际打印效果（热敏打印机限制）
- 字体样式支持：0-正常，1-加粗，2-斜体（用下划线代替）
- 热敏打印机通常不支持真正的斜体，使用下划线效果代替

### 4. 错误处理
- 格式错误的指令会被忽略
- 无效的字体大小会使用默认映射
- 编码错误会自动降级到UTF-8

## 🚀 使用示例

### 在ReliablePrinter中使用
```typescript
const printer = new ReliablePrinter(options)

// 带FontSize指令的文本
const textWithFontSize = `FontSize(14,微软雅黑,1)
重要通知
EndFont
普通内容`

// 打印
await printer.printText(textWithFontSize)
```

### 在print.vue中使用
```typescript
// 构建带FontSize指令的打印数据
const printData = `FontSize(12,微软雅黑,1)
${document.title}
EndFont
${document.content}`

// 添加到打印队列
await addPrintJobToQueue(printData)
```

## 📈 性能影响

### 解析开销
- 每行文本需要进行正则表达式匹配
- 状态管理增加少量内存开销
- 总体性能影响可忽略不计

### 指令优化
- 避免重复发送相同的字体设置指令
- 在FontSize作用域内，每行都会确保字体设置正确
- 自动在EndFont时恢复默认字体

## 🔧 扩展功能

### 未来可能的增强
1. **更多字体样式**：粗体、斜体、下划线
2. **颜色支持**：如果打印机支持
3. **对齐方式**：在FontSize指令中指定对齐
4. **行间距控制**：精确控制行间距

### 自定义字体映射
```typescript
// 可以扩展getFontSizeCommand方法
private getFontSizeCommand(fontSize: number): Uint8Array | null {
  if (fontSize <= 6) return this.FONT_SMALL
  if (fontSize <= 10) return this.FONT_NORMAL
  if (fontSize <= 14) return this.FONT_LARGE
  if (fontSize <= 18) return this.FONT_EXTRA_LARGE
  return this.FONT_LARGE_WIDE
}
```
