/**
 * 打印指令生成器
 * 职责：专门负责ESC/POS指令生成
 * - 文本打印指令（支持gbk编码）
 * - 图片打印指令（位图处理）
 * - 格式设置指令
 * - 控制指令（切纸、换行等）
 */

// 声明gbk编码库类型
declare const require: any

const gbk = require('../../static/gbk.min')

export interface TextPrintOptions {
  fontSize?: 'normal' | 'large' | 'small'
  align?: 'left' | 'center' | 'right'
  bold?: boolean
  underline?: boolean
  lineHeight?: number
}

export interface ImagePrintOptions {
  width?: number
  maxWidth?: number
  quality?: number
  dithering?: boolean
  useOriginalWidth?: boolean
}

export class PrintCommandGenerator {
  private canvas: any = null
  private ctx: any = null
  private readonly defaultMaxWidth = 576 // 80mm热敏打印机最大宽度

  constructor() {
    this.initGbkEncoder()
    this.initCanvas()
  }

  /**
   * 初始化gbk编码器
   */
  private initGbkEncoder(): void {
    try {
      console.log('📝 GBK编码库加载成功')
    }
    catch (error) {
      console.warn('⚠️ GBK编码库加载失败，文本打印可能出现乱码:', error)
      console.warn('请确保 gbk.min.js 文件位于 static 目录下')
    }
  }

  /**
   * 初始化离屏Canvas
   */
  private initCanvas(): void {
    try {
      // #ifdef MP-WEIXIN
      if (uni.createOffscreenCanvas) {
        this.canvas = uni.createOffscreenCanvas({ type: '2d' })
        if (this.canvas) {
          this.ctx = this.canvas.getContext('2d')
          console.log('🖼️ 离屏Canvas初始化成功')
        }
        else {
          console.error('创建离屏Canvas失败')
        }
      }
      else {
        console.error('当前环境不支持createOffscreenCanvas')
      }
      // #endif
    }
    catch (error) {
      console.error('初始化Canvas失败:', error)
    }
  }

  /**
   * ESC/POS指令常量
   */
  private static readonly Commands = {
    // 基础指令
    INIT: new Uint8Array([0x1B, 0x40]), // 初始化打印机
    LF: new Uint8Array([0x0A]), // 换行
    CR: new Uint8Array([0x0D]), // 回车
    CUT: new Uint8Array([0x1D, 0x56, 0x00]), // 切纸
    CUT_PARTIAL: new Uint8Array([0x1D, 0x56, 0x41, 0x00]), // 半切

    // 对齐指令
    ALIGN_LEFT: new Uint8Array([0x1B, 0x61, 0x00]),
    ALIGN_CENTER: new Uint8Array([0x1B, 0x61, 0x01]),
    ALIGN_RIGHT: new Uint8Array([0x1B, 0x61, 0x02]),

    // 字体大小指令
    FONT_NORMAL: new Uint8Array([0x1B, 0x21, 0x00]),
    FONT_LARGE: new Uint8Array([0x1B, 0x21, 0x10]), // 倍高
    FONT_WIDE: new Uint8Array([0x1B, 0x21, 0x20]), // 倍宽
    FONT_LARGE_WIDE: new Uint8Array([0x1B, 0x21, 0x30]), // 倍高倍宽

    // 样式指令
    BOLD_ON: new Uint8Array([0x1B, 0x45, 0x01]),
    BOLD_OFF: new Uint8Array([0x1B, 0x45, 0x00]),
    UNDERLINE_ON: new Uint8Array([0x1B, 0x2D, 0x01]),
    UNDERLINE_OFF: new Uint8Array([0x1B, 0x2D, 0x00]),

    // 行间距指令
    setLineSpacing: (n: number) => new Uint8Array([0x1B, 0x33, n]),

    // 心跳检测指令
    HEARTBEAT: new Uint8Array([0x10, 0x04, 0x01]),
  }

  /**
   * 生成文本打印指令（支持FontSize指令）
   */
  generateTextCommands(text: string, options: TextPrintOptions = {}): Uint8Array {
    const {
      fontSize = 'normal',
      align = 'left',
      bold = false,
      underline = false,
      lineHeight = 0,
    } = options

    const commands: Uint8Array[] = []

    // 初始化打印机
    commands.push(PrintCommandGenerator.Commands.INIT)

    // 设置默认对齐方式
    switch (align) {
      case 'center':
        commands.push(PrintCommandGenerator.Commands.ALIGN_CENTER)
        break
      case 'right':
        commands.push(PrintCommandGenerator.Commands.ALIGN_RIGHT)
        break
      default:
        commands.push(PrintCommandGenerator.Commands.ALIGN_LEFT)
        break
    }

    // 设置默认字体大小
    switch (fontSize) {
      case 'large':
        commands.push(PrintCommandGenerator.Commands.FONT_LARGE)
        break
      case 'small':
        commands.push(PrintCommandGenerator.Commands.FONT_NORMAL)
        break
      default:
        commands.push(PrintCommandGenerator.Commands.FONT_NORMAL)
        break
    }

    // 检查文本是否包含FontSize指令
    const hasFontSizeCommands = text.includes('FontSize(') || text.includes('EndFont')

    // 设置默认样式（如果没有FontSize指令，才应用全局样式）
    if (bold && !hasFontSizeCommands) {
      commands.push(PrintCommandGenerator.Commands.BOLD_ON)
    }
    if (underline && !hasFontSizeCommands) {
      commands.push(PrintCommandGenerator.Commands.UNDERLINE_ON)
    }

    // 设置行间距
    if (lineHeight > 0) {
      commands.push(PrintCommandGenerator.Commands.setLineSpacing(lineHeight))
    }

    // 解析并处理带有FontSize指令的文本
    const processedCommands = this.parseTextWithFontCommands(text, { bold, underline })
    commands.push(...processedCommands)

    // 取消样式（如果没有FontSize指令，才清除全局样式）
    if (underline && !hasFontSizeCommands) {
      commands.push(PrintCommandGenerator.Commands.UNDERLINE_OFF)
    }
    if (bold && !hasFontSizeCommands) {
      commands.push(PrintCommandGenerator.Commands.BOLD_OFF)
    }

    // 换行
    commands.push(PrintCommandGenerator.Commands.LF)

    // 合并所有指令
    return this.mergeCommands(commands)
  }

  /**
   * 解析带有FontSize指令的文本
   */
  private parseTextWithFontCommands(text: string, globalOptions: { bold?: boolean, underline?: boolean } = {}): Uint8Array[] {
    const commands: Uint8Array[] = []
    const lines = text.split('\n')
    let currentFontSize: number | null = null
    let currentFontStyle: number | null = null
    let inFontSizeBlock = false

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i]

      // 检查是否包含FontSize指令
      const fontSizeMatch = line.match(/FontSize\((\d+),([^,]+),(\d+)\)/)
      const endFontMatch = line.match(/EndFont/)

      if (fontSizeMatch) {
        // 解析FontSize指令参数
        const fontSize = Number.parseInt(fontSizeMatch[1])
        const fontName = fontSizeMatch[2]
        const fontStyle = Number.parseInt(fontSizeMatch[3])

        console.log(`📝 解析FontSize指令: 大小=${fontSize}, 字体=${fontName}, 样式=${fontStyle}`)

        // 记录当前字体大小、样式和状态
        currentFontSize = fontSize
        currentFontStyle = fontStyle
        inFontSizeBlock = true

        // 应用字体大小设置
        const fontCommand = this.getFontSizeCommand(fontSize)
        if (fontCommand) {
          commands.push(fontCommand)
        }

        // 应用字体样式设置
        const styleCommands = this.getFontStyleCommands(fontStyle)
        styleCommands.forEach(cmd => commands.push(cmd))

        // 移除FontSize指令，保留其他文本
        const cleanLine = line.replace(/FontSize\([^)]+\)/, '').trim()

        if (cleanLine) {
          console.log('cleanLine', cleanLine)
          const textBytes = this.encodeText(cleanLine)
          commands.push(textBytes)
        }
        // 如果指令后面只有空白内容，跳过换行符处理
        else {
          // 检查下一行是否为空行，如果是则跳过
          if (i + 1 < lines.length && lines[i + 1].trim() === '') {
            i++ // 跳过下一个空行
          }
        }
      }
      else if (endFontMatch) {
        // 遇到EndFont指令，恢复默认字体和样式
        console.log('📝 遇到EndFont指令，恢复默认字体和样式')

        // 清除字体样式
        if (currentFontStyle !== null) {
          const clearStyleCommands = this.clearFontStyleCommands(currentFontStyle)
          clearStyleCommands.forEach(cmd => commands.push(cmd))
        }

        // 恢复默认字体
        commands.push(PrintCommandGenerator.Commands.FONT_NORMAL)

        // 重置状态
        currentFontSize = null
        currentFontStyle = null
        inFontSizeBlock = false

        // 移除EndFont指令，保留其他文本
        const cleanLine = line.replace(/EndFont/, '').trim()
        if (cleanLine) {
          console.log('EndFont cleanLine', cleanLine)

          const textBytes = this.encodeText(cleanLine)
          commands.push(textBytes)
        }
        // 如果指令后面只有空白内容，跳过换行符处理
        else {
          // 检查下一行是否为空行，如果是则跳过
          if (i + 1 < lines.length && lines[i + 1].trim() === '') {
            i++ // 跳过下一个空行
          }
        }
      }
      else {
        // 普通文本行
        if (line.trim()) {
          // 如果当前在FontSize指令作用范围内，确保字体设置正确
          if (inFontSizeBlock && currentFontSize !== null) {
            const fontCommand = this.getFontSizeCommand(currentFontSize)
            if (fontCommand) {
              commands.push(fontCommand)
            }

            // 确保字体样式设置正确
            if (currentFontStyle !== null) {
              const styleCommands = this.getFontStyleCommands(currentFontStyle)
              styleCommands.forEach(cmd => commands.push(cmd))
            }
          }
          // 如果不在FontSize指令作用范围内，应用全局样式
          else {
            if (globalOptions.bold) {
              commands.push(PrintCommandGenerator.Commands.BOLD_ON)
            }
            if (globalOptions.underline) {
              commands.push(PrintCommandGenerator.Commands.UNDERLINE_ON)
            }
          }

          const textBytes = this.encodeText(line)
          commands.push(textBytes)

          // 如果应用了全局样式，在文本后清除
          if (!inFontSizeBlock) {
            if (globalOptions.underline) {
              commands.push(PrintCommandGenerator.Commands.UNDERLINE_OFF)
            }
            if (globalOptions.bold) {
              commands.push(PrintCommandGenerator.Commands.BOLD_OFF)
            }
          }
        }
      }

      // 添加换行符（除了最后一行）
      if (i < lines.length - 1) {
        commands.push(PrintCommandGenerator.Commands.LF)
      }
    }

    return commands
  }

  /**
   * 根据字体大小获取对应的ESC/POS指令
   */
  private getFontSizeCommand(fontSize: number): Uint8Array | null {
    // 根据字体大小映射到ESC/POS指令
    if (fontSize <= 8) {
      // 小字体
      return PrintCommandGenerator.Commands.FONT_NORMAL
    }
    else if (fontSize <= 12) {
      // 正常字体
      return PrintCommandGenerator.Commands.FONT_NORMAL
    }
    else if (fontSize <= 16) {
      // 大字体（倍高）
      return PrintCommandGenerator.Commands.FONT_LARGE
    }
    else {
      // 超大字体（倍高倍宽）
      return PrintCommandGenerator.Commands.FONT_LARGE_WIDE
    }
  }

  /**
   * 根据字体样式获取对应的ESC/POS指令
   * @param fontStyle 字体样式：0-平体，1-加粗，2-斜体，4-下划线
   */
  private getFontStyleCommands(fontStyle: number): Uint8Array[] {
    const commands: Uint8Array[] = []

    // 样式可以组合，使用位操作判断
    if (fontStyle & 1) {
      // 加粗
      commands.push(PrintCommandGenerator.Commands.BOLD_ON)
    }

    if (fontStyle & 2) {
      // 斜体 - 热敏打印机不支持真正的斜体，使用下划线替代
      commands.push(PrintCommandGenerator.Commands.UNDERLINE_ON)
      console.log('📝 斜体样式使用下划线替代显示')
    }

    if (fontStyle & 4) {
      // 下划线
      commands.push(PrintCommandGenerator.Commands.UNDERLINE_ON)
    }

    if (fontStyle === 0) {
      // 平体（正常）- 不需要额外指令
    }

    return commands
  }

  /**
   * 清除字体样式的ESC/POS指令
   * @param fontStyle 要清除的字体样式
   */
  private clearFontStyleCommands(fontStyle: number): Uint8Array[] {
    const commands: Uint8Array[] = []

    // 样式可以组合，使用位操作判断
    if (fontStyle & 1) {
      // 清除加粗
      commands.push(PrintCommandGenerator.Commands.BOLD_OFF)
    }

    if (fontStyle & 2) {
      // 清除斜体（下划线替代）
      commands.push(PrintCommandGenerator.Commands.UNDERLINE_OFF)
    }

    if (fontStyle & 4) {
      // 清除下划线
      commands.push(PrintCommandGenerator.Commands.UNDERLINE_OFF)
    }

    return commands
  }

  /**
   * 文本编码处理（支持gbk）
   */
  private encodeText(text: string): Uint8Array {
    const lines = text.split('\n')
    const processedTextBytes: number[] = []

    lines.forEach((line, index) => {
      if (gbk && line.trim()) {
        try {
          // 使用gbk编码
          const lineEncoded = gbk.encode(line)
          processedTextBytes.push(...Array.from(lineEncoded as number[]))
        }
        catch (error) {
          console.warn('GBK编码失败，使用UTF-8:', error)
          const fallbackBytes = new TextEncoder().encode(line)
          processedTextBytes.push(...Array.from(fallbackBytes))
        }
      }
      else if (line.trim()) {
        // 使用UTF-8编码
        const fallbackBytes = new TextEncoder().encode(line)
        processedTextBytes.push(...Array.from(fallbackBytes))
      }

      // 添加换行符（除了最后一行）
      if (index < lines.length - 1) {
        processedTextBytes.push(0x0A) // LF
      }
    })

    return new Uint8Array(processedTextBytes)
  }

  /**
   * 生成图片打印指令
   */
  async generateImageCommands(imageUrl: string, options: ImagePrintOptions = {}): Promise<Uint8Array> {
    if (!this.canvas || !this.ctx) {
      throw new Error('Canvas未初始化，当前环境可能不支持离屏Canvas')
    }

    try {
      console.log('🖼️ 开始生成图片打印指令:', imageUrl)

      // 加载并处理图片
      const { width, height } = await this.loadImageToCanvas(imageUrl, options)

      // 转换为位图数据
      const bitmapData = this.canvasToMonochrome(width, height, options)

      // 生成打印指令
      const commands: Uint8Array[] = []

      // 初始化打印机
      commands.push(PrintCommandGenerator.Commands.INIT)

      // 设置行间距
      commands.push(PrintCommandGenerator.Commands.setLineSpacing(0))

      // 打印位图
      commands.push(this.generateBitmapCommand(width, height, bitmapData))

      // 换行
      commands.push(PrintCommandGenerator.Commands.LF)
      commands.push(PrintCommandGenerator.Commands.LF)

      console.log('🖼️ 图片打印指令生成完成')
      return this.mergeCommands(commands)
    }
    catch (error) {
      console.error('生成图片打印指令失败:', error)
      throw new Error(`生成图片打印指令失败: ${error}`)
    }
  }

  /**
   * 生成位图打印指令
   */
  private generateBitmapCommand(width: number, height: number, data: Uint8Array): Uint8Array {
    const w = Math.ceil(width / 8)
    const cmd = new Uint8Array(8 + data.length)
    cmd[0] = 0x1D // GS
    cmd[1] = 0x76 // v
    cmd[2] = 0x30 // 0
    cmd[3] = 0x00 // m = 0 (normal)
    cmd[4] = w & 0xFF // xL
    cmd[5] = (w >> 8) & 0xFF // xH
    cmd[6] = height & 0xFF // yL
    cmd[7] = (height >> 8) & 0xFF // yH
    cmd.set(data, 8)
    return cmd
  }

  /**
   * 加载网络图片并绘制到Canvas
   */
  private async loadImageToCanvas(imageUrl: string, options: ImagePrintOptions = {}): Promise<{ width: number, height: number }> {
    const {
      width,
      maxWidth = this.defaultMaxWidth,
      useOriginalWidth = true,
    } = options

    return new Promise((resolve, reject) => {
      // #ifdef MP-WEIXIN
      uni.downloadFile({
        url: imageUrl,
        success: (downloadRes) => {
          if (downloadRes.statusCode === 200) {
            uni.getImageInfo({
              src: downloadRes.tempFilePath,
              success: (imgInfo) => {
                try {
                  let targetWidth: number
                  let targetHeight: number

                  if (width) {
                    targetWidth = width
                    const scale = width / imgInfo.width
                    targetHeight = Math.floor(imgInfo.height * scale)
                  }
                  else if (useOriginalWidth) {
                    targetWidth = Math.min(imgInfo.width, maxWidth)
                    const scale = targetWidth / imgInfo.width
                    targetHeight = Math.floor(imgInfo.height * scale)
                  }
                  else {
                    targetWidth = 384 // 默认58mm宽度
                    const scale = targetWidth / imgInfo.width
                    targetHeight = Math.floor(imgInfo.height * scale)
                  }

                  console.log(`图片尺寸: ${imgInfo.width}x${imgInfo.height} -> ${targetWidth}x${targetHeight}`)

                  // 设置canvas尺寸
                  this.canvas.width = targetWidth
                  this.canvas.height = targetHeight

                  // 创建图片对象
                  const img = this.canvas.createImage()

                  img.onload = () => {
                    try {
                      // 清空canvas并设置白色背景
                      this.ctx.clearRect(0, 0, targetWidth, targetHeight)
                      this.ctx.fillStyle = '#FFFFFF'
                      this.ctx.fillRect(0, 0, targetWidth, targetHeight)

                      // 绘制图片
                      this.ctx.drawImage(img, 0, 0, targetWidth, targetHeight)

                      resolve({ width: targetWidth, height: targetHeight })
                    }
                    catch (error) {
                      reject(new Error(`绘制图片失败: ${error}`))
                    }
                  }

                  img.onerror = () => {
                    reject(new Error('图片加载失败'))
                  }

                  img.src = downloadRes.tempFilePath
                }
                catch (error) {
                  reject(new Error(`处理图片失败: ${error}`))
                }
              },
              fail: (error) => {
                reject(new Error(`获取图片信息失败: ${JSON.stringify(error)}`))
              },
            })
          }
          else {
            reject(new Error(`下载图片失败，状态码: ${downloadRes.statusCode}`))
          }
        },
        fail: (error) => {
          reject(new Error(`下载图片失败: ${JSON.stringify(error)}`))
        },
      })
      // #endif

      // #ifndef MP-WEIXIN
      reject(new Error('当前环境不支持离屏Canvas'))
      // #endif
    })
  }

  /**
   * 将Canvas图像数据转换为黑白位图
   */
  private canvasToMonochrome(width: number, height: number, options: ImagePrintOptions = {}): Uint8Array {
    const { dithering = true } = options

    try {
      const imageData = this.ctx.getImageData(0, 0, width, height)
      const data = imageData.data
      const threshold = 128
      const monoData: number[] = []

      if (dithering) {
        // Floyd-Steinberg抖动算法
        const errors: number[][] = Array(height).fill(null).map(() => Array(width).fill(0))

        for (let y = 0; y < height; y++) {
          for (let x = 0; x < width; x++) {
            const index = (y * width + x) * 4
            let gray = Math.round(0.299 * data[index] + 0.587 * data[index + 1] + 0.114 * data[index + 2])

            gray += errors[y][x]
            gray = Math.max(0, Math.min(255, gray))

            const pixel = gray < threshold ? 0 : 1
            monoData.push(pixel)

            const error = gray - (pixel === 0 ? 0 : 255)

            // 分散误差到相邻像素
            if (x + 1 < width)
              errors[y][x + 1] += error * 7 / 16
            if (y + 1 < height) {
              if (x > 0)
                errors[y + 1][x - 1] += error * 3 / 16
              errors[y + 1][x] += error * 5 / 16
              if (x + 1 < width)
                errors[y + 1][x + 1] += error * 1 / 16
            }
          }
        }
      }
      else {
        // 简单阈值处理
        for (let i = 0; i < data.length; i += 4) {
          const gray = Math.round(0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2])
          monoData.push(gray < threshold ? 0 : 1)
        }
      }

      // 将像素数据打包为字节
      const bytesPerRow = Math.ceil(width / 8)
      const bitmapData = new Uint8Array(bytesPerRow * height)

      for (let y = 0; y < height; y++) {
        for (let x = 0; x < width; x++) {
          const pixelIndex = y * width + x
          const byteIndex = y * bytesPerRow + Math.floor(x / 8)
          const bitIndex = 7 - (x % 8)

          if (monoData[pixelIndex] === 0) {
            bitmapData[byteIndex] |= (1 << bitIndex)
          }
        }
      }

      console.log(`位图数据生成完成: ${width}x${height}, ${bitmapData.length} bytes`)
      return bitmapData
    }
    catch (error) {
      throw new Error(`图像数据处理失败: ${error}`)
    }
  }

  /**
   * 生成心跳检测指令
   */
  generateHeartbeatCommand(): Uint8Array {
    return PrintCommandGenerator.Commands.HEARTBEAT
  }

  /**
   * 生成切纸指令
   */
  generateCutCommand(partial: boolean = false): Uint8Array {
    return partial
      ? PrintCommandGenerator.Commands.CUT_PARTIAL
      : PrintCommandGenerator.Commands.CUT
  }

  /**
   * 生成初始化指令
   */
  generateInitCommand(): Uint8Array {
    return PrintCommandGenerator.Commands.INIT
  }

  /**
   * 合并指令数组
   */
  private mergeCommands(commands: Uint8Array[]): Uint8Array {
    const totalLength = commands.reduce((sum, cmd) => sum + cmd.length, 0)
    const result = new Uint8Array(totalLength)
    let offset = 0

    for (const cmd of commands) {
      result.set(cmd, offset)
      offset += cmd.length
    }

    return result
  }

  /**
   * 检查gbk编码器是否可用
   */
  isGbkAvailable(): boolean {
    return gbk !== null
  }

  /**
   * 检查Canvas是否可用
   */
  isCanvasAvailable(): boolean {
    return this.canvas !== null && this.ctx !== null
  }

  /**
   * 释放资源
   */
  dispose(): void {
    this.canvas = null
    this.ctx = null
  }
}
