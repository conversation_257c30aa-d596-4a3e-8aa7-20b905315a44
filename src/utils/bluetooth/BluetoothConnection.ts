/**
 * 蓝牙连接管理器
 * 职责：纯粹的蓝牙连接管理，不涉及业务逻辑
 * - 设备搜索和连接
 * - 数据传输
 * - 连接状态管理
 * - MTU优化
 */

export interface BluetoothDevice {
  deviceId: string
  name: string
  RSSI: number
  advertisData: any
  advertisServiceUUIDs: string[]
  localName: string
  serviceData: any
  checked?: boolean
}

export interface BluetoothConnectionOptions {
  onDeviceFound?: (device: BluetoothDevice) => void
  onConnected?: (deviceId: string) => void
  onDisconnected?: (deviceId: string) => void
  onError?: (error: any) => void
}

export class BluetoothConnection {
  private currentDevice: BluetoothDevice | null = null
  private isConnected = false
  private isScanning = false
  private serviceId = ''
  private characteristicId = ''
  private options: BluetoothConnectionOptions
  private sendQueue: Uint8Array[] = []
  private isSending = false
  private MAX_CHUNK_SIZE = 20
  private readonly SEND_DELAY = 100
  private currentMTU = 23
  private bluetoothAdapterFailed = false

  // 常见打印机服务UUID
  private readonly PRINTER_SERVICE_UUIDS = [
    '18F0',
    '49535343-FE7D-4AE5-8FA9-9FAFD205E455',
    'E7810A71-73AE-499D-8C15-FAA9AEF0C3F2',
  ]

  // 写入特征UUID
  private readonly WRITE_CHARACTERISTIC_UUIDS = [
    '2AF1',
    '*************-43F4-A8D4-ECBE34729BB3',
    'BEF8D6C9-9C21-4C9E-B632-BD58C1009F9F',
  ]

  constructor(options: BluetoothConnectionOptions = {}) {
    this.options = options
  }

  /**
   * 初始化蓝牙适配器
   */
  async initBluetooth(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!uni.openBluetoothAdapter) {
        this.bluetoothAdapterFailed = true
        reject(new Error('当前微信版本过低，无法使用该功能，请升级到最新微信版本后重试'))
        return
      }

      uni.openBluetoothAdapter({
        mode: 'central',
        success: () => {
          console.log('🔷 蓝牙适配器初始化成功')
          this.bluetoothAdapterFailed = false
          resolve()
        },
        fail: (error) => {
          console.error('🔷 蓝牙适配器初始化失败:', error)
          this.bluetoothAdapterFailed = true
          const errorMessage = this.getBluetoothErrorMessage(error.errCode)
          reject(new Error(errorMessage))
        },
      })
    })
  }

  /**
   * 根据错误码获取对应的错误信息
   */
  private getBluetoothErrorMessage(errCode: number): string {
    switch (errCode) {
      case 0:
        return '操作成功'
      case -1:
        return '蓝牙适配器已连接'
      case 10000:
        return '蓝牙适配器未初始化，请重试'
      case 10001:
        return '当前蓝牙适配器不可用，请检查手机蓝牙是否开启'
      case 10002:
        return '没有找到指定的蓝牙设备'
      case 10003:
        return '蓝牙连接失败，请重试'
      case 10004:
        return '没有找到指定的蓝牙服务'
      case 10005:
        return '没有找到指定的蓝牙特征值'
      case 10006:
        return '当前蓝牙连接已断开'
      case 10007:
        return '当前蓝牙特征不支持此操作'
      case 10008:
        return '蓝牙系统异常，请重启应用后重试'
      case 10009:
        return 'Android系统版本过低，不支持蓝牙BLE功能'
      case 10012:
        return '蓝牙连接超时，请重试'
      case 10013:
        return '设备ID格式不正确'
      default:
        return `蓝牙操作失败 (${errCode})，请确保已开启手机蓝牙，且已开启使用权限`
    }
  }

  /**
   * 获取蓝牙适配器状态
   */
  async getBluetoothState(): Promise<UniApp.GetBluetoothAdapterStateSuccess> {
    return new Promise((resolve, reject) => {
      uni.getBluetoothAdapterState({
        success: (res) => {
          if (res.available) {
            resolve(res)
          }
          else {
            reject(new Error('蓝牙不可用，请确保已开启手机蓝牙'))
          }
        },
        fail: (error) => {
          console.error('🔷 获取蓝牙状态失败:', error)
          const errorMessage = this.getBluetoothErrorMessage(error.errCode)
          reject(new Error(errorMessage))
        },
      })
    })
  }

  /**
   * 开始搜索蓝牙设备
   */
  async startScan(): Promise<BluetoothDevice[]> {
    if (this.bluetoothAdapterFailed) {
      throw new Error('蓝牙适配器初始化失败，无法进行搜索操作')
    }

    if (this.isScanning) {
      throw new Error('正在搜索中，请勿重复操作')
    }

    this.isScanning = true

    return new Promise((resolve, reject) => {
      const devices: BluetoothDevice[] = []

      uni.onBluetoothDeviceFound((res) => {
        res.devices.forEach((device) => {
          if (device.name && device.name !== '未知设备' && !devices.find(d => d.deviceId === device.deviceId)) {
            const bluetoothDevice: BluetoothDevice = {
              deviceId: device.deviceId,
              name: device.name,
              RSSI: device.RSSI,
              advertisData: device.advertisData,
              advertisServiceUUIDs: device.advertisServiceUUIDs || [],
              localName: device.localName || device.name,
              serviceData: device.serviceData,
              checked: false,
            }
            devices.push(bluetoothDevice)
            this.options.onDeviceFound?.(bluetoothDevice)
          }
        })
      })

      uni.startBluetoothDevicesDiscovery({
        allowDuplicatesKey: false,
        interval: 0,
        success: () => {
          console.log('🔍 开始搜索蓝牙设备')
          setTimeout(() => {
            this.stopScan()
            this.isScanning = false
            resolve(devices)
          }, 5000)
        },
        fail: (error) => {
          console.error('🔍 搜索蓝牙设备失败:', error)
          this.isScanning = false
          const errorMessage = this.getBluetoothErrorMessage(error.errCode)
          reject(new Error(errorMessage))
        },
      })
    })
  }

  /**
   * 停止搜索
   */
  stopScan(): void {
    uni.stopBluetoothDevicesDiscovery({
      success: () => {
        console.log('🔍 停止搜索蓝牙设备')
      },
    })
  }

  /**
   * 设置MTU
   */
  private async setBLEMTU(deviceId: string): Promise<void> {
    return new Promise((resolve) => {
      if (!uni.setBLEMTU) {
        console.log('📡 当前版本不支持 setBLEMTU，跳过 MTU 设置')
        resolve()
        return
      }

      const targetMTU = 512

      uni.setBLEMTU({
        deviceId,
        mtu: targetMTU,
        success: (res: any) => {
          const actualMTU = res.mtu || targetMTU
          this.currentMTU = actualMTU
          this.MAX_CHUNK_SIZE = Math.max(20, actualMTU - 3)
          console.log(`📡 MTU 设置成功: ${actualMTU} bytes, 包大小: ${this.MAX_CHUNK_SIZE} bytes`)
          resolve()
        },
        fail: (error: any) => {
          console.error('📡 MTU 设置失败，使用默认值:', error)
          // MTU设置失败不影响正常连接，所以仍然resolve
          resolve()
        },
      })
    })
  }

  /**
   * 连接蓝牙设备
   */
  async connectDevice(device: BluetoothDevice): Promise<void> {
    if (this.bluetoothAdapterFailed) {
      throw new Error('蓝牙适配器初始化失败，无法进行连接操作')
    }

    // 如果已连接同一设备，直接返回成功
    if (this.isConnected && this.currentDevice?.deviceId === device.deviceId) {
      console.log('🔗 设备已连接，无需重复连接:', device.name)
      return
    }

    // 如果连接了其他设备，先断开
    if (this.isConnected && this.currentDevice) {
      console.log('🔄 切换设备，断开当前连接:', this.currentDevice.name)
      await this.disconnect()
    }

    this.currentDevice = device

    return new Promise((resolve, reject) => {
      uni.createBLEConnection({
        deviceId: device.deviceId,
        success: async () => {
          console.log('🔗 设备连接成功:', device.deviceId)
          try {
            await this.setBLEMTU(device.deviceId)
            await this.getServices(device.deviceId)
            this.isConnected = true
            this.options.onConnected?.(device.deviceId)
            resolve()
          }
          catch (error) {
            reject(error)
          }
        },
        fail: (error) => {
          console.error('🔗 设备连接失败:', error)
          const errorMessage = this.getBluetoothErrorMessage(error.errCode)
          reject(new Error(errorMessage))
        },
      })

      // 监听连接状态变化
      uni.onBLEConnectionStateChange((res) => {
        if (res.deviceId === device.deviceId && !res.connected) {
          this.isConnected = false
          this.currentDevice = null
          this.options.onDisconnected?.(device.deviceId)
        }
      })
    })
  }

  /**
   * 获取设备服务
   */
  private async getServices(deviceId: string): Promise<void> {
    return new Promise((resolve, reject) => {
      uni.getBLEDeviceServices({
        deviceId,
        success: async (res) => {
          console.log('🔧 获取服务成功:', res.services.length)

          let targetService = null
          for (const service of res.services) {
            if (this.PRINTER_SERVICE_UUIDS.some(uuid =>
              service.uuid.toUpperCase().includes(uuid.toUpperCase()),
            )) {
              targetService = service
              break
            }
          }

          if (!targetService && res.services.length > 0) {
            targetService = res.services[0]
          }

          if (targetService) {
            this.serviceId = targetService.uuid
            try {
              await this.getCharacteristics(deviceId, this.serviceId)
              resolve()
            }
            catch (error) {
              reject(error)
            }
          }
          else {
            reject(new Error('未找到可用服务'))
          }
        },
        fail: (error) => {
          console.error('🔧 获取设备服务失败:', error)
          const errorMessage = this.getBluetoothErrorMessage(error.errCode)
          reject(new Error(errorMessage))
        },
      })
    })
  }

  /**
   * 获取特征值
   */
  private async getCharacteristics(deviceId: string, serviceId: string): Promise<void> {
    return new Promise((resolve, reject) => {
      uni.getBLEDeviceCharacteristics({
        deviceId,
        serviceId,
        success: (res) => {
          console.log('⚙️ 获取特征值成功:', res.characteristics.length)

          let writeCharacteristic = null
          for (const char of res.characteristics) {
            if (char.properties.write || char.properties.writeNoResponse) {
              if (this.WRITE_CHARACTERISTIC_UUIDS.some(uuid =>
                char.uuid.toUpperCase().includes(uuid.toUpperCase()),
              )) {
                writeCharacteristic = char
                break
              }
            }
          }

          if (!writeCharacteristic) {
            writeCharacteristic = res.characteristics.find(char =>
              char.properties.write || char.properties.writeNoResponse,
            )
          }

          if (writeCharacteristic) {
            this.characteristicId = writeCharacteristic.uuid
            resolve()
          }
          else {
            reject(new Error('未找到可写入特征'))
          }
        },
        fail: (error) => {
          console.error('⚙️ 获取设备特征失败:', error)
          const errorMessage = this.getBluetoothErrorMessage(error.errCode)
          reject(new Error(errorMessage))
        },
      })
    })
  }

  /**
   * 分包数据
   */
  private splitDataIntoChunks(data: ArrayBuffer): Uint8Array[] {
    const uint8Array = new Uint8Array(data)
    const chunks: Uint8Array[] = []

    for (let i = 0; i < uint8Array.length; i += this.MAX_CHUNK_SIZE) {
      const chunk = uint8Array.slice(i, i + this.MAX_CHUNK_SIZE)
      chunks.push(chunk)
    }

    return chunks
  }

  /**
   * 发送单个数据包
   */
  private async sendChunk(chunk: Uint8Array): Promise<void> {
    if (!this.isConnected || !this.currentDevice) {
      throw new Error('设备未连接')
    }

    return new Promise((resolve, reject) => {
      const buffer = chunk.buffer.slice(chunk.byteOffset, chunk.byteOffset + chunk.byteLength)

      uni.writeBLECharacteristicValue({
        deviceId: this.currentDevice!.deviceId,
        serviceId: this.serviceId,
        characteristicId: this.characteristicId,
        value: buffer as any,
        success: () => {
          resolve()
        },
        fail: (error) => {
          console.error('📤 数据包发送失败:', error)
          const errorMessage = this.getBluetoothErrorMessage(error.errCode)
          reject(new Error(errorMessage))
        },
      })
    })
  }

  /**
   * 处理发送队列
   */
  private async processSendQueue(): Promise<void> {
    if (this.isSending || this.sendQueue.length === 0) {
      return
    }

    this.isSending = true

    try {
      while (this.sendQueue.length > 0) {
        const chunk = this.sendQueue.shift()!
        await this.sendChunk(chunk)

        if (this.sendQueue.length > 0) {
          await new Promise(resolve => setTimeout(resolve, this.SEND_DELAY))
        }
      }
    }
    catch (error) {
      this.sendQueue = []
      throw error
    }
    finally {
      this.isSending = false
    }
  }

  /**
   * 发送数据
   */
  async sendData(data: ArrayBuffer): Promise<void> {
    if (this.bluetoothAdapterFailed) {
      throw new Error('蓝牙适配器初始化失败，无法发送数据')
    }

    if (!this.isConnected || !this.currentDevice) {
      throw new Error('设备未连接')
    }

    const chunks = this.splitDataIntoChunks(data)
    console.log(`📤 发送数据: ${data.byteLength} bytes，分为 ${chunks.length} 个包`)

    this.sendQueue.push(...chunks)
    await this.processSendQueue()
  }

  /**
   * 断开连接
   */
  async disconnect(): Promise<void> {
    if (!this.currentDevice) {
      return
    }

    this.sendQueue = []
    this.isSending = false

    return new Promise((resolve) => {
      uni.closeBLEConnection({
        deviceId: this.currentDevice!.deviceId,
        success: () => {
          console.log('🔌 设备连接已断开')
          this.isConnected = false
          this.currentDevice = null
          resolve()
        },
        fail: (error) => {
          console.error('🔌 断开连接失败:', error)
          // 无论如何都要清理状态
          this.isConnected = false
          this.currentDevice = null
          resolve()
        },
      })
    })
  }

  /**
   * 关闭蓝牙适配器
   */
  async closeBluetooth(): Promise<void> {
    return new Promise((resolve) => {
      uni.closeBluetoothAdapter({
        complete: () => {
          this.isConnected = false
          this.currentDevice = null
          this.sendQueue = []
          this.isSending = false
          this.bluetoothAdapterFailed = false
          resolve()
        },
      })
    })
  }

  /**
   * 获取当前连接的设备
   */
  getCurrentDevice(): BluetoothDevice | null {
    return this.currentDevice
  }

  /**
   * 检查连接状态
   */
  getConnectionStatus(): boolean {
    return this.isConnected
  }

  /**
   * 获取MTU信息
   */
  getMTUInfo(): { mtu: number, chunkSize: number } {
    return {
      mtu: this.currentMTU,
      chunkSize: this.MAX_CHUNK_SIZE,
    }
  }

  /**
   * 检查蓝牙适配器是否可用
   */
  isBluetoothAdapterAvailable(): boolean {
    return !this.bluetoothAdapterFailed
  }
}
