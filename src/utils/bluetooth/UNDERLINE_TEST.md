# 下划线功能测试文档

## 下划线功能说明

下划线功能现在支持两种使用方式：

### 1. 全局下划线选项
```typescript
await reliablePrinter.printText('这是带下划线的文本', {
  underline: true
})
```

### 2. FontSize指令中的下划线
```typescript
const textWithUnderline = `
FontSize(12,宋体,4)这是带下划线的文本EndFont
FontSize(16,宋体,5)这是加粗+下划线的文本EndFont
`
await reliablePrinter.printText(textWithUnderline)
```

## FontSize指令样式参数说明

FontSize指令的第三个参数（fontStyle）现在使用位操作：

- **0**: 普通样式
- **1**: 加粗（bold）
- **2**: 斜体（italic，热敏打印机使用下划线替代）
- **4**: 下划线（underline）
- **5**: 加粗+下划线（1+4）
- **6**: 斜体+下划线（2+4，都显示为下划线）
- **7**: 加粗+斜体+下划线（1+2+4，加粗+下划线）

## 测试用例

### 测试1：纯下划线
```
FontSize(12,宋体,4)下划线文本EndFont
```

### 测试2：加粗+下划线
```
FontSize(16,宋体,5)加粗下划线文本EndFont
```

### 测试2.5：斜体替代
```
FontSize(14,宋体,2)斜体文本（显示为下划线）EndFont
```

### 测试3：混合使用
```
普通文本
FontSize(14,宋体,4)下划线文本EndFont
FontSize(16,宋体,1)加粗文本EndFont
FontSize(14,宋体,2)斜体文本（显示为下划线）EndFont
FontSize(14,宋体,5)加粗下划线文本EndFont
FontSize(14,宋体,6)斜体下划线文本（都显示为下划线）EndFont
结束文本
```

### 测试4：全局下划线（不与FontSize指令混合）
```typescript
// 这种方式会给整个文本添加下划线
await reliablePrinter.printText('全部文本都有下划线', {
  underline: true
})
```

## 注意事项

1. **不要混合使用**：如果文本中包含FontSize指令，不要同时使用全局的underline选项，否则可能出现下划线重复或冲突。

2. **换行符处理**：FontSize和EndFont指令后的空行会被自动处理，避免多余的换行。

3. **样式组合**：FontSize指令支持样式组合，如加粗+下划线（fontStyle=5）。

4. **热敏打印机限制**：斜体样式在大多数热敏打印机上不支持，现在使用下划线替代显示。

## 修复的问题

1. **下划线不生效问题**：现在正确区分了全局下划线和FontSize指令中的下划线。
2. **换行符处理问题**：FontSize和EndFont指令后的空行会被正确处理。
3. **样式冲突问题**：避免了全局样式与FontSize指令样式的冲突。
