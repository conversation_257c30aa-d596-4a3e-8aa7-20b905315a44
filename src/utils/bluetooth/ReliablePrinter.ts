/**
 * 可靠打印管理器
 * 职责：高级打印管理和业务逻辑
 * - 长连接管理和自动重连
 * - 打印队列管理和任务调度
 * - 状态监听和错误恢复
 * - 心跳检测和连接保活
 * - 统计信息和性能监控
 */

import type { BluetoothDevice } from './BluetoothConnection'
import { BluetoothConnection } from './BluetoothConnection'
import type { ImagePrintOptions, TextPrintOptions } from './PrintCommandGenerator'
import { PrintCommandGenerator } from './PrintCommandGenerator'

export interface PrintJob {
  id: string
  type: 'text' | 'image'
  data: string
  options?: TextPrintOptions | ImagePrintOptions
  resolve: (value?: any) => void
  reject: (reason?: any) => void
  retryCount: number
  createdAt: number
}

export interface ReliablePrinterOptions {
  // 连接配置
  autoReconnect?: boolean
  maxReconnectAttempts?: number
  reconnectDelay?: number
  connectionTimeout?: number

  // 队列配置
  maxQueueSize?: number
  printTimeout?: number
  maxRetryAttempts?: number

  // 心跳配置
  heartbeatInterval?: number
  heartbeatTimeout?: number

  // 事件回调
  onConnectionStateChange?: (state: ConnectionState, error?: Error) => void
  onPrintJobStateChange?: (job: PrintJob, state: PrintJobState) => void
  onQueueStateChange?: (queueSize: number, processing: boolean) => void
  onError?: (error: Error) => void
}

export enum ConnectionState {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error',
}

export enum PrintJobState {
  QUEUED = 'queued',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

export class ReliablePrinter {
  private connection: BluetoothConnection
  private commandGenerator: PrintCommandGenerator
  private options: Required<ReliablePrinterOptions>

  // 连接状态管理
  private connectionState: ConnectionState = ConnectionState.DISCONNECTED
  private currentDevice: BluetoothDevice | undefined = undefined
  private reconnectAttempts = 0
  private reconnectTimer: number | null = null

  // 打印队列管理
  private printQueue: PrintJob[] = []
  private isProcessingQueue = false
  private currentJob: PrintJob | undefined = undefined

  // 心跳检测
  private heartbeatTimer: number | null = null
  private lastHeartbeatTime = 0

  // 统计信息
  private stats = {
    totalPrintJobs: 0,
    successfulPrintJobs: 0,
    failedPrintJobs: 0,
    reconnectCount: 0,
    lastConnectedTime: 0,
    lastDisconnectedTime: 0,
    averagePrintTime: 0,
  }

  constructor(options: ReliablePrinterOptions = {}) {
    this.options = {
      autoReconnect: true,
      maxReconnectAttempts: 5,
      reconnectDelay: 2000,
      connectionTimeout: 10000,
      maxQueueSize: 50,
      printTimeout: 30000,
      maxRetryAttempts: 3,
      heartbeatInterval: 30000,
      heartbeatTimeout: 5000,
      onConnectionStateChange: () => {},
      onPrintJobStateChange: () => {},
      onQueueStateChange: () => {},
      onError: () => {},
      ...options,
    }

    // 初始化连接层
    this.connection = new BluetoothConnection({
      onConnected: this.handleConnected.bind(this),
      onDisconnected: this.handleDisconnected.bind(this),
      onError: this.handleConnectionError.bind(this),
    })

    // 初始化指令生成器
    this.commandGenerator = new PrintCommandGenerator()

    console.log('🖨️ 可靠打印管理器初始化完成')
    console.log(`📄 GBK编码: ${this.commandGenerator.isGbkAvailable() ? '可用' : '不可用'}`)
    console.log(`🖼️ Canvas: ${this.commandGenerator.isCanvasAvailable() ? '可用' : '不可用'}`)
  }

  /**
   * 连接到指定设备
   */
  async connect(device: BluetoothDevice): Promise<void> {
    try {
      this.currentDevice = device
      this.setConnectionState(ConnectionState.CONNECTING)

      await this.connection.initBluetooth()
      await this.connection.getBluetoothState()
      await this.connection.connectDevice(device)

      console.log(`🔗 成功连接到设备: ${device.name}`)
    }
    catch (error: any) {
      console.error('🚨 连接失败:', error)

      // 检查是否是蓝牙适配器初始化失败
      if (error && error.message && (
        error.message.includes('蓝牙适配器')
        || error.message.includes('无法使用该功能')
        || error.message.includes('蓝牙不可用')
        || error.message.includes('蓝牙系统异常')
      )) {
        // 蓝牙适配器失败，执行彻底清理
        this.handleBluetoothAdapterFailure(error)
      }

      const connectionError = new Error(`连接失败: ${error.message}`)
      this.setConnectionState(ConnectionState.ERROR, connectionError)
      throw connectionError
    }
  }

  /**
   * 断开连接
   */
  async disconnect(): Promise<void> {
    try {
      this.stopHeartbeat()
      this.stopReconnect()
      this.clearQueue()

      await this.connection.disconnect()

      this.setConnectionState(ConnectionState.DISCONNECTED)
      this.currentDevice = undefined

      console.log('🔌 设备连接已断开')
    }
    catch (error: any) {
      console.error('断开连接失败:', error)
      throw error
    }
  }

  /**
   * 添加文本打印任务
   */
  async printText(text: string, options: TextPrintOptions = {}): Promise<void> {
    return this.addPrintJob('text', text, options)
  }

  /**
   * 添加图片打印任务
   */
  async printImage(imageUrl: string, options: ImagePrintOptions = {}): Promise<void> {
    return this.addPrintJob('image', imageUrl, options)
  }

  /**
   * 添加打印任务到队列
   */
  private async addPrintJob(type: 'text' | 'image', data: string, options?: any): Promise<void> {
    return new Promise((resolve, reject) => {
      // 检查蓝牙适配器是否可用
      if (!this.connection.isBluetoothAdapterAvailable()) {
        reject(new Error('蓝牙适配器不可用，无法添加打印任务'))
        return
      }

      // 检查连接状态是否为错误状态
      if (this.connectionState === ConnectionState.ERROR) {
        reject(new Error('打印机处于错误状态，无法添加打印任务'))
        return
      }

      if (this.printQueue.length >= this.options.maxQueueSize) {
        reject(new Error('打印队列已满，请稍后再试'))
        return
      }

      const job: PrintJob = {
        id: this.generateJobId(),
        type,
        data,
        options,
        resolve,
        reject,
        retryCount: 0,
        createdAt: Date.now(),
      }

      this.printQueue.push(job)
      this.stats.totalPrintJobs++

      this.options.onQueueStateChange(this.printQueue.length, this.isProcessingQueue)
      this.options.onPrintJobStateChange(job, PrintJobState.QUEUED)

      console.log(`📄 任务加入队列: ${job.id} (${job.type})`)

      this.processQueue()
    })
  }

  /**
   * 处理打印队列
   */
  private async processQueue(): Promise<void> {
    if (this.isProcessingQueue || this.printQueue.length === 0) {
      return
    }

    this.isProcessingQueue = true
    this.options.onQueueStateChange(this.printQueue.length, true)

    while (this.printQueue.length > 0) {
      const job = this.printQueue.shift()!
      this.currentJob = job

      try {
        await this.ensureConnected()
        await this.executePrintJob(job)
      }
      catch (error: any) {
        console.error(`打印任务失败: ${job.id}`, error)

        if (job.retryCount < this.options.maxRetryAttempts) {
          job.retryCount++
          this.printQueue.unshift(job)
          console.log(`🔄 任务重试: ${job.id} (第${job.retryCount}次)`)
          await this.delay(1000 * job.retryCount)
          continue
        }

        this.options.onPrintJobStateChange(job, PrintJobState.FAILED)
        job.reject(error)
        this.stats.failedPrintJobs++
      }

      this.currentJob = undefined
    }

    this.isProcessingQueue = false
    this.options.onQueueStateChange(0, false)
  }

  /**
   * 执行单个打印任务
   */
  private async executePrintJob(job: PrintJob): Promise<void> {
    const startTime = Date.now()

    try {
      this.options.onPrintJobStateChange(job, PrintJobState.PROCESSING)

      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => {
          reject(new Error(`打印任务超时: ${job.id}`))
        }, this.options.printTimeout)
      })

      const printPromise = this.performPrint(job)
      await Promise.race([printPromise, timeoutPromise])

      const duration = Date.now() - startTime
      this.updateAveragePrintTime(duration)

      this.options.onPrintJobStateChange(job, PrintJobState.COMPLETED)
      job.resolve()
      this.stats.successfulPrintJobs++

      console.log(`✅ 任务完成: ${job.id} (${duration}ms)`)
    }
    catch (error: any) {
      this.options.onPrintJobStateChange(job, PrintJobState.FAILED)
      throw error
    }
  }

  /**
   * 执行实际的打印操作
   */
  private async performPrint(job: PrintJob): Promise<void> {
    let commands: Uint8Array

    if (job.type === 'text') {
      commands = this.commandGenerator.generateTextCommands(job.data, job.options as TextPrintOptions)
    }
    else if (job.type === 'image') {
      commands = await this.commandGenerator.generateImageCommands(job.data, job.options as ImagePrintOptions)
    }
    else {
      throw new Error(`不支持的打印类型: ${job.type}`)
    }

    // 转换 ArrayBufferLike 为 ArrayBuffer
    let buffer: ArrayBuffer
    if (commands.buffer instanceof ArrayBuffer) {
      buffer = commands.buffer
    }
    else {
      // 复制 SharedArrayBuffer 到 ArrayBuffer
      buffer = new ArrayBuffer(commands.buffer.byteLength)
      new Uint8Array(buffer).set(new Uint8Array(commands.buffer))
    }

    await this.connection.sendData(buffer)
  }

  /**
   * 确保连接可用
   */
  private async ensureConnected(): Promise<void> {
    // 检查蓝牙适配器是否可用
    if (!this.connection.isBluetoothAdapterAvailable()) {
      throw new Error('蓝牙适配器不可用，无法建立连接')
    }

    if (this.connectionState === ConnectionState.CONNECTED) {
      return
    }

    if (this.connectionState === ConnectionState.CONNECTING
      || this.connectionState === ConnectionState.RECONNECTING) {
      await this.waitForConnection()
      return
    }

    if (!this.currentDevice) {
      throw new Error('未设置打印设备')
    }

    await this.startReconnect()
  }

  /**
   * 等待连接完成
   */
  private async waitForConnection(): Promise<void> {
    const startTime = Date.now()

    while (this.connectionState !== ConnectionState.CONNECTED
      && Date.now() - startTime < this.options.connectionTimeout) {
      await this.delay(100)
    }

    if (this.connectionState !== ConnectionState.CONNECTED) {
      throw new Error('连接超时')
    }
  }

  /**
   * 开始重连
   */
  private async startReconnect(): Promise<void> {
    if (!this.currentDevice || !this.options.autoReconnect) {
      throw new Error('无法重连')
    }

    this.setConnectionState(ConnectionState.RECONNECTING)
    this.stats.reconnectCount++

    for (let attempt = 1; attempt <= this.options.maxReconnectAttempts; attempt++) {
      try {
        console.log(`🔄 重连尝试 ${attempt}/${this.options.maxReconnectAttempts}`)

        // 检查蓝牙适配器是否可用
        if (!this.connection.isBluetoothAdapterAvailable()) {
          console.error('🚨 蓝牙适配器不可用，停止重连')
          this.handleBluetoothAdapterFailure(new Error('蓝牙适配器不可用'))
          return
        }

        await this.connection.connectDevice(this.currentDevice)
        return
      }
      catch (error: any) {
        console.error(`重连失败 (${attempt}/${this.options.maxReconnectAttempts}):`, error.message)

        // 检查是否是蓝牙适配器失败
        if (error && error.message && (
          error.message.includes('蓝牙适配器')
          || error.message.includes('无法使用该功能')
          || error.message.includes('蓝牙不可用')
          || error.message.includes('蓝牙系统异常')
        )) {
          console.error('🚨 蓝牙适配器失败，停止重连')
          this.handleBluetoothAdapterFailure(error)
          return
        }

        if (attempt < this.options.maxReconnectAttempts) {
          await this.delay(this.options.reconnectDelay * attempt)
        }
      }
    }

    const reconnectError = new Error('重连失败，已达到最大重试次数')
    this.setConnectionState(ConnectionState.ERROR, reconnectError)
    throw reconnectError
  }

  /**
   * 停止重连
   */
  private stopReconnect(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
  }

  /**
   * 开始心跳检测
   */
  private startHeartbeat(): void {
    this.stopHeartbeat()

    this.heartbeatTimer = setInterval(() => {
      this.performHeartbeat()
    }, this.options.heartbeatInterval) as unknown as number
  }

  /**
   * 停止心跳检测
   */
  private stopHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }

  /**
   * 执行心跳检测
   */
  private async performHeartbeat(): Promise<void> {
    try {
      // 检查蓝牙适配器是否可用
      if (!this.connection.isBluetoothAdapterAvailable()) {
        console.error('💓 蓝牙适配器不可用，停止心跳检测')
        this.handleBluetoothAdapterFailure(new Error('蓝牙适配器不可用'))
        return
      }

      const heartbeatCommand = this.commandGenerator.generateHeartbeatCommand()

      // 转换 ArrayBufferLike 为 ArrayBuffer
      let buffer: ArrayBuffer
      if (heartbeatCommand.buffer instanceof ArrayBuffer) {
        buffer = heartbeatCommand.buffer
      }
      else {
        buffer = new ArrayBuffer(heartbeatCommand.buffer.byteLength)
        new Uint8Array(buffer).set(new Uint8Array(heartbeatCommand.buffer))
      }

      await this.connection.sendData(buffer)
      this.lastHeartbeatTime = Date.now()
      console.log('💓 心跳正常')
    }
    catch (error: any) {
      console.error('💓 心跳失败:', error.message)

      // 检查是否是蓝牙适配器失败
      if (error && error.message && (
        error.message.includes('蓝牙适配器')
        || error.message.includes('无法使用该功能')
        || error.message.includes('蓝牙不可用')
        || error.message.includes('蓝牙系统异常')
      )) {
        console.error('💓 蓝牙适配器失败，执行清理')
        this.handleBluetoothAdapterFailure(error)
        return
      }

      if (this.connectionState === ConnectionState.CONNECTED) {
        this.handleDisconnected('heartbeat_failed')
      }
    }
  }

  /**
   * 处理连接成功事件
   */
  private handleConnected(deviceId: string): void {
    this.setConnectionState(ConnectionState.CONNECTED)
    this.reconnectAttempts = 0
    this.stats.lastConnectedTime = Date.now()
    this.startHeartbeat()
    console.log(`🔗 设备连接成功: ${deviceId}`)
  }

  /**
   * 处理连接断开事件
   */
  private handleDisconnected(reason: string): void {
    console.log(`🔌 设备连接断开: ${reason}`)
    this.setConnectionState(ConnectionState.DISCONNECTED)
    this.stats.lastDisconnectedTime = Date.now()
    this.stopHeartbeat()

    if (this.options.autoReconnect && this.currentDevice) {
      this.scheduleReconnect()
    }
  }

  /**
   * 处理连接错误事件
   */
  private handleConnectionError(error: any): void {
    console.error('🚨 连接错误:', error)
    this.options.onError(error)
  }

  /**
   * 计划重连
   */
  private scheduleReconnect(): void {
    if (this.reconnectTimer) {
      return
    }

    // 检查蓝牙适配器是否可用
    if (!this.connection.isBluetoothAdapterAvailable()) {
      console.error('🚨 蓝牙适配器不可用，取消重连计划')
      this.handleBluetoothAdapterFailure(new Error('蓝牙适配器不可用'))
      return
    }

    this.reconnectTimer = setTimeout(() => {
      this.reconnectTimer = null
      this.startReconnect().catch((error) => {
        console.error('自动重连失败:', error)
      })
    }, this.options.reconnectDelay) as unknown as number
  }

  /**
   * 设置连接状态
   */
  private setConnectionState(state: ConnectionState, error?: Error): void {
    if (this.connectionState !== state) {
      this.connectionState = state
      this.options.onConnectionStateChange(state, error)
    }
  }

  /**
   * 清空队列
   */
  private clearQueue(): void {
    this.printQueue.forEach((job) => {
      this.options.onPrintJobStateChange(job, PrintJobState.CANCELLED)
      job.reject(new Error('打印任务已取消'))
    })

    if (this.currentJob) {
      this.options.onPrintJobStateChange(this.currentJob, PrintJobState.CANCELLED)
      this.currentJob.reject(new Error('打印任务已取消'))
      this.currentJob = undefined
    }

    this.printQueue = []
    this.isProcessingQueue = false
    this.options.onQueueStateChange(0, false)
  }

  /**
   * 工具方法
   */
  private generateJobId(): string {
    return `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  private updateAveragePrintTime(duration: number): void {
    const total = this.stats.successfulPrintJobs
    this.stats.averagePrintTime = (this.stats.averagePrintTime * (total - 1) + duration) / total
  }

  /**
   * 公共接口
   */
  getConnectionState(): ConnectionState {
    return this.connectionState
  }

  getCurrentDevice(): BluetoothDevice | undefined {
    return this.currentDevice
  }

  getQueueStatus(): { size: number, processing: boolean, currentJob: PrintJob | undefined } {
    return {
      size: this.printQueue.length,
      processing: this.isProcessingQueue,
      currentJob: this.currentJob,
    }
  }

  getStats(): typeof this.stats {
    return { ...this.stats }
  }

  /**
   * 检查是否支持打印操作
   */
  canPrint(): boolean {
    return this.connection.isBluetoothAdapterAvailable() && this.connectionState !== ConnectionState.ERROR
  }

  /**
   * 获取打印机状态描述
   */
  getPrinterStatus(): { available: boolean, message: string } {
    if (!this.connection.isBluetoothAdapterAvailable()) {
      return { available: false, message: '蓝牙适配器不可用' }
    }

    if (this.connectionState === ConnectionState.ERROR) {
      return { available: false, message: '打印机处于错误状态' }
    }

    if (this.connectionState === ConnectionState.DISCONNECTED) {
      return { available: true, message: '打印机未连接' }
    }

    if (this.connectionState === ConnectionState.CONNECTING || this.connectionState === ConnectionState.RECONNECTING) {
      return { available: true, message: '正在连接打印机' }
    }

    if (this.connectionState === ConnectionState.CONNECTED) {
      return { available: true, message: '打印机已连接' }
    }

    return { available: false, message: '未知状态' }
  }

  /**
   * 释放资源
   */
  dispose(): void {
    this.disconnect().catch(console.error)
    this.commandGenerator.dispose()
    console.log('🧹 可靠打印管理器已释放')
  }

  /**
   * 处理蓝牙适配器失败的情况
   */
  private handleBluetoothAdapterFailure(error: Error): void {
    console.error('🚨 蓝牙适配器失败，执行彻底清理:', error.message)

    // 停止所有定时器和重连
    this.stopReconnect()
    this.stopHeartbeat()

    // 清空打印队列和当前任务
    this.clearQueue()

    // 设置连接状态为错误
    this.setConnectionState(ConnectionState.ERROR, error)

    // 清除当前设备
    this.currentDevice = undefined

    // 通知上层应用
    this.options.onError(new Error(`蓝牙适配器失败: ${error.message}`))

    console.log('🧹 已清理所有打印任务和连接状态')
  }
}
