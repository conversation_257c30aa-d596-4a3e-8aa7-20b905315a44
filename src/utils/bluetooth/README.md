# 蓝牙打印架构文档

## 🎯 重构说明

原有的三个类（`BluetoothPrinter`、`ImagePrinter`、`ReliableBluetoothPrinter`）功能分割不明确，存在职责重叠和依赖混乱问题。

重构后采用清晰的三层架构，职责分工明确：

## 🏗️ 架构设计

### 第一层：连接层 - `BluetoothConnection`
**职责**：纯粹的蓝牙通信管理
- 蓝牙适配器初始化
- 设备搜索和连接
- 数据传输和分包
- MTU优化
- 连接状态监听

**特点**：
- 不涉及业务逻辑
- 专注底层通信
- 可复用于其他蓝牙设备
- 智能连接管理（支持设备切换）

### 第二层：指令层 - `PrintCommandGenerator`
**职责**：ESC/POS打印指令生成
- 文本打印指令（支持gbk编码）
- 图片打印指令（位图处理）
- 格式设置指令（字体、对齐、样式）
- 控制指令（切纸、换行、心跳）

**特点**：
- 统一的指令生成接口
- 支持多种打印格式
- gbk编码集成
- Canvas图像处理
- FontSize指令解析（支持动态字体大小控制）

### 第三层：管理层 - `ReliablePrinter`
**职责**：高级打印管理和业务逻辑
- 长连接管理和自动重连
- 打印队列管理和任务调度
- 状态监听和错误恢复
- 心跳检测和连接保活
- 统计信息和性能监控

**特点**：
- 提供业务友好的API
- 自动处理连接问题
- 智能队列管理
- 详细的状态反馈

## 📦 类结构

```
ReliablePrinter (管理层)
├── BluetoothConnection (连接层)
└── PrintCommandGenerator (指令层)
    ├── gbk.min.js (编码库)
    └── OffscreenCanvas (图像处理)
```

## 🚀 使用方式

### 基础使用
```typescript
import { ReliablePrinter } from '@/utils/bluetooth/ReliablePrinter'

// 创建打印管理器
const printer = new ReliablePrinter({
  autoReconnect: true,
  maxReconnectAttempts: 5,
  onConnectionStateChange: (state) => {
    console.log('连接状态:', state)
  }
})

// 连接设备
await printer.connect(device)

// 打印文本
await printer.printText('Hello World', {
  fontSize: 'large',
  align: 'center',
  bold: true
})

// 打印带FontSize指令的文本（支持字体样式）
await printer.printText(`FontSize(14,微软雅黑,1)
重要标题（加粗）
EndFont
FontSize(12,微软雅黑,2)
下划线文本（斜体）
EndFont
普通内容`)

// 打印图片
await printer.printImage('https://example.com/image.jpg', {
  useOriginalWidth: true,
  maxWidth: 576,
  dithering: true
})
```

### 高级配置
```typescript
const printer = new ReliablePrinter({
  // 连接配置
  autoReconnect: true,
  maxReconnectAttempts: 5,
  reconnectDelay: 2000,
  connectionTimeout: 10000,

  // 队列配置
  maxQueueSize: 50,
  printTimeout: 30000,
  maxRetryAttempts: 3,

  // 心跳配置
  heartbeatInterval: 30000,
  heartbeatTimeout: 5000,

  // 事件回调
  onConnectionStateChange: (state) => {
    // 连接状态变化
  },
  onPrintJobStateChange: (job, state) => {
    // 打印任务状态变化
  },
  onQueueStateChange: (size, processing) => {
    // 队列状态变化
  },
})
```

## 🔧 类型定义

### 连接状态
```typescript
enum ConnectionState {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error'
}
```

### 打印任务状态
```typescript
enum PrintJobState {
  QUEUED = 'queued',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}
```

### 文本打印选项
```typescript
interface TextPrintOptions {
  fontSize?: 'normal' | 'large' | 'small'
  align?: 'left' | 'center' | 'right'
  bold?: boolean
  underline?: boolean
  lineHeight?: number
}
```

### 图片打印选项
```typescript
interface ImagePrintOptions {
  width?: number
  maxWidth?: number
  quality?: number
  dithering?: boolean
  useOriginalWidth?: boolean
}
```

## 🎯 重构优势

### 1. 职责明确
- 每个类都有明确的单一职责
- 避免功能重叠和代码重复
- 易于理解和维护

### 2. 依赖清晰
- 分层架构，依赖方向清晰
- 高层依赖低层，低层不依赖高层
- 便于单元测试和模块替换

### 3. 可扩展性
- 新增打印格式只需扩展指令生成器
- 新增连接方式只需扩展连接层
- 业务逻辑变更只需修改管理层

### 4. 代码复用
- 连接层可用于其他蓝牙设备
- 指令生成器可用于其他打印场景
- 管理层提供统一的高级API

## 🔄 迁移指南

### 从旧版本迁移

#### 打印功能迁移
```typescript
// 旧版本
import { ReliableBluetoothPrinter } from '@/utils/bluetooth/ReliableBluetoothPrinter'

// 新版本
import { ReliablePrinter } from '@/utils/bluetooth/ReliablePrinter'

// API保持兼容，直接替换即可
const printer = new ReliablePrinter(options)
```

#### 设备搜索功能迁移
```typescript
// 旧版本
import { BluetoothPrinter } from '@/utils/bluetooth/BluetoothPrinter'

// 新版本
import { BluetoothConnection } from '@/utils/bluetooth/BluetoothConnection'

// 主要变更：
// 1. 类名变更：BluetoothPrinter → BluetoothConnection
// 2. 回调参数增加类型定义
// 3. 方法名保持一致
```

### 主要变更
1. **类名变更**：
   - `ReliableBluetoothPrinter` → `ReliablePrinter`
   - `BluetoothPrinter` → `BluetoothConnection`
2. **方法优化**：移除私有方法暴露，统一使用 `printText()` 和 `printImage()`
3. **架构分离**：内部使用分层架构，但API保持简洁
4. **性能提升**：优化指令生成和数据传输
5. **类型安全**：增强TypeScript类型定义

## 📝 注意事项

1. **gbk编码库**：确保 `static/gbk.min.js` 文件存在
2. **Canvas支持**：仅在微信小程序环境支持图片打印
3. **设备兼容性**：支持大部分ESC/POS指令集的热敏打印机
4. **内存管理**：使用完毕后调用 `dispose()` 释放资源

## 🐛 故障排查

### 常见问题
1. **连接失败**：检查蓝牙权限和设备兼容性
2. **打印乱码**：确认gbk编码库加载成功
3. **图片打印失败**：检查Canvas支持和图片格式
4. **队列堵塞**：检查网络连接和设备状态

### 调试方法
```typescript
// 启用详细日志
const printer = new ReliablePrinter({
  // ... 配置
})

// 监听所有事件
printer.onConnectionStateChange = console.log
printer.onPrintJobStateChange = console.log
printer.onQueueStateChange = console.log
printer.onError = console.error

// 获取统计信息
const stats = printer.getStats()
console.log('打印统计:', stats)
```
