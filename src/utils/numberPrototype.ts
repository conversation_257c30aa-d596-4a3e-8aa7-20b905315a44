/*
 * @Author: weish<PERSON>
 * @Date: 2025-06-03 17:54:18
 * @LastEditTime: 2025-06-26 19:45:19
 * @LastEditors: weisheng
 * @Description:
 * @FilePath: /lsym-cx-mini/src/utils/numberPrototype.ts
 * 记得注释
 */
import Big from 'big.js'

/* eslint-disable no-extend-native */
export function initNumberPrototype() {
  Number.prototype.scale = function (n) {
    const number = Number(this)
    if (Number.isNaN(number) || number >= 10 ** 21) {
      return number
    }
    if (typeof n === 'undefined' || n === 0) {
      return Math.round(number)
    }
    return Math.round(number * 10 ** n) / 10 ** n
  }

  Number.prototype.floorScale = function (n) {
    const number = Number(this)
    if (Number.isNaN(number) || number >= 10 ** 21) {
      return number
    }
    if (typeof n === 'undefined' || n === 0) {
      const result = new Big(number).round(0, Big.roundDown)
      return Number(result.toString())
    }
    const multiplier = new Big(10).pow(n)
    const result = new Big(number).times(multiplier).round(0, Big.roundDown).div(multiplier)
    return Number(result.toString())
  }

  // 加法
  Number.prototype.add = function (n: number) {
    // 返回值转成Number
    n = Number.isNaN(Number(n)) ? 0 : Number(n)
    const result = new Big(Number.isNaN(Number(this)) ? 0 : Number(this)).plus(Number(n))
    return Number(result.toString())
  }

  // 减法
  Number.prototype.minus = function (n: number) {
    n = Number.isNaN(Number(n)) ? 0 : Number(n)
    const result = new Big(Number.isNaN(Number(this)) ? 0 : Number(this)).minus(Number(n))
    return Number(result.toString())
  }
  // 乘法
  Number.prototype.multiply = function (n: number) {
    n = Number.isNaN(Number(n)) ? 0 : Number(n)
    const result = new Big(Number.isNaN(Number(this)) ? 0 : Number(this)).times(Number(n))
    return Number(result.toString())
  }

  // 除法
  Number.prototype.divide = function (n) {
    n = Number.isNaN(Number(n)) ? 1 : Number(n)
    const result = new Big(Number.isNaN(Number(this)) ? 0 : Number(this)).div(Number(n))
    return Number(result.toString())
  }
}
