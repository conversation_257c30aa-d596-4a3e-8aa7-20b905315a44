/*
 * @Author: weisheng
 * @Date: 2025-04-17 15:58:11
 * @LastEditTime: 2025-04-17 15:58:11
 * @LastEditors: weisheng
 * @Description: Authentication utilities
 * @FilePath: /lsym-cx-mini/src/utils/auth.ts
 */

// Token storage keys
const TOKEN_KEY = 'auth_token'
const REFRESH_TOKEN_KEY = 'refresh_token'
const TOKEN_EXPIRES_KEY = 'token_expires'

/**
 * Get token from storage
 */
export function getToken(): string | null {
  return uni.getStorageSync(TOKEN_KEY) || null
}

/**
 * Set token in storage
 */
export function setToken(token: string): void {
  uni.setStorageSync(TOKEN_KEY, token)
}

/**
 * Remove token from storage
 */
export function removeToken(): void {
  uni.removeStorageSync(TOKEN_KEY)
}

/**
 * Get refresh token from storage
 */
export function getRefreshToken(): string | null {
  return uni.getStorageSync(REFRESH_TOKEN_KEY) || null
}

/**
 * Set refresh token in storage
 */
export function setRefreshToken(token: string): void {
  uni.setStorageSync(REFRESH_TOKEN_KEY, token)
}

/**
 * Remove refresh token from storage
 */
export function removeRefreshToken(): void {
  uni.removeStorageSync(REFRESH_TOKEN_KEY)
}

/**
 * Get token expiration timestamp
 */
export function getTokenExpires(): number | null {
  const expires = uni.getStorageSync(TOKEN_EXPIRES_KEY)
  return expires ? Number(expires) : null
}

/**
 * Set token expiration timestamp
 */
export function setTokenExpires(timestamp: number): void {
  uni.setStorageSync(TOKEN_EXPIRES_KEY, timestamp.toString())
}

/**
 * Remove token expiration timestamp
 */
export function removeTokenExpires(): void {
  uni.removeStorageSync(TOKEN_EXPIRES_KEY)
}

/**
 * Check if token is expired
 */
export function isTokenExpired(): boolean {
  const expires = getTokenExpires()
  if (!expires)
    return true

  // Add 5 seconds buffer
  return Date.now() > expires - 5000
}

/**
 * Clear all auth data
 */
export function clearAuth(): void {
  removeToken()
  removeRefreshToken()
  removeTokenExpires()
}
