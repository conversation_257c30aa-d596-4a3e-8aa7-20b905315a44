/*
 * @Author: weish<PERSON>
 * @Date: 2025-01-24 10:00:00
 * @Description: 权限管理工具
 * @FilePath: /lsym-cx-mini/src/utils/permissions.ts
 */

// 权限模块常量
export const PERMISSIONS = {
  // 登录相关
  LOGIN: 'login',

  // 车销领货单相关
  VEH_SALE_USE_SIGN_VIEW: 'vehSaleUseSignView', // 车销领货单-查看
  VEH_SALE_USE_SIGN_CREATE: 'vehSaleUseSignCreate', // 车销领货单-创建
  VEH_SALE_USE_SIGN_ABORT: 'vehSaleUseSignAbort', // 车销领货单-作废
  VEH_SALE_USE_SIGN_AUDIT: 'vehSaleUseSignAudit', // 车销领货单-审核

  // 车销回货单相关
  VEH_SALE_USE_SIGN_ARV_VIEW: 'vehSaleUseSignArvView', // 车销回货单-查看
  VEH_SALE_USE_SIGN_ARV_CREATE: 'vehSaleUseSignArvCreate', // 车销回货单-创建
  VEH_SALE_USE_SIGN_ARV_AUDIT: 'vehSaleUseSignArvAudit', // 车销回货单-审核
  VEH_SALE_USE_SIGN_ARV_ABORT: 'vehSaleUseSignArvAbort', // 车销回货单-作废

  // 车销单相关
  VEH_SALE_VIEW: 'vehSaleView', // 车销单-查看
  VEH_SALE_CREATE: 'vehSaleCreate', // 车销单-创建
  VEH_SALE_ABORT: 'vehSaleAbort', // 车销单-作废

  // 车销退货单相关
  VEH_SALE_BCK_VIEW: 'vehSaleBckView', // 车销退货单-查看
  VEH_SALE_BCK_CREATE: 'vehSaleBckCreate', // 车销退货单-创建
  VEH_SALE_BCK_ABORT: 'vehSaleBckAbort', // 车销退货单-作废

  // 买赔单相关
  WHOLE_SALE_VIEW: 'wholeSaleView', // 买赔单-查看
  WHOLE_SALE_CREATE: 'wholeSaleCreate', // 买赔单-创建

  // 车销领货退货单相关
  VEH_SALE_USE_SIGN_BCK_VIEW: 'vehSaleUseSignBckView', // 车销领货退货单-查看
  VEH_SALE_USE_SIGN_BCK_CREATE: 'vehSaleUseSignBckCreate', // 车销领货退货单-创建

  // 车销回货差异单相关
  VEH_SALE_USE_SIGN_ARV_DIFF_VIEW: 'vehSaleUseSignArvDiffView', // 车销回货差异单-查看
} as const

// 权限组定义 - 将相关权限分组管理
export const PERMISSION_GROUPS = {
  // 车销领货单权限组
  VEH_SALE_USE_SIGN: [
    PERMISSIONS.VEH_SALE_USE_SIGN_VIEW,
    PERMISSIONS.VEH_SALE_USE_SIGN_CREATE,
    PERMISSIONS.VEH_SALE_USE_SIGN_ABORT,
    PERMISSIONS.VEH_SALE_USE_SIGN_AUDIT,
  ],

  // 车销回货单权限组
  VEH_SALE_USE_SIGN_ARV: [
    PERMISSIONS.VEH_SALE_USE_SIGN_ARV_VIEW,
    PERMISSIONS.VEH_SALE_USE_SIGN_ARV_CREATE,
    PERMISSIONS.VEH_SALE_USE_SIGN_ARV_AUDIT,
    PERMISSIONS.VEH_SALE_USE_SIGN_ARV_ABORT,
  ],

  // 车销单权限组
  VEH_SALE: [
    PERMISSIONS.VEH_SALE_VIEW,
    PERMISSIONS.VEH_SALE_CREATE,
    PERMISSIONS.VEH_SALE_ABORT,
  ],

  // 车销退货单权限组
  VEH_SALE_BCK: [
    PERMISSIONS.VEH_SALE_BCK_VIEW,
    PERMISSIONS.VEH_SALE_BCK_CREATE,
    PERMISSIONS.VEH_SALE_BCK_ABORT,
  ],

  // 买赔单权限组
  WHOLE_SALE: [
    PERMISSIONS.WHOLE_SALE_VIEW,
    PERMISSIONS.WHOLE_SALE_CREATE,
  ],

  // 车销领货退货单权限组
  VEH_SALE_USE_SIGN_BCK: [
    PERMISSIONS.VEH_SALE_USE_SIGN_BCK_VIEW,
    PERMISSIONS.VEH_SALE_USE_SIGN_BCK_CREATE,
  ],
} as const

// 功能按钮与权限映射
export const BUTTON_PERMISSIONS: Record<string, PermissionType> = {
  // 车销领货单按钮权限
  'veh-sale-use-sign-create': PERMISSIONS.VEH_SALE_USE_SIGN_CREATE,
  'veh-sale-use-sign-abort': PERMISSIONS.VEH_SALE_USE_SIGN_ABORT,
  'veh-sale-use-sign-audit': PERMISSIONS.VEH_SALE_USE_SIGN_AUDIT,

  // 车销回货单按钮权限
  'veh-sale-use-sign-arv-create': PERMISSIONS.VEH_SALE_USE_SIGN_ARV_CREATE,
  'veh-sale-use-sign-arv-audit': PERMISSIONS.VEH_SALE_USE_SIGN_ARV_AUDIT,
  'veh-sale-use-sign-arv-abort': PERMISSIONS.VEH_SALE_USE_SIGN_ARV_ABORT,

  // 车销单按钮权限
  'veh-sale-create': PERMISSIONS.VEH_SALE_CREATE,
  'veh-sale-abort': PERMISSIONS.VEH_SALE_ABORT,

  // 车销退货单按钮权限
  'veh-sale-bck-create': PERMISSIONS.VEH_SALE_BCK_CREATE,
  'veh-sale-bck-abort': PERMISSIONS.VEH_SALE_BCK_ABORT,

  // 买赔单按钮权限
  'whole-sale-create': PERMISSIONS.WHOLE_SALE_CREATE,

  // 车销领货退货单按钮权限
  'veh-sale-use-sign-bck-create': PERMISSIONS.VEH_SALE_USE_SIGN_BCK_CREATE,
}

export type PermissionType = typeof PERMISSIONS[keyof typeof PERMISSIONS]

/**
 * 页面路由元信息类型定义
 * 用于在页面的 <route lang="json"> 中配置权限
 */
export interface RouteMetaPermission {
  /** 页面所需的权限列表，用户需要拥有其中任意一个权限才能访问 */
  permissions?: PermissionType[]
  /** 是否需要所有权限都满足才能访问，默认false（满足任意一个即可） */
  requireAll?: boolean
}

