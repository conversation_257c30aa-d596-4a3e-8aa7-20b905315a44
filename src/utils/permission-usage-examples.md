# 权限控制系统使用指南

## 概述

本项目实现了一套完整的权限控制系统，支持页面级、组件级和按钮级的权限控制。权限配置更加灵活，支持在页面中直接配置权限要求。

## 权限类型

- **页面权限**：控制用户是否能访问某个页面
- **按钮权限**：控制页面中的按钮是否显示
- **组件权限**：控制组件是否渲染

## 1. 页面权限配置

### 新版本：在页面route中配置权限（推荐）

在页面的 `<route lang="json">` 配置中添加 `meta.permissions` 字段：

```vue
<route lang="json">
{
  "name": "sales-list",
  "style": {
    "navigationBarTitleText": "历史车销单"
  },
  "meta": {
    "permissions": ["vehSaleView", "vehSaleBckView"],
    "requireAll": false
  }
}
</route>
```

**配置说明：**
- `permissions`: 页面所需的权限列表
- `requireAll`: 是否需要用户拥有所有权限才能访问
  - `false`（默认）：用户拥有任意一个权限即可访问
  - `true`：用户必须拥有所有权限才能访问

**配置示例：**

```vue
<!-- 示例1: 用户需要拥有查看车销单权限或查看车销退货单权限中的任意一个 -->
<route lang="json">
{
  "name": "sales-list",
  "meta": {
    "permissions": ["vehSaleView", "vehSaleBckView"],
    "requireAll": false
  }
}
</route>

<!-- 示例2: 用户必须同时拥有创建和审核权限 -->
<route lang="json">
{
  "name": "admin-panel",
  "meta": {
    "permissions": ["vehSaleCreate", "vehSaleAudit"],
    "requireAll": true
  }
}
</route>

<!-- 示例3: 无权限要求的页面（不配置meta即可） -->
<route lang="json">
{
  "name": "public-page"
}
</route>
```

### ~~旧版本：集中配置权限（已废弃）~~

~~之前的集中配置方式已废弃，不再推荐使用。~~

## 2. 组件权限控制

### 使用PermissionWrapper组件

```vue
<template>
  <!-- 单个权限检查 -->
  <PermissionWrapper permission="vehSaleCreate">
    <wd-button>创建车销单</wd-button>
  </PermissionWrapper>

  <!-- 多个权限任意一个 -->
  <PermissionWrapper :permissions="['vehSaleView', 'vehSaleBckView']">
    <view>车销相关内容</view>
  </PermissionWrapper>

  <!-- 多个权限全部满足 -->
  <PermissionWrapper
    :permissions="['vehSaleCreate', 'vehSaleAudit']"
    :require-all="true"
  >
    <view>高级管理功能</view>
  </PermissionWrapper>

  <!-- 按钮权限检查 -->
  <PermissionWrapper button-key="veh-sale-create">
    <wd-button>创建按钮</wd-button>
  </PermissionWrapper>

  <!-- 无权限时显示替代内容 -->
  <PermissionWrapper permission="vehSaleCreate">
    <wd-button>创建车销单</wd-button>
    <template #fallback>
      <view class="text-gray-400">
        您没有创建权限
      </view>
    </template>
  </PermissionWrapper>
</template>
```

## 3. 在脚本中使用权限检查

### 使用useAppPermission

```typescript
import { useAppPermission } from '@/composables/usePermission'

// 基础用法
const { currentPermission, checkPermission } = useAppPermission('vehSaleCreate')

// 检查单个权限
if (checkPermission('vehSaleView')) {
  // 有权限时执行
}

// 多个权限检查
const { checkAnyPermission, checkAllPermissions } = useAppPermission()

if (checkAnyPermission(['vehSaleView', 'vehSaleBckView'])) {
  // 有任意一个权限时执行
}

if (checkAllPermissions(['vehSaleCreate', 'vehSaleAudit'])) {
  // 有所有权限时执行
}
```

### 使用usePermissionChecker（工具函数）

```typescript
import { usePermissionChecker } from '@/composables/usePermission'

const { checkPermission, checkAnyPermission, checkButtonPermission } = usePermissionChecker()

// 单个权限检查
const canCreate = checkPermission('vehSaleCreate')

// 多个权限检查
const canViewSales = checkAnyPermission(['vehSaleView', 'vehSaleBckView'])

// 按钮权限检查
const showCreateButton = checkButtonPermission('veh-sale-create')
```

### 使用useAppButtonPermission

```typescript
import { useAppButtonPermission } from '@/composables/usePermission'

const { isVisible } = useAppButtonPermission('veh-sale-create')

// 在模板中使用
<wd-button v-if="isVisible">创建车销单</wd-button>
```

### 使用useAppPagePermission

```typescript
import { useAppPagePermission } from '@/composables/usePermission'

const { hasAccess, accessDenied } = useAppPagePermission(['vehSaleView'])

// 页面权限检查
if (accessDenied.value) {
  // 跳转到无权限页面或显示提示
}
```

### 使用useAppPermissionGuard

```typescript
import { useAppPermissionGuard } from '@/composables/usePermission'

const { hasAccess, checkAccess } = useAppPermissionGuard('vehSaleCreate', {
  tipType: 'toast',
  messgae: '您没有创建权限'
})

// 执行需要权限的操作前检查
function handleCreate() {
  if (!checkAccess()) {
    // 无权限时会自动显示提示
  }

  // 执行创建操作
}
```

## 4. 条件渲染

### 在模板中使用

```vue
<script setup>
import { useAppPermission } from '@/composables/usePermission'

const { checkPermission, checkAnyPermission } = useAppPermission()
</script>

<template>
  <!-- 单个权限 -->
  <wd-button v-if="checkPermission('vehSaleCreate')">
    创建车销单
  </wd-button>

  <!-- 多个权限 -->
  <view v-if="checkAnyPermission(['vehSaleView', 'vehSaleBckView'])">
    车销相关内容
  </view>

  <!-- 结合其他条件 -->
  <wd-button
    v-if="checkPermission('vehSaleCreate') && someOtherCondition"
    @click="handleCreate"
  >
    创建车销单
  </wd-button>
</template>
```

## 5. 权限常量

所有权限常量定义在 `src/utils/permissions.ts` 中：

```typescript
export const PERMISSIONS = {
  // 车销领货单相关
  VEH_SALE_USE_SIGN_VIEW: 'vehSaleUseSignView',
  VEH_SALE_USE_SIGN_CREATE: 'vehSaleUseSignCreate',
  VEH_SALE_USE_SIGN_ABORT: 'vehSaleUseSignAbort',
  VEH_SALE_USE_SIGN_AUDIT: 'vehSaleUseSignAudit',

  // 车销回货单相关
  VEH_SALE_USE_SIGN_ARV_VIEW: 'vehSaleUseSignArvView',
  // ... 更多权限
}
```

## 6. 调试权限

### 开发环境调试

```typescript
import { usePermissionChecker } from '@/composables/usePermission'

const { debugPermissions } = usePermissionChecker()

// 打印权限调试信息
debugPermissions()
```

## 7. 最佳实践

### 页面权限配置

1. **直接在页面配置**：推荐在页面的 `<route lang="json">` 中配置权限，这样更直观
2. **合理使用requireAll**：大多数情况下使用默认的 `requireAll: false`
3. **权限粒度要适中**：不要过于细化权限，也不要权限范围过大

### 组件权限

1. **优先使用PermissionWrapper**：对于简单的权限控制
2. **复杂逻辑使用Composable**：当需要结合其他业务逻辑时
3. **提供fallback内容**：当用户无权限时给出友好提示

### 性能优化

1. **避免重复检查**：在computed中缓存权限检查结果
2. **合理使用工具函数**：usePermissionChecker适合纯函数场景，useAppPermission适合响应式场景

### 代码维护

1. **集中管理权限常量**：所有权限定义在permissions.ts中
2. **统一命名规范**：权限名称使用驼峰命名
3. **及时更新文档**：权限变更时同步更新使用指南

## 8. 常见问题

### Q: 如何处理权限变更？
A: 权限是响应式的，用户权限变更后UI会自动更新。

### Q: 如何添加新权限？
A: 1. 在 `PERMISSIONS` 中添加新常量；2. 在需要的页面route中配置；3. 在组件中使用。

### Q: 页面权限检查失败怎么办？
A: 路由守卫会自动阻止访问并显示提示，无需额外处理。

### Q: 如何处理权限检查的异常？
A: 权限检查方法都有默认值，异常情况下会返回安全的结果（通常是false）。
