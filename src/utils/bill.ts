/*
 * @Author: we<PERSON><PERSON>
 * @Date: 2025-05-17 16:49:46
 * @LastEditTime: 2025-06-11 18:47:18
 * @LastEditors: weisheng
 * @Description:
 * @FilePath: /lsym-cx-mini/src/utils/bill.ts
 * 记得注释
 */
// type OrderStatus = 0 | 100 | 1300 | 300 | 110 | 1310 ｜ 310

// 0: '未审核',
// 100: '已审核',
// 1300: '已申请',
// 300: '已完成',
// 110: '审核后作废',
// 1310: '申请后作废',
// 310: '已完成后作废',
// 差异单：已审核 = 待处理， 已完成  = 已处理
export type OrderStatus = 'audited' | 'applied' | 'canceled' | 'pending' | 'completed' | 'initial' | 'unknown'

/**
 * 获取订单状态枚举值
 * @param status 订单状态
 * @param isDiff 是否是差异单
 * @returns 订单状态
 */
export function getBillStatus(status: number, isDiff: boolean = false): OrderStatus {
  switch (status) {
    case 0:
      return 'initial'
    case 100:
      return isDiff ? 'pending' : 'audited'
    case 1300:
      return 'applied'
    case 300:
      return 'completed'
    case 110:
      return 'canceled'
    case 1310:
      return 'canceled'
    case 310:
      return 'canceled'
    default:
      return 'unknown'
  }
}

// 订单状态中文
// 0: '未审核',
// 100: '已审核',
// 1300: '已申请',
// 300: '已完成',
// 110: '审核后作废',
// 1310: '申请后作废',
// 差异单：已审核 = 待处理， 已完成  = 已处理
// 车销单：300 = 已完成
export function getBillStatusText(status: number, isDiff: boolean = false): string {
  switch (status) {
    case 0:
      return '未审核'
    case 100:
      return isDiff ? '待处理' : '已审核'
    case 1300:
      return '已申请'
    case 300:
      return isDiff ? '已处理' : '已完成'
    case 110:
      return '已作废'
    case 1310:
      return '已作废'
    case 310:
      return '已作废'
    default:
      return '未知'
  }
}

// 各状态颜色
// 0: '#8A8A8A', // 未审核 - 灰色
// 100: '#12B886', // 已审核 - 绿色
// 1300: '#1C64FD', // 已申请 - 蓝色
// 300: '#12B886', // 已完成 - 绿色
// 110: '#8A8A8A', // 审核后作废 - 灰色
// 1310: '#8A8A8A', // 申请后作废 - 灰色
export function getBillStatusColor(status: number): string {
  switch (status) {
    case 0:
      return '#8A8A8A'
    case 100:
      return '#12B886'
    case 1300:
      return '#1C64FD'
    case 300:
      return '#12B886'
    case 110:
      return '#8A8A8A'
    case 1310:
      return '#8A8A8A'
    case 310:
      return '#8A8A8A'
    default:
      return '#8A8A8A'
  }
}
