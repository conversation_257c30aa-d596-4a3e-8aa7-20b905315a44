/**
 * 获取当前页面路径
 * @returns 当前页面路径
 */
export function getCurrentPath() {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  return currentPage.route || ''
}

/**
 * 获取M+N格式的数量
 * @param qty 数量
 * @param qpc 每箱数量
 * @param munit 单位
 * @param minMunit 最小单位
 */
export function getMN(qty: number, qpc: number, munit: string, minMunit: string) {
  const m = Math.floor(qty / qpc)
  const n = qty % qpc
  return `${m}${munit}${n > 0 ? `${n}${minMunit}` : ''}`
}

/**
 * 生成M箱N个格式的数量
 * @param value 数量
 * @param munit 单位
 * @param minunit 最小单位
 * @returns M箱N个格式的数量
 */
export function getQpcQty(value: string, munit: string, minunit: string) {
  if (value) {
    if (value.includes('+')) {
      const arr = value.split('+')
      if (arr[0] === '0') {
        value = arr[1] + minunit
      }
      else if (arr[1] === '0') {
        value = arr[0] + munit
      }
      else {
        value = value.replace(/\+/, `${munit}`) + minunit
      }
    }
    else {
      value = (Number.isNaN(Number(value)) ? value : Number(value)) + munit
    }
  }
  return value
}

/**
 * 格式化价格，分离整数和小数部分
 * @param price 价格
 * @returns 整数和小数部分
 */
export function formatPrice(price: number) {
  const formatted = `${price.floorScale(4)}`
  const [integer, decimal] = formatted.split('.')
  return { integer, decimal }
}

/**
 * hdpos4加密登录密码算法。
 *
 * @param password 登录密码，not null。
 * @return 加密后的密码。
 */
export function encryptPwd(password: string) {
  while (password.length <= 30) {
    password = `${password}$${password}`
  }
  const length = password.length
  const pwds = password.split('')
  for (let i = 1; i <= length - 30; i++) {
    for (let j = 0; j < length - i; j++) {
      const a = pwds[j].charCodeAt(0)
      const b = pwds[j + 1].charCodeAt(0)
      if (i % 2 === 0) {
        pwds[j] = String.fromCharCode(((Math.abs(a - b) * 2 + 1) % 95) + 32)
      }
      else {
        pwds[j] = String.fromCharCode((((a + b) * 2 + 1) % 95) + 32)
      }
    }
  }
  const result = Array.from({ length: 30 })
  for (let i = 0; i < 30; i++) {
    result[i] = pwds[i]
  }
  return result.join('').trim()
}
