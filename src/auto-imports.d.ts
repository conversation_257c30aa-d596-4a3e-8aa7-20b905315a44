/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// noinspection JSUnusedGlobalSymbols
// Generated by unplugin-auto-import
export {}
declare global {
  const BUTTON_PERMISSIONS: typeof import('./utils/permissions')['BUTTON_PERMISSIONS']
  const CommonUtil: typeof import('wot-design-uni')['CommonUtil']
  const EffectScope: typeof import('vue')['EffectScope']
  const PERMISSIONS: typeof import('./utils/permissions')['PERMISSIONS']
  const PERMISSION_GROUPS: typeof import('./utils/permissions')['PERMISSION_GROUPS']
  const ROUTE_PERMISSIONS: typeof import('./utils/permissions')['ROUTE_PERMISSIONS']
  const acceptHMRUpdate: typeof import('pinia')['acceptHMRUpdate']
  const asyncComputed: typeof import('@vueuse/core')['asyncComputed']
  const autoResetRef: typeof import('@vueuse/core')['autoResetRef']
  const checkAllPermissions: typeof import('./utils/permission-helper')['checkAllPermissions']
  const checkAnyPermission: typeof import('./utils/permission-helper')['checkAnyPermission']
  const checkButtonPermission: typeof import('./utils/permission-helper')['checkButtonPermission']
  const checkPermission: typeof import('./utils/permission-helper')['checkPermission']
  const checkRoutePermission: typeof import('./utils/permission-helper')['checkRoutePermission']
  const clearAuth: typeof import('./utils/auth')['clearAuth']
  const computed: typeof import('vue')['computed']
  const computedAsync: typeof import('@vueuse/core')['computedAsync']
  const computedEager: typeof import('@vueuse/core')['computedEager']
  const computedInject: typeof import('@vueuse/core')['computedInject']
  const computedWithControl: typeof import('@vueuse/core')['computedWithControl']
  const controlledComputed: typeof import('@vueuse/core')['controlledComputed']
  const controlledRef: typeof import('@vueuse/core')['controlledRef']
  const createApp: typeof import('vue')['createApp']
  const createEventHook: typeof import('@vueuse/core')['createEventHook']
  const createGlobalState: typeof import('@vueuse/core')['createGlobalState']
  const createInjectionState: typeof import('@vueuse/core')['createInjectionState']
  const createPinia: typeof import('pinia')['createPinia']
  const createReactiveFn: typeof import('@vueuse/core')['createReactiveFn']
  const createReusableTemplate: typeof import('@vueuse/core')['createReusableTemplate']
  const createRouter: typeof import('uni-mini-router')['createRouter']
  const createSharedComposable: typeof import('@vueuse/core')['createSharedComposable']
  const createTemplatePromise: typeof import('@vueuse/core')['createTemplatePromise']
  const createUnrefFn: typeof import('@vueuse/core')['createUnrefFn']
  const customRef: typeof import('vue')['customRef']
  const date: typeof import('./utils/date')['default']
  const debouncedRef: typeof import('@vueuse/core')['debouncedRef']
  const debouncedWatch: typeof import('@vueuse/core')['debouncedWatch']
  const debugPermissions: typeof import('./utils/permission-helper')['debugPermissions']
  const defineAsyncComponent: typeof import('vue')['defineAsyncComponent']
  const defineComponent: typeof import('vue')['defineComponent']
  const defineStore: typeof import('pinia')['defineStore']
  const downOption: typeof import('./utils/mescroll')['downOption']
  const eagerComputed: typeof import('@vueuse/core')['eagerComputed']
  const effectScope: typeof import('vue')['effectScope']
  const encryptPwd: typeof import('./utils/util')['encryptPwd']
  const extendRef: typeof import('@vueuse/core')['extendRef']
  const formatDate: typeof import('./utils/date')['formatDate']
  const formatPrice: typeof import('./utils/util')['formatPrice']
  const fullDate: typeof import('./utils/date')['fullDate']
  const getActivePermissions: typeof import('./utils/permission-helper')['getActivePermissions']
  const getActivePinia: typeof import('pinia')['getActivePinia']
  const getBillStatus: typeof import('./utils/bill')['getBillStatus']
  const getBillStatusColor: typeof import('./utils/bill')['getBillStatusColor']
  const getBillStatusText: typeof import('./utils/bill')['getBillStatusText']
  const getCurrentInstance: typeof import('vue')['getCurrentInstance']
  const getCurrentPath: typeof import('./utils/util')['getCurrentPath']
  const getCurrentScope: typeof import('vue')['getCurrentScope']
  const getMN: typeof import('./utils/util')['getMN']
  const getQpcQty: typeof import('./utils/util')['getQpcQty']
  const getRefreshToken: typeof import('./utils/auth')['getRefreshToken']
  const getToken: typeof import('./utils/auth')['getToken']
  const getTokenExpires: typeof import('./utils/auth')['getTokenExpires']
  const h: typeof import('vue')['h']
  const ignorableWatch: typeof import('@vueuse/core')['ignorableWatch']
  const initNumberPrototype: typeof import('./utils/numberPrototype')['initNumberPrototype']
  const inject: typeof import('vue')['inject']
  const injectLocal: typeof import('@vueuse/core')['injectLocal']
  const isDefined: typeof import('@vueuse/core')['isDefined']
  const isProxy: typeof import('vue')['isProxy']
  const isReactive: typeof import('vue')['isReactive']
  const isReadonly: typeof import('vue')['isReadonly']
  const isRef: typeof import('vue')['isRef']
  const isTokenExpired: typeof import('./utils/auth')['isTokenExpired']
  const makeDestructurable: typeof import('@vueuse/core')['makeDestructurable']
  const mapActions: typeof import('pinia')['mapActions']
  const mapGetters: typeof import('pinia')['mapGetters']
  const mapState: typeof import('pinia')['mapState']
  const mapStores: typeof import('pinia')['mapStores']
  const mapWritableState: typeof import('pinia')['mapWritableState']
  const markRaw: typeof import('vue')['markRaw']
  const nextTick: typeof import('vue')['nextTick']
  const onActivated: typeof import('vue')['onActivated']
  const onAddToFavorites: typeof import('@dcloudio/uni-app')['onAddToFavorites']
  const onBackPress: typeof import('@dcloudio/uni-app')['onBackPress']
  const onBeforeMount: typeof import('vue')['onBeforeMount']
  const onBeforeUnmount: typeof import('vue')['onBeforeUnmount']
  const onBeforeUpdate: typeof import('vue')['onBeforeUpdate']
  const onClickOutside: typeof import('@vueuse/core')['onClickOutside']
  const onDeactivated: typeof import('vue')['onDeactivated']
  const onError: typeof import('@dcloudio/uni-app')['onError']
  const onErrorCaptured: typeof import('vue')['onErrorCaptured']
  const onHide: typeof import('@dcloudio/uni-app')['onHide']
  const onKeyStroke: typeof import('@vueuse/core')['onKeyStroke']
  const onLaunch: typeof import('@dcloudio/uni-app')['onLaunch']
  const onLoad: typeof import('@dcloudio/uni-app')['onLoad']
  const onLongPress: typeof import('@vueuse/core')['onLongPress']
  const onMounted: typeof import('vue')['onMounted']
  const onNavigationBarButtonTap: typeof import('@dcloudio/uni-app')['onNavigationBarButtonTap']
  const onNavigationBarSearchInputChanged: typeof import('@dcloudio/uni-app')['onNavigationBarSearchInputChanged']
  const onNavigationBarSearchInputClicked: typeof import('@dcloudio/uni-app')['onNavigationBarSearchInputClicked']
  const onNavigationBarSearchInputConfirmed: typeof import('@dcloudio/uni-app')['onNavigationBarSearchInputConfirmed']
  const onNavigationBarSearchInputFocusChanged: typeof import('@dcloudio/uni-app')['onNavigationBarSearchInputFocusChanged']
  const onPageNotFound: typeof import('@dcloudio/uni-app')['onPageNotFound']
  const onPageScroll: typeof import('@dcloudio/uni-app')['onPageScroll']
  const onPullDownRefresh: typeof import('@dcloudio/uni-app')['onPullDownRefresh']
  const onReachBottom: typeof import('@dcloudio/uni-app')['onReachBottom']
  const onReady: typeof import('@dcloudio/uni-app')['onReady']
  const onRenderTracked: typeof import('vue')['onRenderTracked']
  const onRenderTriggered: typeof import('vue')['onRenderTriggered']
  const onResize: typeof import('@dcloudio/uni-app')['onResize']
  const onScopeDispose: typeof import('vue')['onScopeDispose']
  const onServerPrefetch: typeof import('vue')['onServerPrefetch']
  const onShareAppMessage: typeof import('@dcloudio/uni-app')['onShareAppMessage']
  const onShareTimeline: typeof import('@dcloudio/uni-app')['onShareTimeline']
  const onShow: typeof import('@dcloudio/uni-app')['onShow']
  const onStartTyping: typeof import('@vueuse/core')['onStartTyping']
  const onTabItemTap: typeof import('@dcloudio/uni-app')['onTabItemTap']
  const onThemeChange: typeof import('@dcloudio/uni-app')['onThemeChange']
  const onUnhandledRejection: typeof import('@dcloudio/uni-app')['onUnhandledRejection']
  const onUnload: typeof import('@dcloudio/uni-app')['onUnload']
  const onUnmounted: typeof import('vue')['onUnmounted']
  const onUpdated: typeof import('vue')['onUpdated']
  const pausableWatch: typeof import('@vueuse/core')['pausableWatch']
  const persistPlugin: typeof import('./store/persist')['persistPlugin']
  const provide: typeof import('vue')['provide']
  const provideLocal: typeof import('@vueuse/core')['provideLocal']
  const reactify: typeof import('@vueuse/core')['reactify']
  const reactifyObject: typeof import('@vueuse/core')['reactifyObject']
  const reactive: typeof import('vue')['reactive']
  const reactiveComputed: typeof import('@vueuse/core')['reactiveComputed']
  const reactiveOmit: typeof import('@vueuse/core')['reactiveOmit']
  const reactivePick: typeof import('@vueuse/core')['reactivePick']
  const readonly: typeof import('vue')['readonly']
  const ref: typeof import('vue')['ref']
  const refAutoReset: typeof import('@vueuse/core')['refAutoReset']
  const refDebounced: typeof import('@vueuse/core')['refDebounced']
  const refDefault: typeof import('@vueuse/core')['refDefault']
  const refThrottled: typeof import('@vueuse/core')['refThrottled']
  const refWithControl: typeof import('@vueuse/core')['refWithControl']
  const removeRefreshToken: typeof import('./utils/auth')['removeRefreshToken']
  const removeToken: typeof import('./utils/auth')['removeToken']
  const removeTokenExpires: typeof import('./utils/auth')['removeTokenExpires']
  const resolveComponent: typeof import('vue')['resolveComponent']
  const resolveRef: typeof import('@vueuse/core')['resolveRef']
  const resolveUnref: typeof import('@vueuse/core')['resolveUnref']
  const setActivePinia: typeof import('pinia')['setActivePinia']
  const setMapStoreSuffix: typeof import('pinia')['setMapStoreSuffix']
  const setRefreshToken: typeof import('./utils/auth')['setRefreshToken']
  const setToken: typeof import('./utils/auth')['setToken']
  const setTokenExpires: typeof import('./utils/auth')['setTokenExpires']
  const shallowReactive: typeof import('vue')['shallowReactive']
  const shallowReadonly: typeof import('vue')['shallowReadonly']
  const shallowRef: typeof import('vue')['shallowRef']
  const storeToRefs: typeof import('pinia')['storeToRefs']
  const syncRef: typeof import('@vueuse/core')['syncRef']
  const syncRefs: typeof import('@vueuse/core')['syncRefs']
  const templateRef: typeof import('@vueuse/core')['templateRef']
  const throttledRef: typeof import('@vueuse/core')['throttledRef']
  const throttledWatch: typeof import('@vueuse/core')['throttledWatch']
  const toRaw: typeof import('vue')['toRaw']
  const toReactive: typeof import('@vueuse/core')['toReactive']
  const toRef: typeof import('vue')['toRef']
  const toRefs: typeof import('vue')['toRefs']
  const toValue: typeof import('vue')['toValue']
  const triggerRef: typeof import('vue')['triggerRef']
  const tryOnBeforeMount: typeof import('@vueuse/core')['tryOnBeforeMount']
  const tryOnBeforeUnmount: typeof import('@vueuse/core')['tryOnBeforeUnmount']
  const tryOnMounted: typeof import('@vueuse/core')['tryOnMounted']
  const tryOnScopeDispose: typeof import('@vueuse/core')['tryOnScopeDispose']
  const tryOnUnmounted: typeof import('@vueuse/core')['tryOnUnmounted']
  const unref: typeof import('vue')['unref']
  const unrefElement: typeof import('@vueuse/core')['unrefElement']
  const until: typeof import('@vueuse/core')['until']
  const upOption: typeof import('./utils/mescroll')['upOption']
  const useActiveElement: typeof import('@vueuse/core')['useActiveElement']
  const useAnimate: typeof import('@vueuse/core')['useAnimate']
  const useAppButtonPermission: typeof import('./composables/usePermission')['useAppButtonPermission']
  const useAppPagePermission: typeof import('./composables/usePermission')['useAppPagePermission']
  const useAppPermission: typeof import('./composables/usePermission')['useAppPermission']
  const useAppPermissionGuard: typeof import('./composables/usePermission')['useAppPermissionGuard']
  const useArrayDifference: typeof import('@vueuse/core')['useArrayDifference']
  const useArrayEvery: typeof import('@vueuse/core')['useArrayEvery']
  const useArrayFilter: typeof import('@vueuse/core')['useArrayFilter']
  const useArrayFind: typeof import('@vueuse/core')['useArrayFind']
  const useArrayFindIndex: typeof import('@vueuse/core')['useArrayFindIndex']
  const useArrayFindLast: typeof import('@vueuse/core')['useArrayFindLast']
  const useArrayIncludes: typeof import('@vueuse/core')['useArrayIncludes']
  const useArrayJoin: typeof import('@vueuse/core')['useArrayJoin']
  const useArrayMap: typeof import('@vueuse/core')['useArrayMap']
  const useArrayReduce: typeof import('@vueuse/core')['useArrayReduce']
  const useArraySome: typeof import('@vueuse/core')['useArraySome']
  const useArrayUnique: typeof import('@vueuse/core')['useArrayUnique']
  const useAsyncQueue: typeof import('@vueuse/core')['useAsyncQueue']
  const useAsyncState: typeof import('@vueuse/core')['useAsyncState']
  const useAttrs: typeof import('vue')['useAttrs']
  const useBase64: typeof import('@vueuse/core')['useBase64']
  const useBattery: typeof import('@vueuse/core')['useBattery']
  const useBluetooth: typeof import('@vueuse/core')['useBluetooth']
  const useBreakpoints: typeof import('@vueuse/core')['useBreakpoints']
  const useBroadcastChannel: typeof import('@vueuse/core')['useBroadcastChannel']
  const useBrowserLocation: typeof import('@vueuse/core')['useBrowserLocation']
  const useButtonPermission: typeof import('./composables/usePermission')['useButtonPermission']
  const useButtonPermissions: typeof import('./composables/usePermission')['useButtonPermissions']
  const useCached: typeof import('@vueuse/core')['useCached']
  const useCategorySelect: typeof import('./composables/useCategorySelect')['useCategorySelect']
  const useCheckIn: typeof import('./composables/useCheckIn')['default']
  const useClipboard: typeof import('@vueuse/core')['useClipboard']
  const useClipboardItems: typeof import('@vueuse/core')['useClipboardItems']
  const useCloned: typeof import('@vueuse/core')['useCloned']
  const useColorMode: typeof import('@vueuse/core')['useColorMode']
  const useConfirmDialog: typeof import('@vueuse/core')['useConfirmDialog']
  const useCounter: typeof import('@vueuse/core')['useCounter']
  const useCssModule: typeof import('vue')['useCssModule']
  const useCssVar: typeof import('@vueuse/core')['useCssVar']
  const useCssVars: typeof import('vue')['useCssVars']
  const useCurrentElement: typeof import('@vueuse/core')['useCurrentElement']
  const useCycleList: typeof import('@vueuse/core')['useCycleList']
  const useDark: typeof import('@vueuse/core')['useDark']
  const useDateFormat: typeof import('@vueuse/core')['useDateFormat']
  const useDebounce: typeof import('@vueuse/core')['useDebounce']
  const useDebounceFn: typeof import('@vueuse/core')['useDebounceFn']
  const useDebouncedRefHistory: typeof import('@vueuse/core')['useDebouncedRefHistory']
  const useDeviceInfo: typeof import('./store/useDeviceInfo')['useDeviceInfo']
  const useDeviceMotion: typeof import('@vueuse/core')['useDeviceMotion']
  const useDeviceOrientation: typeof import('@vueuse/core')['useDeviceOrientation']
  const useDevicePixelRatio: typeof import('@vueuse/core')['useDevicePixelRatio']
  const useDevicesList: typeof import('@vueuse/core')['useDevicesList']
  const useDisplayMedia: typeof import('@vueuse/core')['useDisplayMedia']
  const useDocumentVisibility: typeof import('@vueuse/core')['useDocumentVisibility']
  const useDraft: typeof import('./composables/useDraft')['useDraft']
  const useDraggable: typeof import('@vueuse/core')['useDraggable']
  const useDropZone: typeof import('@vueuse/core')['useDropZone']
  const useElementBounding: typeof import('@vueuse/core')['useElementBounding']
  const useElementByPoint: typeof import('@vueuse/core')['useElementByPoint']
  const useElementHover: typeof import('@vueuse/core')['useElementHover']
  const useElementSize: typeof import('@vueuse/core')['useElementSize']
  const useElementVisibility: typeof import('@vueuse/core')['useElementVisibility']
  const useEventBus: typeof import('@vueuse/core')['useEventBus']
  const useEventListener: typeof import('@vueuse/core')['useEventListener']
  const useEventSource: typeof import('@vueuse/core')['useEventSource']
  const useEyeDropper: typeof import('@vueuse/core')['useEyeDropper']
  const useFavicon: typeof import('@vueuse/core')['useFavicon']
  const useFetch: typeof import('@vueuse/core')['useFetch']
  const useFileDialog: typeof import('@vueuse/core')['useFileDialog']
  const useFileSystemAccess: typeof import('@vueuse/core')['useFileSystemAccess']
  const useFocus: typeof import('@vueuse/core')['useFocus']
  const useFocusWithin: typeof import('@vueuse/core')['useFocusWithin']
  const useFps: typeof import('@vueuse/core')['useFps']
  const useFullscreen: typeof import('@vueuse/core')['useFullscreen']
  const useGamepad: typeof import('@vueuse/core')['useGamepad']
  const useGeolocation: typeof import('@vueuse/core')['useGeolocation']
  const useGlobalLoading: typeof import('./composables/useGlobalLoading')['useGlobalLoading']
  const useGlobalMessage: typeof import('./composables/useGlobalMessage')['useGlobalMessage']
  const useGlobalToast: typeof import('./composables/useGlobalToast')['useGlobalToast']
  const useIdle: typeof import('@vueuse/core')['useIdle']
  const useImage: typeof import('@vueuse/core')['useImage']
  const useInfiniteScroll: typeof import('@vueuse/core')['useInfiniteScroll']
  const useIntersectionObserver: typeof import('@vueuse/core')['useIntersectionObserver']
  const useInterval: typeof import('@vueuse/core')['useInterval']
  const useIntervalFn: typeof import('@vueuse/core')['useIntervalFn']
  const useKeyModifier: typeof import('@vueuse/core')['useKeyModifier']
  const useLastChanged: typeof import('@vueuse/core')['useLastChanged']
  const useLocalStorage: typeof import('@vueuse/core')['useLocalStorage']
  const useLockPage: typeof import('./composables/useLockPage')['useLockPage']
  const useMagicKeys: typeof import('@vueuse/core')['useMagicKeys']
  const useManualRefHistory: typeof import('@vueuse/core')['useManualRefHistory']
  const useMediaControls: typeof import('@vueuse/core')['useMediaControls']
  const useMediaQuery: typeof import('@vueuse/core')['useMediaQuery']
  const useMemoize: typeof import('@vueuse/core')['useMemoize']
  const useMemory: typeof import('@vueuse/core')['useMemory']
  const useMessage: typeof import('wot-design-uni')['useMessage']
  const useMounted: typeof import('@vueuse/core')['useMounted']
  const useMouse: typeof import('@vueuse/core')['useMouse']
  const useMouseInElement: typeof import('@vueuse/core')['useMouseInElement']
  const useMousePressed: typeof import('@vueuse/core')['useMousePressed']
  const useMutationObserver: typeof import('@vueuse/core')['useMutationObserver']
  const useNavigatorLanguage: typeof import('@vueuse/core')['useNavigatorLanguage']
  const useNetwork: typeof import('@vueuse/core')['useNetwork']
  const useNotify: typeof import('wot-design-uni')['useNotify']
  const useNow: typeof import('@vueuse/core')['useNow']
  const useObjectUrl: typeof import('@vueuse/core')['useObjectUrl']
  const useOffsetPagination: typeof import('@vueuse/core')['useOffsetPagination']
  const useOnline: typeof import('@vueuse/core')['useOnline']
  const useOssInfo: typeof import('./composables/UseOssInfo')['useOssInfo']
  const usePageLeave: typeof import('@vueuse/core')['usePageLeave']
  const usePagePermission: typeof import('./composables/usePermission')['usePagePermission']
  const usePagePermissions: typeof import('./composables/usePermission')['usePagePermissions']
  const usePagination: typeof import('alova/client')['usePagination']
  const useParallax: typeof import('@vueuse/core')['useParallax']
  const useParentElement: typeof import('@vueuse/core')['useParentElement']
  const usePerformanceObserver: typeof import('@vueuse/core')['usePerformanceObserver']
  const usePermission: typeof import('./composables/usePermission')['usePermission']
  const usePermissionChecker: typeof import('./composables/usePermission')['usePermissionChecker']
  const usePermissionGuard: typeof import('./composables/usePermission')['usePermissionGuard']
  const usePermissions: typeof import('./composables/usePermission')['usePermissions']
  const usePointer: typeof import('@vueuse/core')['usePointer']
  const usePointerLock: typeof import('@vueuse/core')['usePointerLock']
  const usePointerSwipe: typeof import('@vueuse/core')['usePointerSwipe']
  const usePreferredColorScheme: typeof import('@vueuse/core')['usePreferredColorScheme']
  const usePreferredContrast: typeof import('@vueuse/core')['usePreferredContrast']
  const usePreferredDark: typeof import('@vueuse/core')['usePreferredDark']
  const usePreferredLanguages: typeof import('@vueuse/core')['usePreferredLanguages']
  const usePreferredReducedMotion: typeof import('@vueuse/core')['usePreferredReducedMotion']
  const usePrevious: typeof import('@vueuse/core')['usePrevious']
  const usePrinterStore: typeof import('./store/usePrinterStore')['usePrinterStore']
  const useRafFn: typeof import('@vueuse/core')['useRafFn']
  const useRefHistory: typeof import('@vueuse/core')['useRefHistory']
  const useRequest: typeof import('alova/client')['useRequest']
  const useResizeObserver: typeof import('@vueuse/core')['useResizeObserver']
  const useRoute: typeof import('uni-mini-router')['useRoute']
  const useRouter: typeof import('uni-mini-router')['useRouter']
  const useSalesStore: typeof import('./store/useSalesStore')['useSalesStore']
  const useScreenOrientation: typeof import('@vueuse/core')['useScreenOrientation']
  const useScreenSafeArea: typeof import('@vueuse/core')['useScreenSafeArea']
  const useScriptTag: typeof import('@vueuse/core')['useScriptTag']
  const useScroll: typeof import('@vueuse/core')['useScroll']
  const useScrollLock: typeof import('@vueuse/core')['useScrollLock']
  const useSearchHistoryStore: typeof import('./store/useSearchHistoryStore')['useSearchHistoryStore']
  const useSendRedirect: typeof import('./composables/useRefreshData')['useSendRedirect']
  const useSendRefreshData: typeof import('./composables/useRefreshData')['useSendRefreshData']
  const useSessionStorage: typeof import('@vueuse/core')['useSessionStorage']
  const useShare: typeof import('@vueuse/core')['useShare']
  const useSlots: typeof import('vue')['useSlots']
  const useSortLineSelect: typeof import('./composables/useSortLineSelect')['useSortLineSelect']
  const useSorted: typeof import('@vueuse/core')['useSorted']
  const useSpeechRecognition: typeof import('@vueuse/core')['useSpeechRecognition']
  const useSpeechSynthesis: typeof import('@vueuse/core')['useSpeechSynthesis']
  const useStepper: typeof import('@vueuse/core')['useStepper']
  const useStorage: typeof import('@vueuse/core')['useStorage']
  const useStorageAsync: typeof import('@vueuse/core')['useStorageAsync']
  const useStoreSelect: typeof import('./composables/useStoreSelect')['useStoreSelect']
  const useStyleTag: typeof import('@vueuse/core')['useStyleTag']
  const useSupported: typeof import('@vueuse/core')['useSupported']
  const useSwipe: typeof import('@vueuse/core')['useSwipe']
  const useTabbar: typeof import('./composables/useTabbar')['useTabbar']
  const useTemplateRefsList: typeof import('@vueuse/core')['useTemplateRefsList']
  const useTextDirection: typeof import('@vueuse/core')['useTextDirection']
  const useTextSelection: typeof import('@vueuse/core')['useTextSelection']
  const useTextareaAutosize: typeof import('@vueuse/core')['useTextareaAutosize']
  const useTheme: typeof import('./composables/useTheme')['useTheme']
  const useThrottle: typeof import('@vueuse/core')['useThrottle']
  const useThrottleFn: typeof import('@vueuse/core')['useThrottleFn']
  const useThrottledRefHistory: typeof import('@vueuse/core')['useThrottledRefHistory']
  const useTimeAgo: typeof import('@vueuse/core')['useTimeAgo']
  const useTimeout: typeof import('@vueuse/core')['useTimeout']
  const useTimeoutFn: typeof import('@vueuse/core')['useTimeoutFn']
  const useTimeoutPoll: typeof import('@vueuse/core')['useTimeoutPoll']
  const useTimestamp: typeof import('@vueuse/core')['useTimestamp']
  const useTitle: typeof import('@vueuse/core')['useTitle']
  const useToNumber: typeof import('@vueuse/core')['useToNumber']
  const useToString: typeof import('@vueuse/core')['useToString']
  const useToast: typeof import('wot-design-uni')['useToast']
  const useToggle: typeof import('@vueuse/core')['useToggle']
  const useTransition: typeof import('@vueuse/core')['useTransition']
  const useUrlSearchParams: typeof import('@vueuse/core')['useUrlSearchParams']
  const useUserMedia: typeof import('@vueuse/core')['useUserMedia']
  const useUserStore: typeof import('./store/useUserStore')['useUserStore']
  const useVModel: typeof import('@vueuse/core')['useVModel']
  const useVModels: typeof import('@vueuse/core')['useVModels']
  const useVehSaleEmpSelect: typeof import('./composables/useVehSaleEmpSelect')['useVehSaleEmpSelect']
  const useVibrate: typeof import('@vueuse/core')['useVibrate']
  const useVirtualList: typeof import('@vueuse/core')['useVirtualList']
  const useWakeLock: typeof import('@vueuse/core')['useWakeLock']
  const useWatchReditct: typeof import('./composables/useRefreshData')['useWatchReditct']
  const useWatchRefreshData: typeof import('./composables/useRefreshData')['useWatchRefreshData']
  const useWebNotification: typeof import('@vueuse/core')['useWebNotification']
  const useWebSocket: typeof import('@vueuse/core')['useWebSocket']
  const useWebWorker: typeof import('@vueuse/core')['useWebWorker']
  const useWebWorkerFn: typeof import('@vueuse/core')['useWebWorkerFn']
  const useWindowFocus: typeof import('@vueuse/core')['useWindowFocus']
  const useWindowScroll: typeof import('@vueuse/core')['useWindowScroll']
  const useWindowSize: typeof import('@vueuse/core')['useWindowSize']
  const useWmsSelect: typeof import('./composables/useWmsSelect')['useWmsSelect']
  const useWmsStore: typeof import('./store/useWmsStore')['useWmsStore']
  const useWrhSelect: typeof import('./composables/useWrhSelect')['useWrhSelect']
  const watch: typeof import('vue')['watch']
  const watchArray: typeof import('@vueuse/core')['watchArray']
  const watchAtMost: typeof import('@vueuse/core')['watchAtMost']
  const watchDebounced: typeof import('@vueuse/core')['watchDebounced']
  const watchDeep: typeof import('@vueuse/core')['watchDeep']
  const watchEffect: typeof import('vue')['watchEffect']
  const watchIgnorable: typeof import('@vueuse/core')['watchIgnorable']
  const watchImmediate: typeof import('@vueuse/core')['watchImmediate']
  const watchOnce: typeof import('@vueuse/core')['watchOnce']
  const watchPausable: typeof import('@vueuse/core')['watchPausable']
  const watchPostEffect: typeof import('vue')['watchPostEffect']
  const watchSyncEffect: typeof import('vue')['watchSyncEffect']
  const watchThrottled: typeof import('@vueuse/core')['watchThrottled']
  const watchTriggerable: typeof import('@vueuse/core')['watchTriggerable']
  const watchWithFilter: typeof import('@vueuse/core')['watchWithFilter']
  const whenever: typeof import('@vueuse/core')['whenever']
}
// for type re-export
declare global {
  // @ts-ignore
  export type { Component, ComponentPublicInstance, ComputedRef, ExtractDefaultPropTypes, ExtractPropTypes, ExtractPublicPropTypes, InjectionKey, PropType, Ref, VNode, WritableComputedRef } from 'vue'
  import('vue')
}
// for vue template auto import
import { UnwrapRef } from 'vue'
declare module 'vue' {
  interface GlobalComponents {}
  interface ComponentCustomProperties {
    readonly BUTTON_PERMISSIONS: UnwrapRef<typeof import('./utils/permissions')['BUTTON_PERMISSIONS']>
    readonly CommonUtil: UnwrapRef<typeof import('wot-design-uni')['CommonUtil']>
    readonly EffectScope: UnwrapRef<typeof import('vue')['EffectScope']>
    readonly PERMISSIONS: UnwrapRef<typeof import('./utils/permissions')['PERMISSIONS']>
    readonly PERMISSION_GROUPS: UnwrapRef<typeof import('./utils/permissions')['PERMISSION_GROUPS']>
    readonly acceptHMRUpdate: UnwrapRef<typeof import('pinia')['acceptHMRUpdate']>
    readonly clearAuth: UnwrapRef<typeof import('./utils/auth')['clearAuth']>
    readonly computed: UnwrapRef<typeof import('vue')['computed']>
    readonly createApp: UnwrapRef<typeof import('vue')['createApp']>
    readonly createPinia: UnwrapRef<typeof import('pinia')['createPinia']>
    readonly createRouter: UnwrapRef<typeof import('uni-mini-router')['createRouter']>
    readonly customRef: UnwrapRef<typeof import('vue')['customRef']>
    readonly date: UnwrapRef<typeof import('./utils/date')['default']>
    readonly defineAsyncComponent: UnwrapRef<typeof import('vue')['defineAsyncComponent']>
    readonly defineComponent: UnwrapRef<typeof import('vue')['defineComponent']>
    readonly defineStore: UnwrapRef<typeof import('pinia')['defineStore']>
    readonly downOption: UnwrapRef<typeof import('./utils/mescroll')['downOption']>
    readonly effectScope: UnwrapRef<typeof import('vue')['effectScope']>
    readonly encryptPwd: UnwrapRef<typeof import('./utils/util')['encryptPwd']>
    readonly formatDate: UnwrapRef<typeof import('./utils/date')['formatDate']>
    readonly formatPrice: UnwrapRef<typeof import('./utils/util')['formatPrice']>
    readonly fullDate: UnwrapRef<typeof import('./utils/date')['fullDate']>
    readonly getActivePinia: UnwrapRef<typeof import('pinia')['getActivePinia']>
    readonly getBillStatus: UnwrapRef<typeof import('./utils/bill')['getBillStatus']>
    readonly getBillStatusColor: UnwrapRef<typeof import('./utils/bill')['getBillStatusColor']>
    readonly getBillStatusText: UnwrapRef<typeof import('./utils/bill')['getBillStatusText']>
    readonly getCurrentInstance: UnwrapRef<typeof import('vue')['getCurrentInstance']>
    readonly getCurrentPath: UnwrapRef<typeof import('./utils/util')['getCurrentPath']>
    readonly getCurrentScope: UnwrapRef<typeof import('vue')['getCurrentScope']>
    readonly getMN: UnwrapRef<typeof import('./utils/util')['getMN']>
    readonly getQpcQty: UnwrapRef<typeof import('./utils/util')['getQpcQty']>
    readonly getRefreshToken: UnwrapRef<typeof import('./utils/auth')['getRefreshToken']>
    readonly getToken: UnwrapRef<typeof import('./utils/auth')['getToken']>
    readonly getTokenExpires: UnwrapRef<typeof import('./utils/auth')['getTokenExpires']>
    readonly h: UnwrapRef<typeof import('vue')['h']>
    readonly initNumberPrototype: UnwrapRef<typeof import('./utils/numberPrototype')['initNumberPrototype']>
    readonly inject: UnwrapRef<typeof import('vue')['inject']>
    readonly isProxy: UnwrapRef<typeof import('vue')['isProxy']>
    readonly isReactive: UnwrapRef<typeof import('vue')['isReactive']>
    readonly isReadonly: UnwrapRef<typeof import('vue')['isReadonly']>
    readonly isRef: UnwrapRef<typeof import('vue')['isRef']>
    readonly isTokenExpired: UnwrapRef<typeof import('./utils/auth')['isTokenExpired']>
    readonly mapActions: UnwrapRef<typeof import('pinia')['mapActions']>
    readonly mapGetters: UnwrapRef<typeof import('pinia')['mapGetters']>
    readonly mapState: UnwrapRef<typeof import('pinia')['mapState']>
    readonly mapStores: UnwrapRef<typeof import('pinia')['mapStores']>
    readonly mapWritableState: UnwrapRef<typeof import('pinia')['mapWritableState']>
    readonly markRaw: UnwrapRef<typeof import('vue')['markRaw']>
    readonly nextTick: UnwrapRef<typeof import('vue')['nextTick']>
    readonly onActivated: UnwrapRef<typeof import('vue')['onActivated']>
    readonly onAddToFavorites: UnwrapRef<typeof import('@dcloudio/uni-app')['onAddToFavorites']>
    readonly onBackPress: UnwrapRef<typeof import('@dcloudio/uni-app')['onBackPress']>
    readonly onBeforeMount: UnwrapRef<typeof import('vue')['onBeforeMount']>
    readonly onBeforeUnmount: UnwrapRef<typeof import('vue')['onBeforeUnmount']>
    readonly onBeforeUpdate: UnwrapRef<typeof import('vue')['onBeforeUpdate']>
    readonly onDeactivated: UnwrapRef<typeof import('vue')['onDeactivated']>
    readonly onError: UnwrapRef<typeof import('@dcloudio/uni-app')['onError']>
    readonly onErrorCaptured: UnwrapRef<typeof import('vue')['onErrorCaptured']>
    readonly onHide: UnwrapRef<typeof import('@dcloudio/uni-app')['onHide']>
    readonly onLaunch: UnwrapRef<typeof import('@dcloudio/uni-app')['onLaunch']>
    readonly onLoad: UnwrapRef<typeof import('@dcloudio/uni-app')['onLoad']>
    readonly onMounted: UnwrapRef<typeof import('vue')['onMounted']>
    readonly onNavigationBarButtonTap: UnwrapRef<typeof import('@dcloudio/uni-app')['onNavigationBarButtonTap']>
    readonly onNavigationBarSearchInputChanged: UnwrapRef<typeof import('@dcloudio/uni-app')['onNavigationBarSearchInputChanged']>
    readonly onNavigationBarSearchInputClicked: UnwrapRef<typeof import('@dcloudio/uni-app')['onNavigationBarSearchInputClicked']>
    readonly onNavigationBarSearchInputConfirmed: UnwrapRef<typeof import('@dcloudio/uni-app')['onNavigationBarSearchInputConfirmed']>
    readonly onNavigationBarSearchInputFocusChanged: UnwrapRef<typeof import('@dcloudio/uni-app')['onNavigationBarSearchInputFocusChanged']>
    readonly onPageNotFound: UnwrapRef<typeof import('@dcloudio/uni-app')['onPageNotFound']>
    readonly onPageScroll: UnwrapRef<typeof import('@dcloudio/uni-app')['onPageScroll']>
    readonly onPullDownRefresh: UnwrapRef<typeof import('@dcloudio/uni-app')['onPullDownRefresh']>
    readonly onReachBottom: UnwrapRef<typeof import('@dcloudio/uni-app')['onReachBottom']>
    readonly onReady: UnwrapRef<typeof import('@dcloudio/uni-app')['onReady']>
    readonly onRenderTracked: UnwrapRef<typeof import('vue')['onRenderTracked']>
    readonly onRenderTriggered: UnwrapRef<typeof import('vue')['onRenderTriggered']>
    readonly onResize: UnwrapRef<typeof import('@dcloudio/uni-app')['onResize']>
    readonly onScopeDispose: UnwrapRef<typeof import('vue')['onScopeDispose']>
    readonly onServerPrefetch: UnwrapRef<typeof import('vue')['onServerPrefetch']>
    readonly onShareAppMessage: UnwrapRef<typeof import('@dcloudio/uni-app')['onShareAppMessage']>
    readonly onShareTimeline: UnwrapRef<typeof import('@dcloudio/uni-app')['onShareTimeline']>
    readonly onShow: UnwrapRef<typeof import('@dcloudio/uni-app')['onShow']>
    readonly onTabItemTap: UnwrapRef<typeof import('@dcloudio/uni-app')['onTabItemTap']>
    readonly onThemeChange: UnwrapRef<typeof import('@dcloudio/uni-app')['onThemeChange']>
    readonly onUnhandledRejection: UnwrapRef<typeof import('@dcloudio/uni-app')['onUnhandledRejection']>
    readonly onUnload: UnwrapRef<typeof import('@dcloudio/uni-app')['onUnload']>
    readonly onUnmounted: UnwrapRef<typeof import('vue')['onUnmounted']>
    readonly onUpdated: UnwrapRef<typeof import('vue')['onUpdated']>
    readonly persistPlugin: UnwrapRef<typeof import('./store/persist')['persistPlugin']>
    readonly provide: UnwrapRef<typeof import('vue')['provide']>
    readonly reactive: UnwrapRef<typeof import('vue')['reactive']>
    readonly readonly: UnwrapRef<typeof import('vue')['readonly']>
    readonly ref: UnwrapRef<typeof import('vue')['ref']>
    readonly removeRefreshToken: UnwrapRef<typeof import('./utils/auth')['removeRefreshToken']>
    readonly removeToken: UnwrapRef<typeof import('./utils/auth')['removeToken']>
    readonly removeTokenExpires: UnwrapRef<typeof import('./utils/auth')['removeTokenExpires']>
    readonly resolveComponent: UnwrapRef<typeof import('vue')['resolveComponent']>
    readonly setActivePinia: UnwrapRef<typeof import('pinia')['setActivePinia']>
    readonly setMapStoreSuffix: UnwrapRef<typeof import('pinia')['setMapStoreSuffix']>
    readonly setRefreshToken: UnwrapRef<typeof import('./utils/auth')['setRefreshToken']>
    readonly setToken: UnwrapRef<typeof import('./utils/auth')['setToken']>
    readonly setTokenExpires: UnwrapRef<typeof import('./utils/auth')['setTokenExpires']>
    readonly shallowReactive: UnwrapRef<typeof import('vue')['shallowReactive']>
    readonly shallowReadonly: UnwrapRef<typeof import('vue')['shallowReadonly']>
    readonly shallowRef: UnwrapRef<typeof import('vue')['shallowRef']>
    readonly storeToRefs: UnwrapRef<typeof import('pinia')['storeToRefs']>
    readonly toRaw: UnwrapRef<typeof import('vue')['toRaw']>
    readonly toRef: UnwrapRef<typeof import('vue')['toRef']>
    readonly toRefs: UnwrapRef<typeof import('vue')['toRefs']>
    readonly toValue: UnwrapRef<typeof import('vue')['toValue']>
    readonly triggerRef: UnwrapRef<typeof import('vue')['triggerRef']>
    readonly unref: UnwrapRef<typeof import('vue')['unref']>
    readonly upOption: UnwrapRef<typeof import('./utils/mescroll')['upOption']>
    readonly useAttrs: UnwrapRef<typeof import('vue')['useAttrs']>
    readonly useButtonPermission: UnwrapRef<typeof import('./composables/usePermission')['useButtonPermission']>
    readonly useCategorySelect: UnwrapRef<typeof import('./composables/useCategorySelect')['useCategorySelect']>
    readonly useCheckIn: UnwrapRef<typeof import('./composables/useCheckIn')['default']>
    readonly useCssModule: UnwrapRef<typeof import('vue')['useCssModule']>
    readonly useCssVars: UnwrapRef<typeof import('vue')['useCssVars']>
    readonly useDeviceInfo: UnwrapRef<typeof import('./store/useDeviceInfo')['useDeviceInfo']>
    readonly useDraft: UnwrapRef<typeof import('./composables/useDraft')['useDraft']>
    readonly useGlobalLoading: UnwrapRef<typeof import('./composables/useGlobalLoading')['useGlobalLoading']>
    readonly useGlobalMessage: UnwrapRef<typeof import('./composables/useGlobalMessage')['useGlobalMessage']>
    readonly useGlobalToast: UnwrapRef<typeof import('./composables/useGlobalToast')['useGlobalToast']>
    readonly useLockPage: UnwrapRef<typeof import('./composables/useLockPage')['useLockPage']>
    readonly useMessage: UnwrapRef<typeof import('wot-design-uni')['useMessage']>
    readonly useNotify: UnwrapRef<typeof import('wot-design-uni')['useNotify']>
    readonly useOssInfo: UnwrapRef<typeof import('./composables/UseOssInfo')['useOssInfo']>
    readonly usePagePermission: UnwrapRef<typeof import('./composables/usePermission')['usePagePermission']>
    readonly usePagination: UnwrapRef<typeof import('alova/client')['usePagination']>
    readonly usePermission: UnwrapRef<typeof import('./composables/usePermission')['usePermission']>
    readonly usePermissionChecker: UnwrapRef<typeof import('./composables/usePermission')['usePermissionChecker']>
    readonly usePermissionGuard: UnwrapRef<typeof import('./composables/usePermission')['usePermissionGuard']>
    readonly usePrinterStore: UnwrapRef<typeof import('./store/usePrinterStore')['usePrinterStore']>
    readonly useRequest: UnwrapRef<typeof import('alova/client')['useRequest']>
    readonly useRoute: UnwrapRef<typeof import('uni-mini-router')['useRoute']>
    readonly useRouter: UnwrapRef<typeof import('uni-mini-router')['useRouter']>
    readonly useSalesStore: UnwrapRef<typeof import('./store/useSalesStore')['useSalesStore']>
    readonly useSearchHistoryStore: UnwrapRef<typeof import('./store/useSearchHistoryStore')['useSearchHistoryStore']>
    readonly useSendRedirect: UnwrapRef<typeof import('./composables/useRefreshData')['useSendRedirect']>
    readonly useSendRefreshData: UnwrapRef<typeof import('./composables/useRefreshData')['useSendRefreshData']>
    readonly useSlots: UnwrapRef<typeof import('vue')['useSlots']>
    readonly useSortLineSelect: UnwrapRef<typeof import('./composables/useSortLineSelect')['useSortLineSelect']>
    readonly useStoreSelect: UnwrapRef<typeof import('./composables/useStoreSelect')['useStoreSelect']>
    readonly useTabbar: UnwrapRef<typeof import('./composables/useTabbar')['useTabbar']>
    readonly useTheme: UnwrapRef<typeof import('./composables/useTheme')['useTheme']>
    readonly useToast: UnwrapRef<typeof import('wot-design-uni')['useToast']>
    readonly useUserStore: UnwrapRef<typeof import('./store/useUserStore')['useUserStore']>
    readonly useVehSaleEmpSelect: UnwrapRef<typeof import('./composables/useVehSaleEmpSelect')['useVehSaleEmpSelect']>
    readonly useWatchReditct: UnwrapRef<typeof import('./composables/useRefreshData')['useWatchReditct']>
    readonly useWatchRefreshData: UnwrapRef<typeof import('./composables/useRefreshData')['useWatchRefreshData']>
    readonly useWmsSelect: UnwrapRef<typeof import('./composables/useWmsSelect')['useWmsSelect']>
    readonly useWmsStore: UnwrapRef<typeof import('./store/useWmsStore')['useWmsStore']>
    readonly useWrhSelect: UnwrapRef<typeof import('./composables/useWrhSelect')['useWrhSelect']>
    readonly watch: UnwrapRef<typeof import('vue')['watch']>
    readonly watchEffect: UnwrapRef<typeof import('vue')['watchEffect']>
    readonly watchPostEffect: UnwrapRef<typeof import('vue')['watchPostEffect']>
    readonly watchSyncEffect: UnwrapRef<typeof import('vue')['watchSyncEffect']>
  }
}
