/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by vite-plugin-uni-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    BackgroundGradient: typeof import('./components/BackgroundGradient.vue')['default']
    CartBar: typeof import('./business/CartBar.vue')['default']
    CartNavBar: typeof import('./business/CartNavBar.vue')['default']
    CartSkeleton: typeof import('./business/CartSkeleton.vue')['default']
    DetailInfoCard: typeof import('./business/DetailInfoCard.vue')['default']
    DetailRelatedBills: typeof import('./business/DetailRelatedBills.vue')['default']
    DetailSkeleton: typeof import('./business/DetailSkeleton.vue')['default']
    DetailStatsCard: typeof import('./business/DetailStatsCard.vue')['default']
    DetailStatusHeader: typeof import('./business/DetailStatusHeader.vue')['default']
    DetailTableHeader: typeof import('./business/DetailTableHeader.vue')['default']
    DraftSubmitVerify: typeof import('./business/DraftSubmitVerify.vue')['default']
    EditSort: typeof import('./business/EditSort.vue')['default']
    EmptyStatus: typeof import('./components/EmptyStatus.vue')['default']
    GlobalLoading: typeof import('./components/GlobalLoading.vue')['default']
    GlobalMessage: typeof import('./components/GlobalMessage.vue')['default']
    GlobalToast: typeof import('./components/GlobalToast.vue')['default']
    NavSearchBar: typeof import('./components/NavSearchBar.vue')['default']
    PermissionWrapper: typeof import('./components/PermissionWrapper.vue')['default']
    PrivacyPopup: typeof import('./components/PrivacyPopup.vue')['default']
    ReturnVerifyDialog: typeof import('./business/ReturnVerifyDialog.vue')['default']
    SalesListSkeleton: typeof import('./business/SalesListSkeleton.vue')['default']
    SearchBar: typeof import('./components/SearchBar.vue')['default']
    SkuImage: typeof import('./components/SkuImage.vue')['default']
    SkuListSkeleton: typeof import('./business/SkuListSkeleton.vue')['default']
    TimeFilter: typeof import('./components/TimeFilter.vue')['default']
    UploadImgPop: typeof import('./business/UploadImgPop.vue')['default']
    WdButton: typeof import('wot-design-uni/components/wd-button/wd-button.vue')['default']
    WdCell: typeof import('wot-design-uni/components/wd-cell/wd-cell.vue')['default']
    WdCellGroup: typeof import('wot-design-uni/components/wd-cell-group/wd-cell-group.vue')['default']
    WdCheckbox: typeof import('wot-design-uni/components/wd-checkbox/wd-checkbox.vue')['default']
    WdConfigProvider: typeof import('wot-design-uni/components/wd-config-provider/wd-config-provider.vue')['default']
    WdForm: typeof import('wot-design-uni/components/wd-form/wd-form.vue')['default']
    WdGap: typeof import('wot-design-uni/components/wd-gap/wd-gap.vue')['default']
    WdIcon: typeof import('wot-design-uni/components/wd-icon/wd-icon.vue')['default']
    WdLoading: typeof import('wot-design-uni/components/wd-loading/wd-loading.vue')['default']
    WdMessageBox: typeof import('wot-design-uni/components/wd-message-box/wd-message-box.vue')['default']
    WdNavbar: typeof import('wot-design-uni/components/wd-navbar/wd-navbar.vue')['default']
    WdNoticeBar: typeof import('wot-design-uni/components/wd-notice-bar/wd-notice-bar.vue')['default']
    WdNotify: typeof import('wot-design-uni/components/wd-notify/wd-notify.vue')['default']
    WdSelectPicker: typeof import('wot-design-uni/components/wd-select-picker/wd-select-picker.vue')['default']
    WdTab: typeof import('wot-design-uni/components/wd-tab/wd-tab.vue')['default']
    WdTabbar: typeof import('wot-design-uni/components/wd-tabbar/wd-tabbar.vue')['default']
    WdTabbarItem: typeof import('wot-design-uni/components/wd-tabbar-item/wd-tabbar-item.vue')['default']
    WdTabs: typeof import('wot-design-uni/components/wd-tabs/wd-tabs.vue')['default']
    WdTag: typeof import('wot-design-uni/components/wd-tag/wd-tag.vue')['default']
    WdToast: typeof import('wot-design-uni/components/wd-toast/wd-toast.vue')['default']
  }
}
