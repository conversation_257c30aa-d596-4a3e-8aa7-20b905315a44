<!--
 * @Author: we<PERSON><PERSON>
 * @Date: 2025-05-15 16:05:00
 * @LastEditTime: 2025-06-25 15:58:26
 * @LastEditors: weisheng
 * @Description: 物流中心选择页面
 * @FilePath: /lsym-cx-mini/src/pagesBase/login/wms-select.vue
-->
<script setup lang="ts">
import { onMounted, ref } from 'vue'
import BackgroundGradient from '@/components/BackgroundGradient.vue'

const router = useRouter()
const route = useRoute()
const wmsStore = useWmsStore()

const { warning: showWarning } = useGlobalToast()

// 使用物流中心选择组合式API
const { wmsList, wmsLoading, wmsId, handleWmsSelect, currentWms } = useWmsSelect()

const wmsDataList = computed(() => {
  return wmsList.value.filter(item => item.value !== '')
})

// 判断是否从个人中心页面进入
const isFromProfile = computed(() => {
  return route.params?.from === 'profile'
})

function handleBack() {
  if (isFromProfile.value) {
    router.back()
  }
}

// 处理确认按钮点击
function handleConfirm() {
  if (!wmsId.value) {
    showWarning('请选择一个物流中心')
    return
  }

  if (currentWms.value) {
    // 解析标签中的名称和编码
    const labelMatch = currentWms.value.label.match(/^([^[]+)\[([^[\]]+)\]$/)
    let name = currentWms.value.label
    let code = ''

    if (labelMatch && labelMatch.length >= 3) {
      name = labelMatch[1]
      code = labelMatch[2]
    }

    // 保存到store
    wmsStore.setCurrentWms({
      gid: Number(currentWms.value.value),
      name,
      code,
    })

    // 如果是从个人中心页面进入，则返回个人中心页面
    if (isFromProfile.value) {
      router.back()
    }
    else {
      // 否则导航到工作台
      router.replaceAll({
        name: 'warehouse-dashboard',
      })
    }
  }
}

// 初始化时检查是否已有选中的物流中心
onMounted(() => {
  if (wmsStore.currentWms) {
    wmsId.value = `${wmsStore.currentWms.gid}`
  }
})

const scrollTop = ref(0)

onPageScroll((event) => {
  scrollTop.value = event.scrollTop
})

const navbarStyle = computed(() => {
  const opacity = Math.min(scrollTop.value / 100, 1)
  return `background-color: rgba(255, 255, 255, ${opacity}); transition: background-color 0.3s;`
})
</script>

<template>
  <view class="wms-select min-h-screen flex flex-col" style="--wot-checkbox-large-size: 40rpx;--wot-checkbox-icon-size: 32rpx;">
    <wd-navbar
      :bordered="false"
      :left-arrow="isFromProfile"
      safe-area-inset-top placeholder fixed
      title="选择物流中心"
      :custom-style="navbarStyle"
      @click-left="handleBack"
    />

    <!-- 使用背景组件 -->
    <BackgroundGradient />
    <!-- 物流中心列表 -->
    <view class="relative z-10 flex-1 px-3 pt-3">
      <view v-if="wmsLoading" class="flex items-center justify-center py-8">
        <wd-loading size="32px" class="text-primary" />
      </view>
      <view v-else class="space-y-2">
        <view
          v-for="(wms, index) in wmsDataList"
          :key="index"
          class="flex items-center justify-between border border-white rounded-lg border-solid bg-white p-4"
          :class="{ 'border-[var(--frequentapplication-primary-content)]!': wmsId === wms.value }"
          @click="handleWmsSelect({ value: wms.value, selectedItems: wms })"
        >
          <text class="break-all text-4 text-[var(--textapplication-text-1)] font-500">
            {{ wms.label }}
          </text>
          <wd-checkbox size="large" :model-value="wmsId === wms.value" custom-style="margin:0;" />
        </view>
      </view>
    </view>
    <wd-gap height="156rpx" />
    <!-- 底部按钮 -->
    <view class="fixed bottom-0 left-0 right-0 z-11 box-border w-100vw bg-white px-3 pt-2 shadow-[0_-4px_12px_0_rgba(0,0,0,0.05)]">
      <wd-button
        type="primary"
        size="large"
        custom-class="h-88rpx!"
        block
        :round="false"
        @click="handleConfirm"
      >
        确定
      </wd-button>
      <wd-gap height="28rpx" />
    </view>
  </view>
</template>

<style lang="scss" scoped>
.wms-select {
  position: relative;
  z-index: 0;
}
</style>

<route lang="json">
{
  "name": "wms-select",
  "style": {
    "navigationBarTitleText": "选择物流中心",
    "navigationStyle": "custom"
  }
}
</route>
