<!--
* @Author: we<PERSON><PERSON>
 * @Date: 2025-04-18 10:00:00
 * @LastEditTime: 2025-05-26 14:19:01
 * @LastEditors: weisheng
 * @Description: 登录页面
 * @FilePath: /lsym-cx-mini/src/pagesBase/login/login.vue
-->
<script setup lang="ts">
import { ref } from 'vue'
import { createGlobalLoadingMiddleware } from '@/api/core/middleware'
import type { LoginCredentials } from '@/api/globals'
import { usePermissionChecker } from '@/composables/usePermission'

const router = useRouter()
// 获取全局提示
const toast = useGlobalToast()

const { close: closeGlobalLoading } = useGlobalLoading()

// 获取用户存储
const userStore = useUserStore()

// 获取权限检查工具
const { debugPermissions } = usePermissionChecker()

// 表单数据
const username = ref('')
const password = ref('')
const showPassword = ref(false)

// 创建登录请求
const {
  send: sendLoginRequest,
} = useRequest(
  (data: LoginCredentials) => Apis.loginInterface.loginUsingPOST({
    data,
  }),
  {
    immediate: false,
    middleware: createGlobalLoadingMiddleware({
      loadingText: '登录中...',
    }),
  },
).onSuccess((event) => {
// 登录成功处理
  closeGlobalLoading()
  const response = event.data
  // 保存用户信息到 Pinia
  // 注意：userStore.setUser 会自动处理token的存储
  if (response.data) {
    // 根据roleCode判断用户类型：01-车销业务员，02-仓管
    const roleCode = response.data.roleCode || '01'
    const userType = roleCode === '01' ? '业务员' : '仓管'

    // 设置用户信息，不再传递activeTab
    userStore.setUser(response.data)

    // 开发环境下打印权限调试信息
    if (process.env.NODE_ENV === 'development') {
      setTimeout(() => {
        console.log('🔐 用户权限信息:')
        debugPermissions()
      }, 1000)
    }

    // 登录成功提示
    toast.success({
      duration: 500,
      msg: `${userType}登录成功`,
      closed() {
        // 根据用户类型跳转到不同页面
        if (roleCode === '02') {
          // 仓管跳转选择物流中心
          router.replace({
            name: 'wms-select',
          })
        }
        else {
          // 业务员跳转到首页
          router.replaceAll({
            name: 'salesman-home',
          })
        }
      },
    })
  }
  else {
    // 如果没有用户数据，显示错误
    toast.error('登录失败，服务器返回数据不完整')
  }
}).onError((error) => {
  // 登录失败处理
  closeGlobalLoading()
  toast.error(error.error?.message || '登录失败，请检查账号密码')
})

// 切换密码可见性
function togglePasswordVisibility() {
  showPassword.value = !showPassword.value
}

// 登录处理
function handleLogin() {
  if (!username.value) {
    toast.error('请输入员工工号')
    return
  }

  if (!password.value) {
    toast.error('请输入密码')
    return
  }

  // 发送登录请求
  sendLoginRequest({
    loginCode: username.value,
    password: encryptPwd(password.value),
  })
}
</script>

<template>
  <view class="login relative min-h-screen flex flex-col items-center overflow-hidden">
    <view class="bg-login-gradient absolute left-0 top-0 z-0 h-full w-full">
      <view class="bg-primary-blue/30 absolute h-80 w-80 rounded-full -bottom-40 -right-40" />
      <view class="absolute h-60 w-60 rounded-full bg-cyan-400/30 -bottom-20 -right-10" />
      <view class="absolute h-70 w-70 rounded-full from-blue-600 to-teal-400/30 bg-gradient-to-br -bottom-35 -right-20" />
    </view>

    <!-- 内容区 -->
    <view class="relative z-1 mt-20 w-600rpx flex flex-col items-center">
      <view class="h-120rpx w-full pb-9">
        <image
          src="../../static/images/login/icon-bg.svg"
          mode="widthFix"
          class="h-110rpx w-110rpx"
        />
      </view>
      <!-- 标题 -->
      <view class="mb-6 text-center text-2xl text-black font-semibold">
        欢迎使用车销系统（VBS）
      </view>

      <!-- 表单 -->
      <view class="mb-6 mt-6 w-full flex flex-col gap-3">
        <!-- 工号输入框 -->
        <view class="h-12 flex items-center border border-gray-200 rounded-sm bg-white pr-4">
          <view class="mx-3 h-5 w-5 flex items-center justify-center text-gray-400">
            <text class="i-carbon-user" />
          </view>
          <wd-input
            v-model="username"
            no-border
            placeholder="请输入员工工号"
            clearable
            custom-class="input-custom border-none bg-transparent h-full w-full flex-1"
          />
        </view>

        <!-- 密码输入框 -->
        <view class="mt-3 h-12 flex items-center border border-gray-200 rounded-sm bg-white pr-4">
          <view class="mx-3 h-5 w-5 flex items-center justify-center text-gray-400">
            <text class="i-carbon-password" />
          </view>
          <wd-input
            v-model="password"
            no-border
            placeholder="请输入密码"
            clearable
            :show-password="!showPassword"

            custom-class="input-custom h-full w-full flex-1 border-none bg-transparent"
          >
            <template #right>
              <view class="flex items-center justify-center px-3">
                <text
                  class="h-5 w-5 text-gray-400"
                  :class="showPassword ? 'i-carbon-view' : 'i-carbon-view-off'"
                  @click="togglePasswordVisibility"
                />
              </view>
            </template>
          </wd-input>
        </view>
      </view>

      <!-- 登录按钮 -->
      <wd-button
        block
        type="primary"
        custom-class="btn-login w-full h-12! rounded-2! font-medium! text-lg!"
        @click="handleLogin"
      >
        登录
      </wd-button>
    </view>
  </view>
</template>

<style lang="scss">
/* 使用UnoCSS定义自定义背景渐变和颜色 */
.bg-login-gradient {
  background: linear-gradient(135deg, #E5EDFF 0%, #F5F5F5 100%);
}

.bg-primary-blue {
  background-color: #1C64FD;
}

.text-primary-blue {
  color: #1C64FD;
}

/* wot-design-uni组件样式覆盖 */
.login {
  :deep() {
    /* Tabs样式 */
      .wd-tabs__nav {
        width: 100%;
        background: transparent !important;
        justify-content: space-around;
        height: 50px;
      }

      .wd-tabs__line {
        width: 36px !important;
        border-radius: 2px;
      }

    /* 输入框样式 */
    .input-custom {

      .wd-input__body,.wd-input__value {
        height: 100% !important;
      }
      .wd-input__inner {
        border: none !important;
        background: transparent !important;
        font-size: 16px !important;
        padding: 0 !important;
        color: #2D2D2D;
        height: 100% !important;
      }

      .wd-input__placeholder {
        color: #BFC2CC;
      }
    }

    /* 登录按钮样式 */
    .btn-login {
      background-color: #1C64FD !important;
      border-radius: 4px !important;
      font-size: 16px !important;
      border: none !important;
      margin-top: 24px;
    }

    /* 复选框样式 */
    .checkbox-square {
      .wd-checkbox__shape {
        width: 16px;
        height: 16px;
      }

    }
  }
}
</style>

<route lang="json">
  {
    "name": "login",
    "style": {
      "navigationStyle": "custom"
    }
  }
</route>
