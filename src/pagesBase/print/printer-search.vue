<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from 'vue'
import { BluetoothConnection } from '@/utils/bluetooth/BluetoothConnection'
import type { BluetoothDevice } from '@/utils/bluetooth/BluetoothConnection'

const { error: showError, success: showSuccess } = useGlobalToast()
const printerStore = usePrinterStore()

// 搜索关键词
const searchKeyword = ref('')
// 设备列表
const bluetoothDevices = ref<BluetoothDevice[]>([])
// 加载状态
const deviceLoading = ref(false)
// 蓝牙连接实例
let bluetoothConnection: BluetoothConnection | null = null

// 根据搜索关键词过滤设备
const displayDevices = computed(() => {
  if (!searchKeyword.value.trim()) {
    return bluetoothDevices.value
  }

  const keyword = searchKeyword.value.toLowerCase()
  return bluetoothDevices.value.filter((device: BluetoothDevice) =>
    device.name.toLowerCase().includes(keyword)
    || device.deviceId.toLowerCase().includes(keyword),
  )
})

// 初始化蓝牙连接工具
function initBluetoothConnection() {
  bluetoothConnection = new BluetoothConnection({
    onDeviceFound: (device: BluetoothDevice) => {
      console.log('发现设备:', device)
      // 更新设备列表
      const existingIndex = bluetoothDevices.value.findIndex((d: BluetoothDevice) => d.deviceId === device.deviceId)
      if (existingIndex === -1) {
        bluetoothDevices.value.push(device)
      }
      // 同时更新store中的可用设备列表
      printerStore.addAvailablePrinter(device)
    },
    onConnected: (deviceId: string) => {
      console.log('设备已连接:', deviceId)
    },
    onDisconnected: (deviceId: string) => {
      console.log('设备连接已断开:', deviceId)
    },
    onError: (error: Error) => {
      showError(error.message || '蓝牙操作失败')
    },
  })
}

// 搜索蓝牙设备
async function searchBluetoothDevices() {
  if (!bluetoothConnection) {
    initBluetoothConnection()
  }

  try {
    deviceLoading.value = true
    bluetoothDevices.value = []
    printerStore.clearAvailablePrinters()

    // 初始化蓝牙
    await bluetoothConnection!.initBluetooth()

    // 检查蓝牙状态
    await bluetoothConnection!.getBluetoothState()

    // 开始搜索
    const devices = await bluetoothConnection!.startScan()

    bluetoothDevices.value = devices
    // 更新store中的可用设备列表
    printerStore.setAvailablePrinters(devices)
    deviceLoading.value = false

    if (devices.length === 0) {
      showError('未搜索到蓝牙设备')
    }
  }
  catch (error: any) {
    deviceLoading.value = false
    showError(error.message || '搜索蓝牙设备失败')
  }
}

// 选择蓝牙设备（不连接，只保存到store）
function selectBluetoothDevice(device: BluetoothDevice) {
  // 保存选中的设备到store
  printerStore.setSelectedPrinter(device)

  // 更新本地设备列表的选中状态
  bluetoothDevices.value.forEach((d: BluetoothDevice) => {
    d.checked = d.deviceId === device.deviceId
  })

  showSuccess(`已选择打印机: ${device.name}`)

  // 返回上一页
  uni.navigateBack()
}

// 处理搜索框清空
function handleClear() {
  searchKeyword.value = ''
}

// 刷新设备列表
function handleRefresh() {
  searchBluetoothDevices()
}

// 页面加载时初始化
onMounted(() => {
  initBluetoothConnection()
  // 自动开始搜索
  searchBluetoothDevices()
})

// 页面卸载时清理资源
onUnmounted(() => {
  if (bluetoothConnection) {
    bluetoothConnection.closeBluetooth()
  }
})
</script>

<template>
  <view class="printer-search relative box-border min-h-screen w-screen overflow-x-hidden bg-[#F9F9F9] pt-52px">
    <!-- 搜索栏 -->
    <view class="fixed top-0 z-10 box-border w-full bg-white">
      <SearchBar
        v-model="searchKeyword"
        placeholder="搜索打印机名称或设备ID"
        :show-filter="false"
        @clear="handleClear"
      >
        <template #action>
          <view class="flex gap-2">
            <wd-button
              size="small"
              type="primary"
              :loading="deviceLoading"
              @click="handleRefresh"
            >
              {{ deviceLoading ? '搜索中...' : '刷新' }}
            </wd-button>
            <!-- <wd-button
              size="small"
              @click="openBluetoothConfirm"
            >
              蓝牙权限
            </wd-button> -->
          </view>
        </template>
      </SearchBar>
    </view>

    <!-- 主内容区域 -->
    <view class="main relative z-1 w-screen bg-[#F9F9F9]">
      <!-- 设备列表 -->
      <view v-if="displayDevices.length > 0" class="p-3">
        <view class="space-y-2">
          <view
            v-for="device in displayDevices"
            :key="device.deviceId"
            class="flex items-center justify-between rounded-lg bg-white p-4 shadow-sm"
            @click="selectBluetoothDevice(device)"
          >
            <view class="flex-1">
              <text class="block text-4 text-[#2D2D2D] font-medium">
                {{ device.name }}
              </text>
              <text class="text-3 text-gray-500">
                ID: {{ device.deviceId }}
              </text>
            </view>
            <view class="flex items-center">
              <text class="mr-3 text-3 text-gray-400">
                {{ device.RSSI }}dBm
              </text>
              <text
                v-if="printerStore.getCurrentPrinterId === device.deviceId"
                class="i-carbon-checkmark-filled text-lg text-green-500"
              />
              <text
                v-else
                class="i-carbon-chevron-right text-lg text-gray-300"
              />
            </view>
          </view>
        </view>
      </view>

      <!-- 搜索中状态 -->
      <view v-else-if="deviceLoading" class="flex flex-col items-center justify-center py-20">
        <wd-loading custom-class="mb-4" />
        <text class="text-gray-500">
          正在搜索蓝牙设备...
        </text>
      </view>

      <!-- 无设备状态 -->
      <view v-else class="flex flex-col items-center justify-center py-20">
        <text class="i-carbon-bluetooth text-6xl text-gray-300" />
        <text class="mt-4 text-gray-500">
          {{ searchKeyword.trim() ? '未找到匹配的设备' : '未搜索到蓝牙设备' }}
        </text>
        <text class="mt-1 text-28rpx text-gray-400">
          {{ searchKeyword.trim() ? '请尝试其他关键词' : '请确保设备已开启并处于可发现状态' }}
        </text>
        <wd-button
          v-if="!searchKeyword.trim()"
          type="primary"
          size="small"
          class="mt-4"
          :loading="deviceLoading"
          @click="handleRefresh"
        >
          重新搜索
        </wd-button>
      </view>
    </view>

    <wd-gap height="0" safe-area-bottom />
  </view>
</template>

<style lang="scss" scoped>
.printer-search {
  min-height: 100vh;
  background-color: #F9F9F9;
}
</style>

<route lang="json">
{
  "name": "printer-search",
  "style": {
    "navigationBarTitleText": "选择打印机",
    "backgroundColor": "#F9F9F9"
  }
}
</route>
