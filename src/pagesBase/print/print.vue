<script setup lang="ts">
import { createGlobalLoadingMiddleware } from '@/api/core/middleware'
import type { PrintRequestDTO, PrintTemplateResponseDTO } from '@/api/globals'
import { ConnectionState, ReliablePrinter } from '@/utils/bluetooth/ReliablePrinter'

const globalLoading = useGlobalLoading()
const printerStore = usePrinterStore()
const userStore = useUserStore()

// 查询参数
// 打印模板列表
const printTemplate = ref<PrintTemplateResponseDTO[]>([])
const { error: showError, success: showSuccess } = useGlobalToast()

const model = ref({
  // 模板名称
  templateName: '',
  //
  value1: '',
})

const moduleId = ref<string>() // 模块ID
const num = ref<string>() // 单号

// 可靠蓝牙打印机实例
let reliablePrinter: ReliablePrinter | null = null

// 连接状态和统计信息
const connectionState = ref<ConnectionState>(ConnectionState.DISCONNECTED)
const queueStatus = ref<{ size: number, processing: boolean, currentJob: any }>({ size: 0, processing: false, currentJob: null })
const printerStats = ref({
  totalPrintJobs: 0,
  successfulPrintJobs: 0,
  failedPrintJobs: 0,
  averagePrintTime: 0,
})

// 是否显示连接状态
const showConnectionStatus = ref(false)

// 蓝牙适配器是否可用
const bluetoothAdapterAvailable = ref(true)

// 初始化可靠打印工具
function initReliablePrinter() {
  reliablePrinter = new ReliablePrinter({
    // 连接状态变化回调
    onConnectionStateChange: (state: ConnectionState, error?: Error) => {
      connectionState.value = state

      switch (state) {
        case ConnectionState.CONNECTED:
          // showSuccess('设备连接成功')
          break
        case ConnectionState.DISCONNECTED:
          // if (printerStore.getCurrentPrinter) {
          //   showError('设备连接已断开，正在尝试重连...')
          // }
          break
        case ConnectionState.RECONNECTING:
          console.log('🔄 正在重连...')
          break
        case ConnectionState.ERROR:
          showError(error?.message || '连接出现错误，请检查设备')
          // 检查是否是蓝牙适配器失败
          if (error?.message && (
            error.message.includes('蓝牙适配器')
            || error.message.includes('无法使用该功能')
            || error.message.includes('蓝牙不可用')
            || error.message.includes('蓝牙系统异常')
          )) {
            bluetoothAdapterAvailable.value = false
            console.log('🚨 蓝牙适配器不可用，禁用打印功能')
          }
          break
      }
    },
    // 队列状态变化回调
    onQueueStateChange: (size: number, processing: boolean) => {
      queueStatus.value = {
        size,
        processing,
        currentJob: reliablePrinter?.getQueueStatus().currentJob || null,
      }
      // 管理全局loading状态
      if (processing && size > 0) {
        globalLoading.loading(`正在打印... (队列: ${size})`)
      }
      else {
        globalLoading.close()
      }

      console.log(`📋 打印队列: ${size} 个任务, 处理中: ${processing}`)
    },

    // 错误回调
    onError: (error: Error) => {
      console.error('🚨 蓝牙错误:', error)
      globalLoading.close()
      showError(error.message || '蓝牙操作失败')

      // 检查是否是蓝牙适配器失败
      if (error.message && (
        error.message.includes('蓝牙适配器')
        || error.message.includes('无法使用该功能')
        || error.message.includes('蓝牙不可用')
        || error.message.includes('蓝牙系统异常')
      )) {
        bluetoothAdapterAvailable.value = false
        console.log('🚨 蓝牙适配器不可用，禁用打印功能')
      }
    },
  })

  // 初始化时检查蓝牙适配器状态
  checkBluetoothAdapter()
}

// 检查蓝牙适配器状态
async function checkBluetoothAdapter() {
  if (!reliablePrinter) {
    return
  }

  try {
    // 检查是否可以进行打印操作
    const canPrint = reliablePrinter.canPrint()
    bluetoothAdapterAvailable.value = canPrint

    if (!canPrint) {
      const status = reliablePrinter.getPrinterStatus()
      console.log('🚨 打印功能不可用:', status.message)
    }
  }
  catch (error: any) {
    console.error('🚨 检查蓝牙适配器失败:', error)
    bluetoothAdapterAvailable.value = false
  }
}

// 连接到打印机
async function connectToPrinter(showLoading: boolean = true): Promise<void> {
  const selectedPrinter = printerStore.getCurrentPrinter
  if (!selectedPrinter) {
    showError('请先选择打印机')
    return
  }

  if (!reliablePrinter) {
    showError('打印工具未初始化')
    return
  }

  try {
    if (showLoading) {
      globalLoading.loading('连接打印机中...')
    }
    await reliablePrinter.connect(selectedPrinter)
    if (showLoading) {
      globalLoading.close()
    }

    // 连接成功，更新统计信息
    updatePrintStats()
    // 连接成功，确保蓝牙适配器状态为可用
    bluetoothAdapterAvailable.value = true

    console.log(`🔗 成功连接到设备: ${selectedPrinter.name}`)
  }
  catch (error: any) {
    if (showLoading) {
      globalLoading.close()
    }

    // 检查是否是蓝牙适配器失败
    if (error.message && (
      error.message.includes('蓝牙适配器')
      || error.message.includes('无法使用该功能')
      || error.message.includes('蓝牙不可用')
      || error.message.includes('蓝牙系统异常')
    )) {
      bluetoothAdapterAvailable.value = false
      console.log('🚨 蓝牙适配器不可用，禁用打印功能')
    }

    // 静默重连时不显示错误信息，避免干扰用户
    if (showLoading) {
      showError(error.message || '连接打印机失败')
    }
    else {
      console.warn('自动重连失败:', error.message)
    }
  }
}

// 断开连接
async function disconnectPrinter(): Promise<void> {
  if (!reliablePrinter)
    return

  try {
    await reliablePrinter.disconnect()
    showSuccess('已断开连接')
  }
  catch (error: any) {
    showError(error.message || '断开连接失败')
  }
}

// 跳转到打印机搜索页面
function goToPrinterSearch() {
  uni.navigateTo({
    url: '/pagesBase/print/printer-search',
  })
}

// 获取打印模板
const { send: getTemplate, loading: templateLoading } = useRequest(
  (moduleId: string) => Apis.printInterface.getTemplatesUsingGET({
    params: {
      moduleId,
      // 使用场景： 1-草稿，2-单据
      usage: num.value ? 2 : 1,
    },
  }),
  {
    immediate: false,
    middleware: createGlobalLoadingMiddleware({
      loadingText: '加载中...',
    }),
  },
).onSuccess((resp) => {
  globalLoading.close()
  const data = resp.data?.data
  if (data && data?.length > 0) {
    printTemplate.value = data
    if (resp.data.data?.length === 1) {
      model.value.templateName = resp.data.data[0].templateName!
    }
    else if (printerStore.getCurrentPrintTemplateName && data.find(item => item.templateName === printerStore.getCurrentPrintTemplateName)) {
      model.value.templateName = printerStore.getCurrentPrintTemplateName
    }
  }
  else {
    showError('没有可用的打印模板')
  }
}).onError((err) => {
  console.error(`获取打印模板失败${err.error}`)
  globalLoading.close()
  showError('获取打印模板失败')
})

// 获取打印文件
const { send: getPrintStr } = useRequest(
  (data: PrintRequestDTO) => Apis.printInterface.printUsingPOST({
    data,
  }),
  {
    immediate: false,
    middleware: createGlobalLoadingMiddleware({
      loadingText: '生成打印文件中...',
    }),
  },
).onSuccess(async (resp) => {
  globalLoading.close()
  if (resp.data?.data) {
    // 使用队列打印，支持文本和图片
    await addPrintJobToQueue(resp.data.data!)
  }
  else {
    showError('打印文件格式不支持')
  }
}).onError(() => {
  globalLoading.close()
  showError('获取打印文件失败')
})

// 添加打印任务到队列
async function addPrintJobToQueue(data: string): Promise<void> {
  if (!reliablePrinter) {
    showError('打印工具未初始化')
    return
  }

  try {
    // 确保连接
    if (connectionState.value !== ConnectionState.CONNECTED) {
      await connectToPrinter()
    }

    // 判断数据类型（简单判断：以http开头的为图片URL）
    const isImage = data.startsWith('http')

    console.log(`📄 添加${isImage ? '图片' : '文本'}打印任务到队列`)

    // 添加到打印队列
    if (isImage) {
      await reliablePrinter.printImage(data, {
        // 图片打印选项
        useOriginalWidth: true,
        maxWidth: 576,
        dithering: true,
      })
    }
    else {
      // 打印带FontSize指令的文本
      await reliablePrinter.printText(data, {
        // 文本打印选项
        fontSize: 'normal',
        align: 'left',
      })
    }

    console.log(`✅ 打印任务已添加到队列`)
  }
  catch (error: any) {
    showError(error.message || '添加打印任务失败')
  }
}

// 更新打印统计信息
function updatePrintStats(): void {
  if (reliablePrinter) {
    printerStats.value = reliablePrinter.getStats()
  }
}

// 处理路由参数
onLoad((options: any) => {
  num.value = options.id
  moduleId.value = options.moduleId
  getTemplate(options.moduleId)

  // 初始化可靠打印工具
  initReliablePrinter()
})

// 页面显示时自动连接
onShow(() => {
  // 延迟执行，等待蓝牙系统初始化完成
  setTimeout(() => {
    // 检查蓝牙适配器状态
    checkBluetoothAdapter()

    // 如果有选中的打印机且未连接，则自动连接
    if (printerStore.getCurrentPrinter && connectionState.value === ConnectionState.DISCONNECTED) {
      // 静默重连，不显示loading和错误信息
      connectToPrinter(false)
    }
  }, 500) // 延迟500ms，让蓝牙系统有时间初始化

  // 更新统计信息
  updatePrintStats()
})

// 页面卸载时清理资源
onUnmounted(() => {
  if (reliablePrinter) {
    reliablePrinter.dispose()
  }
})

const form = ref()

const selectedTemplate = computed(() => {
  return printTemplate.value.find(item => item.templateName === model.value.templateName)
})

// 获取连接状态显示文本
const connectionStatusText = computed(() => {
  switch (connectionState.value) {
    case ConnectionState.CONNECTED:
      return '已连接'
    case ConnectionState.CONNECTING:
      return '连接中...'
    case ConnectionState.RECONNECTING:
      return '重连中...'
    case ConnectionState.DISCONNECTED:
      return '未连接'
    case ConnectionState.ERROR:
      return '连接错误'
    default:
      return '未知状态'
  }
})

// 获取连接状态颜色
const connectionStatusColor = computed(() => {
  switch (connectionState.value) {
    case ConnectionState.CONNECTED:
      return '#07c160'
    case ConnectionState.CONNECTING:
    case ConnectionState.RECONNECTING:
      return '#ffc300'
    case ConnectionState.DISCONNECTED:
      return '#969799'
    case ConnectionState.ERROR:
      return '#ee0a24'
    default:
      return '#969799'
  }
})

function printOrder() {
  form.value.validate().then(() => {
    if (!printerStore.hasSelectedPrinter) {
      showError('请先选择打印设备')
      return
    }

    if (!selectedTemplate.value) {
      showError('请选择打印模板')
      return
    }

    // 构建打印请求
    const printData: PrintRequestDTO = {
      fileName: selectedTemplate.value?.fileName || '',
      moduleId: moduleId.value!,
      cls: moduleId.value,
      vehSaleEmpGid: userStore.vehSaleEmp?.gid || 0,
      num: num.value,
    }

    // 获取打印文件并添加到队列
    getPrintStr(printData)
  })
    .catch((error: any) => {
      console.log(error, 'error')
    })
}

function changeTemplateName(params: {
  value: string
}) {
  printerStore.setSelectPrintTemplateName(params.value)
}

// 切换连接状态显示
function toggleConnectionStatus() {
  showConnectionStatus.value = !showConnectionStatus.value
}

// 手动重连
async function manualReconnect() {
  if (reliablePrinter && printerStore.getCurrentPrinter) {
    try {
      // 手动重连显示loading和错误信息
      await connectToPrinter(true)
    }
    catch (error) {
      console.error('手动重连失败:', error)
    }
  }
  else {
    if (!printerStore.getCurrentPrinter) {
      showError('请先选择打印机')
      return
    }

    if (!reliablePrinter) {
      showError('打印工具未初始化')
    }
  }
}
</script>

<template>
  <view class="print relative box-border min-h-screen bg-[#F9F9F9]">
    <!-- 连接状态栏 -->
    <view class="connection-status-bar" @click="toggleConnectionStatus">
      <view class="status-indicator">
        <view
          class="status-dot"
          :style="{ backgroundColor: connectionStatusColor }"
        />
        <text class="status-text">
          {{ connectionStatusText }}
        </text>

        <!-- 队列状态 -->
        <text v-if="queueStatus.size > 0" class="queue-info">
          队列: {{ queueStatus.size }}
        </text>
      </view>

      <wd-icon name="arrow-down" size="12px" />
    </view>

    <!-- 连接详情面板 -->
    <view v-if="showConnectionStatus" class="connection-detail-panel">
      <view class="detail-item">
        <text class="detail-label">
          当前设备
        </text>
        <text class="detail-value">
          {{ printerStore.getCurrentPrinterName || '未选择' }}
        </text>
      </view>

      <view class="detail-item">
        <text class="detail-label">
          连接状态
        </text>
        <text class="detail-value" :style="{ color: connectionStatusColor }">
          {{ connectionStatusText }}
        </text>
      </view>

      <view class="detail-actions">
        <wd-button
          v-if="connectionState !== ConnectionState.CONNECTED"
          custom-class="action-btn"
          size="small"
          type="primary"
          @click="manualReconnect"
        >
          重新连接
        </wd-button>

        <wd-button
          v-if="connectionState === ConnectionState.CONNECTED"
          custom-class="action-btn"
          size="small"
          type="warning"
          @click="disconnectPrinter"
        >
          断开连接
        </wd-button>
      </view>
    </view>

    <wd-form ref="form" :model="model">
      <wd-cell-group border>
        <!-- 蓝牙打印机选择 -->
        <view :class="{ 'place-holder': !printerStore.getCurrentPrinterName }">
          <wd-cell
            title="打印机"
            :value="printerStore.getCurrentPrinterName || '请选择打印机'"
            is-link
            clickable
            required
            @click="goToPrinterSearch"
          />
        </view>

        <!-- 打印模板选择 -->
        <wd-select-picker
          v-model="model.templateName"
          prop="templateName"
          title="选择模板"
          label="打印模板"
          type="radio"
          required
          align-right
          label-key="templateName"
          placeholder="请选择打印模板"
          value-key="templateName"
          :columns="printTemplate"
          :loading="templateLoading"
          @confirm="changeTemplateName"
        />
      </wd-cell-group>
    </wd-form>

    <!-- 队列状态显示 -->
    <view v-if="queueStatus.processing" class="queue-status">
      <wd-loading size="16px" />
      <text class="queue-text">
        正在处理打印队列 ({{ queueStatus.size }} 个任务)
      </text>
    </view>

    <!-- 蓝牙适配器不可用提示 -->
    <wd-notice-bar
      v-if="!bluetoothAdapterAvailable"
      text="蓝牙适配器不可用，请检查设备蓝牙设置"
      type="danger"
      :scrollable="false"
      left-icon="warning"
    />

    <view class="print-bottom p-4">
      <wd-button
        custom-class="flex-auto h-11!"
        block
        type="primary"
        :round="false"
        :disabled="!bluetoothAdapterAvailable || queueStatus.processing || connectionState === ConnectionState.CONNECTING"
        @click="printOrder"
      >
        {{ !bluetoothAdapterAvailable ? '蓝牙不可用' : queueStatus.processing ? '打印中...' : '打印' }}
      </wd-button>
    </view>
  </view>
</template>

<style lang="scss" scoped>
  .print {
    position: relative;
    height: calc(100vh - 42px - 120rpx - var(--navbar-total-height)) !important;

    :deep() {
      .place-holder {
        .wd-cell__value {
          color: var(--wot-input-placeholder-color, #bfbfbf)
        }
      }
    }

    .label-required {
      position: relative;
      width: var(--wot-input-cell-label-width, 33%);
      color: var(--wot-cell-title-color, rgba(0, 0, 0, 0.85));
      margin-right: var(--wot-cell-padding, var(--wot-size-side-padding, 15px));
      box-sizing: border-box;

      &::before {
        content: '*';
        color: red;
        position: absolute;
        left: -20rpx;
        top: 0;
        font-size: 14px;
      }
    }

    :deep() {
      .wd-select-picker__header {
        height: 112rpx;
        line-height: 112rpx;
      }
      .wd-radio__label {
        width: calc(100% - 60rpx);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        text-align: left;
      }

      .wd-cell__left {
        min-width: 33%;
        max-width: 33%;
      }
      .wd-cell__value {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .wd-cell__right {
        min-width: 63%;
        max-width: 63%;
      }
    }

    &-bottom {
      position: absolute;
      bottom: 0;
      width: 100%;
      box-sizing: border-box;
      padding-bottom: 100rpx;
      background-color: #fff;
    }
  }

  /* 连接状态栏样式 */
  .connection-status-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 15px;
    background-color: #f8f8f8;
    border-bottom: 1px solid #e5e5e5;
    cursor: pointer;

    .status-indicator {
      display: flex;
      align-items: center;
      gap: 8px;

      .status-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
      }

      .status-text {
        font-size: 14px;
        color: #333;
      }

      .queue-info {
        font-size: 12px;
        color: #999;
        margin-left: 10px;
      }
    }
  }

  /* 连接详情面板样式 */
  .connection-detail-panel {
    background-color: #fff;
    border-bottom: 1px solid #e5e5e5;
    padding: 15px;

    .detail-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid #f5f5f5;

      &:last-child {
        border-bottom: none;
      }

      .detail-label {
        font-size: 14px;
        color: #666;
      }

      .detail-value {
        font-size: 14px;
        color: #333;
      }
    }

    .detail-actions {
      display: flex;
      gap: 10px;
      margin-top: 15px;
      justify-content: center;

      .action-btn {
        flex: 0 0 auto;
        min-width: 80px;
      }
    }
  }

  /* 队列状态样式 */
  .queue-status {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 15px;
    background-color: #fff3cd;
    border-left: 4px solid #ffc107;
    margin: 10px 15px;
    border-radius: 4px;

    .queue-text {
      font-size: 14px;
      color: #856404;
    }
  }

  .text-overflow-ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
</style>

<route lang="json">
{
  "name": "print",
  "style": {
    "navigationBarTitleText": "打印",
    "backgroundColor": "#F9F9F9"
  }
}
</route>
