/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by vite-plugin-uni-pages

interface NavigateToOptions {
  url: "/pages/index/index" |
       "/pages/profile/index" |
       "/pages/sales/sales-cart" |
       "/pages/sales/sales-checkin-history" |
       "/pages/sales/sales-detail-sku-list" |
       "/pages/sales/sales-detail" |
       "/pages/sales/sales-edit-search" |
       "/pages/sales/sales-edit" |
       "/pages/sales/sales-list" |
       "/pages/sales/sales-store-detail" |
       "/pages/sales/sales-store-list" |
       "/pages/salesman/index" |
       "/pages/salesman/salesman-search" |
       "/pages/warehouse/dashboard" |
       "/pagesBase/login/login" |
       "/pagesBase/login/wms-select" |
       "/pagesBase/print/print" |
       "/pagesBase/print/printer-search" |
       "/pagesSalesMan/back/back-detail-sku-list" |
       "/pagesSalesMan/back/back-detail" |
       "/pagesSalesMan/back/back-edit" |
       "/pagesSalesMan/back/back-list" |
       "/pagesSalesMan/back-diff/back-diff-detail-sku-list" |
       "/pagesSalesMan/back-diff/back-diff-detail" |
       "/pagesSalesMan/back-diff/back-diff-edit" |
       "/pagesSalesMan/back-diff/back-diff-list" |
       "/pagesSalesMan/compensation/compensation-detail-sku-list" |
       "/pagesSalesMan/compensation/compensation-detail" |
       "/pagesSalesMan/compensation/compensation-list" |
       "/pagesSalesMan/pick/pick-cart" |
       "/pagesSalesMan/pick/pick-detail-sku-list" |
       "/pagesSalesMan/pick/pick-detail" |
       "/pagesSalesMan/pick/pick-edit-search" |
       "/pagesSalesMan/pick/pick-edit" |
       "/pagesSalesMan/pick/pick-list" |
       "/pagesSalesMan/sales-back/sales-back-cart" |
       "/pagesSalesMan/sales-back/sales-back-detail-sku-list" |
       "/pagesSalesMan/sales-back/sales-back-detail" |
       "/pagesSalesMan/sales-back/sales-back-edit-search" |
       "/pagesSalesMan/sales-back/sales-back-edit" |
       "/pagesWarehouse/back-confirm/back-confirm-detail" |
       "/pagesWarehouse/back-confirm/back-confirm-list" |
       "/pagesWarehouse/pick-confirm/pick-confirm-detail" |
       "/pagesWarehouse/pick-confirm/pick-confirm-list" |
       "/pagesWarehouse/return-pick/return-pick-cart" |
       "/pagesWarehouse/return-pick/return-pick-detail-sku-list" |
       "/pagesWarehouse/return-pick/return-pick-detail" |
       "/pagesWarehouse/return-pick/return-pick-edit" |
       "/pagesWarehouse/return-pick/return-pick-list" |
       "/pagesWarehouse/warehouse-pick/warehouse-pick-cart" |
       "/pagesWarehouse/warehouse-pick/warehouse-pick-edit-search" |
       "/pagesWarehouse/warehouse-pick/warehouse-pick-edit" |
       "/pagesWarehouse/warehouse-sales/warehouse-sales-back-edit" |
       "/pagesWarehouse/warehouse-sales/warehouse-sales-edit";
}
interface RedirectToOptions extends NavigateToOptions {}

interface SwitchTabOptions {
  url: "/pages/salesman/index" | "/pages/warehouse/dashboard" | "/pages/sales/sales-store-list" | "/pages/profile/index"
}

type ReLaunchOptions = NavigateToOptions | SwitchTabOptions;

declare interface Uni {
  navigateTo(options: UniNamespace.NavigateToOptions & NavigateToOptions): void;
  redirectTo(options: UniNamespace.RedirectToOptions & RedirectToOptions): void;
  switchTab(options: UniNamespace.SwitchTabOptions & SwitchTabOptions): void;
  reLaunch(options: UniNamespace.ReLaunchOptions & ReLaunchOptions): void;
}
