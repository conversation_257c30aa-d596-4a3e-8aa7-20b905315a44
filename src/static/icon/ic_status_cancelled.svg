<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_44_50793)">
<path d="M12 24C18.6274 24 24 18.6274 24 12C24 5.37258 18.6274 0 12 0C5.37258 0 0 5.37258 0 12C0 18.6274 5.37258 24 12 24Z" fill="url(#paint0_linear_44_50793)"/>
<mask id="mask0_44_50793" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24">
<path d="M12 24C18.6274 24 24 18.6274 24 12C24 5.37258 18.6274 0 12 0C5.37258 0 0 5.37258 0 12C0 18.6274 5.37258 24 12 24Z" fill="white"/>
</mask>
<g mask="url(#mask0_44_50793)">
<path opacity="0.107507" d="M21 34.8C27.6274 34.8 33 29.4274 33 22.8C33 16.1726 27.6274 10.8 21 10.8C14.3726 10.8 9 16.1726 9 22.8C9 29.4274 14.3726 34.8 21 34.8Z" fill="url(#paint1_linear_44_50793)"/>
</g>
<mask id="mask1_44_50793" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24">
<path d="M12 24C18.6274 24 24 18.6274 24 12C24 5.37258 18.6274 0 12 0C5.37258 0 0 5.37258 0 12C0 18.6274 5.37258 24 12 24Z" fill="white"/>
</mask>
<g mask="url(#mask1_44_50793)">
<g filter="url(#filter0_d_44_50793)">
<path d="M11.9905 18.9999C11.0624 19.0042 10.1424 18.8262 9.28285 18.476C8.46031 18.1445 7.70434 17.6674 7.05129 17.0674C5.74069 15.8384 4.9919 14.1366 5.00007 12.397C5.00178 11.878 5.0685 11.3607 5.19783 10.8624C5.32552 10.3723 5.51333 9.89039 5.75799 9.43158C6.00206 8.97185 6.30289 8.54457 6.65341 8.15977C7.00763 7.7685 7.4088 7.41431 7.84374 7.10706C8.089 6.93296 8.39264 6.86171 8.68974 6.90855C8.98053 6.94314 9.2499 7.09553 9.42289 7.32617C9.60823 7.54445 9.68731 7.83852 9.63542 8.12517C9.59423 8.40194 9.43195 8.66059 9.19059 8.82862C8.59551 9.22731 8.10663 9.76508 7.76631 10.3953C6.85441 12.1054 7.20698 14.1679 8.64526 15.5254C9.08405 15.9376 9.59621 16.264 10.1552 16.4875C10.7401 16.7215 11.3595 16.8401 11.9955 16.8401C12.6314 16.8401 13.2509 16.7215 13.8358 16.4875C14.3943 16.2612 14.9068 15.9349 15.3482 15.5246C15.777 15.1232 16.1234 14.6419 16.368 14.1078C16.6217 13.5617 16.7469 12.9834 16.742 12.3895C16.742 10.9439 15.9874 9.59056 14.7238 8.76767C14.472 8.61251 14.298 8.35768 14.2452 8.06669C14.1895 7.77801 14.2609 7.47935 14.4412 7.24709C14.6574 6.96521 14.9947 6.80285 15.3498 6.8097C15.5838 6.8097 15.8111 6.8756 16.0039 6.99998L16.0492 7.02798L16.0517 7.0354C16.9375 7.63315 17.6736 8.42721 18.2025 9.3558C18.7283 10.2838 19.0031 11.3328 18.9999 12.3994C19.0037 13.2881 18.8125 14.1668 18.4397 14.9735C18.0786 15.7605 17.5696 16.4708 16.9405 17.0658C16.2943 17.6702 15.5404 18.148 14.718 18.4743C13.8612 18.8221 12.9449 19.0003 12.0202 18.999H11.9905V18.9999ZM11.9872 12.6078C11.6849 12.6078 11.4065 12.4983 11.1997 12.2998C10.989 12.1047 10.869 11.8307 10.8686 11.5436V6.08236C10.8678 5.79406 10.9856 5.52223 11.2006 5.31795C11.3999 5.11779 11.6841 5 11.9798 5C12.3052 5 12.5968 5.11697 12.8036 5.3196C13.0178 5.52306 13.1348 5.79406 13.1348 6.08318V11.5444C13.1364 11.8286 13.0145 12.1045 12.8011 12.3014C12.5855 12.4997 12.3032 12.6097 12.0103 12.6095L11.9872 12.6086V12.6078Z" fill="black"/>
</g>
<path d="M11.9905 18.9999C11.0624 19.0042 10.1424 18.8262 9.28285 18.476C8.46031 18.1445 7.70434 17.6674 7.05129 17.0674C5.74069 15.8384 4.9919 14.1366 5.00007 12.397C5.00178 11.878 5.0685 11.3607 5.19783 10.8624C5.32552 10.3723 5.51333 9.89039 5.75799 9.43158C6.00206 8.97185 6.30289 8.54457 6.65341 8.15977C7.00763 7.7685 7.4088 7.41431 7.84374 7.10706C8.089 6.93296 8.39264 6.86171 8.68974 6.90855C8.98053 6.94314 9.2499 7.09553 9.42289 7.32617C9.60823 7.54445 9.68731 7.83852 9.63542 8.12517C9.59423 8.40194 9.43195 8.66059 9.19059 8.82862C8.59551 9.22731 8.10663 9.76508 7.76631 10.3953C6.85441 12.1054 7.20698 14.1679 8.64526 15.5254C9.08405 15.9376 9.59621 16.264 10.1552 16.4875C10.7401 16.7215 11.3595 16.8401 11.9955 16.8401C12.6314 16.8401 13.2509 16.7215 13.8358 16.4875C14.3943 16.2612 14.9068 15.9349 15.3482 15.5246C15.777 15.1232 16.1234 14.6419 16.368 14.1078C16.6217 13.5617 16.7469 12.9834 16.742 12.3895C16.742 10.9439 15.9874 9.59056 14.7238 8.76767C14.472 8.61251 14.298 8.35768 14.2452 8.06669C14.1895 7.77801 14.2609 7.47935 14.4412 7.24709C14.6574 6.96521 14.9947 6.80285 15.3498 6.8097C15.5838 6.8097 15.8111 6.8756 16.0039 6.99998L16.0492 7.02798L16.0517 7.0354C16.9375 7.63315 17.6736 8.42721 18.2025 9.3558C18.7283 10.2838 19.0031 11.3328 18.9999 12.3994C19.0037 13.2881 18.8125 14.1668 18.4397 14.9735C18.0786 15.7605 17.5696 16.4708 16.9405 17.0658C16.2943 17.6702 15.5404 18.148 14.718 18.4743C13.8612 18.8221 12.9449 19.0003 12.0202 18.999H11.9905V18.9999ZM11.9872 12.6078C11.6849 12.6078 11.4065 12.4983 11.1997 12.2998C10.989 12.1047 10.869 11.8307 10.8686 11.5436V6.08236C10.8678 5.79406 10.9856 5.52223 11.2006 5.31795C11.3999 5.11779 11.6841 5 11.9798 5C12.3052 5 12.5968 5.11697 12.8036 5.3196C13.0178 5.52306 13.1348 5.79406 13.1348 6.08318V11.5444C13.1364 11.8286 13.0145 12.1045 12.8011 12.3014C12.5855 12.4997 12.3032 12.6097 12.0103 12.6095L11.9872 12.6086V12.6078Z" fill="white"/>
</g>
</g>
<defs>
<filter id="filter0_d_44_50793" x="1" y="3" width="22" height="22" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.504918 0 0 0 0 0.52465 0 0 0 0 0.564113 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_44_50793"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_44_50793" result="shape"/>
</filter>
<linearGradient id="paint0_linear_44_50793" x1="191.389" y1="130.31" x2="2180.2" y2="1909.59" gradientUnits="userSpaceOnUse">
<stop stop-color="#B3B5BA"/>
<stop offset="1" stop-color="#858C99"/>
</linearGradient>
<linearGradient id="paint1_linear_44_50793" x1="855.221" y1="154.943" x2="1001.43" y2="1555.39" gradientUnits="userSpaceOnUse">
<stop stop-color="#E7E7E7"/>
<stop offset="1" stop-color="#C7C7C7" stop-opacity="0"/>
</linearGradient>
<clipPath id="clip0_44_50793">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>
</svg>
