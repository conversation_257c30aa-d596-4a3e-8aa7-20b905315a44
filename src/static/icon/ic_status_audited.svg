<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="12" cy="12" r="12" fill="url(#paint0_linear_0_8)"/>
<mask id="mask0_0_8" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24">
<circle cx="12" cy="12" r="12" fill="white"/>
</mask>
<g mask="url(#mask0_0_8)">
<circle opacity="0.107507" cx="21" cy="22.8005" r="12" fill="url(#paint1_linear_0_8)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M18.2111 10.3717C18.7928 9.79004 18.7928 8.84699 18.2111 8.26534C17.6295 7.68369 16.6864 7.68369 16.1048 8.26534L11.1045 13.2656L8.89708 11.0582C8.31543 10.4766 7.37238 10.4766 6.79073 11.0582C6.20908 11.6399 6.20908 12.5829 6.79073 13.1646L9.98768 16.3615C10.1512 16.525 10.3433 16.6426 10.5479 16.7142C11.0907 16.9456 11.743 16.8399 12.1858 16.3971L18.2111 10.3717Z" fill="white"/>
<mask id="mask1_0_8" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="6" y="7" width="13" height="10">
<path fill-rule="evenodd" clip-rule="evenodd" d="M18.2111 10.3717C18.7928 9.79004 18.7928 8.84699 18.2111 8.26534C17.6295 7.68369 16.6864 7.68369 16.1048 8.26534L11.1045 13.2656L8.89708 11.0582C8.31543 10.4766 7.37238 10.4766 6.79073 11.0582C6.20908 11.6399 6.20908 12.5829 6.79073 13.1646L9.98768 16.3615C10.1512 16.525 10.3433 16.6426 10.5479 16.7142C11.0907 16.9456 11.743 16.8399 12.1858 16.3971L18.2111 10.3717Z" fill="white"/>
</mask>
<g mask="url(#mask1_0_8)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M9.19855 15.5776L11.1477 13.2131C11.1477 13.2131 4.8124 6.28445 4.58554 6.3123C4.35867 6.34016 1.23564 8.6515 1.23564 8.6515L1.35591 13.5525L3.03193 16.9599L9.19855 15.5776Z" fill="url(#paint2_linear_0_8)"/>
</g>
</g>
<defs>
<linearGradient id="paint0_linear_0_8" x1="-8.24856" y1="9.38339" x2="9.45174" y2="30.7838" gradientUnits="userSpaceOnUse">
<stop stop-color="#27D66B"/>
<stop offset="1" stop-color="#13BE78"/>
</linearGradient>
<linearGradient id="paint1_linear_0_8" x1="10.4257" y1="12.9766" x2="11.8949" y2="27.0495" gradientUnits="userSpaceOnUse">
<stop stop-color="#79FCB4"/>
<stop offset="1" stop-color="white" stop-opacity="0.01"/>
</linearGradient>
<linearGradient id="paint2_linear_0_8" x1="12.6016" y1="10.6216" x2="4.79272" y2="5.68331" gradientUnits="userSpaceOnUse">
<stop stop-color="#45E5B3" stop-opacity="0.118902"/>
<stop offset="1" stop-color="#21C77C" stop-opacity="0.01"/>
</linearGradient>
</defs>
</svg>
