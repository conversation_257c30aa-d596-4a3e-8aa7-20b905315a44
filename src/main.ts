import { createSSRApp } from 'vue'
import App from './App.vue'
import 'uno.css'
import router from './router'
import './api'
import datePlugin from './plugins/datePlugin'

initNumberPrototype()

const pinia = createPinia()
pinia.use(persistPlugin)

export function createApp() {
  const app = createSSRApp(App)
  app.config.warnHandler = () => null
  app.use(pinia)
  app.use(router)
  app.use(datePlugin)
  return {
    app,
    pinia,
  }
}
