<script setup lang="ts">
const deviceInfo = useDeviceInfo()
onLaunch(() => {
  if (!deviceInfo.initialized) {
    deviceInfo.init()
  }
})
</script>

<style lang="scss">
:root,
page {
  /* ==================== 基础原子色 - 主色系 ==================== */
  --atom-primary-primary-1: #f5f8ff;
  --atom-primary-primary-2: #e5edff;
  --atom-primary-primary-3: #b8cfff;
  --atom-primary-primary-4: #7ca4ff;
  --atom-primary-primary-5: #4480ff;
  --atom-primary-primary-6: #1c64fd;
  --atom-primary-primary-7: #164ed1;
  --atom-primary-primary-8: #1341ad;
  --atom-primary-primary-9: #0f3285;
  --atom-primary-primary-10: #0a235c;

  /* ==================== 文本和灰色应用 ==================== */
  --textapplication-text-1: var(--atom-grey-neutral-neutral-1, #2d2d2d);
  --textapplication-text-2: var(--atom-grey-neutral-neutral-2, #5c5c5c);
  --textapplication-text-3: var(--atom-grey-neutral-neutral-3, #8a8a8a);
  --textapplication-text-4: var(--atom-grey-neutral-neutral-4, #b2b2b2);
  --greyapplication-inputbox: var(--atom-grey-neutral-neutral-5, #d6d6d6);
  --greyapplication-line: var(--atom-grey-neutral-neutral-6, #e5e5e5);
  --greyapplication-secondary: var(--atom-grey-neutral-neutral-7, #f5f5f5);
  --greyapplication-bottomlayer: var(--atom-grey-neutral-neutral-8, #f9f9f9);

  /* ==================== 基础原子色 - 功能色系 ==================== */
  /* ----- 橙色 ----- */
  --atom-function-orange-orange-1: #fff6eb;
  --atom-function-orange-orange-2: #ffe8cc;
  --atom-function-orange-orange-3: #ffd8a8;
  --atom-function-orange-orange-4: #ffc078;
  --atom-function-orange-orange-5: #ffa94d;
  --atom-function-orange-orange-6: #f57f00;
  --atom-function-orange-orange-7: #d05706;
  --atom-function-orange-orange-8: #a94605;
  --atom-function-orange-orange-9: #813604;
  --atom-function-orange-orange-10: #592503;

  /* ----- 红色 ----- */
  --atom-function-red-red-1: #fff5f5;
  --atom-function-red-red-2: #ffe3e3;
  --atom-function-red-red-3: #ffc9c9;
  --atom-function-red-red-4: #ffa8a8;
  --atom-function-red-red-5: #fb7c7c;
  --atom-function-red-red-6: #f14646;
  --atom-function-red-red-7: #dc2c2c;
  --atom-function-red-red-8: #bc2626;
  --atom-function-red-red-9: #a01515;
  --atom-function-red-red-10: #790909;

  /* ----- 绿色 ----- */
  --atom-function-green-green-1: #f3fbf9;
  --atom-function-green-green-2: #e7f8f3;
  --atom-function-green-green-3: #b8eadb;
  --atom-function-green-green-4: #88dbc3;
  --atom-function-green-green-5: #59cdaa;
  --atom-function-green-green-6: #12b886;
  --atom-function-green-green-7: #0f956c;
  --atom-function-green-green-8: #0b6f51;
  --atom-function-green-green-9: #074a36;
  --atom-function-green-green-10: #04251b;

  /* ==================== 基础原子色 - 分类色系 ==================== */
  /* ----- 黄色 ----- */
  --atom-classify-yellow-yellow-1: #fffaf1;
  --atom-classify-yellow-yellow-2: #fde5b4;
  --atom-classify-yellow-yellow-3: #fdd78c;
  --atom-classify-yellow-yellow-4: #fcc964;
  --atom-classify-yellow-yellow-5: #fbbb3c;
  --atom-classify-yellow-yellow-6: #faad14;
  --atom-classify-yellow-yellow-7: #e19705;
  --atom-classify-yellow-yellow-8: #b97c04;
  --atom-classify-yellow-yellow-9: #916103;
  --atom-classify-yellow-yellow-10: #694702;

  /* ----- 青色 ----- */
  --atom-classify-cyan-cyan-1: #f4fbfd;
  --atom-classify-cyan-cyan-2: #e9f8fa;
  --atom-classify-cyan-cyan-3: #bdeaf1;
  --atom-classify-cyan-cyan-4: #90dbe7;
  --atom-classify-cyan-cyan-5: #64cddd;
  --atom-classify-cyan-cyan-6: #22b8cf;
  --atom-classify-cyan-cyan-7: #1c98ab;
  --atom-classify-cyan-cyan-8: #167988;
  --atom-classify-cyan-cyan-9: #115a65;
  --atom-classify-cyan-cyan-10: #0b3a42;

  /* ----- 紫色 ----- */
  --atom-classify-purple-purple-1: #f9f8ff;
  --atom-classify-purple-purple-2: #e5dbff;
  --atom-classify-purple-purple-3: #d0bfff;
  --atom-classify-purple-purple-4: #b197fc;
  --atom-classify-purple-purple-5: #9775fa;
  --atom-classify-purple-purple-6: #8059f3;
  --atom-classify-purple-purple-7: #5b29ef;
  --atom-classify-purple-purple-8: #4511df;
  --atom-classify-purple-purple-9: #390eb9;
  --atom-classify-purple-purple-10: #2d0b93;

  /* ----- 葡萄色 ----- */
  --atom-classify-grape-grape-1: #fbf6fd;
  --atom-classify-grape-grape-2: #f3d9fa;
  --atom-classify-grape-grape-3: #eebefa;
  --atom-classify-grape-grape-4: #e599f7;
  --atom-classify-grape-grape-5: #da77f2;
  --atom-classify-grape-grape-6: #ae3ec9;
  --atom-classify-grape-grape-7: #9731af;
  --atom-classify-grape-grape-8: #7b288f;
  --atom-classify-grape-grape-9: #601f70;
  --atom-classify-grape-grape-10: #451650;

  /* ----- 粉色 ----- */
  --atom-classify-pink-pink-1: #fff0f6;
  --atom-classify-pink-pink-2: #ffdeeb;
  --atom-classify-pink-pink-3: #fcc2d7;
  --atom-classify-pink-pink-4: #faa2c1;
  --atom-classify-pink-pink-5: #f783ac;
  --atom-classify-pink-pink-6: #ff357c;
  --atom-classify-pink-pink-7: #ff0a60;
  --atom-classify-pink-pink-8: #e0004f;
  --atom-classify-pink-pink-9: #b80040;
  --atom-classify-pink-pink-10: #8f0032;

  /* ==================== 基础原子色 - 中性色系 ==================== */
  --atom-grey-neutral-neutral-0: #000000;
  --atom-grey-neutral-neutral-1: #2d2d2d;
  --atom-grey-neutral-neutral-2: #5c5c5c;
  --atom-grey-neutral-neutral-3: #8a8a8a;
  --atom-grey-neutral-neutral-4: #b2b2b2;
  --atom-grey-neutral-neutral-5: #d6d6d6;
  --atom-grey-neutral-neutral-6: #e5e5e5;
  --atom-grey-neutral-neutral-7: #f5f5f5;
  --atom-grey-neutral-neutral-8: #f9f9f9;
  --atom-grey-neutral-neutral-9: #ffffff;
  --atom-grey-neutral-neutral-mask: rgba(0, 0, 0, 0.60);

  /* ==================== 应用色 - 主色应用 ==================== */
  --frequentapplication-prmari-background: var(--atom-primary-primary-2, #e5edff);
  --frequentapplication-primary-content: var(--atom-primary-primary-6, #1c64fd);
  --frequentapplication-prmari-border: var(--atom-primary-primary-3, #b8cfff);

  /* ==================== 应用色 - 功能色应用 ==================== */
  /* ----- 红色应用 ----- */
  --frequentapplication-red-background: var(--atom-function-red-red-1, #fff5f5);
  --frequentapplication-red-border: var(--atom-function-red-red-3, #ffc9c9);
  --frequentapplication-red-content: var(--atom-function-red-red-6, #f14646);

  /* ----- 绿色应用 ----- */
  --frequentapplication-green-background: var(--atom-function-green-green-1, #f3fbf9);
  --frequentapplication-green-border: var(--atom-function-green-green-3, #b8eadb);
  --frequentapplication-green-content: var(--atom-function-green-green-6, #12b886);

  /* ----- 橙色应用 ----- */
  --frequentapplication-orange-background: var(--atom-function-orange-orange-1, #fff6eb);
  --frequentapplication-orange--border: var(--atom-function-orange-orange-3, #ffd8a8);
  --frequentapplication-orange-content: var(--atom-function-orange-orange-6, #f57f00);

  /* ==================== 应用色 - 分类色应用 ==================== */
  /* ----- 黄色应用 ----- */
  --classifyapplication-yellow-background: var(--atom-classify-yellow-yellow-1, #fffaf1);
  --classifyapplication-yellow-border: var(--atom-classify-yellow-yellow-3, #fdd78c);
  --classifyapplication-yellow-content: var(--atom-classify-yellow-yellow-6, #faad14);

  /* ----- 青色应用 ----- */
  --classifyapplication-cyan-background: var(--atom-classify-cyan-cyan-1, #f4fbfd);
  --classifyapplication-cyan-border: var(--atom-classify-cyan-cyan-3, #bdeaf1);
  --classifyapplication-cyan-content: var(--atom-classify-cyan-cyan-6, #22b8cf);

  /* ----- 紫色应用 ----- */
  --classifyapplication-purple-background: var(--atom-classify-purple-purple-1, #f9f8ff);
  --classifyapplication-purple-border: var(--atom-classify-purple-purple-3, #d0bfff);
  --classifyapplication-purple-content: var(--atom-classify-purple-purple-6, #8059f3);

  /* ----- 葡萄色应用 ----- */
  --classifyapplication-grape-background: var(--atom-classify-grape-grape-1, #fbf6fd);
  --classifyapplication-grape-border: var(--atom-classify-grape-grape-3, #eebefa);
  --classifyapplication-grape-content: var(--atom-classify-grape-grape-6, #ae3ec9);

  /* ----- 粉色应用 ----- */
  --classifyapplication-pink-background: var(--atom-classify-pink-pink-1, #fff0f6);
  --classifyapplication-pink-border: var(--atom-classify-pink-pink-3, #fcc2d7);
  --classifyapplication-pink-content: var(--atom-classify-pink-pink-6, #ff357c);

  :deep() {
    .wd-sidebar-item--active {
      font-weight: 400 !important;
    }

    .wd-tooltip__pos {
      min-height: unset !important;
    }

    .wd-toast__msg {
      word-break: break-all;
    }

    .wd-radio__label {
      word-break: break-all;
    }
    .wd-sidebar-item__badge {
      word-break: break-all;
    }

    .wd-tabs__map{
      z-index: 2 !important;
    }

  }
}

::-webkit-scrollbar {
  width: 0;
  height: 0;
  background-color: transparent;
}
</style>
