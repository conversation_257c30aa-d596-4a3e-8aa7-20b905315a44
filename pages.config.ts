/*
 * @Author: weisheng
 * @Date: 2025-03-27 17:06:25
 * @LastEditTime: 2025-05-15 12:58:16
 * @LastEditors: weisheng
 * @Description:
 * @FilePath: /lsym-cx-mini/pages.config.ts
 * 记得注释
 */
import { defineUniPages } from '@uni-helper/vite-plugin-uni-pages'

export default defineUniPages({
  pages: [
  ],
  globalStyle: {
    navigationBarBackgroundColor: '#ffffff',
    navigationBarTextStyle: 'black',
  },
  tabBar: {
    custom: true,
    height: '0',
    color: '#bfbfbf',
    selectedColor: '#0165FF',
    list: [{
      pagePath: 'pages/salesman/index',
    }, {
      pagePath: 'pages/warehouse/dashboard',
    }, {
      pagePath: 'pages/sales/sales-store-list',
    }, {
      pagePath: 'pages/profile/index',
    }],
  },
})
